{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "1c143785bdf2dae727441a2386527436", "packages": [{"name": "airbrake/phpbrake", "version": "v1.0.0", "source": {"type": "git", "url": "https://github.com/airbrake/phpbrake.git", "reference": "****************************************"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/airbrake/phpbrake/zipball/****************************************", "reference": "****************************************", "shasum": ""}, "require": {"cash/lrucache": "^1.0", "guzzlehttp/guzzle": ">=6.3", "php": ">=8.1"}, "require-dev": {"mikey179/vfsstream": "^1.6", "monolog/monolog": "^1.22|^2.0", "phpunit/phpunit": "^10.0", "psy/psysh": "^0.11", "squizlabs/php_codesniffer": "^3.7"}, "suggest": {"guzzlehttp/guzzle": "Guzzle HTTP client"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"files": ["src/constants.php"], "psr-4": {"Airbrake\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Airbrake exception and error notifier for PHP", "homepage": "https://airbrake.io", "keywords": ["airbrake", "error", "exception", "logging", "notifier"], "support": {"issues": "https://github.com/airbrake/phpbrake/issues", "source": "https://github.com/airbrake/phpbrake/tree/v1.0.0"}, "time": "2023-12-07T00:39:40+00:00"}, {"name": "aws/aws-crt-php", "version": "v1.2.7", "source": {"type": "git", "url": "https://github.com/awslabs/aws-crt-php.git", "reference": "d71d9906c7bb63a28295447ba12e74723bd3730e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/awslabs/aws-crt-php/zipball/d71d9906c7bb63a28295447ba12e74723bd3730e", "reference": "d71d9906c7bb63a28295447ba12e74723bd3730e", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "^4.8.35||^5.6.3||^9.5", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"ext-awscrt": "Make sure you install awscrt native extension to use any of the functionality."}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "AWS SDK Common Runtime Team", "email": "<EMAIL>"}], "description": "AWS Common Runtime for PHP", "homepage": "https://github.com/awslabs/aws-crt-php", "keywords": ["amazon", "aws", "crt", "sdk"], "support": {"issues": "https://github.com/awslabs/aws-crt-php/issues", "source": "https://github.com/awslabs/aws-crt-php/tree/v1.2.7"}, "time": "2024-10-18T22:15:13+00:00"}, {"name": "aws/aws-sdk-php", "version": "3.337.3", "source": {"type": "git", "url": "https://github.com/aws/aws-sdk-php.git", "reference": "06dfc8f76423b49aaa181debd25bbdc724c346d6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aws/aws-sdk-php/zipball/06dfc8f76423b49aaa181debd25bbdc724c346d6", "reference": "06dfc8f76423b49aaa181debd25bbdc724c346d6", "shasum": ""}, "require": {"aws/aws-crt-php": "^1.2.3", "ext-json": "*", "ext-pcre": "*", "ext-simplexml": "*", "guzzlehttp/guzzle": "^6.5.8 || ^7.4.5", "guzzlehttp/promises": "^1.4.0 || ^2.0", "guzzlehttp/psr7": "^1.9.1 || ^2.4.5", "mtdowling/jmespath.php": "^2.6", "php": ">=7.2.5", "psr/http-message": "^1.0 || ^2.0"}, "require-dev": {"andrewsville/php-token-reflection": "^1.4", "aws/aws-php-sns-message-validator": "~1.0", "behat/behat": "~3.0", "composer/composer": "^1.10.22", "dms/phpunit-arraysubset-asserts": "^0.4.0", "doctrine/cache": "~1.4", "ext-dom": "*", "ext-openssl": "*", "ext-pcntl": "*", "ext-sockets": "*", "nette/neon": "^2.3", "paragonie/random_compat": ">= 2", "phpunit/phpunit": "^5.6.3 || ^8.5 || ^9.5", "psr/cache": "^1.0 || ^2.0 || ^3.0", "psr/simple-cache": "^1.0 || ^2.0 || ^3.0", "sebastian/comparator": "^1.2.3 || ^4.0", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"aws/aws-php-sns-message-validator": "To validate incoming SNS notifications", "doctrine/cache": "To use the DoctrineCacheAdapter", "ext-curl": "To send requests using cURL", "ext-openssl": "Allows working with CloudFront private distributions and verifying received SNS messages", "ext-sockets": "To use client-side monitoring"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Aws\\": "src/"}, "exclude-from-classmap": ["src/data/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Amazon Web Services", "homepage": "http://aws.amazon.com"}], "description": "AWS SDK for PHP - Use Amazon Web Services in your PHP project", "homepage": "http://aws.amazon.com/sdkforphp", "keywords": ["amazon", "aws", "cloud", "dynamodb", "ec2", "glacier", "s3", "sdk"], "support": {"forum": "https://forums.aws.amazon.com/forum.jspa?forumID=80", "issues": "https://github.com/aws/aws-sdk-php/issues", "source": "https://github.com/aws/aws-sdk-php/tree/3.337.3"}, "time": "2025-01-21T19:10:05+00:00"}, {"name": "brick/math", "version": "0.12.3", "source": {"type": "git", "url": "https://github.com/brick/math.git", "reference": "866551da34e9a618e64a819ee1e01c20d8a588ba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/brick/math/zipball/866551da34e9a618e64a819ee1e01c20d8a588ba", "reference": "866551da34e9a618e64a819ee1e01c20d8a588ba", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^10.1", "vimeo/psalm": "6.8.8"}, "type": "library", "autoload": {"psr-4": {"Brick\\Math\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Arbitrary-precision arithmetic library", "keywords": ["Arbitrary-precision", "BigInteger", "BigRational", "arithmetic", "bigdecimal", "bignum", "bignumber", "brick", "decimal", "integer", "math", "mathematics", "rational"], "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.12.3"}, "funding": [{"url": "https://github.com/BenMorel", "type": "github"}], "time": "2025-02-28T13:11:00+00:00"}, {"name": "brick/varexporter", "version": "0.5.0", "source": {"type": "git", "url": "https://github.com/brick/varexporter.git", "reference": "84b2a7a91f69aa5d079aec5a0a7256ebf2dceb6b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/brick/varexporter/zipball/84b2a7a91f69aa5d079aec5a0a7256ebf2dceb6b", "reference": "84b2a7a91f69aa5d079aec5a0a7256ebf2dceb6b", "shasum": ""}, "require": {"nikic/php-parser": "^5.0", "php": "^7.4 || ^8.0"}, "require-dev": {"php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^9.3", "psalm/phar": "5.21.1"}, "type": "library", "autoload": {"psr-4": {"Brick\\VarExporter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A powerful alternative to var_export(), which can export closures and objects without __set_state()", "keywords": ["var_export"], "support": {"issues": "https://github.com/brick/varexporter/issues", "source": "https://github.com/brick/varexporter/tree/0.5.0"}, "funding": [{"url": "https://github.com/BenMorel", "type": "github"}], "time": "2024-05-10T17:15:19+00:00"}, {"name": "carbonphp/carbon-doctrine-types", "version": "3.2.0", "source": {"type": "git", "url": "https://github.com/CarbonPHP/carbon-doctrine-types.git", "reference": "18ba5ddfec8976260ead6e866180bd5d2f71aa1d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/CarbonPHP/carbon-doctrine-types/zipball/18ba5ddfec8976260ead6e866180bd5d2f71aa1d", "reference": "18ba5ddfec8976260ead6e866180bd5d2f71aa1d", "shasum": ""}, "require": {"php": "^8.1"}, "conflict": {"doctrine/dbal": "<4.0.0 || >=5.0.0"}, "require-dev": {"doctrine/dbal": "^4.0.0", "nesbot/carbon": "^2.71.0 || ^3.0.0", "phpunit/phpunit": "^10.3"}, "type": "library", "autoload": {"psr-4": {"Carbon\\Doctrine\\": "src/Carbon/Doctrine/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "KyleKatarn", "email": "<EMAIL>"}], "description": "Types to use Carbon in Doctrine", "keywords": ["carbon", "date", "datetime", "doctrine", "time"], "support": {"issues": "https://github.com/CarbonPHP/carbon-doctrine-types/issues", "source": "https://github.com/CarbonPHP/carbon-doctrine-types/tree/3.2.0"}, "funding": [{"url": "https://github.com/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/nesbot/carbon", "type": "tidelift"}], "time": "2024-02-09T16:56:22+00:00"}, {"name": "cash/lrucache", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/cash/LRUCache.git", "reference": "4fa4c6834cec59690b43526c4da41d6153026289"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cash/LRUCache/zipball/4fa4c6834cec59690b43526c4da41d6153026289", "reference": "4fa4c6834cec59690b43526c4da41d6153026289", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "autoload": {"psr-0": {"cash": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "An efficient memory-based Least Recently Used (LRU) cache", "homepage": "https://github.com/cash/LRUCache", "keywords": ["cache", "lru"], "support": {"issues": "https://github.com/cash/LRUCache/issues", "source": "https://github.com/cash/LRUCache/tree/1.0.0"}, "time": "2013-09-20T18:59:12+00:00"}, {"name": "clue/stream-filter", "version": "v1.7.0", "source": {"type": "git", "url": "https://github.com/clue/stream-filter.git", "reference": "049509fef80032cb3f051595029ab75b49a3c2f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/clue/stream-filter/zipball/049509fef80032cb3f051595029ab75b49a3c2f7", "reference": "049509fef80032cb3f051595029ab75b49a3c2f7", "shasum": ""}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"Clue\\StreamFilter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A simple and modern approach to stream filtering in PHP", "homepage": "https://github.com/clue/stream-filter", "keywords": ["bucket brigade", "callback", "filter", "php_user_filter", "stream", "stream_filter_append", "stream_filter_register"], "support": {"issues": "https://github.com/clue/stream-filter/issues", "source": "https://github.com/clue/stream-filter/tree/v1.7.0"}, "funding": [{"url": "https://clue.engineering/support", "type": "custom"}, {"url": "https://github.com/clue", "type": "github"}], "time": "2023-12-20T15:40:13+00:00"}, {"name": "composer/pcre", "version": "3.3.2", "source": {"type": "git", "url": "https://github.com/composer/pcre.git", "reference": "b2bed4734f0cc156ee1fe9c0da2550420d99a21e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/pcre/zipball/b2bed4734f0cc156ee1fe9c0da2550420d99a21e", "reference": "b2bed4734f0cc156ee1fe9c0da2550420d99a21e", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "conflict": {"phpstan/phpstan": "<1.11.10"}, "require-dev": {"phpstan/phpstan": "^1.12 || ^2", "phpstan/phpstan-strict-rules": "^1 || ^2", "phpunit/phpunit": "^8 || ^9"}, "type": "library", "extra": {"phpstan": {"includes": ["extension.neon"]}, "branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Pcre\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "PCRE wrapping library that offers type-safe preg_* replacements.", "keywords": ["PCRE", "preg", "regex", "regular expression"], "support": {"issues": "https://github.com/composer/pcre/issues", "source": "https://github.com/composer/pcre/tree/3.3.2"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-11-12T16:29:46+00:00"}, {"name": "doctrine/dbal", "version": "4.2.3", "source": {"type": "git", "url": "https://github.com/doctrine/dbal.git", "reference": "33d2d7fe1269b2301640c44cf2896ea607b30e3e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/dbal/zipball/33d2d7fe1269b2301640c44cf2896ea607b30e3e", "reference": "33d2d7fe1269b2301640c44cf2896ea607b30e3e", "shasum": ""}, "require": {"doctrine/deprecations": "^0.5.3|^1", "php": "^8.1", "psr/cache": "^1|^2|^3", "psr/log": "^1|^2|^3"}, "require-dev": {"doctrine/coding-standard": "12.0.0", "fig/log-test": "^1", "jetbrains/phpstorm-stubs": "2023.2", "phpstan/phpstan": "2.1.1", "phpstan/phpstan-phpunit": "2.0.3", "phpstan/phpstan-strict-rules": "^2", "phpunit/phpunit": "10.5.39", "slevomat/coding-standard": "8.13.1", "squizlabs/php_codesniffer": "3.10.2", "symfony/cache": "^6.3.8|^7.0", "symfony/console": "^5.4|^6.3|^7.0"}, "suggest": {"symfony/console": "For helpful console commands such as SQL execution and import of files."}, "type": "library", "autoload": {"psr-4": {"Doctrine\\DBAL\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful PHP database abstraction layer (DBAL) with many features for database schema introspection and management.", "homepage": "https://www.doctrine-project.org/projects/dbal.html", "keywords": ["abstraction", "database", "db2", "dbal", "ma<PERSON>b", "mssql", "mysql", "oci8", "oracle", "pdo", "pgsql", "postgresql", "queryobject", "sasql", "sql", "sqlite", "sqlserver", "sqlsrv"], "support": {"issues": "https://github.com/doctrine/dbal/issues", "source": "https://github.com/doctrine/dbal/tree/4.2.3"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdbal", "type": "tidelift"}], "time": "2025-03-07T18:29:05+00:00"}, {"name": "doctrine/deprecations", "version": "1.1.5", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38", "reference": "459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"phpunit/phpunit": "<=7.5 || >=13"}, "require-dev": {"doctrine/coding-standard": "^9 || ^12 || ^13", "phpstan/phpstan": "1.4.10 || 2.1.11", "phpstan/phpstan-phpunit": "^1.0 || ^2", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.6 || ^10.5 || ^11.5 || ^12", "psr/log": "^1 || ^2 || ^3"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/1.1.5"}, "time": "2025-04-07T20:06:18+00:00"}, {"name": "doctrine/event-manager", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/doctrine/event-manager.git", "reference": "b680156fa328f1dfd874fd48c7026c41570b9c6e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/event-manager/zipball/b680156fa328f1dfd874fd48c7026c41570b9c6e", "reference": "b680156fa328f1dfd874fd48c7026c41570b9c6e", "shasum": ""}, "require": {"php": "^8.1"}, "conflict": {"doctrine/common": "<2.9"}, "require-dev": {"doctrine/coding-standard": "^12", "phpstan/phpstan": "^1.8.8", "phpunit/phpunit": "^10.5", "vimeo/psalm": "^5.24"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "event dispatcher", "event manager", "event system", "events"], "support": {"issues": "https://github.com/doctrine/event-manager/issues", "source": "https://github.com/doctrine/event-manager/tree/2.0.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fevent-manager", "type": "tidelift"}], "time": "2024-05-22T20:47:39+00:00"}, {"name": "doctrine/migrations", "version": "3.9.0", "source": {"type": "git", "url": "https://github.com/doctrine/migrations.git", "reference": "325b61e41d032f5f7d7e2d11cbefff656eadc9ab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/migrations/zipball/325b61e41d032f5f7d7e2d11cbefff656eadc9ab", "reference": "325b61e41d032f5f7d7e2d11cbefff656eadc9ab", "shasum": ""}, "require": {"composer-runtime-api": "^2", "doctrine/dbal": "^3.6 || ^4", "doctrine/deprecations": "^0.5.3 || ^1", "doctrine/event-manager": "^1.2 || ^2.0", "php": "^8.1", "psr/log": "^1.1.3 || ^2 || ^3", "symfony/console": "^5.4 || ^6.0 || ^7.0", "symfony/stopwatch": "^5.4 || ^6.0 || ^7.0", "symfony/var-exporter": "^6.2 || ^7.0"}, "conflict": {"doctrine/orm": "<2.12 || >=4"}, "require-dev": {"doctrine/coding-standard": "^12", "doctrine/orm": "^2.13 || ^3", "doctrine/persistence": "^2 || ^3 || ^4", "doctrine/sql-formatter": "^1.0", "ext-pdo_sqlite": "*", "fig/log-test": "^1", "phpstan/phpstan": "^1.10", "phpstan/phpstan-deprecation-rules": "^1.1", "phpstan/phpstan-phpunit": "^1.3", "phpstan/phpstan-strict-rules": "^1.4", "phpstan/phpstan-symfony": "^1.3", "phpunit/phpunit": "^10.3", "symfony/cache": "^5.4 || ^6.0 || ^7.0", "symfony/process": "^5.4 || ^6.0 || ^7.0", "symfony/yaml": "^5.4 || ^6.0 || ^7.0"}, "suggest": {"doctrine/sql-formatter": "Allows to generate formatted SQL with the diff command.", "symfony/yaml": "Allows the use of yaml for migration configuration files."}, "bin": ["bin/doctrine-migrations"], "type": "library", "autoload": {"psr-4": {"Doctrine\\Migrations\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Doctrine Migrations project offer additional functionality on top of the database abstraction layer (DBAL) for versioning your database schema and easily deploying changes to it. It is a very easy to use and a powerful tool.", "homepage": "https://www.doctrine-project.org/projects/migrations.html", "keywords": ["database", "dbal", "migrations"], "support": {"issues": "https://github.com/doctrine/migrations/issues", "source": "https://github.com/doctrine/migrations/tree/3.9.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fmigrations", "type": "tidelift"}], "time": "2025-03-26T06:48:45+00:00"}, {"name": "dompdf/dompdf", "version": "v3.1.0", "source": {"type": "git", "url": "https://github.com/dompdf/dompdf.git", "reference": "a51bd7a063a65499446919286fb18b518177155a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dompdf/dompdf/zipball/a51bd7a063a65499446919286fb18b518177155a", "reference": "a51bd7a063a65499446919286fb18b518177155a", "shasum": ""}, "require": {"dompdf/php-font-lib": "^1.0.0", "dompdf/php-svg-lib": "^1.0.0", "ext-dom": "*", "ext-mbstring": "*", "masterminds/html5": "^2.0", "php": "^7.1 || ^8.0"}, "require-dev": {"ext-gd": "*", "ext-json": "*", "ext-zip": "*", "mockery/mockery": "^1.3", "phpunit/phpunit": "^7.5 || ^8 || ^9 || ^10 || ^11", "squizlabs/php_codesniffer": "^3.5", "symfony/process": "^4.4 || ^5.4 || ^6.2 || ^7.0"}, "suggest": {"ext-gd": "Needed to process images", "ext-gmagick": "Improves image processing performance", "ext-imagick": "Improves image processing performance", "ext-zlib": "Needed for pdf stream compression"}, "type": "library", "autoload": {"psr-4": {"Dompdf\\": "src/"}, "classmap": ["lib/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1"], "authors": [{"name": "The Dompdf Community", "homepage": "https://github.com/dompdf/dompdf/blob/master/AUTHORS.md"}], "description": "DOMPDF is a CSS 2.1 compliant HTML to PDF converter", "homepage": "https://github.com/dompdf/dompdf", "support": {"issues": "https://github.com/dompdf/dompdf/issues", "source": "https://github.com/dompdf/dompdf/tree/v3.1.0"}, "time": "2025-01-15T14:09:04+00:00"}, {"name": "dompdf/php-font-lib", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/dompdf/php-font-lib.git", "reference": "6137b7d4232b7f16c882c75e4ca3991dbcf6fe2d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dompdf/php-font-lib/zipball/6137b7d4232b7f16c882c75e4ca3991dbcf6fe2d", "reference": "6137b7d4232b7f16c882c75e4ca3991dbcf6fe2d", "shasum": ""}, "require": {"ext-mbstring": "*", "php": "^7.1 || ^8.0"}, "require-dev": {"symfony/phpunit-bridge": "^3 || ^4 || ^5 || ^6"}, "type": "library", "autoload": {"psr-4": {"FontLib\\": "src/FontLib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-or-later"], "authors": [{"name": "The FontLib Community", "homepage": "https://github.com/dompdf/php-font-lib/blob/master/AUTHORS.md"}], "description": "A library to read, parse, export and make subsets of different types of font files.", "homepage": "https://github.com/dompdf/php-font-lib", "support": {"issues": "https://github.com/dompdf/php-font-lib/issues", "source": "https://github.com/dompdf/php-font-lib/tree/1.0.1"}, "time": "2024-12-02T14:37:59+00:00"}, {"name": "dompdf/php-svg-lib", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/dompdf/php-svg-lib.git", "reference": "eb045e518185298eb6ff8d80d0d0c6b17aecd9af"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dompdf/php-svg-lib/zipball/eb045e518185298eb6ff8d80d0d0c6b17aecd9af", "reference": "eb045e518185298eb6ff8d80d0d0c6b17aecd9af", "shasum": ""}, "require": {"ext-mbstring": "*", "php": "^7.1 || ^8.0", "sabberworm/php-css-parser": "^8.4"}, "require-dev": {"phpunit/phpunit": "^7.5 || ^8.5 || ^9.5"}, "type": "library", "autoload": {"psr-4": {"Svg\\": "src/Svg"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "The SvgLib Community", "homepage": "https://github.com/dompdf/php-svg-lib/blob/master/AUTHORS.md"}], "description": "A library to read, parse and export to PDF SVG files.", "homepage": "https://github.com/dompdf/php-svg-lib", "support": {"issues": "https://github.com/dompdf/php-svg-lib/issues", "source": "https://github.com/dompdf/php-svg-lib/tree/1.0.0"}, "time": "2024-04-29T13:26:35+00:00"}, {"name": "evenement/evenement", "version": "v3.0.2", "source": {"type": "git", "url": "https://github.com/igorw/evenement.git", "reference": "0a16b0d71ab13284339abb99d9d2bd813640efbc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/igorw/evenement/zipball/0a16b0d71ab13284339abb99d9d2bd813640efbc", "reference": "0a16b0d71ab13284339abb99d9d2bd813640efbc", "shasum": ""}, "require": {"php": ">=7.0"}, "require-dev": {"phpunit/phpunit": "^9 || ^6"}, "type": "library", "autoload": {"psr-4": {"Evenement\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}], "description": "Événement is a very simple event dispatching library for PHP", "keywords": ["event-dispatcher", "event-emitter"], "support": {"issues": "https://github.com/igorw/evenement/issues", "source": "https://github.com/igorw/evenement/tree/v3.0.2"}, "time": "2023-08-08T05:53:35+00:00"}, {"name": "fig/http-message-util", "version": "1.1.5", "source": {"type": "git", "url": "https://github.com/php-fig/http-message-util.git", "reference": "9d94dc0154230ac39e5bf89398b324a86f63f765"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message-util/zipball/9d94dc0154230ac39e5bf89398b324a86f63f765", "reference": "9d94dc0154230ac39e5bf89398b324a86f63f765", "shasum": ""}, "require": {"php": "^5.3 || ^7.0 || ^8.0"}, "suggest": {"psr/http-message": "The package containing the PSR-7 interfaces"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Fig\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Utility classes and constants for use with PSR-7 (psr/http-message)", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"issues": "https://github.com/php-fig/http-message-util/issues", "source": "https://github.com/php-fig/http-message-util/tree/1.1.5"}, "time": "2020-11-24T22:02:12+00:00"}, {"name": "firebase/php-jwt", "version": "v6.11.1", "source": {"type": "git", "url": "https://github.com/firebase/php-jwt.git", "reference": "d1e91ecf8c598d073d0995afa8cd5c75c6e19e66"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/firebase/php-jwt/zipball/d1e91ecf8c598d073d0995afa8cd5c75c6e19e66", "reference": "d1e91ecf8c598d073d0995afa8cd5c75c6e19e66", "shasum": ""}, "require": {"php": "^8.0"}, "require-dev": {"guzzlehttp/guzzle": "^7.4", "phpspec/prophecy-phpunit": "^2.0", "phpunit/phpunit": "^9.5", "psr/cache": "^2.0||^3.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0"}, "suggest": {"ext-sodium": "Support EdDSA (Ed25519) signatures", "paragonie/sodium_compat": "Support EdDSA (Ed25519) signatures when libsodium is not present"}, "type": "library", "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "keywords": ["jwt", "php"], "support": {"issues": "https://github.com/firebase/php-jwt/issues", "source": "https://github.com/firebase/php-jwt/tree/v6.11.1"}, "time": "2025-04-09T20:32:01+00:00"}, {"name": "google/auth", "version": "v1.44.0", "source": {"type": "git", "url": "https://github.com/googleapis/google-auth-library-php.git", "reference": "5670e56307d7a2eac931f677c0e59a4f8abb2e43"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-auth-library-php/zipball/5670e56307d7a2eac931f677c0e59a4f8abb2e43", "reference": "5670e56307d7a2eac931f677c0e59a4f8abb2e43", "shasum": ""}, "require": {"firebase/php-jwt": "^6.0", "guzzlehttp/guzzle": "^7.4.5", "guzzlehttp/psr7": "^2.4.5", "php": "^8.1", "psr/cache": "^2.0||^3.0", "psr/http-message": "^1.1||^2.0"}, "require-dev": {"guzzlehttp/promises": "^2.0", "kelvinmo/simplejwt": "0.7.1", "phpseclib/phpseclib": "^3.0.35", "phpspec/prophecy-phpunit": "^2.1", "phpunit/phpunit": "^9.6", "sebastian/comparator": ">=1.2.3", "squizlabs/php_codesniffer": "^3.5", "symfony/process": "^6.0||^7.0", "webmozart/assert": "^1.11"}, "suggest": {"phpseclib/phpseclib": "May be used in place of OpenSSL for signing strings or for token management. Please require version ^2."}, "type": "library", "autoload": {"psr-4": {"Google\\Auth\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Google Auth Library for PHP", "homepage": "http://github.com/google/google-auth-library-php", "keywords": ["Authentication", "google", "oauth2"], "support": {"docs": "https://googleapis.github.io/google-auth-library-php/main/", "issues": "https://github.com/googleapis/google-auth-library-php/issues", "source": "https://github.com/googleapis/google-auth-library-php/tree/v1.44.0"}, "time": "2024-12-04T15:34:58+00:00"}, {"name": "google/cloud-core", "version": "v1.60.0", "source": {"type": "git", "url": "https://github.com/googleapis/google-cloud-php-core.git", "reference": "7d63ba4295b799dc63227b6c9daf9dc207650eb4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-cloud-php-core/zipball/7d63ba4295b799dc63227b6c9daf9dc207650eb4", "reference": "7d63ba4295b799dc63227b6c9daf9dc207650eb4", "shasum": ""}, "require": {"google/auth": "^1.34", "google/gax": "^1.34.0", "guzzlehttp/guzzle": "^6.5.8|^7.4.4", "guzzlehttp/promises": "^1.4||^2.0", "guzzlehttp/psr7": "^2.6", "monolog/monolog": "^2.9|^3.0", "php": "^8.0", "psr/http-message": "^1.0|^2.0", "rize/uri-template": "~0.3"}, "require-dev": {"erusev/parsedown": "^1.6", "google/cloud-common-protos": "~0.5", "opis/closure": "^3", "phpdocumentor/reflection": "^5.3.3", "phpdocumentor/reflection-docblock": "^5.3", "phpspec/prophecy-phpunit": "^2.0", "phpunit/phpunit": "^9.0", "squizlabs/php_codesniffer": "2.*"}, "suggest": {"opis/closure": "May be used to serialize closures to process jobs in the batch daemon. Please require version ^3.", "symfony/lock": "Required for the Spanner cached based session pool. Please require the following commit: 3.3.x-dev#1ba6ac9"}, "bin": ["bin/google-cloud-batch"], "type": "library", "extra": {"component": {"id": "cloud-core", "path": "Core", "entry": "src/ServiceBuilder.php", "target": "googleapis/google-cloud-php-core.git"}}, "autoload": {"psr-4": {"Google\\Cloud\\Core\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Google Cloud PHP shared dependency, providing functionality useful to all components.", "support": {"source": "https://github.com/googleapis/google-cloud-php-core/tree/v1.60.0"}, "time": "2024-09-28T04:24:22+00:00"}, {"name": "google/cloud-translate", "version": "v1.20.0", "source": {"type": "git", "url": "https://github.com/googleapis/google-cloud-php-translate.git", "reference": "985a0cae919746376fe5f678f74fdff7185515c6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-cloud-php-translate/zipball/985a0cae919746376fe5f678f74fdff7185515c6", "reference": "985a0cae919746376fe5f678f74fdff7185515c6", "shasum": ""}, "require": {"google/cloud-core": "^1.52.7", "google/gax": "^1.34.0", "php": "^8.0"}, "require-dev": {"erusev/parsedown": "^1.6", "phpdocumentor/reflection": "^5.3.3", "phpdocumentor/reflection-docblock": "^5.3", "phpspec/prophecy-phpunit": "^2.0", "phpunit/phpunit": "^9.0", "squizlabs/php_codesniffer": "2.*"}, "suggest": {"ext-grpc": "The gRPC extension enables use of the performant gRPC transport", "ext-protobuf": "Provides a significant increase in throughput over the pure PHP protobuf implementation. See https://cloud.google.com/php/grpc for installation instructions."}, "type": "library", "extra": {"component": {"id": "cloud-translate", "path": "Translate", "entry": "src/TranslateClient.php", "target": "googleapis/google-cloud-php-translate.git"}}, "autoload": {"psr-4": {"Google\\Cloud\\Translate\\": "src", "GPBMetadata\\Google\\Cloud\\Translate\\": "metadata"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Cloud Translation Client for PHP", "support": {"source": "https://github.com/googleapis/google-cloud-php-translate/tree/v1.20.0"}, "time": "2024-11-06T21:50:43+00:00"}, {"name": "google/common-protos", "version": "4.12.0", "source": {"type": "git", "url": "https://github.com/googleapis/common-protos-php.git", "reference": "96f6873c65fb0289fcdb6ac3d5239f9c9e5750b0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/common-protos-php/zipball/96f6873c65fb0289fcdb6ac3d5239f9c9e5750b0", "reference": "96f6873c65fb0289fcdb6ac3d5239f9c9e5750b0", "shasum": ""}, "require": {"google/protobuf": "^v3.25.3||^4.26.1", "php": "^8.0"}, "require-dev": {"phpunit/phpunit": "^9.6"}, "type": "library", "extra": {"component": {"id": "common-protos", "path": "CommonProtos", "entry": "README.md", "target": "googleapis/common-protos-php.git"}}, "autoload": {"psr-4": {"Google\\Api\\": "src/Api", "Google\\Iam\\": "src/Iam", "Google\\Rpc\\": "src/Rpc", "Google\\Type\\": "src/Type", "Google\\Cloud\\": "src/Cloud", "GPBMetadata\\Google\\Api\\": "metadata/Api", "GPBMetadata\\Google\\Iam\\": "metadata/Iam", "GPBMetadata\\Google\\Rpc\\": "metadata/Rpc", "GPBMetadata\\Google\\Type\\": "metadata/Type", "GPBMetadata\\Google\\Cloud\\": "metadata/Cloud", "GPBMetadata\\Google\\Logging\\": "metadata/Logging"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Google API Common Protos for PHP", "homepage": "https://github.com/googleapis/common-protos-php", "keywords": ["google"], "support": {"source": "https://github.com/googleapis/common-protos-php/tree/v4.12.0"}, "time": "2025-04-18T20:49:28+00:00"}, {"name": "google/gax", "version": "v1.35.1", "source": {"type": "git", "url": "https://github.com/googleapis/gax-php.git", "reference": "336005867c0ca3e2ad95183cf9dd74fa67915dd9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/gax-php/zipball/336005867c0ca3e2ad95183cf9dd74fa67915dd9", "reference": "336005867c0ca3e2ad95183cf9dd74fa67915dd9", "shasum": ""}, "require": {"google/auth": "^1.34.0", "google/common-protos": "^4.4", "google/grpc-gcp": "^0.4", "google/longrunning": "~0.4", "google/protobuf": "^v3.25.3||^4.26.1", "grpc/grpc": "^1.13", "guzzlehttp/promises": "^2.0", "guzzlehttp/psr7": "^2.0", "php": "^8.0", "ramsey/uuid": "^4.0"}, "conflict": {"ext-protobuf": "<3.7.0"}, "require-dev": {"phpspec/prophecy-phpunit": "^2.1", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^9.6", "squizlabs/php_codesniffer": "3.*"}, "type": "library", "autoload": {"psr-4": {"Google\\ApiCore\\": "src", "GPBMetadata\\ApiCore\\": "metadata/ApiCore"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Google API Core for PHP", "homepage": "https://github.com/googleapis/gax-php", "keywords": ["google"], "support": {"issues": "https://github.com/googleapis/gax-php/issues", "source": "https://github.com/googleapis/gax-php/tree/v1.35.1"}, "time": "2024-12-04T15:32:12+00:00"}, {"name": "google/grpc-gcp", "version": "v0.4.1", "source": {"type": "git", "url": "https://github.com/GoogleCloudPlatform/grpc-gcp-php.git", "reference": "e585b7721bbe806ef45b5c52ae43dfc2bff89968"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/GoogleCloudPlatform/grpc-gcp-php/zipball/e585b7721bbe806ef45b5c52ae43dfc2bff89968", "reference": "e585b7721bbe806ef45b5c52ae43dfc2bff89968", "shasum": ""}, "require": {"google/auth": "^1.3", "google/protobuf": "^v3.25.3||^4.26.1", "grpc/grpc": "^v1.13.0", "php": "^8.0", "psr/cache": "^1.0.1||^2.0.0||^3.0.0"}, "require-dev": {"google/cloud-spanner": "^1.7", "phpunit/phpunit": "^9.0"}, "type": "library", "autoload": {"psr-4": {"Grpc\\Gcp\\": "src/"}, "classmap": ["src/generated/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "gRPC GCP library for channel management", "support": {"issues": "https://github.com/GoogleCloudPlatform/grpc-gcp-php/issues", "source": "https://github.com/GoogleCloudPlatform/grpc-gcp-php/tree/v0.4.1"}, "time": "2025-02-19T21:53:22+00:00"}, {"name": "google/longrunning", "version": "0.4.7", "source": {"type": "git", "url": "https://github.com/googleapis/php-longrunning.git", "reference": "624cabb874c10e5ddc9034c999f724894b70a3d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/php-longrunning/zipball/624cabb874c10e5ddc9034c999f724894b70a3d3", "reference": "624cabb874c10e5ddc9034c999f724894b70a3d3", "shasum": ""}, "require-dev": {"google/gax": "^1.36.0", "phpunit/phpunit": "^9.0"}, "type": "library", "extra": {"component": {"id": "longrunning", "path": "<PERSON><PERSON><PERSON>ning", "entry": null, "target": "googleapis/php-longrunning"}}, "autoload": {"psr-4": {"Google\\LongRunning\\": "src/LongRunning", "Google\\ApiCore\\LongRunning\\": "src/ApiCore/LongRunning", "GPBMetadata\\Google\\Longrunning\\": "metadata/Longrunning"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Google LongRunning Client for PHP", "support": {"source": "https://github.com/googleapis/php-longrunning/tree/v0.4.7"}, "time": "2025-01-24T21:24:06+00:00"}, {"name": "google/protobuf", "version": "v4.30.2", "source": {"type": "git", "url": "https://github.com/protocolbuffers/protobuf-php.git", "reference": "a4c4d8565b40b9f76debc9dfeb221412eacb8ced"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/protocolbuffers/protobuf-php/zipball/a4c4d8565b40b9f76debc9dfeb221412eacb8ced", "reference": "a4c4d8565b40b9f76debc9dfeb221412eacb8ced", "shasum": ""}, "require": {"php": ">=7.0.0"}, "require-dev": {"phpunit/phpunit": ">=5.0.0"}, "suggest": {"ext-bcmath": "Need to support JSON deserialization"}, "type": "library", "autoload": {"psr-4": {"Google\\Protobuf\\": "src/Google/Protobuf", "GPBMetadata\\Google\\Protobuf\\": "src/GPBMetadata/Google/Protobuf"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "proto library for PHP", "homepage": "https://developers.google.com/protocol-buffers/", "keywords": ["proto"], "support": {"source": "https://github.com/protocolbuffers/protobuf-php/tree/v4.30.2"}, "time": "2025-03-26T18:01:50+00:00"}, {"name": "grpc/grpc", "version": "1.57.0", "source": {"type": "git", "url": "https://github.com/grpc/grpc-php.git", "reference": "b610c42022ed3a22f831439cb93802f2a4502fdf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/grpc/grpc-php/zipball/b610c42022ed3a22f831439cb93802f2a4502fdf", "reference": "b610c42022ed3a22f831439cb93802f2a4502fdf", "shasum": ""}, "require": {"php": ">=7.0.0"}, "require-dev": {"google/auth": "^v1.3.0"}, "suggest": {"ext-protobuf": "For better performance, install the protobuf C extension.", "google/protobuf": "To get started using grpc quickly, install the native protobuf library."}, "type": "library", "autoload": {"psr-4": {"Grpc\\": "src/lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "gRPC library for PHP", "homepage": "https://grpc.io", "keywords": ["rpc"], "support": {"source": "https://github.com/grpc/grpc-php/tree/v1.57.0"}, "time": "2023-08-14T23:57:54+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.9.3", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "7b2f29fe81dc4da0ca0ea7d42107a0845946ea77"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/7b2f29fe81dc4da0ca0ea7d42107a0845946ea77", "reference": "7b2f29fe81dc4da0ca0ea7d42107a0845946ea77", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.3", "guzzlehttp/psr7": "^2.7.0", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "guzzle/client-integration-tests": "3.0.2", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.39 || ^9.6.20", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.9.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2025-03-27T13:37:11+00:00"}, {"name": "guzzlehttp/promises", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "7c69f28996b0a6920945dd20b3857e499d9ca96c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/7c69f28996b0a6920945dd20b3857e499d9ca96c", "reference": "7c69f28996b0a6920945dd20b3857e499d9ca96c", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.2.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2025-03-27T13:27:01+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.7.1", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/c2270caaabe631b3b44c85f99e5a04bbb8060d16", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "http-interop/http-factory-tests": "0.9.0", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.7.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2025-03-27T12:30:47+00:00"}, {"name": "laminas/laminas-authentication", "version": "2.18.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-authentication.git", "reference": "c1da3ec75bd4d6e3c63cf3a89f0f1a59a81a82bd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-authentication/zipball/c1da3ec75bd4d6e3c63cf3a89f0f1a59a81a82bd", "reference": "c1da3ec75bd4d6e3c63cf3a89f0f1a59a81a82bd", "shasum": ""}, "require": {"ext-mbstring": "*", "laminas/laminas-stdlib": "^3.19.0", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "conflict": {"zendframework/zend-authentication": "*"}, "require-dev": {"laminas/laminas-coding-standard": "~2.5.0", "laminas/laminas-db": "^2.20.0", "laminas/laminas-http": "^2.19.0", "laminas/laminas-ldap": "^2.18.1", "laminas/laminas-session": "^2.21.0", "laminas/laminas-uri": "^2.12.0", "laminas/laminas-validator": "^2.64.1", "phpunit/phpunit": "^9.6.20", "psalm/plugin-phpunit": "^0.19.0", "squizlabs/php_codesniffer": "^3.10.2", "vimeo/psalm": "^5.26.0"}, "suggest": {"laminas/laminas-db": "Laminas\\Db component", "laminas/laminas-http": "Laminas\\Http component", "laminas/laminas-ldap": "Laminas\\Ldap component", "laminas/laminas-session": "Laminas\\Session component", "laminas/laminas-uri": "Laminas\\Uri component", "laminas/laminas-validator": "Laminas\\Validator component"}, "type": "library", "autoload": {"psr-4": {"Laminas\\Authentication\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "provides an API for authentication and includes concrete authentication adapters for common use case scenarios", "homepage": "https://laminas.dev", "keywords": ["Authentication", "laminas"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-authentication/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-authentication/issues", "rss": "https://github.com/laminas/laminas-authentication/releases.atom", "source": "https://github.com/laminas/laminas-authentication"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2024-10-21T10:45:35+00:00"}, {"name": "laminas/laminas-cli", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-cli.git", "reference": "159b48f896fb2502cb6618a95307b56a0f23e592"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-cli/zipball/159b48f896fb2502cb6618a95307b56a0f23e592", "reference": "159b48f896fb2502cb6618a95307b56a0f23e592", "shasum": ""}, "require": {"composer-runtime-api": "^2.0.0", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0", "psr/container": "^1.0 || ^2.0", "symfony/console": "^6.0 || ^7.0", "symfony/event-dispatcher": "^6.0 || ^7.0", "webmozart/assert": "^1.10"}, "conflict": {"amphp/amp": "<2.6.4"}, "require-dev": {"laminas/laminas-coding-standard": "^3.0.1", "laminas/laminas-mvc": "^3.8.0", "laminas/laminas-servicemanager": "^3.23.0", "mikey179/vfsstream": "2.0.x-dev", "phpunit/phpunit": "^10.5.38", "psalm/plugin-phpunit": "^0.19.0", "vimeo/psalm": "^5.26.1"}, "bin": ["bin/laminas"], "type": "library", "autoload": {"psr-4": {"Laminas\\Cli\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Command-line interface for Laminas projects", "keywords": ["cli", "command", "console", "laminas"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-cli/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/mezzio/laminas-cli/issues", "rss": "https://github.com/mezzio/laminas-cli/releases.atom", "source": "https://github.com/mezzio/laminas-cli"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2024-11-22T12:39:15+00:00"}, {"name": "laminas/laminas-component-installer", "version": "3.5.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-component-installer.git", "reference": "8752d73b5df8c368ec0bf2aff2b32e00e0ac8b1b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-component-installer/zipball/8752d73b5df8c368ec0bf2aff2b32e00e0ac8b1b", "reference": "8752d73b5df8c368ec0bf2aff2b32e00e0ac8b1b", "shasum": ""}, "require": {"composer-plugin-api": "^2.0", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "conflict": {"zendframework/zend-component-installer": "*"}, "require-dev": {"composer/composer": "^2.7.7", "laminas/laminas-coding-standard": "~3.0.0", "mikey179/vfsstream": "^1.6.11", "phpunit/phpunit": "^10.5.35", "psalm/plugin-phpunit": "^0.19.0", "vimeo/psalm": "^5.24.0", "webmozart/assert": "^1.11.0"}, "type": "composer-plugin", "extra": {"class": "Laminas\\ComponentInstaller\\ComponentInstaller"}, "autoload": {"psr-4": {"Laminas\\ComponentInstaller\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Composer plugin for injecting modules and configuration providers into application configuration", "homepage": "https://laminas.dev", "keywords": ["component installer", "composer", "laminas", "plugin"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-component-installer/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-component-installer/issues", "rss": "https://github.com/laminas/laminas-component-installer/releases.atom", "source": "https://github.com/laminas/laminas-component-installer"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2024-11-26T14:11:43+00:00"}, {"name": "laminas/laminas-config", "version": "3.10.1", "source": {"type": "git", "url": "https://github.com/laminas/laminas-config.git", "reference": "0f50adbf2b2e01e0fe99c13e37d3a6c1ef645185"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-config/zipball/0f50adbf2b2e01e0fe99c13e37d3a6c1ef645185", "reference": "0f50adbf2b2e01e0fe99c13e37d3a6c1ef645185", "shasum": ""}, "require": {"ext-json": "*", "laminas/laminas-stdlib": "^3.6", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0", "psr/container": "^1.0"}, "conflict": {"container-interop/container-interop": "<1.2.0", "zendframework/zend-config": "*"}, "require-dev": {"laminas/laminas-coding-standard": "^3.0.1", "laminas/laminas-filter": "^2.39.0", "laminas/laminas-i18n": "^2.29.0", "laminas/laminas-servicemanager": "^3.23.0", "phpunit/phpunit": "^10.5.38"}, "suggest": {"laminas/laminas-filter": "^2.7.2; install if you want to use the Filter processor", "laminas/laminas-i18n": "^2.7.4; install if you want to use the Translator processor", "laminas/laminas-servicemanager": "^2.7.8 || ^3.3; if you need an extensible plugin manager for use with the Config Factory"}, "type": "library", "autoload": {"psr-4": {"Laminas\\Config\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "provides a nested object property based user interface for accessing this configuration data within application code", "homepage": "https://laminas.dev", "keywords": ["config", "laminas"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-config/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-config/issues", "rss": "https://github.com/laminas/laminas-config/releases.atom", "source": "https://github.com/laminas/laminas-config"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "abandoned": true, "time": "2024-12-05T14:32:05+00:00"}, {"name": "laminas/laminas-db", "version": "2.20.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-db.git", "reference": "207b9ee70a8b518913c1fad688d7a64fe89a8b91"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-db/zipball/207b9ee70a8b518913c1fad688d7a64fe89a8b91", "reference": "207b9ee70a8b518913c1fad688d7a64fe89a8b91", "shasum": ""}, "require": {"laminas/laminas-stdlib": "^3.7.1", "php": "~8.1.0 || ~8.2.0 || ~8.3.0"}, "conflict": {"zendframework/zend-db": "*"}, "require-dev": {"laminas/laminas-coding-standard": "^2.4.0", "laminas/laminas-eventmanager": "^3.6.0", "laminas/laminas-hydrator": "^4.7", "laminas/laminas-servicemanager": "^3.19.0", "phpunit/phpunit": "^9.5.25"}, "suggest": {"laminas/laminas-eventmanager": "Laminas\\EventManager component", "laminas/laminas-hydrator": "(^3.2 || ^4.3) Laminas\\Hydrator component for using HydratingResultSets", "laminas/laminas-servicemanager": "Laminas\\ServiceManager component"}, "type": "library", "extra": {"laminas": {"component": "Laminas\\Db", "config-provider": "Laminas\\Db\\ConfigProvider"}}, "autoload": {"psr-4": {"Laminas\\Db\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Database abstraction layer, SQL abstraction, result set abstraction, and RowDataGateway and TableDataGateway implementations", "homepage": "https://laminas.dev", "keywords": ["db", "laminas"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-db/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-db/issues", "rss": "https://github.com/laminas/laminas-db/releases.atom", "source": "https://github.com/laminas/laminas-db"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2024-04-02T01:04:56+00:00"}, {"name": "laminas/laminas-development-mode", "version": "3.13.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-development-mode.git", "reference": "228efb56b5ecf16c0978082830a2887792b3a67c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-development-mode/zipball/228efb56b5ecf16c0978082830a2887792b3a67c", "reference": "228efb56b5ecf16c0978082830a2887792b3a67c", "shasum": ""}, "require": {"php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "conflict": {"zfcampus/zf-development-mode": "*"}, "require-dev": {"laminas/laminas-coding-standard": "~3.0.1", "mikey179/vfsstream": "^1.6.12", "phpunit/phpunit": "^10.5.38", "psalm/plugin-phpunit": "^0.19.0", "vimeo/psalm": "^5.26.1"}, "bin": ["bin/laminas-development-mode"], "type": "library", "autoload": {"psr-4": {"Laminas\\DevelopmentMode\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Laminas development mode script", "homepage": "https://laminas.dev", "keywords": ["framework", "laminas"], "support": {"chat": "https://laminas.dev/chat", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-development-mode/issues", "rss": "https://github.com/laminas/laminas-development-mode/releases.atom", "source": "https://github.com/laminas/laminas-development-mode"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2024-11-21T21:25:13+00:00"}, {"name": "laminas/laminas-di", "version": "3.15.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-di.git", "reference": "de00225cfbe30d7fab1e9cf476e154be25f2c878"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-di/zipball/de00225cfbe30d7fab1e9cf476e154be25f2c878", "reference": "de00225cfbe30d7fab1e9cf476e154be25f2c878", "shasum": ""}, "require": {"laminas/laminas-stdlib": "^3.18.0", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0", "psr/container": "^1.1.1", "psr/log": "^1.1.4 || ^2.0.0 ||  ^3.0.0"}, "conflict": {"amphp/amp": "<2.6.4", "laminas/laminas-servicemanager": "<3.13.0 || >=4.0.0", "laminas/laminas-servicemanager-di": "*", "zendframework/zend-di": "*"}, "require-dev": {"laminas/laminas-coding-standard": "~2.5.0", "laminas/laminas-servicemanager": "^3.22", "mikey179/vfsstream": "^1.6.12", "phpbench/phpbench": "^1.2.7", "phpunit/phpunit": "^9.6.22", "psalm/plugin-phpunit": "^0.18.0", "squizlabs/php_codesniffer": "^3.7.1", "vimeo/psalm": "^5.0"}, "suggest": {"laminas/laminas-servicemanager": "An IoC container without auto wiring capabilities"}, "type": "library", "extra": {"laminas": {"component": "Laminas\\Di", "config-provider": "Laminas\\Di\\ConfigProvider"}}, "autoload": {"psr-4": {"Laminas\\Di\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Automated dependency injection for PSR-11 containers", "homepage": "https://laminas.dev", "keywords": ["PSR-11", "di", "laminas"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-di/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-di/issues", "rss": "https://github.com/laminas/laminas-di/releases.atom", "source": "https://github.com/laminas/laminas-di"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2025-02-25T14:43:45+00:00"}, {"name": "laminas/laminas-diactoros", "version": "3.6.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-diactoros.git", "reference": "b068eac123f21c0e592de41deeb7403b88e0a89f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-diactoros/zipball/b068eac123f21c0e592de41deeb7403b88e0a89f", "reference": "b068eac123f21c0e592de41deeb7403b88e0a89f", "shasum": ""}, "require": {"php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0", "psr/http-factory": "^1.1", "psr/http-message": "^1.1 || ^2.0"}, "conflict": {"amphp/amp": "<2.6.4"}, "provide": {"psr/http-factory-implementation": "^1.0", "psr/http-message-implementation": "^1.1 || ^2.0"}, "require-dev": {"ext-curl": "*", "ext-dom": "*", "ext-gd": "*", "ext-libxml": "*", "http-interop/http-factory-tests": "^2.2.0", "laminas/laminas-coding-standard": "~3.0.0", "php-http/psr7-integration-tests": "^1.4.0", "phpunit/phpunit": "^10.5.36", "psalm/plugin-phpunit": "^0.19.0", "vimeo/psalm": "^5.26.1"}, "type": "library", "extra": {"laminas": {"module": "Laminas\\Diactoros", "config-provider": "Laminas\\Diactoros\\ConfigProvider"}}, "autoload": {"files": ["src/functions/create_uploaded_file.php", "src/functions/marshal_headers_from_sapi.php", "src/functions/marshal_method_from_sapi.php", "src/functions/marshal_protocol_version_from_sapi.php", "src/functions/normalize_server.php", "src/functions/normalize_uploaded_files.php", "src/functions/parse_cookie_header.php"], "psr-4": {"Laminas\\Diactoros\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "PSR HTTP Message implementations", "homepage": "https://laminas.dev", "keywords": ["http", "laminas", "psr", "psr-17", "psr-7"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-diactoros/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-diactoros/issues", "rss": "https://github.com/laminas/laminas-diactoros/releases.atom", "source": "https://github.com/laminas/laminas-diactoros"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2025-05-05T16:03:34+00:00"}, {"name": "laminas/laminas-escaper", "version": "2.17.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-escaper.git", "reference": "df1ef9503299a8e3920079a16263b578eaf7c3ba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-escaper/zipball/df1ef9503299a8e3920079a16263b578eaf7c3ba", "reference": "df1ef9503299a8e3920079a16263b578eaf7c3ba", "shasum": ""}, "require": {"ext-ctype": "*", "ext-mbstring": "*", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "conflict": {"zendframework/zend-escaper": "*"}, "require-dev": {"infection/infection": "^0.29.8", "laminas/laminas-coding-standard": "~3.0.1", "phpunit/phpunit": "^10.5.45", "psalm/plugin-phpunit": "^0.19.2", "vimeo/psalm": "^6.6.2"}, "type": "library", "autoload": {"psr-4": {"Laminas\\Escaper\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Securely and safely escape HTML, HTML attributes, JavaScript, CSS, and URLs", "homepage": "https://laminas.dev", "keywords": ["escaper", "laminas"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-escaper/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-escaper/issues", "rss": "https://github.com/laminas/laminas-escaper/releases.atom", "source": "https://github.com/laminas/laminas-escaper"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2025-05-06T19:29:36+00:00"}, {"name": "laminas/laminas-eventmanager", "version": "3.14.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-eventmanager.git", "reference": "1837cafaaaee74437f6d8ec9ff7da03e6f81d809"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-eventmanager/zipball/1837cafaaaee74437f6d8ec9ff7da03e6f81d809", "reference": "1837cafaaaee74437f6d8ec9ff7da03e6f81d809", "shasum": ""}, "require": {"php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "conflict": {"container-interop/container-interop": "<1.2", "zendframework/zend-eventmanager": "*"}, "require-dev": {"laminas/laminas-coding-standard": "~3.0.0", "laminas/laminas-stdlib": "^3.20", "phpbench/phpbench": "^1.3.1", "phpunit/phpunit": "^10.5.38", "psalm/plugin-phpunit": "^0.19.0", "psr/container": "^1.1.2 || ^2.0.2", "vimeo/psalm": "^5.26.1"}, "suggest": {"laminas/laminas-stdlib": "^2.7.3 || ^3.0, to use the FilterChain feature", "psr/container": "^1.1.2 || ^2.0.2, to use the lazy listeners feature"}, "type": "library", "autoload": {"psr-4": {"Laminas\\EventManager\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Trigger and listen to events within a PHP application", "homepage": "https://laminas.dev", "keywords": ["event", "eventmanager", "events", "laminas"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-eventmanager/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-eventmanager/issues", "rss": "https://github.com/laminas/laminas-eventmanager/releases.atom", "source": "https://github.com/laminas/laminas-eventmanager"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2024-11-21T11:31:22+00:00"}, {"name": "laminas/laminas-filter", "version": "2.41.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-filter.git", "reference": "eaa00111231bf6669826ae84d3abe85b94477585"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-filter/zipball/eaa00111231bf6669826ae84d3abe85b94477585", "reference": "eaa00111231bf6669826ae84d3abe85b94477585", "shasum": ""}, "require": {"ext-mbstring": "*", "laminas/laminas-servicemanager": "^3.21.0", "laminas/laminas-stdlib": "^3.19.0", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "conflict": {"laminas/laminas-validator": "<2.10.1", "zendframework/zend-filter": "*"}, "require-dev": {"laminas/laminas-coding-standard": "~3.0", "laminas/laminas-crypt": "^3.12", "laminas/laminas-i18n": "^2.28.1", "laminas/laminas-uri": "^2.12", "pear/archive_tar": "^1.5.0", "phpunit/phpunit": "^10.5.36", "psalm/plugin-phpunit": "^0.19.0", "psr/http-factory": "^1.1.0", "vimeo/psalm": "^5.26.1"}, "suggest": {"laminas/laminas-crypt": "Laminas\\Crypt component, for encryption filters", "laminas/laminas-i18n": "Laminas\\I18n component for filters depending on i18n functionality", "laminas/laminas-uri": "Laminas\\Uri component, for the UriNormalize filter", "psr/http-factory-implementation": "psr/http-factory-implementation, for creating file upload instances when consuming PSR-7 in file upload filters"}, "type": "library", "extra": {"laminas": {"component": "Laminas\\Filter", "config-provider": "Laminas\\Filter\\ConfigProvider"}}, "autoload": {"psr-4": {"Laminas\\Filter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Programmatically filter and normalize data and files", "homepage": "https://laminas.dev", "keywords": ["filter", "laminas"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-filter/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-filter/issues", "rss": "https://github.com/laminas/laminas-filter/releases.atom", "source": "https://github.com/laminas/laminas-filter"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2025-05-05T02:02:31+00:00"}, {"name": "laminas/laminas-form", "version": "3.21.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-form.git", "reference": "653c869d10c361027ae6c660c991ec3e3f38ed65"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-form/zipball/653c869d10c361027ae6c660c991ec3e3f38ed65", "reference": "653c869d10c361027ae6c660c991ec3e3f38ed65", "shasum": ""}, "require": {"laminas/laminas-hydrator": "^4.13.0", "laminas/laminas-inputfilter": "^2.24.0", "laminas/laminas-stdlib": "^3.16.1", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "conflict": {"doctrine/annotations": "<1.14.0", "laminas/laminas-captcha": "<2.16.0", "laminas/laminas-eventmanager": "<3.10.0", "laminas/laminas-i18n": "<2.21.0", "laminas/laminas-recaptcha": "<3.6.0", "laminas/laminas-servicemanager": "<3.20.0", "laminas/laminas-view": "<2.27.0"}, "require-dev": {"doctrine/annotations": "^1.14.3 || ^2.0.1", "ext-intl": "*", "laminas/laminas-captcha": "^2.17", "laminas/laminas-coding-standard": "^2.5", "laminas/laminas-db": "^2.20", "laminas/laminas-escaper": "^2.13", "laminas/laminas-eventmanager": "^3.13.1", "laminas/laminas-filter": "^2.36", "laminas/laminas-i18n": "^2.28.0", "laminas/laminas-modulemanager": "^2.16.0", "laminas/laminas-recaptcha": "^3.7", "laminas/laminas-servicemanager": "^3.22.1", "laminas/laminas-session": "^2.21", "laminas/laminas-text": "^2.11.0", "laminas/laminas-validator": "^2.64.1", "laminas/laminas-view": "^2.35", "phpunit/phpunit": "^10.5.29", "psalm/plugin-phpunit": "^0.19.0", "vimeo/psalm": "^5.25"}, "suggest": {"doctrine/annotations": "^1.14, required to use laminas-form annotations support", "laminas/laminas-captcha": "^2.16, required for using CAPTCHA form elements", "laminas/laminas-eventmanager": "^3.10, reuired for laminas-form annotations support", "laminas/laminas-i18n": "^2.21, required when using laminas-form view helpers", "laminas/laminas-recaptcha": "^3.6, in order to use the ReCaptcha form element", "laminas/laminas-servicemanager": "^3.20, required to use the form factories or provide services", "laminas/laminas-view": "^2.27, required for using the laminas-form view helpers"}, "type": "library", "extra": {"laminas": {"component": "Laminas\\Form", "config-provider": "Laminas\\Form\\ConfigProvider"}}, "autoload": {"psr-4": {"Laminas\\Form\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Validate and display simple and complex forms, casting forms to business objects and vice versa", "homepage": "https://laminas.dev", "keywords": ["form", "laminas"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-form/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-form/issues", "rss": "https://github.com/laminas/laminas-form/releases.atom", "source": "https://github.com/laminas/laminas-form"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2024-10-09T08:28:30+00:00"}, {"name": "laminas/laminas-http", "version": "2.22.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-http.git", "reference": "5052177fb8176e00b0d4b89108648f557be072b7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-http/zipball/5052177fb8176e00b0d4b89108648f557be072b7", "reference": "5052177fb8176e00b0d4b89108648f557be072b7", "shasum": ""}, "require": {"laminas/laminas-loader": "^2.10", "laminas/laminas-stdlib": "^3.6", "laminas/laminas-uri": "^2.11", "laminas/laminas-validator": "^2.15 || ^3.0", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "conflict": {"zendframework/zend-http": "*"}, "require-dev": {"ext-curl": "*", "laminas/laminas-coding-standard": "^3.0.1", "phpunit/phpunit": "^10.5.38"}, "suggest": {"paragonie/certainty": "For automated management of cacert.pem"}, "type": "library", "autoload": {"psr-4": {"Laminas\\Http\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Provides an easy interface for performing Hyper-Text Transfer Protocol (HTTP) requests", "homepage": "https://laminas.dev", "keywords": ["http", "http client", "laminas"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-http/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-http/issues", "rss": "https://github.com/laminas/laminas-http/releases.atom", "source": "https://github.com/laminas/laminas-http"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2025-05-06T08:24:40+00:00"}, {"name": "laminas/laminas-hydrator", "version": "4.16.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-hydrator.git", "reference": "a162bd571924968d67ef1f43aed044b8f9c108ef"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-hydrator/zipball/a162bd571924968d67ef1f43aed044b8f9c108ef", "reference": "a162bd571924968d67ef1f43aed044b8f9c108ef", "shasum": ""}, "require": {"laminas/laminas-stdlib": "^3.20", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0", "webmozart/assert": "^1.11"}, "conflict": {"laminas/laminas-servicemanager": "<3.14.0", "zendframework/zend-hydrator": "*"}, "require-dev": {"laminas/laminas-coding-standard": "~3.0", "laminas/laminas-eventmanager": "^3.13.1", "laminas/laminas-modulemanager": "^2.16.0", "laminas/laminas-serializer": "^2.17.0", "laminas/laminas-servicemanager": "^3.23.0", "phpbench/phpbench": "^1.3.1", "phpunit/phpunit": "^10.5.38", "psalm/plugin-phpunit": "^0.19.0", "vimeo/psalm": "^5.26.1"}, "suggest": {"laminas/laminas-eventmanager": "^3.13, to support aggregate hydrator usage", "laminas/laminas-serializer": "^2.17, to use the SerializableStrategy", "laminas/laminas-servicemanager": "^3.22, to support hydrator plugin manager usage"}, "type": "library", "extra": {"laminas": {"component": "Laminas\\Hydrator", "config-provider": "Laminas\\Hydrator\\ConfigProvider"}}, "autoload": {"psr-4": {"Laminas\\Hydrator\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Serialize objects to arrays, and vice versa", "homepage": "https://laminas.dev", "keywords": ["hydrator", "laminas"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-hydrator/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-hydrator/issues", "rss": "https://github.com/laminas/laminas-hydrator/releases.atom", "source": "https://github.com/laminas/laminas-hydrator"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2024-11-13T14:04:02+00:00"}, {"name": "laminas/laminas-i18n", "version": "2.30.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-i18n.git", "reference": "397907ee061e147939364df9d6c485ac1e0fed87"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-i18n/zipball/397907ee061e147939364df9d6c485ac1e0fed87", "reference": "397907ee061e147939364df9d6c485ac1e0fed87", "shasum": ""}, "require": {"ext-intl": "*", "laminas/laminas-servicemanager": "^3.21.0", "laminas/laminas-stdlib": "^3.0", "laminas/laminas-translator": "^1.0", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "conflict": {"laminas/laminas-view": "<2.20.0", "zendframework/zend-i18n": "*"}, "require-dev": {"laminas/laminas-cache": "^3.13.0", "laminas/laminas-cache-storage-adapter-memory": "^2.4.0", "laminas/laminas-cache-storage-deprecated-factory": "^1.3", "laminas/laminas-coding-standard": "~2.5.0", "laminas/laminas-config": "^3.10.1", "laminas/laminas-eventmanager": "^3.14.0", "laminas/laminas-filter": "^2.40", "laminas/laminas-validator": "^2.64.2", "laminas/laminas-view": "^2.36", "phpunit/phpunit": "^10.5.45", "psalm/plugin-phpunit": "^0.19.5", "vimeo/psalm": "^6.10.0"}, "suggest": {"laminas/laminas-cache": "You should install this package to cache the translations", "laminas/laminas-config": "You should install this package to use the INI translation format", "laminas/laminas-eventmanager": "You should install this package to use the events in the translator", "laminas/laminas-filter": "You should install this package to use the provided filters", "laminas/laminas-i18n-resources": "This package provides validator and captcha translations", "laminas/laminas-validator": "You should install this package to use the provided validators", "laminas/laminas-view": "You should install this package to use the provided view helpers"}, "type": "library", "extra": {"laminas": {"component": "Laminas\\I18n", "config-provider": "Laminas\\I18n\\ConfigProvider"}}, "autoload": {"psr-4": {"Laminas\\I18n\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Provide translations for your application, and filter and validate internationalized values", "homepage": "https://laminas.dev", "keywords": ["i18n", "laminas"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-i18n/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-i18n/issues", "rss": "https://github.com/laminas/laminas-i18n/releases.atom", "source": "https://github.com/laminas/laminas-i18n"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2025-04-15T09:07:02+00:00"}, {"name": "laminas/laminas-inputfilter", "version": "2.32.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-inputfilter.git", "reference": "5f04f1e5a6b1bd3c82114b6ea748a70ce65787f6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-inputfilter/zipball/5f04f1e5a6b1bd3c82114b6ea748a70ce65787f6", "reference": "5f04f1e5a6b1bd3c82114b6ea748a70ce65787f6", "shasum": ""}, "require": {"laminas/laminas-filter": "^2.19", "laminas/laminas-servicemanager": "^3.21.0", "laminas/laminas-stdlib": "^3.19", "laminas/laminas-validator": "^2.60.0", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "conflict": {"zendframework/zend-inputfilter": "*"}, "require-dev": {"ext-json": "*", "laminas/laminas-coding-standard": "^3.0.1", "phpunit/phpunit": "^10.5.45", "psalm/plugin-phpunit": "^0.19.5", "psr/http-message": "^2.0", "vimeo/psalm": "^6.10.0", "webmozart/assert": "^1.11"}, "suggest": {"psr/http-message-implementation": "PSR-7 is required if you wish to validate PSR-7 UploadedFileInterface payloads"}, "type": "library", "extra": {"laminas": {"component": "Laminas\\InputFilter", "config-provider": "Laminas\\InputFilter\\ConfigProvider"}}, "autoload": {"psr-4": {"Laminas\\InputFilter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Normalize and validate input sets from the web, APIs, the CLI, and more, including files", "homepage": "https://laminas.dev", "keywords": ["inputfilter", "laminas"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-inputfilter/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-inputfilter/issues", "rss": "https://github.com/laminas/laminas-inputfilter/releases.atom", "source": "https://github.com/laminas/laminas-inputfilter"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2025-04-14T00:23:41+00:00"}, {"name": "laminas/laminas-json", "version": "3.7.1", "source": {"type": "git", "url": "https://github.com/laminas/laminas-json.git", "reference": "a0f9dca08e28f39a7a7a7a04370eb2f017369277"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-json/zipball/a0f9dca08e28f39a7a7a7a04370eb2f017369277", "reference": "a0f9dca08e28f39a7a7a7a04370eb2f017369277", "shasum": ""}, "require": {"php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "conflict": {"zendframework/zend-json": "*"}, "require-dev": {"laminas/laminas-coding-standard": "~2.4.0", "laminas/laminas-stdlib": "^2.7.7 || ^3.19", "phpunit/phpunit": "^9.5.25"}, "suggest": {"laminas/laminas-json-server": "For implementing JSON-RPC servers", "laminas/laminas-xml2json": "For converting XML documents to JSON"}, "type": "library", "autoload": {"psr-4": {"Laminas\\Json\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "provides convenience methods for serializing native PHP to JSON and decoding JSON to native PHP", "homepage": "https://laminas.dev", "keywords": ["json", "laminas"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-json/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-json/issues", "rss": "https://github.com/laminas/laminas-json/releases.atom", "source": "https://github.com/laminas/laminas-json"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "abandoned": true, "time": "2024-12-05T14:51:57+00:00"}, {"name": "laminas/laminas-loader", "version": "2.11.1", "source": {"type": "git", "url": "https://github.com/laminas/laminas-loader.git", "reference": "c507d5eccb969f7208434e3980680a1f6c0b1d8d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-loader/zipball/c507d5eccb969f7208434e3980680a1f6c0b1d8d", "reference": "c507d5eccb969f7208434e3980680a1f6c0b1d8d", "shasum": ""}, "require": {"php": "~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "conflict": {"zendframework/zend-loader": "*"}, "require-dev": {"laminas/laminas-coding-standard": "~2.4.0", "phpunit/phpunit": "~9.5.25"}, "type": "library", "autoload": {"psr-4": {"Laminas\\Loader\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Autoloading and plugin loading strategies", "homepage": "https://laminas.dev", "keywords": ["laminas", "loader"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-loader/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-loader/issues", "rss": "https://github.com/laminas/laminas-loader/releases.atom", "source": "https://github.com/laminas/laminas-loader"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "abandoned": true, "time": "2024-12-05T14:43:32+00:00"}, {"name": "laminas/laminas-log", "version": "2.17.1", "source": {"type": "git", "url": "https://github.com/laminas/laminas-log.git", "reference": "abc93b0c597e5dd55a18d29f0ae3b18c2f0e3f0c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-log/zipball/abc93b0c597e5dd55a18d29f0ae3b18c2f0e3f0c", "reference": "abc93b0c597e5dd55a18d29f0ae3b18c2f0e3f0c", "shasum": ""}, "require": {"laminas/laminas-servicemanager": "^3.21.0", "laminas/laminas-stdlib": "^3.0", "php": "~8.1.0 || ~8.2.0 || ~8.3.0", "psr/log": "^1.1.2"}, "conflict": {"zendframework/zend-log": "*"}, "provide": {"psr/log-implementation": "1.0.0"}, "require-dev": {"ext-dom": "*", "ext-json": "*", "ext-xml": "*", "firephp/firephp-core": "^0.5.3", "laminas/laminas-coding-standard": "~2.3.0", "laminas/laminas-db": "^2.6", "laminas/laminas-escaper": "^2.5", "laminas/laminas-filter": "^2.5", "laminas/laminas-mail": "^2.6.1", "laminas/laminas-validator": "^2.10.1", "mikey179/vfsstream": "^1.6.7", "phpspec/prophecy-phpunit": "^2.0", "phpunit/phpunit": "^9.5.10"}, "suggest": {"ext-mongo": "mongo extension to use Mongo writer", "ext-mongodb": "mongodb extension to use MongoDB writer", "laminas/laminas-db": "Laminas\\Db component to use the database log writer", "laminas/laminas-escaper": "Laminas\\Escaper component, for use in the XML log formatter", "laminas/laminas-mail": "Laminas\\Mail component to use the email log writer", "laminas/laminas-validator": "Laminas\\Validator component to block invalid log messages"}, "type": "library", "extra": {"laminas": {"component": "Laminas\\Log", "config-provider": "Laminas\\Log\\ConfigProvider"}}, "autoload": {"psr-4": {"Laminas\\Log\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Robust, composite logger with filtering, formatting, and PSR-3 support", "homepage": "https://laminas.dev", "keywords": ["laminas", "log", "logging"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-log/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-log/issues", "rss": "https://github.com/laminas/laminas-log/releases.atom", "source": "https://github.com/laminas/laminas-log"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "abandoned": "monolog/monolog", "time": "2024-12-05T15:59:56+00:00"}, {"name": "laminas/laminas-modulemanager", "version": "2.17.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-modulemanager.git", "reference": "3cd6e84ba767b43a47c6c4245a56b30ac3738c6a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-modulemanager/zipball/3cd6e84ba767b43a47c6c4245a56b30ac3738c6a", "reference": "3cd6e84ba767b43a47c6c4245a56b30ac3738c6a", "shasum": ""}, "require": {"brick/varexporter": "^0.3.2 || ^0.4 || ^0.5", "laminas/laminas-config": "^3.7", "laminas/laminas-eventmanager": "^3.4", "laminas/laminas-stdlib": "^3.6", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0", "webimpress/safe-writer": "^1.0.2 || ^2.1"}, "conflict": {"amphp/amp": "<2.6.4", "zendframework/zend-modulemanager": "*"}, "require-dev": {"laminas/laminas-coding-standard": "^3.0.1", "laminas/laminas-loader": "^2.11", "laminas/laminas-mvc": "^3.7.0", "laminas/laminas-servicemanager": "^3.23.0", "phpunit/phpunit": "^10.5.38", "psalm/plugin-phpunit": "^0.19.0", "vimeo/psalm": "^5.26.1"}, "suggest": {"laminas/laminas-console": "Laminas\\Console component", "laminas/laminas-loader": "Laminas\\Loader component if you are not using Composer autoloading for your modules", "laminas/laminas-mvc": "Laminas\\Mvc component", "laminas/laminas-servicemanager": "Laminas\\ServiceManager component"}, "type": "library", "autoload": {"psr-4": {"Laminas\\ModuleManager\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Modular application system for laminas-mvc applications", "homepage": "https://laminas.dev", "keywords": ["laminas", "modulemanager"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-modulemanager/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-modulemanager/issues", "rss": "https://github.com/laminas/laminas-modulemanager/releases.atom", "source": "https://github.com/laminas/laminas-modulemanager"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2024-11-17T22:29:29+00:00"}, {"name": "laminas/laminas-mvc", "version": "3.8.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-mvc.git", "reference": "53ba28b7222d3a3b49747a26babef43d1b17fb6f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-mvc/zipball/53ba28b7222d3a3b49747a26babef43d1b17fb6f", "reference": "53ba28b7222d3a3b49747a26babef43d1b17fb6f", "shasum": ""}, "require": {"container-interop/container-interop": "^1.2", "laminas/laminas-eventmanager": "^3.4", "laminas/laminas-http": "^2.15", "laminas/laminas-modulemanager": "^2.16", "laminas/laminas-router": "^3.11.1", "laminas/laminas-servicemanager": "^3.20.0", "laminas/laminas-stdlib": "^3.19", "laminas/laminas-view": "^2.18.0", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "conflict": {"zendframework/zend-mvc": "*"}, "require-dev": {"laminas/laminas-coding-standard": "^2.5.0", "laminas/laminas-json": "^3.6", "phpunit/phpunit": "^10.5.38", "webmozart/assert": "^1.11"}, "suggest": {"laminas/laminas-json": "(^2.6.1 || ^3.0) To auto-deserialize JSON body content in AbstractRestfulController extensions, when json_decode is unavailable", "laminas/laminas-log": "^2.9.1  To provide log functionality via LogFilterManager, LogFormatterManager, and LogProcessorManager", "laminas/laminas-mvc-console": "laminas-mvc-console provides the ability to expose laminas-mvc as a console application", "laminas/laminas-mvc-i18n": "laminas-mvc-i18n provides integration with laminas-i18n, including a translation bridge and translatable route segments", "laminas/laminas-mvc-middleware": "To dispatch middleware in your laminas-mvc application", "laminas/laminas-mvc-plugin-fileprg": "To provide Post/Redirect/Get functionality around forms that container file uploads", "laminas/laminas-mvc-plugin-flashmessenger": "To provide flash messaging capabilities between requests", "laminas/laminas-mvc-plugin-identity": "To access the authenticated identity (per laminas-authentication) in controllers", "laminas/laminas-mvc-plugin-prg": "To provide Post/Redirect/Get functionality within controllers", "laminas/laminas-paginator": "^2.7 To provide pagination functionality via PaginatorPluginManager", "laminas/laminas-servicemanager-di": "laminas-servicemanager-di provides utilities for integrating laminas-di and laminas-servicemanager in your laminas-mvc application"}, "type": "library", "autoload": {"psr-4": {"Laminas\\Mvc\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Laminas's event-driven MVC layer, including MVC Applications, Controllers, and Plugins", "homepage": "https://laminas.dev", "keywords": ["laminas", "mvc"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-mvc/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-mvc/issues", "rss": "https://github.com/laminas/laminas-mvc/releases.atom", "source": "https://github.com/laminas/laminas-mvc"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2024-11-18T00:14:29+00:00"}, {"name": "laminas/laminas-mvc-i18n", "version": "1.9.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-mvc-i18n.git", "reference": "433e71e949438239cce814536711914a37544c42"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-mvc-i18n/zipball/433e71e949438239cce814536711914a37544c42", "reference": "433e71e949438239cce814536711914a37544c42", "shasum": ""}, "require": {"container-interop/container-interop": "^1.1", "ext-intl": "*", "laminas/laminas-i18n": "^2.13.0", "laminas/laminas-router": "^3.5.0", "laminas/laminas-servicemanager": "^3.15.1", "laminas/laminas-stdlib": "^3.10.1", "laminas/laminas-validator": "^2.19.0", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "conflict": {"laminas/laminas-mvc": "<3.0.0", "phpspec/prophecy": "<1.8.0", "zendframework/zend-mvc-i18n": "*"}, "require-dev": {"laminas/laminas-coding-standard": "~2.5.0", "phpspec/prophecy-phpunit": "^2.0.2", "phpunit/phpunit": "^9.6.13", "psalm/plugin-phpunit": "^0.19.0", "vimeo/psalm": "^5.15"}, "suggest": {"laminas/laminas-cache": "To enable caching of translation strings"}, "type": "library", "extra": {"laminas": {"component": "Laminas\\Mvc\\I18n", "config-provider": "Laminas\\Mvc\\I18n\\ConfigProvider"}}, "autoload": {"psr-4": {"Laminas\\Mvc\\I18n\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Integration between laminas-mvc and laminas-i18n", "homepage": "https://laminas.dev", "keywords": ["i18n", "laminas", "mvc"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-mvc-i18n/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-mvc-i18n/issues", "rss": "https://github.com/laminas/laminas-mvc-i18n/releases.atom", "source": "https://github.com/laminas/laminas-mvc-i18n"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2024-10-11T09:36:44+00:00"}, {"name": "laminas/laminas-mvc-middleware", "version": "2.5.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-mvc-middleware.git", "reference": "db543d3489b8a83e094c29c02c903c23d4acacbd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-mvc-middleware/zipball/db543d3489b8a83e094c29c02c903c23d4acacbd", "reference": "db543d3489b8a83e094c29c02c903c23d4acacbd", "shasum": ""}, "require": {"laminas/laminas-mvc": "^3.2", "laminas/laminas-psr7bridge": "^1.10", "laminas/laminas-servicemanager": "^3.4", "laminas/laminas-stratigility": "^3.3", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0", "psr/http-server-handler": "^1.0.2", "psr/http-server-middleware": "^1.0.2", "webmozart/assert": "^1.9"}, "require-dev": {"brick/varexporter": "^0.5", "laminas/laminas-coding-standard": "~3.0.1", "laminas/laminas-diactoros": "^2.25.2 || ^3.5", "laminas/laminas-router": "^3.14", "phpunit/phpunit": "^10.5.45", "psalm/plugin-phpunit": "^0.19.2", "vimeo/psalm": "^6.9.1"}, "type": "library", "extra": {"laminas": {"component": "Laminas\\Mvc\\Middleware"}}, "autoload": {"psr-4": {"Laminas\\Mvc\\Middleware\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Dispatch middleware pipelines in place of controllers in laminas-mvc.", "homepage": "https://laminas.dev", "keywords": ["components", "laminas", "middleware", "mvc", "psr-15", "psr-7"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-mvc-middleware/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-mvc-middleware/issues", "rss": "https://github.com/laminas/laminas-mvc-middleware/releases.atom", "source": "https://github.com/laminas/laminas-mvc-middleware"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2025-03-17T09:44:47+00:00"}, {"name": "laminas/laminas-mvc-plugin-fileprg", "version": "1.7.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-mvc-plugin-fileprg.git", "reference": "e81c52b88e961306554cb9d3e6efe7ccbffe4afa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-mvc-plugin-fileprg/zipball/e81c52b88e961306554cb9d3e6efe7ccbffe4afa", "reference": "e81c52b88e961306554cb9d3e6efe7ccbffe4afa", "shasum": ""}, "require": {"laminas/laminas-filter": "^2.13.1", "laminas/laminas-form": "^3.1", "laminas/laminas-inputfilter": "^2.13", "laminas/laminas-mvc": "^3.3", "laminas/laminas-session": "^2.12", "laminas/laminas-stdlib": "^3.6.2", "laminas/laminas-validator": "^2.15.1", "php": "~8.1.0 || ~8.2.0 || ~8.3.0"}, "conflict": {"laminas/laminas-mvc": "<3.0.0", "zendframework/zend-mvc-plugin-fileprg": "*"}, "require-dev": {"laminas/laminas-coding-standard": "~2.5.0", "phpunit/phpunit": "^10.5", "psalm/plugin-phpunit": "^0.18.4", "vimeo/psalm": "^5.16"}, "type": "library", "extra": {"laminas": {"component": "Laminas\\Mvc\\Plugin\\FilePrg"}}, "autoload": {"psr-4": {"Laminas\\Mvc\\Plugin\\FilePrg\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Post/Redirect/Get plugin with file upload handling for laminas-mvc controllers", "homepage": "https://laminas.dev", "keywords": ["laminas", "mvc"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-mvc-plugin-fileprg/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-mvc-plugin-fileprg/issues", "rss": "https://github.com/laminas/laminas-mvc-plugin-fileprg/releases.atom", "source": "https://github.com/laminas/laminas-mvc-plugin-fileprg"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2023-12-01T07:56:54+00:00"}, {"name": "laminas/laminas-mvc-plugin-flashmessenger", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-mvc-plugin-flashmessenger.git", "reference": "664822d8d9f259d880ae25b686848421235f96ec"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-mvc-plugin-flashmessenger/zipball/664822d8d9f259d880ae25b686848421235f96ec", "reference": "664822d8d9f259d880ae25b686848421235f96ec", "shasum": ""}, "require": {"laminas/laminas-mvc": "^3.3", "laminas/laminas-session": "^2.12.0", "laminas/laminas-stdlib": "^3.6.4", "laminas/laminas-view": "^2.13.1", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "conflict": {"laminas/laminas-mvc": "<3.0.0", "zendframework/zend-mvc-plugin-flashmessenger": "*"}, "require-dev": {"laminas/laminas-coding-standard": "~3.0.1", "laminas/laminas-i18n": "^2.29.0", "phpunit/phpunit": "^9.6.21", "psalm/plugin-phpunit": "^0.19.0", "vimeo/psalm": "^5.26.1"}, "type": "library", "extra": {"laminas": {"component": "Laminas\\Mvc\\Plugin\\FlashMessenger"}}, "autoload": {"psr-4": {"Laminas\\Mvc\\Plugin\\FlashMessenger\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Plugin for creating and exposing flash messages via laminas-mvc controllers", "homepage": "https://laminas.dev", "keywords": ["laminas", "mvc"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-mvc-plugin-flashmessenger/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-mvc-plugin-flashmessenger/issues", "rss": "https://github.com/laminas/laminas-mvc-plugin-flashmessenger/releases.atom", "source": "https://github.com/laminas/laminas-mvc-plugin-flashmessenger"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2024-11-29T08:28:58+00:00"}, {"name": "laminas/laminas-mvc-plugin-identity", "version": "1.7.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-mvc-plugin-identity.git", "reference": "2b42edde3fdf34f3074fba2fc2c178c1bc50b95c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-mvc-plugin-identity/zipball/2b42edde3fdf34f3074fba2fc2c178c1bc50b95c", "reference": "2b42edde3fdf34f3074fba2fc2c178c1bc50b95c", "shasum": ""}, "require": {"laminas/laminas-authentication": "^2.11.0", "laminas/laminas-mvc": "^3.3.3", "laminas/laminas-servicemanager": "^3.15.1", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "conflict": {"laminas/laminas-mvc": "<3.0.0", "zendframework/zend-mvc-plugin-identity": "*"}, "require-dev": {"laminas/laminas-coding-standard": "~2.5.0", "phpunit/phpunit": "^10.5", "psalm/plugin-phpunit": "^0.19.0", "vimeo/psalm": "^5.16"}, "type": "library", "extra": {"laminas": {"component": "Laminas\\Mvc\\Plugin\\Identity"}}, "autoload": {"psr-4": {"Laminas\\Mvc\\Plugin\\Identity\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Plugin for retrieving the current authenticated identity within laminas-mvc controllers", "homepage": "https://laminas.dev", "keywords": ["laminas", "mvc"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-mvc-plugin-identity/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-mvc-plugin-identity/issues", "rss": "https://github.com/laminas/laminas-mvc-plugin-identity/releases.atom", "source": "https://github.com/laminas/laminas-mvc-plugin-identity"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2025-01-29T08:03:42+00:00"}, {"name": "laminas/laminas-mvc-plugin-prg", "version": "1.8.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-mvc-plugin-prg.git", "reference": "b51e7ba892e52c6a80ce8c7dc7f8c429e3937516"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-mvc-plugin-prg/zipball/b51e7ba892e52c6a80ce8c7dc7f8c429e3937516", "reference": "b51e7ba892e52c6a80ce8c7dc7f8c429e3937516", "shasum": ""}, "require": {"laminas/laminas-mvc": "^3.3.3", "laminas/laminas-session": "^2.12.1", "laminas/laminas-stdlib": "^3.10.1", "php": "~8.1.0 || ~8.2.0 || ~8.3.0"}, "conflict": {"zendframework/zend-mvc-plugin-prg": "*"}, "require-dev": {"laminas/laminas-coding-standard": "~2.5.0", "phpunit/phpunit": "^10.4", "psalm/plugin-phpunit": "^0.18.0", "vimeo/psalm": "^5.16"}, "type": "library", "extra": {"laminas": {"component": "Laminas\\Mvc\\Plugin\\Prg"}}, "autoload": {"psr-4": {"Laminas\\Mvc\\Plugin\\Prg\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Post/Redirect/Get plugin for laminas-mvc controllers", "homepage": "https://laminas.dev", "keywords": ["laminas", "mvc"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-mvc-plugin-prg/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-mvc-plugin-prg/issues", "rss": "https://github.com/laminas/laminas-mvc-plugin-prg/releases.atom", "source": "https://github.com/laminas/laminas-mvc-plugin-prg"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2023-12-01T06:06:12+00:00"}, {"name": "laminas/laminas-mvc-plugins", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-mvc-plugins.git", "reference": "ea91854e410fcf0451c8bc53062da215605cf5ad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-mvc-plugins/zipball/ea91854e410fcf0451c8bc53062da215605cf5ad", "reference": "ea91854e410fcf0451c8bc53062da215605cf5ad", "shasum": ""}, "require": {"laminas/laminas-mvc-plugin-fileprg": "^1.2", "laminas/laminas-mvc-plugin-flashmessenger": "^1.3", "laminas/laminas-mvc-plugin-identity": "^1.2", "laminas/laminas-mvc-plugin-prg": "^1.3"}, "conflict": {"zendframework/zend-mvc-plugins": "*"}, "type": "metapackage", "extra": {"laminas": {"component": ["Laminas\\Mvc\\Plugin\\FilePrg", "Laminas\\Mvc\\Plugin\\FlashMessenger", "Laminas\\Mvc\\Plugin\\Identity", "Laminas\\Mvc\\Plugin\\Prg"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Collection of all stable laminas-mvc plugins served via separate packages.", "homepage": "https://laminas.dev", "keywords": ["laminas", "mvc", "plugins"], "support": {"chat": "https://laminas.dev/chat", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-mvc-plugins/issues", "rss": "https://github.com/laminas/laminas-mvc-plugins/releases.atom", "source": "https://github.com/laminas/laminas-mvc-plugins"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2021-12-06T00:49:46+00:00"}, {"name": "laminas/laminas-paginator", "version": "2.19.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-paginator.git", "reference": "d6339e7ad61491fd76b1490cf9f723d0348ce430"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-paginator/zipball/d6339e7ad61491fd76b1490cf9f723d0348ce430", "reference": "d6339e7ad61491fd76b1490cf9f723d0348ce430", "shasum": ""}, "require": {"ext-json": "*", "laminas/laminas-stdlib": "^3.10.1", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "conflict": {"zendframework/zend-paginator": "*"}, "require-dev": {"laminas/laminas-cache": "^3.12.2", "laminas/laminas-cache-storage-adapter-memory": "^2.3.0", "laminas/laminas-coding-standard": "^2.5.0", "laminas/laminas-config": "^3.9.0", "laminas/laminas-filter": "^2.37", "laminas/laminas-servicemanager": "^3.22.1", "laminas/laminas-view": "^2.35", "phpunit/phpunit": "^10.5.30", "psalm/plugin-phpunit": "^0.19.0", "vimeo/psalm": "^5.25"}, "suggest": {"laminas/laminas-cache": "Laminas\\Cache component to support cache features", "laminas/laminas-filter": "Laminas\\Filter component", "laminas/laminas-paginator-adapter-laminasdb": "Provides pagination adapters for Select statements and TableGateway instances", "laminas/laminas-servicemanager": "Laminas\\ServiceManager component", "laminas/laminas-view": "Laminas\\View component"}, "type": "library", "extra": {"laminas": {"component": "Laminas\\Paginator", "config-provider": "Laminas\\Paginator\\ConfigProvider"}}, "autoload": {"psr-4": {"Laminas\\Paginator\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Paginate collections of data from arbitrary sources", "homepage": "https://laminas.dev", "keywords": ["laminas", "paginator"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-paginator/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-paginator/issues", "rss": "https://github.com/laminas/laminas-paginator/releases.atom", "source": "https://github.com/laminas/laminas-paginator"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2024-10-16T13:10:19+00:00"}, {"name": "laminas/laminas-psr7bridge", "version": "1.12.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-psr7bridge.git", "reference": "51ca6cd0bba968baa7bfc26f184bb4e985b40821"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-psr7bridge/zipball/51ca6cd0bba968baa7bfc26f184bb4e985b40821", "reference": "51ca6cd0bba968baa7bfc26f184bb4e985b40821", "shasum": ""}, "require": {"laminas/laminas-diactoros": "^2.25.2 || ^3.0.0", "laminas/laminas-http": "^2.19.0", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0", "psr/http-message": "^1.1 || ^2.0"}, "require-dev": {"laminas/laminas-coding-standard": "~2.5.0", "phpunit/phpunit": "^10.5.45", "psalm/plugin-phpunit": "^0.18.4", "vimeo/psalm": "^5.26.1"}, "type": "library", "autoload": {"psr-4": {"Laminas\\Psr7Bridge\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Bidirectional conversions between PSR-7 and laminas-http messages", "homepage": "https://laminas.dev", "keywords": ["http", "laminas", "psr", "psr-7"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-psr7bridge/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-psr7bridge/issues", "rss": "https://github.com/laminas/laminas-psr7bridge/releases.atom", "source": "https://github.com/laminas/laminas-psr7bridge"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2025-02-10T09:07:39+00:00"}, {"name": "laminas/laminas-router", "version": "3.14.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-router.git", "reference": "5e1f5ca7fe95200661b50235c891ed3eee02d3f0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-router/zipball/5e1f5ca7fe95200661b50235c891ed3eee02d3f0", "reference": "5e1f5ca7fe95200661b50235c891ed3eee02d3f0", "shasum": ""}, "require": {"laminas/laminas-http": "^2.15", "laminas/laminas-servicemanager": "^3.14.0", "laminas/laminas-stdlib": "^3.10.1", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "conflict": {"zendframework/zend-router": "*"}, "require-dev": {"laminas/laminas-coding-standard": "~2.5.0", "laminas/laminas-i18n": "^2.29.0", "phpunit/phpunit": "^10.5.36", "psalm/plugin-phpunit": "^0.19.0", "vimeo/psalm": "^5.26.1"}, "suggest": {"laminas/laminas-i18n": "^2.15.0 if defining translatable HTTP path segments"}, "type": "library", "extra": {"laminas": {"component": "Laminas\\Router", "config-provider": "Laminas\\Router\\ConfigProvider"}}, "autoload": {"psr-4": {"Laminas\\Router\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Flexible routing system for HTTP and console applications", "homepage": "https://laminas.dev", "keywords": ["laminas", "routing"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-router/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-router/issues", "rss": "https://github.com/laminas/laminas-router/releases.atom", "source": "https://github.com/laminas/laminas-router"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2024-10-11T11:18:03+00:00"}, {"name": "laminas/laminas-servicemanager", "version": "3.23.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-servicemanager.git", "reference": "a8640182b892b99767d54404d19c5c3b3699f79b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-servicemanager/zipball/a8640182b892b99767d54404d19c5c3b3699f79b", "reference": "a8640182b892b99767d54404d19c5c3b3699f79b", "shasum": ""}, "require": {"laminas/laminas-stdlib": "^3.19", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0", "psr/container": "^1.0"}, "conflict": {"ext-psr": "*", "laminas/laminas-code": "<4.10.0", "zendframework/zend-code": "<3.3.1", "zendframework/zend-servicemanager": "*"}, "provide": {"psr/container-implementation": "^1.0"}, "replace": {"container-interop/container-interop": "^1.2.0"}, "require-dev": {"composer/package-versions-deprecated": "^*********", "friendsofphp/proxy-manager-lts": "^1.0.18", "laminas/laminas-code": "^4.14.0", "laminas/laminas-coding-standard": "~2.5.0", "laminas/laminas-container-config-test": "^0.8", "mikey179/vfsstream": "^1.6.12", "phpbench/phpbench": "^1.3.1", "phpunit/phpunit": "^10.5.36", "psalm/plugin-phpunit": "^0.18.4", "vimeo/psalm": "^5.26.1"}, "suggest": {"friendsofphp/proxy-manager-lts": "ProxyManager ^2.1.1 to handle lazy initialization of services"}, "bin": ["bin/generate-deps-for-config-factory", "bin/generate-factory-for-class"], "type": "library", "autoload": {"files": ["src/autoload.php"], "psr-4": {"Laminas\\ServiceManager\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Factory-Driven Dependency Injection Container", "homepage": "https://laminas.dev", "keywords": ["PSR-11", "dependency-injection", "di", "dic", "laminas", "service-manager", "servicemanager"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-servicemanager/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-servicemanager/issues", "rss": "https://github.com/laminas/laminas-servicemanager/releases.atom", "source": "https://github.com/laminas/laminas-servicemanager"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2024-10-28T21:32:16+00:00"}, {"name": "laminas/laminas-session", "version": "2.24.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-session.git", "reference": "487b6debacd3e029e27cbed7ce495b1328908dab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-session/zipball/487b6debacd3e029e27cbed7ce495b1328908dab", "reference": "487b6debacd3e029e27cbed7ce495b1328908dab", "shasum": ""}, "require": {"laminas/laminas-eventmanager": "^3.12", "laminas/laminas-servicemanager": "^3.22", "laminas/laminas-stdlib": "^3.18", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "conflict": {"amphp/amp": "<2.6.4", "zendframework/zend-session": "*"}, "require-dev": {"ext-xdebug": "*", "laminas/laminas-cache": "^3.12.2", "laminas/laminas-cache-storage-adapter-memory": "^2.3", "laminas/laminas-coding-standard": "~3.0.1", "laminas/laminas-db": "^2.20.0", "laminas/laminas-http": "^2.20", "laminas/laminas-validator": "^2.64.1", "mongodb/mongodb": "~1.20.0", "phpunit/phpunit": "^10.5.38", "psalm/plugin-phpunit": "^0.19.0", "vimeo/psalm": "^5.26.1"}, "suggest": {"laminas/laminas-cache": "Laminas\\Cache component", "laminas/laminas-db": "Laminas\\Db component", "laminas/laminas-http": "Laminas\\Http component", "laminas/laminas-servicemanager": "Laminas\\ServiceManager component", "laminas/laminas-validator": "Laminas\\Validator component", "mongodb/mongodb": "If you want to use the MongoDB session save handler"}, "type": "library", "extra": {"laminas": {"component": "Laminas\\Session", "config-provider": "Laminas\\Session\\ConfigProvider"}}, "autoload": {"psr-4": {"Laminas\\Session\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Object-oriented interface to PHP sessions and storage", "homepage": "https://laminas.dev", "keywords": ["laminas", "session"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-session/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-session/issues", "rss": "https://github.com/laminas/laminas-session/releases.atom", "source": "https://github.com/laminas/laminas-session"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2025-02-05T10:39:08+00:00"}, {"name": "laminas/laminas-stdlib", "version": "3.20.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-stdlib.git", "reference": "8974a1213be42c3e2f70b2c27b17f910291ab2f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-stdlib/zipball/8974a1213be42c3e2f70b2c27b17f910291ab2f4", "reference": "8974a1213be42c3e2f70b2c27b17f910291ab2f4", "shasum": ""}, "require": {"php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "conflict": {"zendframework/zend-stdlib": "*"}, "require-dev": {"laminas/laminas-coding-standard": "^3.0", "phpbench/phpbench": "^1.3.1", "phpunit/phpunit": "^10.5.38", "psalm/plugin-phpunit": "^0.19.0", "vimeo/psalm": "^5.26.1"}, "type": "library", "autoload": {"psr-4": {"Laminas\\Stdlib\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "SPL extensions, array utilities, error handlers, and more", "homepage": "https://laminas.dev", "keywords": ["laminas", "stdlib"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-stdlib/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-stdlib/issues", "rss": "https://github.com/laminas/laminas-stdlib/releases.atom", "source": "https://github.com/laminas/laminas-stdlib"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2024-10-29T13:46:07+00:00"}, {"name": "laminas/laminas-stratigility", "version": "3.13.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-stratigility.git", "reference": "3df57528b5c8e9d958515c51006825a83f76d62b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-stratigility/zipball/3df57528b5c8e9d958515c51006825a83f76d62b", "reference": "3df57528b5c8e9d958515c51006825a83f76d62b", "shasum": ""}, "require": {"fig/http-message-util": "^1.1", "laminas/laminas-escaper": "^2.10.0", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0", "psr/http-message": "^1.0 || ^2.0", "psr/http-server-middleware": "^1.0.2"}, "conflict": {"zendframework/zend-stratigility": "*"}, "require-dev": {"laminas/laminas-coding-standard": "~2.5.0", "laminas/laminas-diactoros": "^2.25 || ^3.5.0", "phpunit/phpunit": "^10.5.37", "psalm/plugin-phpunit": "^0.19.0", "vimeo/psalm": "^5.26.1"}, "suggest": {"psr/http-message-implementation": "Please install a psr/http-message-implementation to consume Stratigility; e.g., laminas/laminas-diactoros"}, "type": "library", "autoload": {"files": ["src/functions/double-pass-middleware.php", "src/functions/host.php", "src/functions/middleware.php", "src/functions/path.php", "src/functions/double-pass-middleware.legacy.php", "src/functions/host.legacy.php", "src/functions/middleware.legacy.php", "src/functions/path.legacy.php"], "psr-4": {"Laminas\\Stratigility\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "PSR-7 middleware foundation for building and dispatching middleware pipelines", "homepage": "https://laminas.dev", "keywords": ["http", "laminas", "middleware", "psr-15", "psr-7"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-stratigility/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-stratigility/issues", "rss": "https://github.com/laminas/laminas-stratigility/releases.atom", "source": "https://github.com/laminas/laminas-stratigility"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2024-10-28T11:28:41+00:00"}, {"name": "laminas/laminas-translator", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-translator.git", "reference": "12897e710e21413c1f93fc38fe9dead6b51c5218"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-translator/zipball/12897e710e21413c1f93fc38fe9dead6b51c5218", "reference": "12897e710e21413c1f93fc38fe9dead6b51c5218", "shasum": ""}, "require": {"php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "require-dev": {"laminas/laminas-coding-standard": "~3.0.0", "vimeo/psalm": "^5.24.0"}, "type": "library", "autoload": {"psr-4": {"Laminas\\Translator\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Interfaces for the Translator component of laminas-i18n", "homepage": "https://laminas.dev", "keywords": ["i18n", "laminas"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-i18n/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-translator/issues", "rss": "https://github.com/laminas/laminas-translator/releases.atom", "source": "https://github.com/laminas/laminas-translator"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2024-10-21T15:33:01+00:00"}, {"name": "laminas/laminas-uri", "version": "2.13.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-uri.git", "reference": "de53600ae8153b3605bb6edce8aeeef524eaafba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-uri/zipball/de53600ae8153b3605bb6edce8aeeef524eaafba", "reference": "de53600ae8153b3605bb6edce8aeeef524eaafba", "shasum": ""}, "require": {"laminas/laminas-escaper": "^2.9", "laminas/laminas-validator": "^2.39 || ^3.0", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "conflict": {"zendframework/zend-uri": "*"}, "require-dev": {"laminas/laminas-coding-standard": "~2.4.0", "phpunit/phpunit": "^9.6.20"}, "type": "library", "autoload": {"psr-4": {"Laminas\\Uri\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "A component that aids in manipulating and validating » Uniform Resource Identifiers (URIs)", "homepage": "https://laminas.dev", "keywords": ["laminas", "uri"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-uri/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-uri/issues", "rss": "https://github.com/laminas/laminas-uri/releases.atom", "source": "https://github.com/laminas/laminas-uri"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2024-12-03T12:27:51+00:00"}, {"name": "laminas/laminas-validator", "version": "2.64.2", "source": {"type": "git", "url": "https://github.com/laminas/laminas-validator.git", "reference": "771e504760448ac7af660710237ceb93be602e08"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-validator/zipball/771e504760448ac7af660710237ceb93be602e08", "reference": "771e504760448ac7af660710237ceb93be602e08", "shasum": ""}, "require": {"laminas/laminas-servicemanager": "^3.21.0", "laminas/laminas-stdlib": "^3.19", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0", "psr/http-message": "^1.0.1 || ^2.0.0"}, "conflict": {"zendframework/zend-validator": "*"}, "require-dev": {"laminas/laminas-coding-standard": "^2.5", "laminas/laminas-db": "^2.20", "laminas/laminas-filter": "^2.35.2", "laminas/laminas-i18n": "^2.26.0", "laminas/laminas-session": "^2.20", "laminas/laminas-uri": "^2.11.0", "phpunit/phpunit": "^10.5.20", "psalm/plugin-phpunit": "^0.19.0", "psr/http-client": "^1.0.3", "psr/http-factory": "^1.1.0", "vimeo/psalm": "^5.24.0"}, "suggest": {"laminas/laminas-db": "Laminas\\Db component, required by the (No)RecordExists validator", "laminas/laminas-filter": "Laminas\\Filter component, required by the Digits validator", "laminas/laminas-i18n": "Laminas\\I18n component to allow translation of validation error messages", "laminas/laminas-i18n-resources": "Translations of validator messages", "laminas/laminas-servicemanager": "Laminas\\ServiceManager component to allow using the ValidatorPluginManager and validator chains", "laminas/laminas-session": "Laminas\\Session component, ^2.8; required by the Csrf validator", "laminas/laminas-uri": "Laminas\\Uri component, required by the Uri and Sitemap\\Loc validators", "psr/http-message": "psr/http-message, required when validating PSR-7 UploadedFileInterface instances via the Upload and UploadFile validators"}, "type": "library", "extra": {"laminas": {"component": "Laminas\\Validator", "config-provider": "Laminas\\Validator\\ConfigProvider"}}, "autoload": {"psr-4": {"Laminas\\Validator\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Validation classes for a wide range of domains, and the ability to chain validators to create complex validation criteria", "homepage": "https://laminas.dev", "keywords": ["laminas", "validator"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-validator/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-validator/issues", "rss": "https://github.com/laminas/laminas-validator/releases.atom", "source": "https://github.com/laminas/laminas-validator"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2024-11-26T21:29:17+00:00"}, {"name": "laminas/laminas-view", "version": "2.39.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-view.git", "reference": "673f56af99b1780dc6babc3028d75724177969ed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-view/zipball/673f56af99b1780dc6babc3028d75724177969ed", "reference": "673f56af99b1780dc6babc3028d75724177969ed", "shasum": ""}, "require": {"ext-dom": "*", "ext-filter": "*", "ext-json": "*", "laminas/laminas-escaper": "^2.5", "laminas/laminas-eventmanager": "^3.4", "laminas/laminas-json": "^3.3", "laminas/laminas-servicemanager": "^3.21.0", "laminas/laminas-stdlib": "^3.10.1", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0", "psr/container": "^1 || ^2"}, "conflict": {"amphp/dns": "<2.1.2", "amphp/socket": "<2.3.1", "container-interop/container-interop": "<1.2", "laminas/laminas-router": "<3.0.1", "laminas/laminas-session": "<2.12", "zendframework/zend-view": "*"}, "require-dev": {"laminas/laminas-authentication": "^2.18", "laminas/laminas-coding-standard": "~3.0.1", "laminas/laminas-feed": "^2.23", "laminas/laminas-filter": "^2.40", "laminas/laminas-http": "^2.21", "laminas/laminas-i18n": "^2.30.0", "laminas/laminas-modulemanager": "^2.17", "laminas/laminas-mvc": "^3.8.0", "laminas/laminas-mvc-i18n": "^1.9", "laminas/laminas-mvc-plugin-flashmessenger": "^1.11.0", "laminas/laminas-navigation": "^2.20.0", "laminas/laminas-paginator": "^2.19.0", "laminas/laminas-permissions-acl": "^2.17", "laminas/laminas-router": "^3.14.0", "laminas/laminas-uri": "^2.13", "phpunit/phpunit": "^10.5.45", "psalm/plugin-phpunit": "^0.19.5", "vimeo/psalm": "^6.10.1"}, "suggest": {"laminas/laminas-authentication": "Laminas\\Authentication component", "laminas/laminas-feed": "Laminas\\Feed component", "laminas/laminas-filter": "Laminas\\Filter component", "laminas/laminas-http": "Laminas\\Http component", "laminas/laminas-i18n": "Laminas\\I18n component", "laminas/laminas-mvc": "Laminas\\Mvc component", "laminas/laminas-mvc-plugin-flashmessenger": "laminas-mvc-plugin-flashmessenger component, if you want to use the FlashMessenger view helper with laminas-mvc versions 3 and up", "laminas/laminas-navigation": "Laminas\\Navigation component", "laminas/laminas-paginator": "Laminas\\Paginator component", "laminas/laminas-permissions-acl": "Laminas\\Permissions\\Acl component", "laminas/laminas-uri": "Laminas\\Uri component"}, "bin": ["bin/templatemap_generator.php"], "type": "library", "autoload": {"psr-4": {"Laminas\\View\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Flexible view layer supporting and providing multiple view layers, helpers, and more", "homepage": "https://laminas.dev", "keywords": ["laminas", "view"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-view/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-view/issues", "rss": "https://github.com/laminas/laminas-view/releases.atom", "source": "https://github.com/laminas/laminas-view"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2025-04-30T08:01:59+00:00"}, {"name": "maennchen/zipstream-php", "version": "3.1.2", "source": {"type": "git", "url": "https://github.com/maennchen/ZipStream-PHP.git", "reference": "aeadcf5c412332eb426c0f9b4485f6accba2a99f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maennchen/ZipStream-PHP/zipball/aeadcf5c412332eb426c0f9b4485f6accba2a99f", "reference": "aeadcf5c412332eb426c0f9b4485f6accba2a99f", "shasum": ""}, "require": {"ext-mbstring": "*", "ext-zlib": "*", "php-64bit": "^8.2"}, "require-dev": {"brianium/paratest": "^7.7", "ext-zip": "*", "friendsofphp/php-cs-fixer": "^3.16", "guzzlehttp/guzzle": "^7.5", "mikey179/vfsstream": "^1.6", "php-coveralls/php-coveralls": "^2.5", "phpunit/phpunit": "^11.0", "vimeo/psalm": "^6.0"}, "suggest": {"guzzlehttp/psr7": "^2.4", "psr/http-message": "^2.0"}, "type": "library", "autoload": {"psr-4": {"ZipStream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Jonatan Männchen", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "ZipStream is a library for dynamically streaming dynamic zip files from PHP without writing to the disk at all on the server.", "keywords": ["stream", "zip"], "support": {"issues": "https://github.com/maennchen/ZipStream-PHP/issues", "source": "https://github.com/maennchen/ZipStream-PHP/tree/3.1.2"}, "funding": [{"url": "https://github.com/maennchen", "type": "github"}], "time": "2025-01-27T12:07:53+00:00"}, {"name": "markbaker/complex", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPComplex.git", "reference": "95c56caa1cf5c766ad6d65b6344b807c1e8405b9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPComplex/zipball/95c56caa1cf5c766ad6d65b6344b807c1e8405b9", "reference": "95c56caa1cf5c766ad6d65b6344b807c1e8405b9", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "phpcompatibility/php-compatibility": "^9.3", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "squizlabs/php_codesniffer": "^3.7"}, "type": "library", "autoload": {"psr-4": {"Complex\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with complex numbers", "homepage": "https://github.com/MarkBaker/PHPComplex", "keywords": ["complex", "mathematics"], "support": {"issues": "https://github.com/MarkBaker/PHPComplex/issues", "source": "https://github.com/MarkBaker/PHPComplex/tree/3.0.2"}, "time": "2022-12-06T16:21:08+00:00"}, {"name": "markbaker/matrix", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPMatrix.git", "reference": "728434227fe21be27ff6d86621a1b13107a2562c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPMatrix/zipball/728434227fe21be27ff6d86621a1b13107a2562c", "reference": "728434227fe21be27ff6d86621a1b13107a2562c", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "phpcompatibility/php-compatibility": "^9.3", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "^4.0", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "sebastian/phpcpd": "^4.0", "squizlabs/php_codesniffer": "^3.7"}, "type": "library", "autoload": {"psr-4": {"Matrix\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with matrices", "homepage": "https://github.com/MarkBaker/PHPMatrix", "keywords": ["mathematics", "matrix", "vector"], "support": {"issues": "https://github.com/MarkBaker/PHPMatrix/issues", "source": "https://github.com/MarkBaker/PHPMatrix/tree/3.0.1"}, "time": "2022-12-02T22:17:43+00:00"}, {"name": "masterminds/html5", "version": "2.9.0", "source": {"type": "git", "url": "https://github.com/Masterminds/html5-php.git", "reference": "f5ac2c0b0a2eefca70b2ce32a5809992227e75a6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Masterminds/html5-php/zipball/f5ac2c0b0a2eefca70b2ce32a5809992227e75a6", "reference": "f5ac2c0b0a2eefca70b2ce32a5809992227e75a6", "shasum": ""}, "require": {"ext-dom": "*", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7.21 || ^6 || ^7 || ^8 || ^9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"Masterminds\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An HTML5 parser and serializer.", "homepage": "http://masterminds.github.io/html5-php", "keywords": ["HTML5", "dom", "html", "parser", "querypath", "serializer", "xml"], "support": {"issues": "https://github.com/Masterminds/html5-php/issues", "source": "https://github.com/Masterminds/html5-php/tree/2.9.0"}, "time": "2024-03-31T07:05:07+00:00"}, {"name": "mikehaertl/php-shellcommand", "version": "1.7.0", "source": {"type": "git", "url": "https://github.com/mikehaertl/php-shellcommand.git", "reference": "e79ea528be155ffdec6f3bf1a4a46307bb49e545"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mikehaertl/php-shellcommand/zipball/e79ea528be155ffdec6f3bf1a4a46307bb49e545", "reference": "e79ea528be155ffdec6f3bf1a4a46307bb49e545", "shasum": ""}, "require": {"php": ">= 5.3.0"}, "require-dev": {"phpunit/phpunit": ">4.0 <=9.4"}, "type": "library", "autoload": {"psr-4": {"mikehaertl\\shellcommand\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "An object oriented interface to shell commands", "keywords": ["shell"], "support": {"issues": "https://github.com/mikehaertl/php-shellcommand/issues", "source": "https://github.com/mikehaertl/php-shellcommand/tree/1.7.0"}, "time": "2023-04-19T08:25:22+00:00"}, {"name": "mikehaertl/php-tmpfile", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/mikehaertl/php-tmpfile.git", "reference": "a5392bed91f67e2849a7cb24075d346468e1b1a8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mikehaertl/php-tmpfile/zipball/a5392bed91f67e2849a7cb24075d346468e1b1a8", "reference": "a5392bed91f67e2849a7cb24075d346468e1b1a8", "shasum": ""}, "require-dev": {"php": ">=5.3.0", "phpunit/phpunit": ">4.0 <=9.4"}, "type": "library", "autoload": {"psr-4": {"mikehaertl\\tmp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A convenience class for temporary files", "keywords": ["files"], "support": {"issues": "https://github.com/mikehaertl/php-tmpfile/issues", "source": "https://github.com/mikehaertl/php-tmpfile/tree/1.3.0"}, "time": "2024-10-14T16:12:48+00:00"}, {"name": "mikehaertl/phpwkhtmltopdf", "version": "2.5.0", "source": {"type": "git", "url": "https://github.com/mikehaertl/phpwkhtmltopdf.git", "reference": "17ee71341591415d942774eda2c98d8ba7ea9e90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mikehaertl/phpwkhtmltopdf/zipball/17ee71341591415d942774eda2c98d8ba7ea9e90", "reference": "17ee71341591415d942774eda2c98d8ba7ea9e90", "shasum": ""}, "require": {"mikehaertl/php-shellcommand": "^1.5.0", "mikehaertl/php-tmpfile": "^1.2.1", "php": ">=5.0.0"}, "require-dev": {"phpunit/phpunit": ">4.0 <9.4"}, "type": "library", "autoload": {"psr-4": {"mikehaertl\\wkhtmlto\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A slim PHP wrapper around wkhtmltopdf with an easy to use and clean OOP interface", "homepage": "http://mikehaertl.github.com/phpwkhtmltopdf/", "keywords": ["pdf", "wkhtmltoimage", "wkhtmltopdf"], "support": {"issues": "https://github.com/mikehaertl/phpwkhtmltopdf/issues", "source": "https://github.com/mikehaertl/phpwkhtmltopdf/tree/2.5.0"}, "time": "2021-03-01T19:41:06+00:00"}, {"name": "monolog/monolog", "version": "2.10.0", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "5cf826f2991858b54d5c3809bee745560a1042a7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/5cf826f2991858b54d5c3809bee745560a1042a7", "reference": "5cf826f2991858b54d5c3809bee745560a1042a7", "shasum": ""}, "require": {"php": ">=7.2", "psr/log": "^1.0.1 || ^2.0 || ^3.0"}, "provide": {"psr/log-implementation": "1.0.0 || 2.0.0 || 3.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7 || ^8", "ext-json": "*", "graylog2/gelf-php": "^1.4.2 || ^2@dev", "guzzlehttp/guzzle": "^7.4", "guzzlehttp/psr7": "^2.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "phpspec/prophecy": "^1.15", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^8.5.38 || ^9.6.19", "predis/predis": "^1.1 || ^2.0", "rollbar/rollbar": "^1.3 || ^2 || ^3", "ruflin/elastica": "^7", "swiftmailer/swiftmailer": "^5.3|^6.0", "symfony/mailer": "^5.4 || ^6", "symfony/mime": "^5.4 || ^6"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-curl": "Required to send log messages using the IFTTTHandler, the LogglyHandler, the SendGridHandler, the SlackWebhookHandler or the TelegramBotHandler", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "ext-openssl": "Required to send log messages using SSL", "ext-sockets": "Allow sending log messages to a Syslog server (via UDP driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "https://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.10.0"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "time": "2024-11-12T12:43:37+00:00"}, {"name": "mtdowling/jmespath.php", "version": "2.8.0", "source": {"type": "git", "url": "https://github.com/jmespath/jmespath.php.git", "reference": "a2a865e05d5f420b50cc2f85bb78d565db12a6bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jmespath/jmespath.php/zipball/a2a865e05d5f420b50cc2f85bb78d565db12a6bc", "reference": "a2a865e05d5f420b50cc2f85bb78d565db12a6bc", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "symfony/polyfill-mbstring": "^1.17"}, "require-dev": {"composer/xdebug-handler": "^3.0.3", "phpunit/phpunit": "^8.5.33"}, "bin": ["bin/jp.php"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"files": ["src/JmesPath.php"], "psr-4": {"JmesPath\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Declaratively specify how to extract elements from a JSON document", "keywords": ["json", "jsonpath"], "support": {"issues": "https://github.com/jmespath/jmespath.php/issues", "source": "https://github.com/jmespath/jmespath.php/tree/2.8.0"}, "time": "2024-09-04T18:46:31+00:00"}, {"name": "myclabs/deep-copy", "version": "1.13.1", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "1720ddd719e16cf0db4eb1c6eca108031636d46c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/1720ddd719e16cf0db4eb1c6eca108031636d46c", "reference": "1720ddd719e16cf0db4eb1c6eca108031636d46c", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3 <3.2.2"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpspec/prophecy": "^1.10", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "type": "library", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.13.1"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "time": "2025-04-29T12:36:36+00:00"}, {"name": "nesbot/carbon", "version": "3.9.1", "source": {"type": "git", "url": "https://github.com/CarbonPHP/carbon.git", "reference": "ced71f79398ece168e24f7f7710462f462310d4d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/ced71f79398ece168e24f7f7710462f462310d4d", "reference": "ced71f79398ece168e24f7f7710462f462310d4d", "shasum": ""}, "require": {"carbonphp/carbon-doctrine-types": "<100.0", "ext-json": "*", "php": "^8.1", "psr/clock": "^1.0", "symfony/clock": "^6.3 || ^7.0", "symfony/polyfill-mbstring": "^1.0", "symfony/translation": "^4.4.18 || ^5.2.1|| ^6.0 || ^7.0"}, "provide": {"psr/clock-implementation": "1.0"}, "require-dev": {"doctrine/dbal": "^3.6.3 || ^4.0", "doctrine/orm": "^2.15.2 || ^3.0", "friendsofphp/php-cs-fixer": "^3.57.2", "kylekatarnls/multi-tester": "^2.5.3", "ondrejmirtes/better-reflection": "^********", "phpmd/phpmd": "^2.15.0", "phpstan/extension-installer": "^1.3.1", "phpstan/phpstan": "^1.11.2", "phpunit/phpunit": "^10.5.20", "squizlabs/php_codesniffer": "^3.9.0"}, "bin": ["bin/carbon"], "type": "library", "extra": {"laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "phpstan": {"includes": ["extension.neon"]}, "branch-alias": {"dev-2.x": "2.x-dev", "dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Carbon\\": "src/Carbon/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://markido.com"}, {"name": "kylekatarnls", "homepage": "https://github.com/kylekatarnls"}], "description": "An API extension for DateTime that supports 281 different languages.", "homepage": "https://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "support": {"docs": "https://carbon.nesbot.com/docs", "issues": "https://github.com/CarbonPHP/carbon/issues", "source": "https://github.com/CarbonPHP/carbon"}, "funding": [{"url": "https://github.com/sponsors/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon#sponsor", "type": "opencollective"}, {"url": "https://tidelift.com/subscription/pkg/packagist-nesbot-carbon?utm_source=packagist-nesbot-carbon&utm_medium=referral&utm_campaign=readme", "type": "tidelift"}], "time": "2025-05-01T19:51:51+00:00"}, {"name": "nikic/php-parser", "version": "v5.4.0", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "447a020a1f875a434d62f2a401f53b82a396e494"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/447a020a1f875a434d62f2a401f53b82a396e494", "reference": "447a020a1f875a434d62f2a401f53b82a396e494", "shasum": ""}, "require": {"ext-ctype": "*", "ext-json": "*", "ext-tokenizer": "*", "php": ">=7.4"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v5.4.0"}, "time": "2024-12-30T11:07:19+00:00"}, {"name": "paragonie/constant_time_encoding", "version": "v3.0.0", "source": {"type": "git", "url": "https://github.com/paragonie/constant_time_encoding.git", "reference": "df1e7fde177501eee2037dd159cf04f5f301a512"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/df1e7fde177501eee2037dd159cf04f5f301a512", "reference": "df1e7fde177501eee2037dd159cf04f5f301a512", "shasum": ""}, "require": {"php": "^8"}, "require-dev": {"phpunit/phpunit": "^9", "vimeo/psalm": "^4|^5"}, "type": "library", "autoload": {"psr-4": {"ParagonIE\\ConstantTime\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com", "role": "Maintainer"}, {"name": "<PERSON> 'Sc00bz' <PERSON>", "email": "<EMAIL>", "homepage": "https://www.tobtu.com", "role": "Original Developer"}], "description": "Constant-time Implementations of RFC 4648 Encoding (Base-64, Base-32, Base-16)", "keywords": ["base16", "base32", "base32_decode", "base32_encode", "base64", "base64_decode", "base64_encode", "bin2hex", "encoding", "hex", "hex2bin", "rfc4648"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/constant_time_encoding/issues", "source": "https://github.com/paragonie/constant_time_encoding"}, "time": "2024-05-08T12:36:18+00:00"}, {"name": "paragonie/random_compat", "version": "v9.99.100", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/996434e5492cb4c3edcb9168db6fbb1359ef965a", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a", "shasum": ""}, "require": {"php": ">= 7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/random_compat/issues", "source": "https://github.com/paragonie/random_compat"}, "time": "2020-10-15T08:29:30+00:00"}, {"name": "php-amqplib/php-amqplib", "version": "v3.7.3", "source": {"type": "git", "url": "https://github.com/php-amqplib/php-amqplib.git", "reference": "9f50fe69a9f1a19e2cb25596a354d705de36fe59"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-amqplib/php-amqplib/zipball/9f50fe69a9f1a19e2cb25596a354d705de36fe59", "reference": "9f50fe69a9f1a19e2cb25596a354d705de36fe59", "shasum": ""}, "require": {"ext-mbstring": "*", "ext-sockets": "*", "php": "^7.2||^8.0", "phpseclib/phpseclib": "^2.0|^3.0"}, "conflict": {"php": "7.4.0 - 7.4.1"}, "replace": {"videlalvaro/php-amqplib": "self.version"}, "require-dev": {"ext-curl": "*", "nategood/httpful": "^0.2.20", "phpunit/phpunit": "^7.5|^9.5", "squizlabs/php_codesniffer": "^3.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"PhpAmqpLib\\": "PhpAmqpLib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON><PERSON>", "role": "Original Maintainer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Maintainer"}], "description": "Formerly videlalvaro/php-amqplib.  This library is a pure PHP implementation of the AMQP protocol. It's been tested against RabbitMQ.", "homepage": "https://github.com/php-amqplib/php-amqplib/", "keywords": ["message", "queue", "rabbitmq"], "support": {"issues": "https://github.com/php-amqplib/php-amqplib/issues", "source": "https://github.com/php-amqplib/php-amqplib/tree/v3.7.3"}, "time": "2025-02-18T20:11:13+00:00"}, {"name": "php-ffmpeg/php-ffmpeg", "version": "v1.3.2", "source": {"type": "git", "url": "https://github.com/PHP-FFMpeg/PHP-FFMpeg.git", "reference": "8e74bdc07ad200da7a6cfb21ec2652875e4368e0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHP-FFMpeg/PHP-FFMpeg/zipball/8e74bdc07ad200da7a6cfb21ec2652875e4368e0", "reference": "8e74bdc07ad200da7a6cfb21ec2652875e4368e0", "shasum": ""}, "require": {"evenement/evenement": "^3.0", "php": "^8.0 || ^8.1 || ^8.2 || ^8.3 || ^8.4", "psr/log": "^1.0 || ^2.0 || ^3.0", "spatie/temporary-directory": "^2.0", "symfony/cache": "^5.4 || ^6.0 || ^7.0", "symfony/process": "^5.4 || ^6.0 || ^7.0"}, "require-dev": {"mockery/mockery": "^1.5", "phpunit/phpunit": "^9.5.10 || ^10.0"}, "suggest": {"php-ffmpeg/extras": "A compilation of common audio & video drivers for PHP-FFMpeg"}, "type": "library", "autoload": {"psr-4": {"FFMpeg\\": "src/FFMpeg", "Alchemy\\BinaryDriver\\": "src/Alchemy/BinaryDriver"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.lickmychip.com/"}, {"name": "Phraseanet Team", "email": "<EMAIL>", "homepage": "http://www.phraseanet.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.karisch.guru"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://www.strime.io/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://jens-hausdorf.de"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://protone.media"}], "description": "FFMpeg PHP, an Object Oriented library to communicate with AVconv / ffmpeg", "keywords": ["audio", "audio processing", "avconv", "avprobe", "ffmpeg", "ffprobe", "video", "video processing"], "support": {"issues": "https://github.com/PHP-FFMpeg/PHP-FFMpeg/issues", "source": "https://github.com/PHP-FFMpeg/PHP-FFMpeg/tree/v1.3.2"}, "time": "2025-04-01T20:36:46+00:00"}, {"name": "php-http/discovery", "version": "1.20.0", "source": {"type": "git", "url": "https://github.com/php-http/discovery.git", "reference": "82fe4c73ef3363caed49ff8dd1539ba06044910d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/discovery/zipball/82fe4c73ef3363caed49ff8dd1539ba06044910d", "reference": "82fe4c73ef3363caed49ff8dd1539ba06044910d", "shasum": ""}, "require": {"composer-plugin-api": "^1.0|^2.0", "php": "^7.1 || ^8.0"}, "conflict": {"nyholm/psr7": "<1.0", "zendframework/zend-diactoros": "*"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "*", "psr/http-factory-implementation": "*", "psr/http-message-implementation": "*"}, "require-dev": {"composer/composer": "^1.0.2|^2.0", "graham-campbell/phpspec-skip-example-extension": "^5.0", "php-http/httplug": "^1.0 || ^2.0", "php-http/message-factory": "^1.0", "phpspec/phpspec": "^5.1 || ^6.1 || ^7.3", "sebastian/comparator": "^3.0.5 || ^4.0.8", "symfony/phpunit-bridge": "^6.4.4 || ^7.0.1"}, "type": "composer-plugin", "extra": {"class": "Http\\Discovery\\Composer\\Plugin", "plugin-optional": true}, "autoload": {"psr-4": {"Http\\Discovery\\": "src/"}, "exclude-from-classmap": ["src/Composer/Plugin.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Finds and installs PSR-7, PSR-17, PSR-18 and HTTPlug implementations", "homepage": "http://php-http.org", "keywords": ["adapter", "client", "discovery", "factory", "http", "message", "psr17", "psr7"], "support": {"issues": "https://github.com/php-http/discovery/issues", "source": "https://github.com/php-http/discovery/tree/1.20.0"}, "time": "2024-10-02T11:20:13+00:00"}, {"name": "php-http/guzzle7-adapter", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-http/guzzle7-adapter.git", "reference": "03a415fde709c2f25539790fecf4d9a31bc3d0eb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/guzzle7-adapter/zipball/03a415fde709c2f25539790fecf4d9a31bc3d0eb", "reference": "03a415fde709c2f25539790fecf4d9a31bc3d0eb", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^7.0", "php": "^7.3 | ^8.0", "php-http/httplug": "^2.0", "psr/http-client": "^1.0"}, "provide": {"php-http/async-client-implementation": "1.0", "php-http/client-implementation": "1.0", "psr/http-client-implementation": "1.0"}, "require-dev": {"php-http/client-integration-tests": "^3.0", "php-http/message-factory": "^1.1", "phpspec/prophecy-phpunit": "^2.0", "phpunit/phpunit": "^8.0|^9.3"}, "type": "library", "autoload": {"psr-4": {"Http\\Adapter\\Guzzle7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Guzzle 7 HTTP Adapter", "homepage": "http://httplug.io", "keywords": ["Guzzle", "http"], "support": {"issues": "https://github.com/php-http/guzzle7-adapter/issues", "source": "https://github.com/php-http/guzzle7-adapter/tree/1.1.0"}, "time": "2024-11-26T11:14:36+00:00"}, {"name": "php-http/httplug", "version": "2.4.1", "source": {"type": "git", "url": "https://github.com/php-http/httplug.git", "reference": "5cad731844891a4c282f3f3e1b582c46839d22f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/httplug/zipball/5cad731844891a4c282f3f3e1b582c46839d22f4", "reference": "5cad731844891a4c282f3f3e1b582c46839d22f4", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "php-http/promise": "^1.1", "psr/http-client": "^1.0", "psr/http-message": "^1.0 || ^2.0"}, "require-dev": {"friends-of-phpspec/phpspec-code-coverage": "^4.1 || ^5.0 || ^6.0", "phpspec/phpspec": "^5.1 || ^6.0 || ^7.0"}, "type": "library", "autoload": {"psr-4": {"Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Eric <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "HTTPlug, the HTTP client abstraction for PHP", "homepage": "http://httplug.io", "keywords": ["client", "http"], "support": {"issues": "https://github.com/php-http/httplug/issues", "source": "https://github.com/php-http/httplug/tree/2.4.1"}, "time": "2024-09-23T11:39:58+00:00"}, {"name": "php-http/message", "version": "1.16.2", "source": {"type": "git", "url": "https://github.com/php-http/message.git", "reference": "06dd5e8562f84e641bf929bfe699ee0f5ce8080a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/message/zipball/06dd5e8562f84e641bf929bfe699ee0f5ce8080a", "reference": "06dd5e8562f84e641bf929bfe699ee0f5ce8080a", "shasum": ""}, "require": {"clue/stream-filter": "^1.5", "php": "^7.2 || ^8.0", "psr/http-message": "^1.1 || ^2.0"}, "provide": {"php-http/message-factory-implementation": "1.0"}, "require-dev": {"ergebnis/composer-normalize": "^2.6", "ext-zlib": "*", "guzzlehttp/psr7": "^1.0 || ^2.0", "laminas/laminas-diactoros": "^2.0 || ^3.0", "php-http/message-factory": "^1.0.2", "phpspec/phpspec": "^5.1 || ^6.3 || ^7.1", "slim/slim": "^3.0"}, "suggest": {"ext-zlib": "Used with compressor/decompressor streams", "guzzlehttp/psr7": "Used with Guzzle PSR-7 Factories", "laminas/laminas-diactoros": "Used with Diactoros Factories", "slim/slim": "Used with Slim Framework PSR-7 implementation"}, "type": "library", "autoload": {"files": ["src/filters.php"], "psr-4": {"Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "HTTP Message related tools", "homepage": "http://php-http.org", "keywords": ["http", "message", "psr-7"], "support": {"issues": "https://github.com/php-http/message/issues", "source": "https://github.com/php-http/message/tree/1.16.2"}, "time": "2024-10-02T11:34:13+00:00"}, {"name": "php-http/message-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-http/message-factory.git", "reference": "4d8778e1c7d405cbb471574821c1ff5b68cc8f57"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/message-factory/zipball/4d8778e1c7d405cbb471574821c1ff5b68cc8f57", "reference": "4d8778e1c7d405cbb471574821c1ff5b68cc8f57", "shasum": ""}, "require": {"php": ">=5.4", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Factory interfaces for PSR-7 HTTP Message", "homepage": "http://php-http.org", "keywords": ["factory", "http", "message", "stream", "uri"], "support": {"issues": "https://github.com/php-http/message-factory/issues", "source": "https://github.com/php-http/message-factory/tree/1.1.0"}, "abandoned": "psr/http-factory", "time": "2023-04-14T14:16:17+00:00"}, {"name": "php-http/promise", "version": "1.3.1", "source": {"type": "git", "url": "https://github.com/php-http/promise.git", "reference": "fc85b1fba37c169a69a07ef0d5a8075770cc1f83"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/promise/zipball/fc85b1fba37c169a69a07ef0d5a8075770cc1f83", "reference": "fc85b1fba37c169a69a07ef0d5a8075770cc1f83", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"friends-of-phpspec/phpspec-code-coverage": "^4.3.2 || ^6.3", "phpspec/phpspec": "^5.1.2 || ^6.2 || ^7.4"}, "type": "library", "autoload": {"psr-4": {"Http\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Promise used for asynchronous HTTP requests", "homepage": "http://httplug.io", "keywords": ["promise"], "support": {"issues": "https://github.com/php-http/promise/issues", "source": "https://github.com/php-http/promise/tree/1.3.1"}, "time": "2024-03-15T13:55:21+00:00"}, {"name": "phpoffice/phpspreadsheet", "version": "4.2.0", "source": {"type": "git", "url": "https://github.com/PHPOffice/PhpSpreadsheet.git", "reference": "5f6d7410e5fd72cac1aa67d4f05f4fe664d01ba6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PhpSpreadsheet/zipball/5f6d7410e5fd72cac1aa67d4f05f4fe664d01ba6", "reference": "5f6d7410e5fd72cac1aa67d4f05f4fe664d01ba6", "shasum": ""}, "require": {"composer/pcre": "^1||^2||^3", "ext-ctype": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-iconv": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-xml": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "ext-zip": "*", "ext-zlib": "*", "maennchen/zipstream-php": "^2.1 || ^3.0", "markbaker/complex": "^3.0", "markbaker/matrix": "^3.0", "php": "^8.1", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/simple-cache": "^1.0 || ^2.0 || ^3.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-main", "dompdf/dompdf": "^2.0 || ^3.0", "friendsofphp/php-cs-fixer": "^3.2", "mitoteam/jpgraph": "^10.3", "mpdf/mpdf": "^8.1.1", "phpcompatibility/php-compatibility": "^9.3", "phpstan/phpstan": "^1.1 || ^2.0", "phpstan/phpstan-deprecation-rules": "^1.0 || ^2.0", "phpstan/phpstan-phpunit": "^1.0 || ^2.0", "phpunit/phpunit": "^10.5", "squizlabs/php_codesniffer": "^3.7", "tecnickcom/tcpdf": "^6.5"}, "suggest": {"dompdf/dompdf": "Option for rendering PDF with PDF Writer", "ext-intl": "PHP Internationalization Functions", "mitoteam/jpgraph": "Option for rendering charts, or including charts with PDF or HTML Writers", "mpdf/mpdf": "Option for rendering PDF with PDF Writer", "tecnickcom/tcpdf": "Option for rendering PDF with PDF Writer"}, "type": "library", "autoload": {"psr-4": {"PhpOffice\\PhpSpreadsheet\\": "src/PhpSpreadsheet"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://blog.maartenballiauw.be"}, {"name": "<PERSON>", "homepage": "https://markbakeruk.net"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "description": "PHPSpreadsheet - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "https://github.com/PHPOffice/PhpSpreadsheet", "keywords": ["OpenXML", "excel", "gnumeric", "ods", "php", "spreadsheet", "xls", "xlsx"], "support": {"issues": "https://github.com/PHPOffice/PhpSpreadsheet/issues", "source": "https://github.com/PHPOffice/PhpSpreadsheet/tree/4.2.0"}, "time": "2025-04-17T02:41:45+00:00"}, {"name": "phpseclib/phpseclib", "version": "3.0.43", "source": {"type": "git", "url": "https://github.com/phpseclib/phpseclib.git", "reference": "709ec107af3cb2f385b9617be72af8cf62441d02"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/709ec107af3cb2f385b9617be72af8cf62441d02", "reference": "709ec107af3cb2f385b9617be72af8cf62441d02", "shasum": ""}, "require": {"paragonie/constant_time_encoding": "^1|^2|^3", "paragonie/random_compat": "^1.4|^2.0|^9.99.99", "php": ">=5.6.1"}, "require-dev": {"phpunit/phpunit": "*"}, "suggest": {"ext-dom": "Install the DOM extension to load XML formatted public keys.", "ext-gmp": "Install the GMP (GNU Multiple Precision) extension in order to speed up arbitrary precision integer arithmetic operations.", "ext-libsodium": "SSH2/SFTP can make use of some algorithms provided by the libsodium-php extension.", "ext-mcrypt": "Install the Mcrypt extension in order to speed up a few other cryptographic operations.", "ext-openssl": "Install the OpenSSL extension in order to speed up a wide variety of cryptographic operations."}, "type": "library", "autoload": {"files": ["phpseclib/bootstrap.php"], "psr-4": {"phpseclib3\\": "phpseclib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "PHP Secure Communications Library - Pure-PHP implementations of RSA, AES, SSH2, SFTP, X.509 etc.", "homepage": "http://phpseclib.sourceforge.net", "keywords": ["BigInteger", "aes", "asn.1", "asn1", "blowfish", "crypto", "cryptography", "encryption", "rsa", "security", "sftp", "signature", "signing", "ssh", "twofish", "x.509", "x509"], "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.43"}, "funding": [{"url": "https://github.com/terrafrost", "type": "github"}, {"url": "https://www.patreon.com/phpseclib", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/phpseclib/phpseclib", "type": "tidelift"}], "time": "2024-12-14T21:12:59+00:00"}, {"name": "predis/predis", "version": "v2.4.0", "source": {"type": "git", "url": "https://github.com/predis/predis.git", "reference": "f49e13ee3a2a825631562aa0223ac922ec5d058b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/predis/predis/zipball/f49e13ee3a2a825631562aa0223ac922ec5d058b", "reference": "f49e13ee3a2a825631562aa0223ac922ec5d058b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.3", "phpstan/phpstan": "^1.9", "phpunit/phpcov": "^6.0 || ^8.0", "phpunit/phpunit": "^8.0 || ^9.4"}, "suggest": {"ext-relay": "Faster connection with in-memory caching (>=0.6.2)"}, "type": "library", "autoload": {"psr-4": {"Predis\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "https://till.im", "role": "Maintainer"}], "description": "A flexible and feature-complete Redis/Valkey client for PHP.", "homepage": "http://github.com/predis/predis", "keywords": ["nosql", "predis", "redis"], "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v2.4.0"}, "funding": [{"url": "https://github.com/sponsors/tillkruss", "type": "github"}], "time": "2025-04-30T15:16:02+00:00"}, {"name": "psr/cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/3.0.0"}, "time": "2021-02-03T23:26:27+00:00"}, {"name": "psr/clock", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/clock.git", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/clock/zipball/e41a24703d4560fd0acb709162f73b8adfc3aa0d", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d", "shasum": ""}, "require": {"php": "^7.0 || ^8.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Clock\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for reading the clock.", "homepage": "https://github.com/php-fig/clock", "keywords": ["clock", "now", "psr", "psr-20", "time"], "support": {"issues": "https://github.com/php-fig/clock/issues", "source": "https://github.com/php-fig/clock/tree/1.0.0"}, "time": "2022-11-25T14:36:26+00:00"}, {"name": "psr/container", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/513e0666f7216c7459170d56df27dfcefe1689ea", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/1.1.2"}, "time": "2021-11-05T16:50:12+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory"}, "time": "2024-04-15T12:06:14+00:00"}, {"name": "psr/http-message", "version": "1.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/1.1"}, "time": "2023-04-04T09:50:52+00:00"}, {"name": "psr/http-server-handler", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/http-server-handler.git", "reference": "84c4fb66179be4caaf8e97bd239203245302e7d4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-server-handler/zipball/84c4fb66179be4caaf8e97bd239203245302e7d4", "reference": "84c4fb66179be4caaf8e97bd239203245302e7d4", "shasum": ""}, "require": {"php": ">=7.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Server\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP server-side request handler", "keywords": ["handler", "http", "http-interop", "psr", "psr-15", "psr-7", "request", "response", "server"], "support": {"source": "https://github.com/php-fig/http-server-handler/tree/1.0.2"}, "time": "2023-04-10T20:06:20+00:00"}, {"name": "psr/http-server-middleware", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/http-server-middleware.git", "reference": "c1481f747daaa6a0782775cd6a8c26a1bf4a3829"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-server-middleware/zipball/c1481f747daaa6a0782775cd6a8c26a1bf4a3829", "reference": "c1481f747daaa6a0782775cd6a8c26a1bf4a3829", "shasum": ""}, "require": {"php": ">=7.0", "psr/http-message": "^1.0 || ^2.0", "psr/http-server-handler": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Server\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP server-side middleware", "keywords": ["http", "http-interop", "middleware", "psr", "psr-15", "psr-7", "request", "response"], "support": {"issues": "https://github.com/php-fig/http-server-middleware/issues", "source": "https://github.com/php-fig/http-server-middleware/tree/1.0.2"}, "time": "2023-04-11T06:14:47+00:00"}, {"name": "psr/log", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "d49695b909c3b7628b6289db5479a1c204601f11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/d49695b909c3b7628b6289db5479a1c204601f11", "reference": "d49695b909c3b7628b6289db5479a1c204601f11", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/1.1.4"}, "time": "2021-05-03T11:20:27+00:00"}, {"name": "psr/simple-cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/764e0b3939f5ca87cb904f570ef9be2d78a07865", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/3.0.0"}, "time": "2021-10-29T13:26:27+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "ramsey/collection", "version": "2.1.1", "source": {"type": "git", "url": "https://github.com/ramsey/collection.git", "reference": "344572933ad0181accbf4ba763e85a0306a8c5e2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/collection/zipball/344572933ad0181accbf4ba763e85a0306a8c5e2", "reference": "344572933ad0181accbf4ba763e85a0306a8c5e2", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"captainhook/plugin-composer": "^5.3", "ergebnis/composer-normalize": "^2.45", "fakerphp/faker": "^1.24", "hamcrest/hamcrest-php": "^2.0", "jangregor/phpstan-prophecy": "^2.1", "mockery/mockery": "^1.6", "php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.4", "phpspec/prophecy-phpunit": "^2.3", "phpstan/extension-installer": "^1.4", "phpstan/phpstan": "^2.1", "phpstan/phpstan-mockery": "^2.0", "phpstan/phpstan-phpunit": "^2.0", "phpunit/phpunit": "^10.5", "ramsey/coding-standard": "^2.3", "ramsey/conventional-commits": "^1.6", "roave/security-advisories": "dev-latest"}, "type": "library", "extra": {"captainhook": {"force-install": true}, "ramsey/conventional-commits": {"configFile": "conventional-commits.json"}}, "autoload": {"psr-4": {"Ramsey\\Collection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://benramsey.com"}], "description": "A PHP library for representing and manipulating collections.", "keywords": ["array", "collection", "hash", "map", "queue", "set"], "support": {"issues": "https://github.com/ramsey/collection/issues", "source": "https://github.com/ramsey/collection/tree/2.1.1"}, "time": "2025-03-22T05:38:12+00:00"}, {"name": "ramsey/uuid", "version": "4.7.6", "source": {"type": "git", "url": "https://github.com/ramsey/uuid.git", "reference": "91039bc1faa45ba123c4328958e620d382ec7088"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/uuid/zipball/91039bc1faa45ba123c4328958e620d382ec7088", "reference": "91039bc1faa45ba123c4328958e620d382ec7088", "shasum": ""}, "require": {"brick/math": "^0.8.8 || ^0.9 || ^0.10 || ^0.11 || ^0.12", "ext-json": "*", "php": "^8.0", "ramsey/collection": "^1.2 || ^2.0"}, "replace": {"rhumsaa/uuid": "self.version"}, "require-dev": {"captainhook/captainhook": "^5.10", "captainhook/plugin-composer": "^5.3", "dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "doctrine/annotations": "^1.8", "ergebnis/composer-normalize": "^2.15", "mockery/mockery": "^1.3", "paragonie/random-lib": "^2", "php-mock/php-mock": "^2.2", "php-mock/php-mock-mockery": "^1.3", "php-parallel-lint/php-parallel-lint": "^1.1", "phpbench/phpbench": "^1.0", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-mockery": "^1.1", "phpstan/phpstan-phpunit": "^1.1", "phpunit/phpunit": "^8.5 || ^9", "ramsey/composer-repl": "^1.4", "slevomat/coding-standard": "^8.4", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "^4.9"}, "suggest": {"ext-bcmath": "Enables faster math with arbitrary-precision integers using BCMath.", "ext-gmp": "Enables faster math with arbitrary-precision integers using GMP.", "ext-uuid": "Enables the use of PeclUuidTimeGenerator and PeclUuidRandomGenerator.", "paragonie/random-lib": "Provides RandomLib for use with the RandomLibAdapter", "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type."}, "type": "library", "extra": {"captainhook": {"force-install": true}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Ramsey\\Uuid\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A PHP library for generating and working with universally unique identifiers (UUIDs).", "keywords": ["guid", "identifier", "uuid"], "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid/tree/4.7.6"}, "funding": [{"url": "https://github.com/ramsey", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ramsey/uuid", "type": "tidelift"}], "time": "2024-04-27T21:32:50+00:00"}, {"name": "rize/uri-template", "version": "0.4.0", "source": {"type": "git", "url": "https://github.com/rize/UriTemplate.git", "reference": "56f374a9a42c7c3998f8b55b6b21b224de90c58b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/rize/UriTemplate/zipball/56f374a9a42c7c3998f8b55b6b21b224de90c58b", "reference": "56f374a9a42c7c3998f8b55b6b21b224de90c58b", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.63", "phpstan/phpstan": "^1.12", "phpunit/phpunit": "~10.0"}, "type": "library", "autoload": {"psr-4": {"Rize\\": "src/Rize"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>ut <PERSON>", "homepage": "http://twitter.com/rezigned"}], "description": "PHP URI Template (RFC 6570) supports both expansion & extraction", "keywords": ["RFC 6570", "template", "uri"], "support": {"issues": "https://github.com/rize/UriTemplate/issues", "source": "https://github.com/rize/UriTemplate/tree/0.4.0"}, "funding": [{"url": "https://www.paypal.me/rezigned", "type": "custom"}, {"url": "https://github.com/rezigned", "type": "github"}, {"url": "https://opencollective.com/rize-uri-template", "type": "open_collective"}], "time": "2024-11-27T12:13:42+00:00"}, {"name": "rob<PERSON><PERSON>/twofactor<PERSON>h", "version": "1.8.2", "source": {"type": "git", "url": "https://github.com/RobThree/TwoFactorAuth.git", "reference": "65681de5a324eae05140ac58b08648a60212afc0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/RobThree/TwoFactorAuth/zipball/65681de5a324eae05140ac58b08648a60212afc0", "reference": "65681de5a324eae05140ac58b08648a60212afc0", "shasum": ""}, "require": {"php": ">=5.6.0"}, "require-dev": {"php-parallel-lint/php-parallel-lint": "^1.2", "phpunit/phpunit": "@stable"}, "suggest": {"bacon/bacon-qr-code": "Needed for BaconQrCodeProvider provider", "endroid/qr-code": "Needed for EndroidQrCodeProvider"}, "type": "library", "autoload": {"psr-4": {"RobThree\\Auth\\": "lib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://robiii.me", "role": "Developer"}], "description": "Two Factor Authentication", "homepage": "https://github.com/RobThree/TwoFactorAuth", "keywords": ["Authentication", "MFA", "Multi Factor Authentication", "Two Factor Authentication", "authenticator", "authy", "php", "tfa"], "support": {"issues": "https://github.com/RobThree/TwoFactorAuth/issues", "source": "https://github.com/RobThree/TwoFactorAuth"}, "funding": [{"url": "https://paypal.me/robiii", "type": "custom"}, {"url": "https://github.com/RobThree", "type": "github"}], "time": "2022-03-22T16:11:07+00:00"}, {"name": "sabberworm/php-css-parser", "version": "v8.8.0", "source": {"type": "git", "url": "https://github.com/MyIntervals/PHP-CSS-Parser.git", "reference": "3de493bdddfd1f051249af725c7e0d2c38fed740"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MyIntervals/PHP-CSS-Parser/zipball/3de493bdddfd1f051249af725c7e0d2c38fed740", "reference": "3de493bdddfd1f051249af725c7e0d2c38fed740", "shasum": ""}, "require": {"ext-iconv": "*", "php": "^5.6.20 || ^7.0.0 || ~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "require-dev": {"phpunit/phpunit": "5.7.27 || 6.5.14 || 7.5.20 || 8.5.41"}, "suggest": {"ext-mbstring": "for parsing UTF-8 CSS"}, "type": "library", "extra": {"branch-alias": {"dev-main": "9.0.x-dev"}}, "autoload": {"psr-4": {"Sabberworm\\CSS\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Parser for CSS Files written in PHP", "homepage": "https://www.sabberworm.com/blog/2010/6/10/php-css-parser", "keywords": ["css", "parser", "stylesheet"], "support": {"issues": "https://github.com/MyIntervals/PHP-CSS-Parser/issues", "source": "https://github.com/MyIntervals/PHP-CSS-Parser/tree/v8.8.0"}, "time": "2025-03-23T17:59:05+00:00"}, {"name": "smi2/phpclickhouse", "version": "1.6.0", "source": {"type": "git", "url": "https://github.com/smi2/phpClickHouse.git", "reference": "f79dfb798df96185beff90891efda997b01eb51b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/smi2/phpClickHouse/zipball/f79dfb798df96185beff90891efda997b01eb51b", "reference": "f79dfb798df96185beff90891efda997b01eb51b", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "php": "^8.0"}, "require-dev": {"doctrine/coding-standard": "^8.2", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^9.5", "sebastian/comparator": "^4.0"}, "type": "library", "autoload": {"psr-4": {"ClickHouseDB\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/isublimity"}], "description": "PHP ClickHouse Client", "homepage": "https://github.com/smi2/phpClickHouse", "keywords": ["clickhouse", "client", "curl", "driver", "http", "http client", "php"], "support": {"issues": "https://github.com/smi2/phpClickHouse/issues", "source": "https://github.com/smi2/phpClickHouse/tree/1.6.0"}, "time": "2025-01-15T07:04:59+00:00"}, {"name": "sparkpost/sparkpost", "version": "2.3.0", "source": {"type": "git", "url": "https://github.com/SparkPost/php-sparkpost.git", "reference": "2c7f60d27afbbbea05c72cb33ef9094f9cfceb88"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/SparkPost/php-sparkpost/zipball/2c7f60d27afbbbea05c72cb33ef9094f9cfceb88", "reference": "2c7f60d27afbbbea05c72cb33ef9094f9cfceb88", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "php-http/client-implementation": "^1.0", "php-http/discovery": "^1.0", "php-http/httplug": "^1.0 || ^2.0", "php-http/message": "^1.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.18", "mockery/mockery": "^1.3", "nyholm/nsa": "^1.0", "php-coveralls/php-coveralls": "^2.4", "php-http/guzzle6-adapter": "^1.0", "phpunit/phpunit": "^8.0 || ^9.0"}, "type": "library", "autoload": {"psr-4": {"SparkPost\\": "lib/SparkPost"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "SparkPost"}], "description": "Client library for interfacing with the SparkPost API.", "support": {"issues": "https://github.com/SparkPost/php-sparkpost/issues", "source": "https://github.com/SparkPost/php-sparkpost/tree/2.3.0"}, "time": "2021-03-17T13:59:30+00:00"}, {"name": "spatie/temporary-directory", "version": "2.3.0", "source": {"type": "git", "url": "https://github.com/spatie/temporary-directory.git", "reference": "580eddfe9a0a41a902cac6eeb8f066b42e65a32b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/temporary-directory/zipball/580eddfe9a0a41a902cac6eeb8f066b42e65a32b", "reference": "580eddfe9a0a41a902cac6eeb8f066b42e65a32b", "shasum": ""}, "require": {"php": "^8.0"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "type": "library", "autoload": {"psr-4": {"Spatie\\TemporaryDirectory\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}], "description": "Easily create, use and destroy temporary directories", "homepage": "https://github.com/spatie/temporary-directory", "keywords": ["php", "spatie", "temporary-directory"], "support": {"issues": "https://github.com/spatie/temporary-directory/issues", "source": "https://github.com/spatie/temporary-directory/tree/2.3.0"}, "funding": [{"url": "https://spatie.be/open-source/support-us", "type": "custom"}, {"url": "https://github.com/spatie", "type": "github"}], "time": "2025-01-13T13:04:43+00:00"}, {"name": "stichoza/google-translate-php", "version": "v5.2.0", "source": {"type": "git", "url": "https://github.com/Stichoza/google-translate-php.git", "reference": "9429773d991c98f68a25bec40d20f590ea3312a0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Stichoza/google-translate-php/zipball/9429773d991c98f68a25bec40d20f590ea3312a0", "reference": "9429773d991c98f68a25bec40d20f590ea3312a0", "shasum": ""}, "require": {"ext-dom": "*", "ext-json": "*", "ext-mbstring": "*", "guzzlehttp/guzzle": "^7.0", "php": "^8.0"}, "require-dev": {"phpunit/phpunit": "^9.5.10"}, "type": "library", "autoload": {"psr-4": {"Stichoza\\GoogleTranslate\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Free Google Translate API PHP Package", "homepage": "https://github.com/Stichoza/google-translate-php", "keywords": ["google", "php", "translate", "translating", "translator"], "support": {"issues": "https://github.com/Stichoza/google-translate-php/issues", "source": "https://github.com/Stichoza/google-translate-php/tree/v5.2.0"}, "funding": [{"url": "https://btc.com/******************************************", "type": "custom"}, {"url": "https://www.paypal.me/stichoza", "type": "custom"}, {"url": "https://ko-fi.com/stichoza", "type": "ko_fi"}, {"url": "https://liberapay.com/stichoza", "type": "liberapay"}, {"url": "https://opencollective.com/stichoza", "type": "open_collective"}, {"url": "https://www.patreon.com/stichoza", "type": "patreon"}], "time": "2024-08-05T19:11:36+00:00"}, {"name": "stolt/json-lines", "version": "v4.1.0", "source": {"type": "git", "url": "https://github.com/raphaelstolt/json-lines.git", "reference": "361a2f1dbfbeda3d63fa00acf6c0e4513f908870"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/raphaelstolt/json-lines/zipball/361a2f1dbfbeda3d63fa00acf6c0e4513f908870", "reference": "361a2f1dbfbeda3d63fa00acf6c0e4513f908870", "shasum": ""}, "require": {"php": ">=8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.0", "phpunit/phpunit": "8.*", "stolt/lean-package-validator": "^3.0"}, "suggest": {"ext-zlib": "Allow gzip compression of JSON Lines when writing to a file"}, "type": "library", "autoload": {"psr-4": {"Rs\\JsonLines\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Library for the JSON Lines text file format.", "keywords": ["json", "jsonlines"], "support": {"issues": "https://github.com/raphaelstolt/json-lines/issues", "source": "https://github.com/raphaelstolt/json-lines/tree/v4.1.0"}, "time": "2024-12-09T17:11:04+00:00"}, {"name": "symfony/cache", "version": "v7.3.1", "source": {"type": "git", "url": "https://github.com/symfony/cache.git", "reference": "a7c6caa9d6113cebfb3020b427bcb021ebfdfc9e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache/zipball/a7c6caa9d6113cebfb3020b427bcb021ebfdfc9e", "reference": "a7c6caa9d6113cebfb3020b427bcb021ebfdfc9e", "shasum": ""}, "require": {"php": ">=8.2", "psr/cache": "^2.0|^3.0", "psr/log": "^1.1|^2|^3", "symfony/cache-contracts": "^3.6", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/service-contracts": "^2.5|^3", "symfony/var-exporter": "^6.4|^7.0"}, "conflict": {"doctrine/dbal": "<3.6", "symfony/dependency-injection": "<6.4", "symfony/http-kernel": "<6.4", "symfony/var-dumper": "<6.4"}, "provide": {"psr/cache-implementation": "2.0|3.0", "psr/simple-cache-implementation": "1.0|2.0|3.0", "symfony/cache-implementation": "1.1|2.0|3.0"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/dbal": "^3.6|^4", "predis/predis": "^1.1|^2.0", "psr/simple-cache": "^1.0|^2.0|^3.0", "symfony/clock": "^6.4|^7.0", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/filesystem": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Cache\\": ""}, "classmap": ["Traits/ValueWrapper.php"], "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides extended PSR-6, PSR-16 (and tags) implementations", "homepage": "https://symfony.com", "keywords": ["caching", "psr6"], "support": {"source": "https://github.com/symfony/cache/tree/v7.3.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-27T19:55:54+00:00"}, {"name": "symfony/cache-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/cache-contracts.git", "reference": "5d68a57d66910405e5c0b63d6f0af941e66fc868"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache-contracts/zipball/5d68a57d66910405e5c0b63d6f0af941e66fc868", "reference": "5d68a57d66910405e5c0b63d6f0af941e66fc868", "shasum": ""}, "require": {"php": ">=8.1", "psr/cache": "^3.0"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Cache\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to caching", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/cache-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-13T15:25:07+00:00"}, {"name": "symfony/clock", "version": "v7.2.0", "source": {"type": "git", "url": "https://github.com/symfony/clock.git", "reference": "b81435fbd6648ea425d1ee96a2d8e68f4ceacd24"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/clock/zipball/b81435fbd6648ea425d1ee96a2d8e68f4ceacd24", "reference": "b81435fbd6648ea425d1ee96a2d8e68f4ceacd24", "shasum": ""}, "require": {"php": ">=8.2", "psr/clock": "^1.0", "symfony/polyfill-php83": "^1.28"}, "provide": {"psr/clock-implementation": "1.0"}, "type": "library", "autoload": {"files": ["Resources/now.php"], "psr-4": {"Symfony\\Component\\Clock\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Decouples applications from the system clock", "homepage": "https://symfony.com", "keywords": ["clock", "psr20", "time"], "support": {"source": "https://github.com/symfony/clock/tree/v7.2.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/console", "version": "v7.2.6", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "0e2e3f38c192e93e622e41ec37f4ca70cfedf218"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/0e2e3f38c192e93e622e41ec37f4ca70cfedf218", "reference": "0e2e3f38c192e93e622e41ec37f4ca70cfedf218", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^2.5|^3", "symfony/string": "^6.4|^7.0"}, "conflict": {"symfony/dependency-injection": "<6.4", "symfony/dotenv": "<6.4", "symfony/event-dispatcher": "<6.4", "symfony/lock": "<6.4", "symfony/process": "<6.4"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/lock": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/stopwatch": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command-line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v7.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-07T19:09:28+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "63afe740e99a13ba87ec199bb07bbdee937a5b62"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/63afe740e99a13ba87ec199bb07bbdee937a5b62", "reference": "63afe740e99a13ba87ec199bb07bbdee937a5b62", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/event-dispatcher", "version": "v7.2.0", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "910c5db85a5356d0fea57680defec4e99eb9c8c1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/910c5db85a5356d0fea57680defec4e99eb9c8c1", "reference": "910c5db85a5356d0fea57680defec4e99eb9c8c1", "shasum": ""}, "require": {"php": ">=8.2", "symfony/event-dispatcher-contracts": "^2.5|^3"}, "conflict": {"symfony/dependency-injection": "<6.4", "symfony/service-contracts": "<2.5"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/error-handler": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/stopwatch": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v7.2.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v3.5.1", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "7642f5e970b672283b7823222ae8ef8bbc160b9f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/7642f5e970b672283b7823222ae8ef8bbc160b9f", "reference": "7642f5e970b672283b7823222ae8ef8bbc160b9f", "shasum": ""}, "require": {"php": ">=8.1", "psr/event-dispatcher": "^1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/lock", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/lock.git", "reference": "5bef45fb874b0454a616ac8091447a7982a438cf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/lock/zipball/5bef45fb874b0454a616ac8091447a7982a438cf", "reference": "5bef45fb874b0454a616ac8091447a7982a438cf", "shasum": ""}, "require": {"php": ">=8.2", "psr/log": "^1|^2|^3"}, "conflict": {"doctrine/dbal": "<3.6", "symfony/cache": "<6.4"}, "require-dev": {"doctrine/dbal": "^3.6|^4", "predis/predis": "^1.1|^2.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Lock\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Creates and manages locks, a mechanism to provide exclusive access to a shared resource", "homepage": "https://symfony.com", "keywords": ["cas", "flock", "locking", "mutex", "redlock", "semaphore"], "support": {"source": "https://github.com/symfony/lock/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-20T20:19:01+00:00"}, {"name": "symfony/options-resolver", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/options-resolver.git", "reference": "afb9a8038025e5dbc657378bfab9198d75f10fca"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/options-resolver/zipball/afb9a8038025e5dbc657378bfab9198d75f10fca", "reference": "afb9a8038025e5dbc657378bfab9198d75f10fca", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an improved replacement for the array_replace PHP function", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "support": {"source": "https://github.com/symfony/options-resolver/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-04T13:12:05+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/a3cc8b044a6ea513310cbd48ef7333b384945638", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "3833d7255cc303546435cb650316bff708a1c75c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/3833d7255cc303546435cb650316bff708a1c75c", "reference": "3833d7255cc303546435cb650316bff708a1c75c", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/6d857f4d76bd4b343eac26d6b539585d2bc56493", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493", "shasum": ""}, "require": {"ext-iconv": "*", "php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-23T08:48:59+00:00"}, {"name": "symfony/polyfill-php83", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php83.git", "reference": "2fb86d65e2d424369ad2905e83b236a8805ba491"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php83/zipball/2fb86d65e2d424369ad2905e83b236a8805ba491", "reference": "2fb86d65e2d424369ad2905e83b236a8805ba491", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php83\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php83/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/process", "version": "v7.2.5", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "87b7c93e57df9d8e39a093d32587702380ff045d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/87b7c93e57df9d8e39a093d32587702380ff045d", "reference": "87b7c93e57df9d8e39a093d32587702380ff045d", "shasum": ""}, "require": {"php": ">=8.2"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v7.2.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-13T12:21:46+00:00"}, {"name": "symfony/rate-limiter", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/rate-limiter.git", "reference": "78a3dbd84386cb0aa7f375e3469294e7048e5a1a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/rate-limiter/zipball/78a3dbd84386cb0aa7f375e3469294e7048e5a1a", "reference": "78a3dbd84386cb0aa7f375e3469294e7048e5a1a", "shasum": ""}, "require": {"php": ">=8.2", "symfony/options-resolver": "^7.3"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0", "symfony/lock": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\RateLimiter\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a Token Bucket implementation to rate limit input and output in your application", "homepage": "https://symfony.com", "keywords": ["limiter", "rate-limiter"], "support": {"source": "https://github.com/symfony/rate-limiter/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-12T17:05:47+00:00"}, {"name": "symfony/service-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "f021b05a130d35510bd6b25fe9053c2a8a15d5d4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/f021b05a130d35510bd6b25fe9053c2a8a15d5d4", "reference": "f021b05a130d35510bd6b25fe9053c2a8a15d5d4", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-25T09:37:31+00:00"}, {"name": "symfony/stopwatch", "version": "v7.2.4", "source": {"type": "git", "url": "https://github.com/symfony/stopwatch.git", "reference": "5a49289e2b308214c8b9c2fda4ea454d8b8ad7cd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/stopwatch/zipball/5a49289e2b308214c8b9c2fda4ea454d8b8ad7cd", "reference": "5a49289e2b308214c8b9c2fda4ea454d8b8ad7cd", "shasum": ""}, "require": {"php": ">=8.2", "symfony/service-contracts": "^2.5|^3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Stopwatch\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a way to profile code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/stopwatch/tree/v7.2.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-02-24T10:49:57+00:00"}, {"name": "symfony/string", "version": "v7.2.6", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "a214fe7d62bd4df2a76447c67c6b26e1d5e74931"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/a214fe7d62bd4df2a76447c67c6b26e1d5e74931", "reference": "a214fe7d62bd4df2a76447c67c6b26e1d5e74931", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/translation-contracts": "<2.5"}, "require-dev": {"symfony/emoji": "^7.1", "symfony/error-handler": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/intl": "^6.4|^7.0", "symfony/translation-contracts": "^2.5|^3.0", "symfony/var-exporter": "^6.4|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v7.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-20T20:18:16+00:00"}, {"name": "symfony/translation", "version": "v7.2.6", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "e7fd8e2a4239b79a0fd9fb1fef3e0e7f969c6dc6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/e7fd8e2a4239b79a0fd9fb1fef3e0e7f969c6dc6", "reference": "e7fd8e2a4239b79a0fd9fb1fef3e0e7f969c6dc6", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^2.5|^3.0"}, "conflict": {"symfony/config": "<6.4", "symfony/console": "<6.4", "symfony/dependency-injection": "<6.4", "symfony/http-client-contracts": "<2.5", "symfony/http-kernel": "<6.4", "symfony/service-contracts": "<2.5", "symfony/twig-bundle": "<6.4", "symfony/yaml": "<6.4"}, "provide": {"symfony/translation-implementation": "2.3|3.0"}, "require-dev": {"nikic/php-parser": "^4.18|^5.0", "psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/finder": "^6.4|^7.0", "symfony/http-client-contracts": "^2.5|^3.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/intl": "^6.4|^7.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/routing": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/yaml": "^6.4|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to internationalize your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/translation/tree/v7.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-07T19:09:28+00:00"}, {"name": "symfony/translation-contracts", "version": "v3.5.1", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "4667ff3bd513750603a09c8dedbea942487fb07c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/4667ff3bd513750603a09c8dedbea942487fb07c", "reference": "4667ff3bd513750603a09c8dedbea942487fb07c", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/var-exporter", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/var-exporter.git", "reference": "c9a1168891b5aaadfd6332ef44393330b3498c4c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-exporter/zipball/c9a1168891b5aaadfd6332ef44393330b3498c4c", "reference": "c9a1168891b5aaadfd6332ef44393330b3498c4c", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3"}, "require-dev": {"symfony/property-access": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\VarExporter\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows exporting any serializable PHP data structure to plain PHP code", "homepage": "https://symfony.com", "keywords": ["clone", "construct", "export", "hydrate", "instantiate", "lazy-loading", "proxy", "serialize"], "support": {"source": "https://github.com/symfony/var-exporter/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-15T09:04:05+00:00"}, {"name": "webimpress/safe-writer", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/webimpress/safe-writer.git", "reference": "9d37cc8bee20f7cb2f58f6e23e05097eab5072e6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webimpress/safe-writer/zipball/9d37cc8bee20f7cb2f58f6e23e05097eab5072e6", "reference": "9d37cc8bee20f7cb2f58f6e23e05097eab5072e6", "shasum": ""}, "require": {"php": "^7.3 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^9.5.4", "vimeo/psalm": "^4.7", "webimpress/coding-standard": "^1.2.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2.x-dev", "dev-develop": "2.3.x-dev", "dev-release-1.0": "1.0.x-dev"}}, "autoload": {"psr-4": {"Webimpress\\SafeWriter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "description": "Tool to write files safely, to avoid race conditions", "keywords": ["concurrent write", "file writer", "race condition", "safe writer", "webimpress"], "support": {"issues": "https://github.com/webimpress/safe-writer/issues", "source": "https://github.com/webimpress/safe-writer/tree/2.2.0"}, "funding": [{"url": "https://github.com/michalbundyra", "type": "github"}], "time": "2021-04-19T16:34:45+00:00"}, {"name": "webmozart/assert", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991", "shasum": ""}, "require": {"ext-ctype": "*", "php": "^7.2 || ^8.0"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.11.0"}, "time": "2022-06-03T18:03:27+00:00"}], "packages-dev": [{"name": "fakerphp/faker", "version": "v1.24.1", "source": {"type": "git", "url": "https://github.com/FakerPHP/Faker.git", "reference": "e0ee18eb1e6dc3cda3ce9fd97e5a0689a88a64b5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FakerPHP/Faker/zipball/e0ee18eb1e6dc3cda3ce9fd97e5a0689a88a64b5", "reference": "e0ee18eb1e6dc3cda3ce9fd97e5a0689a88a64b5", "shasum": ""}, "require": {"php": "^7.4 || ^8.0", "psr/container": "^1.0 || ^2.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "conflict": {"fzaninotto/faker": "*"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4.1", "doctrine/persistence": "^1.3 || ^2.0", "ext-intl": "*", "phpunit/phpunit": "^9.5.26", "symfony/phpunit-bridge": "^5.4.16"}, "suggest": {"doctrine/orm": "Required to use Faker\\ORM\\Doctrine", "ext-curl": "Required by Faker\\Provider\\Image to download images.", "ext-dom": "Required by Faker\\Provider\\HtmlLorem for generating random HTML.", "ext-iconv": "Required by Faker\\Provider\\ru_RU\\Text::realText() for generating real Russian text.", "ext-mbstring": "Required for multibyte Unicode string functionality."}, "type": "library", "autoload": {"psr-4": {"Faker\\": "src/Faker/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Faker is a PHP library that generates fake data for you.", "keywords": ["data", "faker", "fixtures"], "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.24.1"}, "time": "2024-11-21T13:46:39+00:00"}, {"name": "laminas/laminas-test", "version": "4.11.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-test.git", "reference": "9049af65fdcf07aa83ea785a62b66439dbe465ff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-test/zipball/9049af65fdcf07aa83ea785a62b66439dbe465ff", "reference": "9049af65fdcf07aa83ea785a62b66439dbe465ff", "shasum": ""}, "require": {"laminas/laminas-eventmanager": "^3.0", "laminas/laminas-http": "^2.15.0", "laminas/laminas-mvc": "^3.3.0", "laminas/laminas-servicemanager": "^3.0.3", "laminas/laminas-uri": "^2.5", "laminas/laminas-view": "^2.13.1", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0", "phpunit/phpunit": "^10.4", "symfony/css-selector": "^6.0 || ^7.0", "symfony/dom-crawler": "^6.0 || ^7.0"}, "conflict": {"zendframework/zend-test": "*"}, "require-dev": {"laminas/laminas-coding-standard": "^3.0", "laminas/laminas-i18n": "^2.21", "laminas/laminas-modulemanager": "^2.14.0", "laminas/laminas-mvc-plugin-flashmessenger": "^1.9.0", "laminas/laminas-serializer": "^2.14.0", "laminas/laminas-session": "^2.16", "laminas/laminas-stdlib": "^3.16.1", "laminas/laminas-validator": "^2.28", "mikey179/vfsstream": "^1.6.11", "psalm/plugin-phpunit": "^0.19.0", "vimeo/psalm": "^5.26"}, "type": "library", "autoload": {"psr-4": {"Laminas\\Test\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Tools to facilitate integration testing of laminas-mvc applications", "homepage": "https://laminas.dev", "keywords": ["laminas", "test"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-test/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-test/issues", "rss": "https://github.com/laminas/laminas-test/releases.atom", "source": "https://github.com/laminas/laminas-test"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2025-01-08T13:39:35+00:00"}, {"name": "larapack/dd", "version": "1.1", "source": {"type": "git", "url": "https://github.com/larapack/dd.git", "reference": "561b5111a13d0094b59b5c81b1572489485fb948"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/larapack/dd/zipball/561b5111a13d0094b59b5c81b1572489485fb948", "reference": "561b5111a13d0094b59b5c81b1572489485fb948", "shasum": ""}, "require": {"symfony/var-dumper": "*"}, "type": "package", "autoload": {"files": ["src/helper.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "`dd` is a helper method in Laravel. This package will add the `dd` to your application.", "support": {"issues": "https://github.com/larapack/dd/issues", "source": "https://github.com/larapack/dd/tree/master"}, "time": "2016-12-15T09:34:34+00:00"}, {"name": "phar-io/manifest", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/phar-io/manifest.git", "reference": "54750ef60c58e43759730615a392c31c80e23176"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/manifest/zipball/54750ef60c58e43759730615a392c31c80e23176", "reference": "54750ef60c58e43759730615a392c31c80e23176", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-phar": "*", "ext-xmlwriter": "*", "phar-io/version": "^3.0.1", "php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "support": {"issues": "https://github.com/phar-io/manifest/issues", "source": "https://github.com/phar-io/manifest/tree/2.0.4"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2024-03-03T12:33:53+00:00"}, {"name": "phar-io/version", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/phar-io/version.git", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/version/zipball/4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "support": {"issues": "https://github.com/phar-io/version/issues", "source": "https://github.com/phar-io/version/tree/3.2.1"}, "time": "2022-02-21T01:04:05+00:00"}, {"name": "phpunit/php-code-coverage", "version": "10.1.16", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "7e308268858ed6baedc8704a304727d20bc07c77"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/7e308268858ed6baedc8704a304727d20bc07c77", "reference": "7e308268858ed6baedc8704a304727d20bc07c77", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^4.19.1 || ^5.1.0", "php": ">=8.1", "phpunit/php-file-iterator": "^4.1.0", "phpunit/php-text-template": "^3.0.1", "sebastian/code-unit-reverse-lookup": "^3.0.0", "sebastian/complexity": "^3.2.0", "sebastian/environment": "^6.1.0", "sebastian/lines-of-code": "^2.0.2", "sebastian/version": "^4.0.1", "theseer/tokenizer": "^1.2.3"}, "require-dev": {"phpunit/phpunit": "^10.1"}, "suggest": {"ext-pcov": "PHP extension that provides line coverage", "ext-xdebug": "PHP extension that provides line coverage as well as branch and path coverage"}, "type": "library", "extra": {"branch-alias": {"dev-main": "10.1.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/10.1.16"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-08-22T04:31:57+00:00"}, {"name": "phpunit/php-file-iterator", "version": "4.1.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "a95037b6d9e608ba092da1b23931e537cadc3c3c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/a95037b6d9e608ba092da1b23931e537cadc3c3c", "reference": "a95037b6d9e608ba092da1b23931e537cadc3c3c", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/4.1.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-08-31T06:24:48+00:00"}, {"name": "phpunit/php-invoker", "version": "4.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-invoker.git", "reference": "f5e568ba02fa5ba0ddd0f618391d5a9ea50b06d7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-invoker/zipball/f5e568ba02fa5ba0ddd0f618391d5a9ea50b06d7", "reference": "f5e568ba02fa5ba0ddd0f618391d5a9ea50b06d7", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"ext-pcntl": "*", "phpunit/phpunit": "^10.0"}, "suggest": {"ext-pcntl": "*"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Invoke callables with a timeout", "homepage": "https://github.com/sebastian<PERSON>mann/php-invoker/", "keywords": ["process"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-invoker/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/tree/4.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:56:09+00:00"}, {"name": "phpunit/php-text-template", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "0c7b06ff49e3d5072f057eb1fa59258bf287a748"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-text-template/zipball/0c7b06ff49e3d5072f057eb1fa59258bf287a748", "reference": "0c7b06ff49e3d5072f057eb1fa59258bf287a748", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/3.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-08-31T14:07:24+00:00"}, {"name": "phpunit/php-timer", "version": "6.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "e2a2d67966e740530f4a3343fe2e030ffdc1161d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-timer/zipball/e2a2d67966e740530f4a3343fe2e030ffdc1161d", "reference": "e2a2d67966e740530f4a3343fe2e030ffdc1161d", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-timer/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-timer/tree/6.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:57:52+00:00"}, {"name": "phpunit/phpunit", "version": "10.5.46", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "8080be387a5be380dda48c6f41cee4a13aadab3d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/phpunit/zipball/8080be387a5be380dda48c6f41cee4a13aadab3d", "reference": "8080be387a5be380dda48c6f41cee4a13aadab3d", "shasum": ""}, "require": {"ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "ext-xmlwriter": "*", "myclabs/deep-copy": "^1.13.1", "phar-io/manifest": "^2.0.4", "phar-io/version": "^3.2.1", "php": ">=8.1", "phpunit/php-code-coverage": "^10.1.16", "phpunit/php-file-iterator": "^4.1.0", "phpunit/php-invoker": "^4.0.0", "phpunit/php-text-template": "^3.0.1", "phpunit/php-timer": "^6.0.0", "sebastian/cli-parser": "^2.0.1", "sebastian/code-unit": "^2.0.0", "sebastian/comparator": "^5.0.3", "sebastian/diff": "^5.1.1", "sebastian/environment": "^6.1.0", "sebastian/exporter": "^5.1.2", "sebastian/global-state": "^6.0.2", "sebastian/object-enumerator": "^5.0.0", "sebastian/recursion-context": "^5.0.0", "sebastian/type": "^4.0.0", "sebastian/version": "^4.0.1"}, "suggest": {"ext-soap": "To be able to generate mocks based on WSDL files"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-main": "10.5-dev"}}, "autoload": {"files": ["src/Framework/Assert/Functions.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/phpunit/issues", "security": "https://github.com/sebastian<PERSON>mann/phpunit/security/policy", "source": "https://github.com/sebastian<PERSON>mann/phpunit/tree/10.5.46"}, "funding": [{"url": "https://phpunit.de/sponsors.html", "type": "custom"}, {"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://liberapay.com/sebastian<PERSON>mann", "type": "liberapay"}, {"url": "https://thanks.dev/u/gh/sebas<PERSON><PERSON><PERSON>", "type": "thanks_dev"}, {"url": "https://tidelift.com/funding/github/packagist/phpunit/phpunit", "type": "tidelift"}], "time": "2025-05-02T06:46:24+00:00"}, {"name": "sebastian/cli-parser", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/cli-parser.git", "reference": "c34583b87e7b7a8055bf6c450c2c77ce32a24084"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/cli-parser/zipball/c34583b87e7b7a8055bf6c450c2c77ce32a24084", "reference": "c34583b87e7b7a8055bf6c450c2c77ce32a24084", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for parsing CLI options", "homepage": "https://github.com/sebastian<PERSON>mann/cli-parser", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/tree/2.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T07:12:49+00:00"}, {"name": "sebastian/code-unit", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/code-unit.git", "reference": "a81fee9eef0b7a76af11d121767abc44c104e503"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/code-unit/zipball/a81fee9eef0b7a76af11d121767abc44c104e503", "reference": "a81fee9eef0b7a76af11d121767abc44c104e503", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/code-unit", "support": {"issues": "https://github.com/sebastian<PERSON>mann/code-unit/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit/tree/2.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:58:43+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "5e3a687f7d8ae33fb362c5c0743794bbb2420a1d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/5e3a687f7d8ae33fb362c5c0743794bbb2420a1d", "reference": "5e3a687f7d8ae33fb362c5c0743794bbb2420a1d", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/tree/3.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:59:15+00:00"}, {"name": "sebastian/comparator", "version": "5.0.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "a18251eb0b7a2dcd2f7aa3d6078b18545ef0558e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/a18251eb0b7a2dcd2f7aa3d6078b18545ef0558e", "reference": "a18251eb0b7a2dcd2f7aa3d6078b18545ef0558e", "shasum": ""}, "require": {"ext-dom": "*", "ext-mbstring": "*", "php": ">=8.1", "sebastian/diff": "^5.0", "sebastian/exporter": "^5.0"}, "require-dev": {"phpunit/phpunit": "^10.5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "security": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator/security/policy", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/5.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-10-18T14:56:07+00:00"}, {"name": "sebastian/complexity", "version": "3.2.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/complexity.git", "reference": "68ff824baeae169ec9f2137158ee529584553799"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/complexity/zipball/68ff824baeae169ec9f2137158ee529584553799", "reference": "68ff824baeae169ec9f2137158ee529584553799", "shasum": ""}, "require": {"nikic/php-parser": "^4.18 || ^5.0", "php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for calculating the complexity of PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/complexity", "support": {"issues": "https://github.com/sebastian<PERSON>mann/complexity/issues", "security": "https://github.com/sebastian<PERSON>mann/complexity/security/policy", "source": "https://github.com/sebastian<PERSON>mann/complexity/tree/3.2.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-12-21T08:37:17+00:00"}, {"name": "sebastian/diff", "version": "5.1.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "c41e007b4b62af48218231d6c2275e4c9b975b2e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/diff/zipball/c41e007b4b62af48218231d6c2275e4c9b975b2e", "reference": "c41e007b4b62af48218231d6c2275e4c9b975b2e", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0", "symfony/process": "^6.4"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/diff/issues", "security": "https://github.com/sebastian<PERSON>mann/diff/security/policy", "source": "https://github.com/sebastian<PERSON>mann/diff/tree/5.1.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T07:15:17+00:00"}, {"name": "sebastian/environment", "version": "6.1.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "8074dbcd93529b357029f5cc5058fd3e43666984"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastianbergmann/environment/zipball/8074dbcd93529b357029f5cc5058fd3e43666984", "reference": "8074dbcd93529b357029f5cc5058fd3e43666984", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "suggest": {"ext-posix": "*"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "https://github.com/sebastian<PERSON>mann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/environment/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/environment/security/policy", "source": "https://github.com/sebastian<PERSON>mann/environment/tree/6.1.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-23T08:47:14+00:00"}, {"name": "sebastian/exporter", "version": "5.1.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "955288482d97c19a372d3f31006ab3f37da47adf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/955288482d97c19a372d3f31006ab3f37da47adf", "reference": "955288482d97c19a372d3f31006ab3f37da47adf", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=8.1", "sebastian/recursion-context": "^5.0"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "https://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/exporter/issues", "security": "https://github.com/sebastian<PERSON>mann/exporter/security/policy", "source": "https://github.com/sebastian<PERSON>mann/exporter/tree/5.1.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T07:17:12+00:00"}, {"name": "sebastian/global-state", "version": "6.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "987bafff24ecc4c9ac418cab1145b96dd6e9cbd9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/987bafff24ecc4c9ac418cab1145b96dd6e9cbd9", "reference": "987bafff24ecc4c9ac418cab1145b96dd6e9cbd9", "shasum": ""}, "require": {"php": ">=8.1", "sebastian/object-reflector": "^3.0", "sebastian/recursion-context": "^5.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "https://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/global-state/security/policy", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/6.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T07:19:19+00:00"}, {"name": "sebastian/lines-of-code", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code.git", "reference": "856e7f6a75a84e339195d48c556f23be2ebf75d0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/lines-of-code/zipball/856e7f6a75a84e339195d48c556f23be2ebf75d0", "reference": "856e7f6a75a84e339195d48c556f23be2ebf75d0", "shasum": ""}, "require": {"nikic/php-parser": "^4.18 || ^5.0", "php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for counting the lines of code in PHP source code", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/tree/2.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-12-21T08:38:20+00:00"}, {"name": "sebastian/object-enumerator", "version": "5.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "202d0e344a580d7f7d04b3fafce6933e59dae906"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/202d0e344a580d7f7d04b3fafce6933e59dae906", "reference": "202d0e344a580d7f7d04b3fafce6933e59dae906", "shasum": ""}, "require": {"php": ">=8.1", "sebastian/object-reflector": "^3.0", "sebastian/recursion-context": "^5.0"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/5.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T07:08:32+00:00"}, {"name": "sebastian/object-reflector", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "reference": "24ed13d98130f0e7122df55d06c5c4942a577957"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/object-reflector/zipball/24ed13d98130f0e7122df55d06c5c4942a577957", "reference": "24ed13d98130f0e7122df55d06c5c4942a577957", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/3.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T07:06:18+00:00"}, {"name": "sebastian/recursion-context", "version": "5.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "05909fb5bc7df4c52992396d0116aed689f93712"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/05909fb5bc7df4c52992396d0116aed689f93712", "reference": "05909fb5bc7df4c52992396d0116aed689f93712", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/5.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T07:05:40+00:00"}, {"name": "sebastian/type", "version": "4.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/type.git", "reference": "462699a16464c3944eefc02ebdd77882bd3925bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/type/zipball/462699a16464c3944eefc02ebdd77882bd3925bf", "reference": "462699a16464c3944eefc02ebdd77882bd3925bf", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the types of the PHP type system", "homepage": "https://github.com/sebastian<PERSON>mann/type", "support": {"issues": "https://github.com/sebastian<PERSON>mann/type/issues", "source": "https://github.com/sebastian<PERSON>mann/type/tree/4.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T07:10:45+00:00"}, {"name": "sebastian/version", "version": "4.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "c51fa83a5d8f43f1402e3f32a005e6262244ef17"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/c51fa83a5d8f43f1402e3f32a005e6262244ef17", "reference": "c51fa83a5d8f43f1402e3f32a005e6262244ef17", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/4.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-07T11:34:05+00:00"}, {"name": "squizlabs/php_codesniffer", "version": "3.12.2", "source": {"type": "git", "url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "reference": "6d4cf6032d4b718f168c90a96e36c7d0eaacb2aa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/6d4cf6032d4b718f168c90a96e36c7d0eaacb2aa", "reference": "6d4cf6032d4b718f168c90a96e36c7d0eaacb2aa", "shasum": ""}, "require": {"ext-simplexml": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.3.4"}, "bin": ["bin/phpcbf", "bin/phpcs"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "role": "Former lead"}, {"name": "<PERSON>", "role": "Current lead"}, {"name": "Contributors", "homepage": "https://github.com/PHPCSStandards/PHP_CodeSniffer/graphs/contributors"}], "description": "PHP_CodeSniffer tokenizes PHP, JavaScript and CSS files and detects violations of a defined set of coding standards.", "homepage": "https://github.com/PHPCSStandards/PHP_CodeSniffer", "keywords": ["phpcs", "standards", "static analysis"], "support": {"issues": "https://github.com/PHPCSStandards/PHP_CodeSniffer/issues", "security": "https://github.com/PHPCSStandards/PHP_CodeSniffer/security/policy", "source": "https://github.com/PHPCSStandards/PHP_CodeSniffer", "wiki": "https://github.com/PHPCSStandards/PHP_CodeSniffer/wiki"}, "funding": [{"url": "https://github.com/PHPCSStandards", "type": "github"}, {"url": "https://github.com/jrfnl", "type": "github"}, {"url": "https://opencollective.com/php_codesniffer", "type": "open_collective"}, {"url": "https://thanks.dev/u/gh/phpcsstandards", "type": "thanks_dev"}], "time": "2025-04-13T04:10:18+00:00"}, {"name": "symfony/css-selector", "version": "v7.2.0", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "601a5ce9aaad7bf10797e3663faefce9e26c24e2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/601a5ce9aaad7bf10797e3663faefce9e26c24e2", "reference": "601a5ce9aaad7bf10797e3663faefce9e26c24e2", "shasum": ""}, "require": {"php": ">=8.2"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Converts CSS selectors to XPath expressions", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/css-selector/tree/v7.2.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/dom-crawler", "version": "v7.2.4", "source": {"type": "git", "url": "https://github.com/symfony/dom-crawler.git", "reference": "19cc7b08efe9ad1ab1b56e0948e8d02e15ed3ef7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dom-crawler/zipball/19cc7b08efe9ad1ab1b56e0948e8d02e15ed3ef7", "reference": "19cc7b08efe9ad1ab1b56e0948e8d02e15ed3ef7", "shasum": ""}, "require": {"masterminds/html5": "^2.6", "php": ">=8.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0"}, "require-dev": {"symfony/css-selector": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\DomCrawler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases DOM navigation for HTML and XML documents", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/dom-crawler/tree/v7.2.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-02-17T15:53:07+00:00"}, {"name": "symfony/var-dumper", "version": "v7.2.6", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "9c46038cd4ed68952166cf7001b54eb539184ccb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/9c46038cd4ed68952166cf7001b54eb539184ccb", "reference": "9c46038cd4ed68952166cf7001b54eb539184ccb", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/console": "<6.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/uid": "^6.4|^7.0", "twig/twig": "^3.12"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v7.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-09T08:14:01+00:00"}, {"name": "theseer/tokenizer", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/theseer/tokenizer.git", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theseer/tokenizer/zipball/737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2", "shasum": ""}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "support": {"issues": "https://github.com/theseer/tokenizer/issues", "source": "https://github.com/theseer/tokenizer/tree/1.2.3"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2024-03-03T12:36:25+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {}, "prefer-stable": false, "prefer-lowest": false, "platform": {"php": "~8.3.14"}, "platform-dev": {}, "plugin-api-version": "2.6.0"}