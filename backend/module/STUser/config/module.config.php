<?php

declare(strict_types=1);

namespace STUser;

use STCompany\Service\Interfaces\UserSelectorInterface as CompanyUserSelectorInterface;
use STCompany\Service\Interfaces\AuthCodesGeneratorInterface as CompanyAuthCodesGeneratorInterface;
use STLib\Mvc\Data\TableFactory;
use STLib\Mvc\DependencyInjection\DefaultFactory;
use STUser\ServiceProviding\UserServiceProvider;

return [
    'controller_plugins' => [
        'invokables' => [
            'auth' => Controller\Plugin\Auth::class,
            'user' => Controller\Plugin\User::class,
            'userPermissionChecker' => Controller\Plugin\UserPermissionChecker::class,
        ]
    ],
    'service_manager' => [
        'factories' => [
            Service\AuthService::class => DefaultFactory::class,
            Service\UserService::class => DefaultFactory::class,
            Service\UserAvatarService::class => DefaultFactory::class,
            Service\TwoFactorAuthService::class => Service\TwoFactorAuthServiceFactory::class,
            Data\UsersTable::class => TableFactory::class,
        ],
        'aliases' => [
            CompanyUserSelectorInterface::class => UserServiceProvider::class,
            CompanyAuthCodesGeneratorInterface::class => UserServiceProvider::class,
        ]
    ]
];
