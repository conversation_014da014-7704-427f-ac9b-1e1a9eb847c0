<?php

namespace STUser\Entity;

class User
{
    use UserAvatarTrait;

    public const string DEFAULT_HIDDEN_ADMIN_EMAIL = '<EMAIL>';
    public const string DEFAULT_HIDDEN_ADMIN_NAME = 'defaultHiddenAdmin';

    /**
     *
     * @var int|null
     */
    protected ?int $id = null;

    /**
     *
     * @var string|null
     */
    protected ?string $name = null;

    /**
     *
     * @var string|null
     */
    protected ?string $email = null;

    /**
     *
     * @var string
     */
    protected ?string $password = null;

    /**
     *
     * @var string|null
     */
    protected ?string $passwordConfirm = null;

    /**
     *
     * @var string|null
     */
    protected ?string $twoFactorSecret = null;

    /**
     *
     * @var \Carbon\Carbon|null
     */
    protected ?\Carbon\Carbon $registrationDate = null;

    /**
     *
     * @var string|null
     */
    protected ?string $token = null;

    /**
     *
     * @var string|null
     */
    protected ?string $avatar = null;

    /**
     *
     * @var bool
     */
    protected bool $isFirstLogin = false;

    /**
     *
     * @var bool
     */
    protected bool $isGlobalAdmin = false;

    /**
     *
     * @var int|null
     */
    protected ?int $frontIdGlobalAdmin = null;

    protected bool $createdWithEmail = true;

    /**
     *
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     *
     * @return string|null
     */
    public function getName(): ?string
    {
        return $this->name;
    }

    /**
     *
     * @return string|null
     */
    public function getEmail(): ?string
    {
        return $this->email;
    }

    /**
     *
     * @return string|null
     */
    public function getPassword(): ?string
    {
        return $this->password;
    }

    /**
     *
     * @return string|null
     */
    public function getPasswordConfirm(): ?string
    {
        return $this->passwordConfirm;
    }

    /**
     *
     * @return string|null
     */
    public function getTwoFactorSecret(): ?string
    {
        return $this->twoFactorSecret;
    }

    /**
     *
     * @return \Carbon\Carbon|null
     */
    public function getRegistrationDate(): ?\Carbon\Carbon
    {
        return $this->registrationDate;
    }

    /**
     *
     * @return string|null
     */
    public function getToken(): ?string
    {
        return $this->token;
    }

    /**
     *
     * @return string|null
     */
    public function getAvatar(): ?string
    {
        return $this->avatar;
    }

    /**
     *
     * @return int|null
     */
    public function getFrontIdGlobalAdmin(): ?int
    {
        return $this->frontIdGlobalAdmin;
    }

    /**
     *
     * @param int|null $id
     * @return User
     */
    public function setId(?int $id): User
    {
        $this->id = $id;
        return $this;
    }

    /**
     *
     * @param string|null $name
     * @return User
     */
    public function setName(?string $name): User
    {
        $this->name = $name;
        return $this;
    }

    /**
     *
     * @param string|null $email
     * @return User
     */
    public function setEmail(?string $email): User
    {
        $this->email = $email;
        return $this;
    }

    /**
     *
     * @param string|null $password
     * @return User
     */
    public function setPassword(?string $password): User
    {
        $this->password = $password;
        return $this;
    }

    /**
     *
     * @param string|null $passwordConfirm
     * @return User
     */
    public function setPasswordConfirm(?string $passwordConfirm): User
    {
        $this->passwordConfirm = $passwordConfirm;
        return $this;
    }

    /**
     *
     * @param string|null $twoFactorSecret
     * @return User
     */
    public function setTwoFactorSecret(?string $twoFactorSecret): User
    {
        $this->twoFactorSecret = $twoFactorSecret;
        return $this;
    }

    /**
     *
     * @param \Carbon\Carbon|string $registrationDate
     * @return User
     */
    public function setRegistrationDate(\Carbon\Carbon|string $registrationDate): User
    {
        if ($registrationDate instanceof \Carbon\Carbon) {
            $this->registrationDate = $registrationDate;
        } elseif (is_string($registrationDate)) {
            $this->registrationDate = \Carbon\Carbon::createFromTimeString($registrationDate);
        }
        return $this;
    }

    /**
     *
     * @param string|null $token
     * @return User
     */
    public function setToken(?string $token): User
    {
        $this->token = $token;
        return $this;
    }

    /**
     *
     * @param string|null $avatar
     * @return User
     */
    public function setAvatar(?string $avatar): User
    {
        $this->avatar = $avatar;
        return $this;
    }

    /**
     *
     * @param int|null $frontIdGlobalAdmin
     * @return User
     */
    public function setFrontIdGlobalAdmin(?int $frontIdGlobalAdmin): User
    {
        $this->frontIdGlobalAdmin = $frontIdGlobalAdmin;
        return $this;
    }

    /**
     *
     * @param bool $force
     * @return User
     */
    public function initRegistrationDate(bool $force = false): User
    {
        if ($force || !($this->registrationDate instanceof \Carbon\Carbon)) {
            $this->registrationDate = \Carbon\Carbon::now();
        }
        return $this;
    }

    /**
     *
     * @param int $length
     * @return User
     */
    public function generateToken(int $length = 31): User
    {
        $this->token = $this->getId() . '-' . bin2hex(random_bytes($length));
        return $this;
    }

    /**
     *
     * @return User
     */
    public function clearToken(): User
    {
        $this->token = null;
        return $this;
    }

    /**
     *
     * @return bool
     */
    public function hasTwoFactorSecret(): bool
    {
        return !is_null($this->twoFactorSecret);
    }

    /**
     *
     * @return User
     */
    public function hashPassword($salt): User
    {
        $this->initRegistrationDate();
        $this->setPassword(hash('whirlpool', $salt . $this->password . $this->getRegistrationDate()->format('h:i:sU')));
        return $this;
    }

    /**
     *
     * @return User
     */
    public function clearPassword(): User
    {
        $this->password = null;
        $this->passwordConfirm = null;
        return $this;
    }

    /**
     *
     * @return string
     */
    public function getPersonalAvatarDir(): string
    {
        return $this->getAvatarDir($this->getId());
    }

    /**
     *
     * @return string
     */
    public function getPersonalAvatarFile(): string
    {
        return $this->getAvatarFile($this->getId());
    }

    /**
     * @param bool|null $isFirstLogin
     * @return bool|$this
     */
    public function isFirstLogin(?bool $isFirstLogin = null): bool|User
    {
        if ($isFirstLogin === null) {
            return $this->isFirstLogin;
        }
        $this->isFirstLogin = $isFirstLogin;
        return $this;
    }

    /**
     *
     * @param bool|null $isGlobalAdmin
     * @return bool|$this
     */
    public function isGlobalAdmin(?bool $isGlobalAdmin = null): bool|User
    {
        if ($isGlobalAdmin === null) {
            return $this->isGlobalAdmin;
        }
        $this->isGlobalAdmin = $isGlobalAdmin;
        return $this;
    }

    public function isCreatedWithEmail(): bool
    {
        return $this->createdWithEmail;
    }

    public function setCreatedWithEmail(bool $createdWithEmail): User
    {
        $this->createdWithEmail = $createdWithEmail;
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        $array = [
            'id' => $this->id,
            'email' => $this->email,
            'name' => $this->name,
            'avatar' => $this->avatar,
            'created_with_email' => $this->createdWithEmail,
            'personal_avatar_file' => $this->getPersonalAvatarFile(),
            'registration_date' => $this->registrationDate,
            'is_first_login' => $this->isFirstLogin,
            'is_global_admin' => $this->isGlobalAdmin,
        ];
        if (!empty($this->token)) {
            $array['token'] = $this->token;
        }
        return $array;
    }
}
