<?php

namespace STUser\Entity;

trait UserAvatarTrait
{
    /**
     *
     * @return string
     */
    public function getAvatarDir(int $userId): string
    {
        return str_repeat('0', max(0, 3 - strlen($userId % 1000))) . $userId % 1000 . '/';
    }

    /**
     *
     * @return string
     */
    public function getAvatarFile(int $userId): string
    {
        return \STUser\Service\UserAvatarService::DEFAULT_USER_AVATAR_DIR . $this->getAvatarDir($userId) . $userId . '.' . \STUser\Service\UserAvatarService::DEFAULT_USER_AVATAR_FILE_EXTENTION;
    }
}
