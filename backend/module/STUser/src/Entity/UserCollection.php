<?php

declare(strict_types=1);

namespace STUser\Entity;

class UserCollection extends \STLib\Expand\Collection
{
    /**
     *
     * @param mixed $user
     * @param string|int|null $key
     * @return \STLib\Expand\Collection
     * @throws \RuntimeException
     */
    public function add(mixed $user, string|int|null $key = null): \STLib\Expand\Collection
    {
        if (!($user instanceof User)) {
            throw new \RuntimeException('User must be an instace of "\STUser\Entity\User"');
        }
        parent::add($user, $key ?? $user->getId());
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        $result = [];
        foreach ($this as $user) {
            $result[] = $user->toArray();
        }
        return $result;
    }
}
