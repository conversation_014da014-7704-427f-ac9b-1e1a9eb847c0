<?php

declare(strict_types=1);

namespace STUser\Service;

class UserAvatarService
{
    public const DEFAULT_USER_AVATAR_FILE = 'default.png';
    public const DEFAULT_USER_AVATAR_FILE_EXTENTION = 'png';
    public const DEFAULT_USER_AVATAR_DIR = '/user/avatar/';
    public const SYSTEM_USER_AVATAR_DIR = '/public/user/avatar/';

    /**
     *
     * @param \STUser\Entity\User $user
     * @return string
     */
    public function saveAvatar(\STUser\Entity\User $user): string
    {
        $userAvatarPath = getcwd() . static::SYSTEM_USER_AVATAR_DIR . $user->getPersonalAvatarDir() . '/';
        $userAvatarFile = $userAvatarPath . $user->getId() . '.png';

        if (!is_dir($userAvatarPath)) {
            mkdir($userAvatarPath);
        }
        if (is_string($user->getAvatar())) {
            file_put_contents($userAvatarFile, base64_decode(preg_replace('/^data:image\/[\w]+;base64,/', '', $user->getAvatar())));
        } else {
            copy(getcwd() . static::SYSTEM_USER_AVATAR_DIR . static::DEFAULT_USER_AVATAR_FILE, $userAvatarFile);
        }
        return $userAvatarFile;
    }

    /**
     *
     * @param \STUser\Entity\User $user
     * @return bool
     */
    public function deleteAvatar(\STUser\Entity\User $user): bool
    {
        $userAvatarPath = getcwd() . static::SYSTEM_USER_AVATAR_DIR . $user->getPersonalAvatarDir() . '/';
        $userAvatarFile = $userAvatarPath . $user->getId() . '.png';

        if (file_exists($userAvatarFile)) {
            unlink($userAvatarFile);
        }
        return true;
    }
}
