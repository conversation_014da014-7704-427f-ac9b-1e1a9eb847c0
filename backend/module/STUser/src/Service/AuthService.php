<?php

declare(strict_types=1);

namespace STUser\Service;

use Exception;
use ReflectionException;
use STApi\Entity\Exception\BaseApiException;
use STApi\Entity\Exception\NotFoundApiException;
use STApi\Entity\Exception\TooManyUnsuccessfullyLoginAttempts;
use STLib\Db\ProvidesTransaction;
use STLib\Mvc\Hydrator\BaseHydratorTrait;
use STRedis\Service\ProvidesRedis;
use STRedis\Service\RedisService;
use STUser\Data\UsersTable;
use STUser\Entity\User;

class AuthService
{
    use BaseHydratorTrait;
    use ProvidesTransaction;
    use ProvidesRedis;

    protected const string AUTH_TOKEN_REDIS_KEY = 'auth:token';
    protected const int|float AUTH_TOKEN_LIFETIME = 60 * 60 * 24 * 30;

    protected const string RECOVERY_CODE_REDIS_KEY = 'auth:recovery';
    protected const int|float RECOVERY_CODE_LIFETIME = 15 * 60;

    protected const int MAX_UNSUCCESSFULLY_LOGIN_ATTEMPTS = 6;

    /**
     *
     * @var User|null
     */
    protected ?User $user = null;

    /**
     *
     * @var UsersTable
     */
    protected UsersTable $usersTable;

    /**
     *
     * @param UsersTable $usersTable
     */
    public function __construct(
        UsersTable $usersTable,
        private readonly RedisService $redisService
    ) {
        $this->usersTable = $usersTable;
    }

    /**
     *
     * @return boolean
     */
    public function isSigned(): bool
    {
        return !is_null($this->user);
    }

    /**
     *
     * @return User|null
     * @throws Exception
     */
    public function getUser(): ?User
    {
        if (!$this->isSigned()) {
            throw new Exception('User is not signed');
        }
        return $this->user;
    }

    /**
     * @param User $user
     * @return User
     * @throws NotFoundApiException
     * @throws ReflectionException
     * @throws TooManyUnsuccessfullyLoginAttempts
     */
    public function signIn(User $user): User
    {
        $loginAttemptsKey = $this->getRedisLoginAttemptsKey($user->getId());
        $loginAttempts = (int) $this->redisService->get($loginAttemptsKey);

        if ($loginAttempts === self::MAX_UNSUCCESSFULLY_LOGIN_ATTEMPTS) {
            throw new TooManyUnsuccessfullyLoginAttempts();
        }

        try {
            $userData = $this->usersTable->getUserByCredentials($user->getEmail(), $user->getPassword());
        } catch (NotFoundApiException $e) {
            $this->redisService->incr($loginAttemptsKey);
            $this->redisService->expire($loginAttemptsKey, 10 * 60);

            throw $e;
        }

        $this->redisService->delete($this->getRedisLoginAttemptsKey($user->getId()));

        $createdUser = $this->hydrate((array) $userData->current(), User::class);
        $createdUser->clearPassword();

        return $createdUser;
    }

    /**
     *
     * @param string $token
     * @return bool
     */
    public function signOut(string $token): bool
    {
        $this->redis()->del($this->getRedisAuthKey($token));
        return true;
    }

    /**
     *
     * @param User $user
     * @return void
     */
    public function backup(User $user): void
    {
        $user->generateToken();
        $this->redisService->set($this->getRedisAuthKey($user->getToken()), $user->getId());
        $this->redisService->expire($this->getRedisAuthKey($user->getToken()), static::AUTH_TOKEN_LIFETIME);
    }

    /**
     * @param string|null $token
     * @return User|null
     * @throws NotFoundApiException
     * @throws ReflectionException
     */
    public function restore(?string $token): ?User
    {
        if (!is_null($token) && is_null($this->user)) {
            $userId = (int) $this->redis()->get($this->getRedisAuthKey($token));
            if ($userId > 0) {
                $savedUserData = $this->usersTable->getUserById($userId);
                $this->user = $this->hydrate($savedUserData->current()->getArrayCopy(), User::class);
            }
        }
        return $this->user;
    }

    /**
     *
     * @param User $user
     * @param int $lifetime
     * @return string
     */
    public function getChangePasswordCode(User $user, int $lifetime = null): string
    {
        if (is_null($lifetime)) {
            $lifetime = static::RECOVERY_CODE_LIFETIME;
        }
        $code = uniqid('', true);
        $codeKey = $this->getRedisRecoveryKey($code);
        $this->redis()->set($codeKey, $user->getId());
        $this->redis()->expire($codeKey, $lifetime);
        return $code;
    }

    /**
     *
     * @param int $code
     * @return User
     * @throws BaseApiException
     */
    public function getUserByChangePasswordCode(string $code): User
    {
        $codeKey = $this->getRedisRecoveryKey($code);
        if (!$this->redis()->exists($codeKey)) {
            throw new BaseApiException(
                'Cannot find key, probably key has been expired',
                \Laminas\Http\Response::STATUS_CODE_422
            );
        }
        $userId = (int) $this->redis()->get($codeKey);
        $userData = $this->usersTable->getUserById($userId);
        return $this->hydrate((array) $userData->current(), User::class);
    }

    /**
     *
     * @param int $code
     * @return void
     */
    public function removeRecoveryCode(string $code): void
    {
        $codeKey = $this->getRedisRecoveryKey($code);
        $this->redis()->del($codeKey);
    }

    /**
     *
     * @param string $token
     * @return string
     */
    protected function getRedisAuthKey(string $token): string
    {
        return static::AUTH_TOKEN_REDIS_KEY . ':' . $token;
    }

    /**
     *
     * @param string $code
     * @return string
     */
    protected function getRedisRecoveryKey(string $code): string
    {
        return static::RECOVERY_CODE_REDIS_KEY . ':' . $code;
    }

    /**
     *
     * @param int $userId
     * @return string
     */
    protected function getRedisLoginAttemptsKey(int $userId): string
    {
        return sprintf('user:%s:login_attempts', $userId);
    }
}
