<?php

declare(strict_types=1);

namespace STUser\Service;

use <PERSON><PERSON><PERSON>ee\Auth\TwoFactorAuth;
use <PERSON><PERSON><PERSON>ee\Auth\TwoFactorAuthException;
use STUser\Service\Interfaces\ConfigurationInterface;

class TwoFactorAuthFactory
{
    public function __construct(private readonly ConfigurationInterface $configuration)
    {
    }

    /**
     * @throws TwoFactorAuthException
     */
    public function create(): TwoFactorAuth
    {
        return new TwoFactorAuth($this->configuration->get('auth')['two-factor-auth']['issuer'] ?? null);
    }
}
