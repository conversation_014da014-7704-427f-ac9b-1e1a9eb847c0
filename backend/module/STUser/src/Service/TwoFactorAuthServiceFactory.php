<?php

declare(strict_types=1);

namespace STUser\Service;

use Interop\Container\ContainerInterface;
use <PERSON>inas\ServiceManager\Factory\FactoryInterface;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use <PERSON><PERSON><PERSON><PERSON>\Auth\TwoFactorAuthException;

class TwoFactorAuthServiceFactory implements FactoryInterface
{
    /**
     * @param ContainerInterface $container
     * @param $requestedName
     * @param array|null $options
     * @return TwoFactorAuthService
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws TwoFactorAuthException
     */
    public function createService(ContainerInterface $container, $requestedName, array $options = null): TwoFactorAuthService
    {
        return $this($container, $requestedName ?: PatternPluginManager::class, $this->creationOptions);
    }

    /**
     * @param ContainerInterface $container
     * @param $requestedName
     * @param array|null $options
     * @return TwoFactorAuthService
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws TwoFactorAuthException
     */
    public function __invoke(ContainerInterface $container, $requestedName, array $options = null): TwoFactorAuthService
    {
        /** @var TwoFactorAuthFactory $twoFactorAuthFactory */
        $twoFactorAuthFactory = $container->get(TwoFactorAuthFactory::class);

        return new TwoFactorAuthService($twoFactorAuthFactory->create());
    }
}
