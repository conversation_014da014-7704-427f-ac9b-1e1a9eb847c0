<?php

declare(strict_types=1);

namespace STUser\Service;

use RobThree\Auth\TwoFactorAuth;
use STUser\Data\UsersTable;

class TwoFactorAuthService
{
    /**
     *
     * @todo Swith to our own implementation
     *
     * @var TwoFactorAuth
     */
    protected TwoFactorAuth $twoFactorAuth;

    /**
     * @param TwoFactorAuth $twoFactorAuth
     */
    public function __construct(TwoFactorAuth $twoFactorAuth)
    {
        $this->twoFactorAuth = $twoFactorAuth;
    }

    /**
     *
     * @see \RobThree\Auth\TwoFactorAuth::createSecret()
     *
     * @return string
     */
    public function createSecret(): string
    {
        return $this->twoFactorAuth->createSecret();
    }

    /**
     *
     * @see \RobThree\Auth\TwoFactorAuth::getQRCodeImageAsDataUri()
     *
     * @param string $label
     * @param string $secret
     * @return string
     */
    public function getQRCodeImageAsDataUri(string $label, string $secret): string
    {
        return $this->twoFactorAuth->getQRCodeImageAsDataUri($label, $secret);
    }

    /**
     *
     * @see \RobThree\Auth\TwoFactorAuth::verifyCode()
     *
     * @param string $secret
     * @param string $code
     * @return bool
     */
    public function verifyCode(string $secret, string $code): bool
    {
        return $this->twoFactorAuth->verifyCode($secret, $code);
    }
}
