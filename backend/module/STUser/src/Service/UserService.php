<?php

declare(strict_types=1);

namespace STUser\Service;

use ReflectionException;
use ST<PERSON>pi\Entity\Exception\NotFoundApiException;
use STUser\Data\UsersTable;
use STUser\Entity\User;
use STUser\Entity\UserCollection;

class UserService
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    /**
     *
     * @var UsersTable
     */
    private UsersTable $usersTable;

    /**
     *
     * @param UsersTable $usersTable
     */
    public function __construct(UsersTable $usersTable)
    {
        $this->usersTable = $usersTable;
    }

    /**
     *
     * @param int $userId
     * @return User
     */
    public function getUserById(int $userId): User
    {
        $userData = $this->usersTable->getUserById($userId);
        return $this->hydrate($userData->current()->getArrayCopy(), User::class);
    }

    /**
     * @param array $userIds
     * @return UserCollection
     * @throws ReflectionException
     */
    public function getUsersById(array $userIds): UserCollection
    {
        $userCollection = new UserCollection();

        try {
            $usersData = $this->usersTable->getUserById($userIds);
        } catch (NotFoundApiException $e) {
            return $userCollection;
        }

        foreach ($usersData as $userData) {
            $userCollection->add($this->hydrate((array) $userData, User::class));
        }

        return $userCollection;
    }

    /**
     * @param string $userEmail
     * @return User
     * @throws NotFoundApiException
     * @throws ReflectionException
     */
    public function getUserByEmail(string $userEmail): User
    {
        $userData = $this->usersTable->getUserByEmail($userEmail);

        return $this->hydrate($userData->current()->getArrayCopy(), User::class);
    }

    /**
     * @return UserCollection
     * @throws ReflectionException
     */
    public function getGlobalAdminUsers(): UserCollection
    {
        $userCollection = new UserCollection();
        $usersData = $this->usersTable->getGlobalAdminUsers();
        foreach ($usersData as $userData) {
            $userCollection->add($this->hydrate((array) $userData, User::class));
        }
        return $userCollection;
    }

    /**
     *
     * @param int $frontId
     * @return UserCollection
     */
    public function getFrontGlobalAdminUsers(int $frontId): UserCollection
    {
        $userCollection = new UserCollection();
        $usersData = $this->usersTable->getFrontGlobalAdminUsers($frontId);
        foreach ($usersData as $userData) {
            $userCollection->add($this->hydrate((array) $userData, User::class));
        }
        return $userCollection;
    }

    /**
     *
     * @param User $user
     * @param bool $updatePassword
     * @return int
     */
    public function saveUser(User $user, bool $updatePassword = false): int
    {
        return $this->usersTable->saveUser($user, $updatePassword);
    }
}
