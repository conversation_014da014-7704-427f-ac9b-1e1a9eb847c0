<?php

declare(strict_types=1);

namespace STUser\Data;

use Laminas\Db\ResultSet\ResultSet;
use Laminas\Db\Sql\Where;

class UsersTable extends \STLib\Db\AbstractTable
{
    /**
     *
     * @param int|array $userId
     * @param int $companyId
     * @param array $teamIds
     * @return ResultSet
     * @throws \STApi\Entity\Exception\NotFoundApiException
     */
    public function getUserById(int|array $userId, int $companyId = null, array $teamIds = []): ResultSet
    {
        $select = $this->tableGateway->getSql()->select();
        $select->where([
            'users.user_id' => $userId,
        ]);
        if (is_int($companyId)) {
            $select
                ->join(
                    [
                        'ucr' => 'users_companies_roles',
                    ],
                    'ucr.user_id = users.user_id',
                    [
                        'is_active',
                        'role_id',
                        'company_user_id',
                        'is_auto_analyze_calls',
                    ],
                    \Laminas\Db\Sql\Select::JOIN_INNER
                )
                ->where([
                    'ucr.company_id' => $companyId,
                ]);
        }
        if (count($teamIds) > 0) {
            $select
                ->join(
                    [
                        'ut' => 'users_teams',
                    ],
                    'ut.user_id = users.user_id',
                    [],
                    \Laminas\Db\Sql\Select::JOIN_INNER
                )
                ->where([
                    'ut.team_id' => $teamIds,
                ]);
        }
        $result = $this->tableGateway->selectWith($select);
        if ($result->count() === 0 && is_int($userId)) {
            throw new \STApi\Entity\Exception\NotFoundApiException('User not found');
        }
        return $result;
    }

    /**
     *
     * @param int $companyId
     * @param string $userName
     * @return ResultSet
     * @throws \STApi\Entity\Exception\NotFoundApiException
     */
    public function getUserByName(int $companyId, string $userName): ResultSet
    {
        $select = $this->tableGateway->getSql()->select();
        $select
            ->join(
                [
                    'ucr' => 'users_companies_roles',
                ],
                'ucr.user_id = users.user_id',
                [
                    'is_active',
                ],
                \Laminas\Db\Sql\Select::JOIN_INNER
            )
            ->where([
                'ucr.company_id' => $companyId,
                'users.user_name' => $userName,
            ]);
        $result = $this->tableGateway->selectWith($select);
        if ($result->count() === 0) {
            throw new \STApi\Entity\Exception\NotFoundApiException('User not found');
        }
        return $result;
    }

    /**
     *
     * @param int $userEmail
     * @return ResultSet
     * @throws \STApi\Entity\Exception\NotFoundApiException
     */
    public function getUserByEmail(string $userEmail): ResultSet
    {
        $result = $this->tableGateway->select([
            'user_email' => $userEmail,
        ]);
        if ($result->count() === 0) {
            throw new \STApi\Entity\Exception\NotFoundApiException('User not found');
        }
        return $result;
    }

    public function isEmailAlreadyExists(?string $userEmail, int $excludeId): bool
    {
        $select = $this->tableGateway->getSql()->select();
        $select
            ->where(function (Where $where) use ($userEmail, $excludeId) {
                $where->equalTo('user_email', $userEmail);
                $where->notEqualTo('user_id', $excludeId);
            });

        $result = $this->tableGateway->selectWith($select);

        return $result->count() !== 0;
    }

    /**
     *
     * @param string $email
     * @param string $password
     * @return ResultSet
     * @throws \STApi\Entity\Exception\NotFoundApiException
     */
    public function getUserByCredentials(string $email, string $password): ResultSet
    {
        $result = $this->tableGateway->select([
            'user_email' => $email,
            'user_password' => $password,
        ]);
        if ($result->count() === 0) {
            throw new \STApi\Entity\Exception\NotFoundApiException('User not found');
        }
        return $result;
    }

    /**
     *
     * @param int $companyId
     * @param array $teamIds
     * @return ResultSet
     */
    public function getUsersByCompanyId(int $companyId, array $teamIds = []): ResultSet
    {
        $select = $this->tableGateway->getSql()->select();
        $select
            ->join(
                [
                    'ucr' => 'users_companies_roles',
                ],
                'ucr.user_id = users.user_id',
                [
                    'role_id',
                    'is_active',
                    'company_user_id',
                    'is_auto_analyze_calls',
                    'is_pending',
                ],
                \Laminas\Db\Sql\Select::JOIN_INNER
            )
            ->where([
                'ucr.company_id' => $companyId,
            ]);

        if (count($teamIds) > 0) {
            $select
                ->join(
                    [
                        'ut' => 'users_teams',
                    ],
                    'ut.user_id = users.user_id',
                    [],
                    \Laminas\Db\Sql\Select::JOIN_INNER
                )
                ->where([
                    'ut.team_id' => $teamIds,
                ]);
        }

        $result = $this->tableGateway->selectWith($select);
        return $result;
    }

    /**
     *
     * @param int $companyId
     * @param array $userIds
     * @return ResultSet
     */
    public function getUsersByUserIds(int $companyId, array $userIds): ResultSet
    {
        $select = $this->tableGateway->getSql()->select();
        $select
            ->join(
                [
                    'ucr' => 'users_companies_roles',
                ],
                'ucr.user_id = users.user_id',
                [
                    'user_id',
                    'is_active',
                ],
                \Laminas\Db\Sql\Select::JOIN_LEFT
            )
            ->where([
                'ucr.company_id' => $companyId,
                'users.user_id' => $userIds,
            ]);

        $result = $this->tableGateway->selectWith($select);
        return $result;
    }

    /**
     *
     * @return ResultSet
     */
    public function getGlobalAdminUsers(): ResultSet
    {
        $result = $this->tableGateway->select([
            'is_global_admin' => true,
        ]);
        return $result;
    }

    /**
     *
     * @param int $frontId
     * @return ResultSet
     */
    public function getFrontGlobalAdminUsers(int $frontId): ResultSet
    {
        $result = $this->tableGateway->select([
            'front_id_global_admin' => $frontId,
        ]);
        return $result;
    }

    /**
     *
     * @param int $companyId
     * @param array $teamIds
     * @return Resultset
     */
    public function getUsersHierarchy(int $companyId, array $teamIds = []): Resultset
    {
        $select = $this->tableGateway->getSql()->select();
        $select
            ->quantifier('DISTINCT')
            ->join(
                [
                    'ucr' => 'users_companies_roles',
                ],
                'users.user_id = ucr.user_id',
                [
                    'role_id',
                    'is_active',
                    'company_user_id',
                    'is_auto_analyze_calls',
                ],
                \Laminas\Db\Sql\Select::JOIN_INNER
            )
            ->join(
                [
                    'r' => 'roles',
                ],
                'ucr.role_id = r.role_id',
                [
                    'role_name',
                    'role_type',
                ],
            )
            ->order('ucr.is_active DESC')
            ->where
            ->equalTo('ucr.company_id', $companyId);
        if (count($teamIds) > 0) {
            $select
                ->join(
                    [
                        'ut' => 'users_teams',
                    ],
                    'ut.user_id = users.user_id',
                    [],
                    \Laminas\Db\Sql\Select::JOIN_INNER
                )
                ->where([
                    'ut.team_id' => $teamIds,
                ]);
        }
        $result = $this->tableGateway->selectWith($select);
        return $result;
    }

    /**
     *
     * @param int $companyId
     * @param array $userTypes
     * @param array $teamIds
     * @return \Laminas\Db\ResultSet\ResultSet
     */
    public function getUsersByRoleType(
        int $companyId,
        array $userTypes,
        array $teamIds = []
    ): \Laminas\Db\ResultSet\ResultSet {
        $select = $this->tableGateway->getSql()->select();
        $select
            ->join(
                [
                    'ucr' => 'users_companies_roles',
                ],
                'users.user_id = ucr.user_id',
                [
                    'is_auto_analyze_calls',
                ],
                \Laminas\Db\Sql\Select::JOIN_LEFT
            )
            ->join(
                [
                    'r' => 'roles',
                ],
                'ucr.role_id = r.role_id',
                [
                    'role_name',
                    'role_type',
                ],
                \Laminas\Db\Sql\Select::JOIN_LEFT
            )
            ->where([
                'ucr.company_id' => $companyId,
                'r.company_id' => $companyId,
                'role_type' => $userTypes,
            ]);
        if (count($teamIds) > 0) {
            $select
                ->join(
                    [
                        'ut' => 'users_teams',
                    ],
                    'ut.user_id = users.user_id',
                    [],
                    \Laminas\Db\Sql\Select::JOIN_INNER
                )
                ->where([
                    'ut.team_id' => $teamIds,
                ]);
        }
        return $this->tableGateway->selectWith($select);
    }

    /**
     *
     * @param int $companyId
     * @param \STLib\Paginator\Params $paginationParams
     * @return \STLib\Paginator\Paginator
     */
    public function getAgentsWithPagination(
        int $companyId,
        \STLib\Paginator\Params $paginationParams,
    ): \STLib\Paginator\Paginator {
        $select = $this->tableGateway->getSql()->select();
        $select
            ->join(
                [
                    'ucr' => 'users_companies_roles',
                ],
                'users.user_id = ucr.user_id',
                [],
                \Laminas\Db\Sql\Select::JOIN_LEFT
            )
            ->join(
                [
                    'r' => 'roles',
                ],
                'ucr.role_id = r.role_id',
                [
                    'role_name',
                    'role_type',
                ],
                \Laminas\Db\Sql\Select::JOIN_LEFT
            )
            ->where([
                'ucr.company_id' => $companyId,
                'r.company_id' => $companyId,
                'role_type' => \STCompany\Entity\Role::AGENT_ROLE_TYPE,
            ]);
        if (!empty($paginationParams->getSearch())) {
            $select
                ->where
                ->AND
                ->nest()
                ->like('users.user_name', '%' . $paginationParams->getSearch() . '%')
                ->OR
                ->like('users.user_id', '%' . $paginationParams->getSearch() . '%')
                ->OR
                ->like('users.user_email', '%' . $paginationParams->getSearch() . '%')
                ->unnest();
        }
        $paginatorAdapter = new \Laminas\Paginator\Adapter\DbSelect($select, $this->tableGateway->getAdapter());
        $paginator = new \STLib\Paginator\Paginator($paginatorAdapter);
        $paginator->setDefaultItemCountPerPage($paginationParams->getLimit());
        $paginator->setCurrentPageNumber($paginationParams->getPage());
        $paginator->setSortColumn($paginationParams->getSortColumn());
        $paginator->setSortDirection($paginationParams->getSortDirection());
        return $paginator;
    }

    /**
     *
     * @param \STUser\Entity\User $user
     * @param bool $updatePassword
     * @return int
     */
    public function saveUser(\STUser\Entity\User $user, bool $updatePassword = false): int
    {
        $data = [
            'user_email' => $user->getEmail(),
            'user_name' => $user->getName(),
            'user_avatar' => $user->getAvatar(),
            'user_two_factor_secret' => $user->getTwoFactorSecret(),
            'registration_date' => $user->getRegistrationDate()->toDateTimeString(),
            'is_first_login' => $user->isFirstLogin(),
            'created_with_email' => $user->isCreatedWithEmail(),
        ];

        if ($updatePassword || is_null($user->getId())) {
            $data['user_password'] = $user->getPassword();
        }

        if ($user->getId() > 0) {
            $this->tableGateway->update($data, [
                'user_id' => $user->getId(),
            ]);
        } else {
            $this->tableGateway->insert($data);
            $user->setId((int) $this->tableGateway->lastInsertValue);
        }

        return $user->getId();
    }

    /**
     *
     * @param int $userId
     * @param string $userName
     * @return int
     */
    public function updateUserName(int $userId, string $userName): int
    {
        return $this->tableGateway->update([
            'user_name' => $userName,
        ], [
            'user_id' => $userId,
        ]);
    }

    public function deleteUser(int $userId): int
    {
        return $this->tableGateway->delete([
            'user_id' => $userId,
        ]);
    }
}
