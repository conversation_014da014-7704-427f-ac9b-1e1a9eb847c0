<?php

declare(strict_types=1);

namespace STUser\Controller\Plugin;

use Api\Controller\V0\BaseController;
use Exception;
use Lam<PERSON>\Mvc\Controller\Plugin\AbstractPlugin;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use STApi\Entity\Exception\UnauthentificatedApiException;
use STFront\Service\FrontService;

/**
 * @method BaseController getController
 */
class UserPermissionChecker extends AbstractPlugin
{
    /**
     *
     * @return bool
     * @throws UnauthentificatedApiException
     */
    public function checkAuthentification(): bool
    {
        if (!$this->getController()->auth()->isSigned()) {
            throw new UnauthentificatedApiException();
        }
        return true;
    }

    /**
     *
     * @return bool
     * @throws UnauthentificatedApiException
     * @throws Exception
     */
    public function checkGlobalAdminAccess(): bool
    {
        if ($this->getController()->auth()->getUser()->isGlobalAdmin()) {
            return true;
        }

        throw new UnauthentificatedApiException('Method is available for global admins only');
    }

    /**
     * @return bool
     * @throws UnauthentificatedApiException
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function checkGlobalAdminOrFrontGlobalAdminAccess(): bool
    {
        if ($this->getController()->auth()->getUser()->isGlobalAdmin()) {
            return true;
        }

        if (
            $this->getController()->getServiceManager()->get(FrontService::class)->getActiveFront()->getId() ===
            $this->getController()->auth()->getUser()->getFrontIdGlobalAdmin()
        ) {
            return true;
        }
        throw new UnauthentificatedApiException('Method is available for global admins of front global admins only');
    }
}
