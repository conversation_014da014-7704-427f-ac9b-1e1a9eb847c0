<?php

declare(strict_types=1);

namespace STUser\Controller\Plugin;

use Laminas\Mvc\Controller\Plugin\AbstractPlugin;

class User extends AbstractPlugin
{
    /**
     *
     * @param string $name
     * @param array $arguments
     * @return mixed
     * @throws \BadMethodCallException
     */
    public function __call(string $name, array $arguments): mixed
    {
        $userService = $this->getController()->getServiceManager()->get(\STUser\Service\UserService::class);
        if (!method_exists($userService, $name)) {
            throw new \BadMethodCallException('Invalid UserService method: ' . $name);
        }
        return call_user_func_array([
            $userService,
            $name
        ], $arguments);
    }

    /**
     *
     * @return \STUser\Service\UserAvatarService
     */
    public function avatar(): \STUser\Service\UserAvatarService
    {
        return $this->getController()->getServiceManager()->get(\STUser\Service\UserAvatarService::class);
    }
}
