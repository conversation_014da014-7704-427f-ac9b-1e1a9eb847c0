<?php

declare(strict_types=1);

namespace STUser\Controller\Plugin;

use <PERSON><PERSON>\Mvc\Controller\Plugin\AbstractPlugin;

class Auth extends AbstractPlugin
{
    /**
     *
     * @param string $name
     * @param array $arguments
     * @return mixed
     * @throws \BadMethodCallException
     */
    public function __call(string $name, array $arguments): mixed
    {
        $authService = $this->getController()->getServiceManager()->get(\STUser\Service\AuthService::class);
        if (!method_exists($authService, $name)) {
            throw new \BadMethodCallException('Invalid AuthService method: ' . $name);
        }
        return call_user_func_array([
            $authService,
            $name
        ], $arguments);
    }

    /**
     *
     * @return \STUser\Service\TwoFactorAuthService
     */
    public function twoFactor(): \STUser\Service\TwoFactorAuthService
    {
        return $this->getController()->getServiceManager()->get(\STUser\Service\TwoFactorAuthService::class);
    }
}
