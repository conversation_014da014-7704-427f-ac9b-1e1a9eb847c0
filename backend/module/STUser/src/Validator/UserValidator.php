<?php

declare(strict_types=1);

namespace STUser\Validator;

use InvalidArgumentException;
use <PERSON><PERSON>\Validator\Db\NoRecordExists;
use <PERSON><PERSON>\Validator\EmailAddress;
use <PERSON><PERSON>\Validator\NotEmpty;
use <PERSON><PERSON>\Validator\Regex;
use <PERSON><PERSON>\Validator\StringLength;
use ST<PERSON>pi\Entity\Exception\NotFoundApiException;
use STLib\Validator\Validator;
use STUser\Data\UsersTable;
use STUser\Entity\User;

class UserValidator extends Validator
{
    public function __construct(private readonly UsersTable $usersTable)
    {
    }

    protected const string EMPTY_NAME_ERROR = 'Empty user name';
    protected const string INCORRECT_EMAIL_ERROR = 'Incorrect e-mail';
    protected const string DIFFERENT_PASS_AND_PASS_CONFIRM_ERROR = 'Password and password confirmation are not equal';
    protected const string PASSWORD_LENGTH_ERROR = 'Password must contain at least 10 symbols';
    protected const string PASSWORD_NUMERAL_ERROR = 'Password must contain at least one numeral';
    protected const string PASSWORD_UPPERCASE_ERROR = 'Password must contain at least one uppercase letter';
    protected const string EMAIL_DB_RECORD_EXISTS_ERROR = 'User with specified e-mail has already registered';
    protected const string EMAIL_DB_RECORD_NOT_EXISTS_ERROR = 'User with specified e-mail is not exists';
    protected const string INVALID_TYPE_ERROR = 'Invalid user type';
    protected const string TOO_LARGE_AVATAR_ERROR = 'Avatar must be less than 500 kb';

    /**
     *
     * @param User $user
     * @return Validator
     * @throws InvalidArgumentException
     */
    public function setInstance($user): Validator
    {
        if (!($user instanceof User)) {
            throw new InvalidArgumentException('User must be instance of "\STUser\Entity\User"');
        }
        return parent::setInstance($user);
    }

    /**
     * @return void
     */
    public function run()
    {
        if ($this->hasCheck('name')) {
            $name = $this->getInstance()->getName();
            $nameValidator = new NotEmpty();
            if (!$nameValidator->isValid($name)) {
                $this->addError('name', self::EMPTY_NAME_ERROR);
            }
        }

        if ($this->hasCheck('email')) {
            $email = $this->getInstance()->getEmail();
            $emailValidator = new EmailAddress();
            if (!$emailValidator->isValid($email)) {
                $this->addError('email', self::INCORRECT_EMAIL_ERROR);
            }
        }

        if ($this->hasCheck('email-not-exists')) {
            $email = $this->getInstance()->getEmail();
            if ($this->usersTable->isEmailAlreadyExists($email, $this->getInstance()->getId() ?? 0)) {
                $this->addError('email', self::EMAIL_DB_RECORD_EXISTS_ERROR);
            }
        }

        if ($this->hasCheck('email-exists')) {
            $email = $this->getInstance()->getEmail();
            try {
                $this->usersTable->getUserByEmail($email);
            } catch (NotFoundApiException) {
                $this->addError('email', self::EMAIL_DB_RECORD_NOT_EXISTS_ERROR);
            }
        }

        if ($this->hasCheck('password')) {
            $password = $this->getInstance()->getPassword();

            if (!(new StringLength(['min' => 10]))->isValid($password)) {
                $this->addError('password', self::PASSWORD_LENGTH_ERROR);
            }

            if (!(new Regex('/[0-9]/'))->isValid($password)) {
                $this->addError('password', self::PASSWORD_NUMERAL_ERROR);
            }

            if (!(new Regex('/[A-Z]/'))->isValid($password)) {
                $this->addError('password', self::PASSWORD_UPPERCASE_ERROR);
            }
        }

        if ($this->hasCheck('password-confirm')) {
            $password = $this->getInstance()->getPassword();
            $passwordConfirm = $this->getInstance()->getPasswordConfirm();
            if ($passwordConfirm !== $password) {
                $this->addError('password', self::DIFFERENT_PASS_AND_PASS_CONFIRM_ERROR);
            }
        }

        if ($this->hasCheck('avatar') && !is_null($this->getInstance()->getAvatar())) {
            $avatarValidator = new StringLength([
                'max' => 100 * 1024,
            ]);
            if (!$avatarValidator->isValid($this->getInstance()->getAvatar())) {
                $this->addError('avatar', self::TOO_LARGE_AVATAR_ERROR);
            }
        }
    }
}
