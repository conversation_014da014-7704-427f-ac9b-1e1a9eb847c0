<?php

declare(strict_types=1);

namespace STUser\ServiceProviding;

use STCompany\Service\Interfaces\UserSelectorInterface as CompanyUserSelectorInterface;
use STCompany\Service\Interfaces\AuthCodesGeneratorInterface as CompanyAuthCodesGeneratorInterface;
use STUser\Entity\User;
use STUser\Service\AuthService;
use STUser\Service\UserService;

final readonly class UserServiceProvider implements CompanyUserSelectorInterface, CompanyAuthCodesGeneratorInterface
{
    public function __construct(
        private UserService $userService,
        private AuthService $authService,
    ) {
    }

    public function getUserByEmail(string $email): User
    {
        return $this->userService->getUserByEmail($email);
    }

    public function getChangePasswordCode(User $user, int $lifetime = null): string
    {
        return $this->authService->getChangePasswordCode($user, $lifetime);
    }
}
