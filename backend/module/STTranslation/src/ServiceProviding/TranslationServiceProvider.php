<?php

declare(strict_types=1);

namespace STTranslation\ServiceProviding;

use GuzzleHttp\Exception\GuzzleException;
use STAlgo\Service\Interfaces\TranslatorInterface as AlgoTranslatorInterface;
use ST<PERSON>pi\Entity\Exception\ThirdPartyApiException;
use STCall\Service\Interfaces\TranslatorInterface as CallTranslatorInterface;
use STTranslation\Service\Drivers\GoogleDriver;
use STTranslation\Service\TranslatorService;

final readonly class TranslationServiceProvider implements CallTranslatorInterface, AlgoTranslatorInterface
{
    public function __construct(private TranslatorService $translator)
    {
    }

    /**
     * @throws GuzzleException
     * @throws ThirdPartyApiException
     */
    public function translate(?string $text, string $driverName = GoogleDriver::DRIVER_NAME): string
    {
        return $this->translator->translate($text, $driverName);
    }

    public function needBatchTranslate(string $driverName): bool
    {
        return $this->translator->needBatchTranslate($driverName);
    }

    public function translateBatch(array $texts, string $driverName, string $languageCode): array
    {
        return $this->translator->translateBatch($texts, $driverName, $languageCode);
    }

    /**
     * @throws GuzzleException
     */
    public function detectLanguage(?string $text): string
    {
        return $this->translator->detectLanguage($text);
    }

    public function detectContentLanguage(string $content): string
    {
        return $this->translator->detectContentLanguage($content);
    }
}
