<?php

declare(strict_types=1);

namespace STTranslation\Service\Drivers\Interfaces;

use GuzzleHttp\Exception\GuzzleException;
use JsonException;

interface AlgoClientInterface
{
    /**
     * @throws GuzzleException
     * @throws JsonException
     */
    public function translate(array $params): array;

    public function detectLanguage(string $content): array;

    public function detectLanguageByText(string $text): array;
}
