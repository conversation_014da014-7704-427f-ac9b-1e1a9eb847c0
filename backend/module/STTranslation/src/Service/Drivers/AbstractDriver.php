<?php

namespace STTranslation\Service\Drivers;

use STApi\Entity\Exception\ThirdPartyApiException;

abstract class AbstractDriver
{
    protected const string SYMBOLS_OF_SENTENCE_ENDING = '.!?。';
    protected const string SYMBOLS_OF_WORD_ENDING = '\s';

    /**
     * @param string $text
     * @return string
     */
    abstract public function detectLanguage(string $text): string;

    /**
     * @param string $text
     * @return string
     */
    abstract protected function translateText(string $text): string;

    /**
     * @param string $text
     * @return string
     * @throws ThirdPartyApiException
     */
    public function translate(string $text): string
    {
        $textParts = $this->splitTextByParts($text);
        $translatedTextParts = [];
        foreach ($textParts as $textPart) {
            $translatedTextParts[] = $this->translateText($textPart);
        }
        return implode(' ', $translatedTextParts);
    }

    /**
     *
     * @param string $text
     * @return array
     */
    protected function splitTextByParts(string $text): array
    {
        $textParts = [];
        $sentences = $this->splitTextBySymbols($text, static::SYMBOLS_OF_SENTENCE_ENDING);
        foreach ($sentences as $sentence) {
            if (strlen($sentence) <= $this->getMaxTextLength()) {
                $textParts[] = $sentence;
            } else {
                $textParts += $this->splitTextBySymbols($sentence, static::SYMBOLS_OF_WORD_ENDING);
            }
        }
        return $textParts;
    }

    /**
     *
     * @param string $text
     * @return array
     */
    protected function splitTextBySymbols(string $text, string $symbols): array
    {
        $textParts = [];
        $textTokens = $this->tokenizeText($text, $symbols);

        // split by sentences
        $activePart = '';
        $activeTextPartIndex = 0;
        foreach ($textTokens as $textToken) {
            if (empty($activePart) || strlen($activePart . $textToken) <= $this->getMaxTextLength()) {
                $activePart .= $textToken;
            } else {
                $activePart = $textToken;
                $activeTextPartIndex++;
            }
            $textParts[$activeTextPartIndex] = $activePart;
        }
        return $textParts;
    }

    /**
     *
     * @param string $text
     * @param string $symbols
     * @return array
     */
    protected function tokenizeText(string $text, string $symbols): array
    {
        return preg_split(
            '/([^' . $symbols . ']+[' . $symbols . ']+)/',
            $text,
            -1,
            PREG_SPLIT_NO_EMPTY | PREG_SPLIT_DELIM_CAPTURE
        );
    }
}
