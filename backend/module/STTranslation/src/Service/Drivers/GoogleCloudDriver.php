<?php

declare(strict_types=1);

namespace STTranslation\Service\Drivers;

use Google\Cloud\Core\Exception\GoogleException;
use Google\Cloud\Core\Exception\ServiceException;
use Google\Cloud\Translate\V2\TranslateClient;
use RuntimeException;

class GoogleCloudDriver extends AbstractDriver implements DriverInterface
{
    public const string DRIVER_NAME = 'google-cloud';
    private const int MAX_TEXT_LENGTH = 2500;
    private const string UNDEFINED_LANGUAGE = 'und';

    /**
     *
     * @var TranslateClient
     */
    private TranslateClient $client;

    /**
     * @throws GoogleException
     */
    public function __construct()
    {
        $this->client = new TranslateClient([
            'key' => getenv('ROBONOTE_GOOGLE_CLOUD_KEY'),
        ]);
    }

    /**
     *
     * @param string $text
     * @return string
     * @throws ServiceException
     */
    public function detectLanguage(string $text): string
    {
        $result = $this->client->detectLanguage($text);
        $language = strtolower($result['languageCode'] ?? static::UNDEFINED_LANGUAGE);
        if ($language === self::UNDEFINED_LANGUAGE) {
            throw new RuntimeException('Google cloud cannot define language for text "' . $text . '"');
        }
        return $language;
    }

    /**
     *
     * @param string $text
     * @return string
     * @throws ServiceException
     */
    protected function translateText(string $text): string
    {
        $result = $this->client->translate($text);
        return $result['text'] ?? '';
    }

    /**
     * @return int
     */
    public function getMaxTextLength(): int
    {
        return self::MAX_TEXT_LENGTH;
    }

    public function isSupportBatchTranslation(): bool
    {
        return false;
    }

    public function needTextsCombine(): bool
    {
        return false;
    }

    public function translateBatch(array $texts, string $languageCode): array
    {
        throw new RuntimeException('Batch translation is not implemented in Google Cloud driver.');
    }
}
