<?php

declare(strict_types=1);

namespace STTranslation\Service\Drivers;

use GuzzleHttp\Exception\GuzzleException;
use JsonException;
use RuntimeException;
use STApi\Entity\Exception\ThirdPartyApiException;
use STTranslation\Service\Drivers\Interfaces\AlgoClientInterface;
use STTranslation\Service\LanguageDetector;

class AlgoDriver implements DriverInterface
{
    public const string DRIVER_NAME = 'algo';

    public function __construct(
        private readonly AlgoClientInterface $algoClient,
        private readonly LanguageDetector $languageDetector,
    ) {
    }

    /**
     * @throws GuzzleException
     * @throws JsonException
     */
    public function translateBatch(array $texts, string $languageCode): array
    {
        $requestData = [
            'language' => $languageCode,
            'paragraphs' => $texts,
        ];

        $responseData = $this->algoClient->translate($requestData);
        if (!$this->isResponseOk($responseData)) {
            throw new RuntimeException('Unexpected Algo translator response: ' . json_encode($responseData));
        }

        return $responseData['segments'];
    }

    /**
     * @param string $text
     * @return string
     * @throws GuzzleException
     * @throws JsonException
     * @throws ThirdPartyApiException
     */
    public function translate(string $text): string
    {
        $languageCode = $this->languageDetector->detectLanguageByText($text);
        $result = $this->translateBatch([$text], $languageCode);

        return $result ? current($result) : '';
    }

    private function isResponseOk(array $responseData): bool
    {
        if (!array_key_exists('segments', $responseData)) {
            return false;
        }
        if (!is_array($responseData['segments'])) {
            return false;
        }

        return true;
    }

    public function isSupportBatchTranslation(): bool
    {
        return true;
    }

    public function needTextsCombine(): bool
    {
        return false;
    }

    public function getMaxTextLength(): int
    {
        throw new RuntimeException('getMaxTextLength() is not implemented in Algo driver.');
    }
}
