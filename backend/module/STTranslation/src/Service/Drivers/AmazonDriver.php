<?php

declare(strict_types=1);

namespace STTranslation\Service\Drivers;

use Aws\Result;
use Aws\Sdk;
use RuntimeException;
use STApi\Entity\Exception\Translation\AmazonTranslationException;

class AmazonDriver extends AbstractDriver implements DriverInterface
{
    public const string DRIVER_NAME = 'amazon';

    // eu-central-1, because aws translate eu-west-3 doesn't support 'SourceLanguageCode' => 'auto'
    protected const string AWS_REGION = 'eu-central-1';

    /**
     *
     * @var array
     */
    protected array $awsConfig = [];

    /**
     *
     * @var Sdk|null
     */
    protected ?Sdk $awsSdk = null;

    /**
     *
     * @todo Limit is 10k BYTES, but AbstractDriver::translateLongText() based on characters, not bytes
     */
    private const int MAX_TEXT_LENGTH = 4000;

    /**
     * @param array $awsConfig
     * @return AmazonDriver
     */
    public function setAwsConfig(array $awsConfig): AmazonDriver
    {
        $awsConfig['region'] = self::AWS_REGION;
        $this->awsConfig = $awsConfig;
        return $this;
    }
    /**
     *
     * @param string $text
     * @return string
     * @throws AmazonTranslationException
     */
    public function detectLanguage(string $text): string
    {
        $result = $this->getTranslationResult($text);

        if (!$result->hasKey('SourceLanguageCode')) {
            throw new AmazonTranslationException((string) $result);
        }

        return $result->get('SourceLanguageCode');
    }

    /**
     *
     * @param string $text
     * @return string
     * @throws AmazonTranslationException
     */
    protected function translateText(string $text): string
    {
        $result = $this->getTranslationResult($text);

        if (!$result->hasKey('TranslatedText')) {
            throw new AmazonTranslationException((string) $result);
        }

        return $result->get('TranslatedText');
    }

    /**
     * @return int
     */
    public function getMaxTextLength(): int
    {
        return self::MAX_TEXT_LENGTH;
    }

    /**
     * @param string $text
     * @return Result
     */
    protected function getTranslationResult(string $text): Result
    {
        return $this->getAwsSdk()->createTranslate()->translateText([
            'SourceLanguageCode' => 'auto',
            'TargetLanguageCode' => 'en',
            'Text' => $text,
        ]);
    }

    protected function getAwsSdk(): Sdk
    {
        if (is_null($this->awsSdk)) {
            $this->awsSdk = new Sdk($this->awsConfig);
        }
        return $this->awsSdk;
    }

    public function isSupportBatchTranslation(): bool
    {
        return false;
    }

    public function needTextsCombine(): bool
    {
        return false;
    }

    public function translateBatch(array $texts, string $languageCode): array
    {
        throw new RuntimeException('Batch translation is not implemented in Amazon driver.');
    }
}
