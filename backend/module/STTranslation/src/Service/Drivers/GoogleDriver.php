<?php

declare(strict_types=1);

namespace STTranslation\Service\Drivers;

use ErrorException;
use RuntimeException;
use Stichoza\GoogleTranslate\GoogleTranslate;

class GoogleDriver extends AbstractDriver implements DriverInterface
{
    public const string DRIVER_NAME = 'google';
    private const int MAX_TEXT_LENGTH = 2500;

    /**
     *
     * @var GoogleTranslate
     */
    private GoogleTranslate $client;

    public function __construct()
    {
        $this->client = new GoogleTranslate();
    }

    /**
     *
     * @param string $text
     * @return string
     * @throws ErrorException
     */
    public function detectLanguage(string $text): string
    {
        $this->client->setTarget('en');
        $this->client->translate($text);
        return $this->client->getLastDetectedSource();
    }

    /**
     * @param string $text
     * @return string
     * @throws ErrorException
     */
    protected function translateText(string $text): string
    {
        return $this->client->translate($text);
    }

    /**
     * @return int
     */
    public function getMaxTextLength(): int
    {
        return self::MAX_TEXT_LENGTH;
    }

    public function isSupportBatchTranslation(): bool
    {
        return false;
    }

    public function needTextsCombine(): bool
    {
        return true;
    }

    public function translateBatch(array $texts, string $languageCode): array
    {
        throw new RuntimeException('Batch translation is not implemented in Google driver.');
    }
}
