<?php

declare(strict_types=1);

namespace STTranslation\Service\Drivers;

interface DriverInterface
{
    /**
     *
     * @param string $text
     * @return string
     */
    public function translate(string $text): string;

    public function isSupportBatchTranslation(): bool;
    public function needTextsCombine(): bool;

    public function translateBatch(array $texts, string $languageCode): array;

    public function getMaxTextLength(): int;
}
