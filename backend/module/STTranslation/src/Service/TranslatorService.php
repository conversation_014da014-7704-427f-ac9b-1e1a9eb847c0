<?php

declare(strict_types=1);

namespace STTranslation\Service;

use GuzzleHttp\Exception\GuzzleException;
use STApi\Entity\Exception\ThirdPartyApiException;
use STTranslation\Service\Drivers\GoogleDriver;

class TranslatorService
{
    protected const string DEFAULT_LANGUAGE = 'en';

    /**
     * @param TextCombiner $textCombiner
     * @param TranslatorDriverFactory $translatorDriverFactory
     * @param LanguageDetector $languageDetector
     */
    public function __construct(
        private readonly TextCombiner $textCombiner,
        private readonly TranslatorDriverFactory $translatorDriverFactory,
        private readonly LanguageDetector $languageDetector,
    ) {
    }

    /**
     *
     * @param string|null $text
     * @param string $driverName
     * @return string
     * @throws GuzzleException
     * @throws ThirdPartyApiException
     */
    public function translate(?string $text, string $driverName = GoogleDriver::DRIVER_NAME): string
    {
        if (empty($text)) {
            return '';
        }
        if ($this->detectLanguage($text) === 'en') {
            return $text;
        }
        return $this->translatorDriverFactory->createDriver($driverName)->translate($text);
    }

    public function needBatchTranslate(string $driverName): bool
    {
        $driver = $this->translatorDriverFactory->createDriver($driverName);

        if ($driver->isSupportBatchTranslation()) {
            return true;
        }

        if ($driver->needTextsCombine()) {
            return true;
        }

        return false;
    }

    /**
     * @throws GuzzleException
     * @throws ThirdPartyApiException
     */
    public function translateBatch(array $texts, string $driverName, string $languageCode): array
    {
        $driver = $this->translatorDriverFactory->createDriver($driverName);

        if ($driver->isSupportBatchTranslation()) {
            return $driver->translateBatch($texts, $languageCode);
        }

        $combinedTexts = $this->textCombiner->combine($texts, $driver->getMaxTextLength());

        $combinedTranslatedTexts = [];
        foreach ($combinedTexts as $combinedText) {
            $combinedTranslatedTexts[] = $this->translate($combinedText, $driverName);
        }

        return $this->textCombiner->split($combinedTranslatedTexts);
    }

    /**
     * @param string|null $text
     * @return string
     * @throws GuzzleException
     */
    public function detectLanguage(?string $text): string
    {
        if (empty($text)) {
            return static::DEFAULT_LANGUAGE;
        }

        return $this->languageDetector->detectLanguageByText($text);
    }

    /**
     * @param string $content
     * @return string
     * @throws GuzzleException
     */
    public function detectContentLanguage(string $content): string
    {
        return $this->languageDetector->detectLanguageByContent($content);
    }
}
