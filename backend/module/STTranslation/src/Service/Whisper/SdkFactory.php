<?php

declare(strict_types=1);

namespace STTranslation\Service\Whisper;

use Interop\Container\ContainerInterface;
use <PERSON>inas\ServiceManager\Factory\FactoryInterface;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;

class SdkFactory implements FactoryInterface
{
    /**
     * @param ContainerInterface $container
     * @param $requestedName
     * @param array|null $options
     * @return Sdk
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function createService(ContainerInterface $container, $requestedName, array $options = null): Sdk
    {
        return $this($container, $requestedName ?: PatternPluginManager::class, $this->creationOptions);
    }

    /**
     * @param ContainerInterface $container
     * @param $requestedName
     * @param array|null $options
     * @return Sdk
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function __invoke(ContainerInterface $container, $requestedName, array $options = null): Sdk
    {
        $config = $container->get('config');
        $whisperSdkConfig = $config['whisper-sdk'];

        return new Sdk($whisperSdkConfig);
    }
}
