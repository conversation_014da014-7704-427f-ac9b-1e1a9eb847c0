<?php

declare(strict_types=1);

namespace STTranslation\Service\Whisper;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use stdClass;

class Sdk
{
    protected const string DETECT_LANGUAGE_URL = '/detect-language';
    protected const string DETECT_LANGUAGE_BY_TEXT_URL = '/detect-language-by-text';

    /**
     *
     * @var Client
     */
    protected Client $client;

    /**
     *
     * @var string
     */
    protected string $apiPath;

    /**
     *
     * @param array $whisperSdkConfig
     */
    public function __construct(array $whisperSdkConfig)
    {
        if (!isset($whisperSdkConfig['api-path'])) {
            throw new \RuntimeException('Api path must be set');
        }
        $this->apiPath = $whisperSdkConfig['api-path'];
    }

    /**
     * @param string $content
     * @return string
     * @throws GuzzleException
     */
    public function detectLanguage(string $content): string
    {
        $result = $this->call('POST', static::DETECT_LANGUAGE_URL, [
            'content' => base64_encode($content),
        ]);
        return $result->language;
    }

    /**
     *
     * @param string|null $text
     * @return string
     * @throws GuzzleException
     */
    public function detectLanguageByText(?string $text): string
    {
        $result = $this->call('POST', static::DETECT_LANGUAGE_BY_TEXT_URL, [
            'text' => (string) $text,
        ]);
        return $result->language;
    }

    /**
     *
     * @param string $method
     * @param string $url
     * @param array $params
     * @return stdClass
     * @throws GuzzleException
     */
    protected function call(string $method, string $url, array $params = []): stdClass
    {
        $client = $this->getClient();
        $result = match (strtolower($method)) {
            'get' => $client->get($url, [
                'query' => $params,
            ]),
            'post' => $client->post($url, [
                'body' => json_encode($params),
            ]),
        };
        return json_decode($result->getBody()->getContents());
    }

    /**
     *
     * @return Client
     */
    protected function getClient(): Client
    {
        if (!isset($this->client)) {
            $this->client = new Client([
                'base_uri' => $this->apiPath,
            ]);
        }
        return $this->client;
    }
}
