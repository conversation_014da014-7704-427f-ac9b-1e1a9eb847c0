<?php

declare(strict_types=1);

namespace STTranslation\Service;

use Laminas\Filter\Word\DashToCamelCase;
use LogicException;
use STCall\Service\Interfaces\ConfigurationInterface;
use STTranslation\Service\Drivers\AlgoDriver;
use STTranslation\Service\Drivers\AmazonDriver;
use STTranslation\Service\Drivers\DriverInterface;

class TranslatorDriverFactory
{
    private const string DRIVER_NAME_ALGO = 'algo';
    private array $translatorDriversStorage = [];

    public function __construct(
        private readonly ConfigurationInterface $configuration,
        private readonly AlgoDriver $algoDriver,
    ) {
    }

    public function createDriver(string $driverName): DriverInterface
    {
        if (isset($this->translatorDriversStorage[$driverName])) {
            return $this->translatorDriversStorage[$driverName];
        }

        if (self::DRIVER_NAME_ALGO === $driverName) {
            return $this->algoDriver;
        }

        $dashToCamelCase = new DashToCamelCase();
        $namespace = '\STTranslation\Service\Drivers\Factory\\' . $dashToCamelCase->filter(
            $driverName
        ) . 'DriverFactory';

        if (!class_exists($namespace)) {
            throw new LogicException('Can\'t find translation driver "' . $namespace . '"');
        }

        $driver = (new $namespace())->create();

        if ($driver instanceof AmazonDriver) {
            $driver->setAwsConfig($this->configuration->get('aws'));
        }

        $this->translatorDriversStorage[$driverName] = $driver;

        return $driver;
    }
}
