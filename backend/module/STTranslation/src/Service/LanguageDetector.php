<?php

declare(strict_types=1);

namespace STTranslation\Service;

use STApi\Entity\Exception\ThirdPartyApiException;
use STTranslation\Service\Drivers\Interfaces\AlgoClientInterface as TranslationAlgoClientInterface;
use STTranslation\Service\Interfaces\ConfigurationInterface as TranslationConfigurationInterface;
use STTranslation\Service\Whisper\Sdk as WhisperSdk;

class LanguageDetector
{
    public const string PROVIDER_WHISPER = 'whisper';
    public const string PROVIDER_ALGO_API = 'algo_api';

    protected readonly string $provider;

    public function __construct(
        private readonly WhisperSdk $whisperSdk,
        private readonly TranslationAlgoClientInterface $algoClient,
        private readonly TranslationConfigurationInterface $configuration,
    ) {
        $this->setProvider($this->configuration->get('translation')['language_detection_provider']);
    }

    public function detectLanguageByText(?string $text): string
    {
        if (empty($text)) {
            throw new ThirdPartyApiException('Cannot detect language from empty text');
        }

        try {
            $result = match ($this->provider) {
                self::PROVIDER_ALGO_API => $this->algoClient->detectLanguageByText($text),
                default => $this->whisperSdk->detectLanguageByText($text),
            };

            if ($this->provider === self::PROVIDER_ALGO_API) {
                if (is_array($result) && isset($result['results']['language'])) {
                    return $result['results']['language'];
                }

                throw new ThirdPartyApiException('Failed to detect language using algo API');
            }

            return $result;
        } catch (\Exception $e) {
            throw new ThirdPartyApiException('Failed to detect language: ' . $e->getMessage());
        }
    }

    public function detectLanguageByContent(string $content): string
    {
        if (empty($content)) {
            throw new ThirdPartyApiException('Cannot detect language from empty content');
        }

        try {
            $result = match ($this->provider) {
                self::PROVIDER_ALGO_API => $this->algoClient->detectLanguage($content),
                default => $this->whisperSdk->detectLanguage($content),
            };

            if ($this->provider === self::PROVIDER_ALGO_API) {
                if (is_array($result) && isset($result['language'])) {
                    return $result['language'];
                }

                throw new ThirdPartyApiException('Failed to detect language using algo API');
            }

            return $result;
        } catch (\Exception $e) {
            throw new ThirdPartyApiException('Failed to detect language: ' . $e->getMessage());
        }
    }

    private function setProvider(string $provider): void
    {
        $validProviders = [
            self::PROVIDER_WHISPER,
            self::PROVIDER_ALGO_API,
        ];

        if (!in_array($provider, $validProviders)) {
            throw new \InvalidArgumentException(
                sprintf(
                    'Invalid language detection provider: %s. Valid providers are: %s',
                    $provider,
                    implode(', ', $validProviders)
                )
            );
        }

        $this->provider = $provider;
    }
}
