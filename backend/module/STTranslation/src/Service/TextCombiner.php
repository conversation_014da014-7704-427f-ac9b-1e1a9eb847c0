<?php

declare(strict_types=1);

namespace STTranslation\Service;

class Text<PERSON>ombiner
{
    // Please do not change the type of quotes in the value of this constant,
    // otherwise the translation will not work correctly. Quotes must be double.
    private const string DELIMITER = "\n\n\n_ ";

    public function combine(array $texts, int $limit): array
    {
        $result = [];
        $union = '';
        for ($i = 0; $i < count($texts); $i++) {
            $text = $texts[$i];

            if ($i === 0) {
                $union = $text;
                continue;
            }

            $unionLength = strlen($union . self::DELIMITER . $text);

            if ($unionLength > $limit) {
                $result[] = $union;
                $union = $text;
            } else {
                $union .= self::DELIMITER . $text;
            }

            if ($i === count($texts) - 1) {
                $result[] = $union;
            }
        }

        return $result;
    }

    public function split(array $combinedTexts): array
    {
        $texts = [];
        foreach ($combinedTexts as $combinedText) {
            $texts = array_merge($texts, explode(self::DELIMITER, $combinedText));
        }

        return $texts;
    }
}
