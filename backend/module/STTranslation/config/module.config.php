<?php

declare(strict_types=1);

namespace STTranslation;

use STAlgo\Service\Interfaces\TranslatorInterface as AlgoTranslatorInterface;
use STCall\Service\Interfaces\TranslatorInterface as CallTranslatorInterface;
use STTranslation\Controller\Plugin\Translation;
use STTranslation\ServiceProviding\TranslationServiceProvider;

return [
    'controller_plugins' => [
        'invokables' => [
            'translation' => Translation::class,
        ],
    ],
    'service_manager' => [
        'factories' => [
        ],
        'aliases' => [
            CallTranslatorInterface::class => TranslationServiceProvider::class,
            AlgoTranslatorInterface::class => TranslationServiceProvider::class,
        ],
    ],
];
