<?php

declare(strict_types=1);

namespace STRoboTruck;

use <PERSON><PERSON>\ModuleManager\ModuleEvent;
use <PERSON><PERSON>\ModuleManager\ModuleManager;
use STLib\ModuleManager\MultiConfigModule;
use STRabbit\Service\RabbitService;

class Module extends MultiConfigModule
{
    /**
     *
     * @param ModuleManager $moduleManager
     * @return void
     */
    public function init(ModuleManager $moduleManager): void
    {
        $events = $moduleManager->getEventManager();
        $events->attach(ModuleEvent::EVENT_LOAD_MODULES_POST, function (ModuleEvent $moduleEvent) {
            $serviceManager = $moduleEvent->getParam('ServiceManager');
            $rabbit = $serviceManager->get(RabbitService::class);
            Service\Logger\Writer::setRabbit($rabbit);
        });
    }
}
