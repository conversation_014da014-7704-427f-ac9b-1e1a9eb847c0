<?php

declare(strict_types=1);

namespace STRoboTruck;

use Laminas\Di\Container\ServiceManager\AutowireFactory;
use STRoboTruck\Controller\Plugin\RoboTruck;

return [
    'controller_plugins' => [
        'invokables' => [
            'roboTruck' => RoboTruck::class,
        ],
    ],
    'service_manager' => [
        'factories' => [
            Service\DataCollection\DataCollector::class => AutowireFactory::class,
            Service\PusherService::class => AutowireFactory::class,
        ]
    ],
];
