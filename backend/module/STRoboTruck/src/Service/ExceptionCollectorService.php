<?php

namespace STRoboTruck\Service;

use ST<PERSON>ib\IdGenerator\IdGeneratorService;
use STRoboTruck\Service\DataCollection\DataCollector;
use Throwable;

class ExceptionCollectorService
{
    public function __construct(
        private readonly IdGeneratorService $exceptionIdProvider,
        private readonly DataCollector $dataCollector
    ) {
    }

    public function collect(Throwable $throwable, string $exceptionId = null): void
    {
        $exceptionId = $exceptionId ?? $this->exceptionIdProvider->generatePseudoUniqueId();
        $this->dataCollector->collect(
            DataCollector::EVENT_EXCEPTION,
            $throwable::class,
            [
                'exception_id' => $exceptionId,
                'error_message' => $throwable->getMessage(),
                'file' => $throwable->getFile(),
                'line' => $throwable->getLine(),
                'stack_trace' => $throwable->getTrace(),
            ]
        );
    }
}
