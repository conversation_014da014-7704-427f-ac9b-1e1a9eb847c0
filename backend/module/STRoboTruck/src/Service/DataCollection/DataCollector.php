<?php

namespace STRoboTruck\Service\DataCollection;

use PhpAmqpLib\Message\AMQPMessage;
use STLog\Service\LogManager;
use STLog\Service\ProviderLogger;
use STRabbit\Service\RabbitService;
use STRoboTruck\Service\DataMaskService;
use STRoboTruck\Service\PusherService;

class DataCollector
{
    use ProviderLogger;

    public function __construct(
        private readonly LogManager $logManager,
        private readonly RabbitService $rabbit,
        private readonly DataMaskService $dataMaskService,
    ) {
    }

    public const string SOURCE_BACKEND = 'robonote';
    public const string SOURCE_FRONT = 'front';

    private const string LOGGER_NAME = 'robo-truck';
    public const string EVENT_CONTROLLER_API_REQUEST = 'controller_api_request';
    public const string EVENT_EXCEPTION = 'exception';
    public const string EVENT_EXTERNAL_LOG = 'external_log';
    public const string EVENT_ONBOARDING_NOTIFICATION = 'onboarding_notification';
    public const string EVENT_ADD_STEP_RESULTS_TO_WEBHOOKS_QUEUE_FAIL = 'add_step_results_to_webhooks_queue_fail';
    public const string EVENT_ADD_COMPANY_EVENTS_TO_WEBHOOKS_QUEUE_FAIL = 'add_company_events_to_webhooks_queue_fail';
    public const string EVENT_SEND_WEBHOOK_FAIL = 'send_webhook_fail';
    public const string EVENT_CALL_UPLOAD_FILE_DOWNLOAD_STEP_SUCCESS = 'call_upload_file_download_step_success';
    public const string EVENT_CALL_UPLOAD_FILE_DOWNLOAD_STEP_ERROR = 'call_upload_file_download_step_error';
    public const string EVENT_CALL_ANALYZE_STEP_DAEMON = 'call_analyze_step_daemon';
    public const string EVENT_CALL_ANALYZE_TRANSCRIBING_DRIVER = 'call_analyze_transcribing_driver';
    public const string EVENT_CALL_ANALYZE_TRANSCRIBING_STEP_STARTED = 'call_analyze_transcribing_step_started';
    public const string EVENT_CALL_ANALYZE_TRANSCRIBING_STEP_ENDED = 'call_analyze_transcribing_step_ended';
    public const string EVENT_CALL_ANALYZE_TRANSCRIBING_DISTRIBUTION_STEP_SUCCESS = 'call_analyze_transcribing_distribution_step_success';
    public const string EVENT_CALL_ANALYZE_TRANSCRIBING_JOB_CREATION_STEP_SUCCESS = 'call_analyze_transcribing_job_creation_step_success';
    public const string EVENT_CALL_ANALYZE_TRANSLATION_BATCH_FAIL = 'call_analyze_translation_batch_fail';
    public const string EVENT_CALL_UPLOAD_API_REQUEST = 'call_upload_api_request';
    public const string EVENT_CALL_CHECKLIST_RESULT = 'call_checklist_result';
    public const string EVENT_CALL_CHECKLIST_RESULT_FAIL = 'call_checklist_result_fail';
    public const string EVENT_CALL_CLIENT_SUMMARY = 'call_client_summary';
    public const string EVENT_CALL_CLIENT_SUMMARY_MESSAGE_FAIL = 'call_client_summary_message_fail';
    public const string EVENT_CALL_ANALYSIS_NOT_AVAILABLE = 'call_analysis_not_available';

    public function collect(
        string $eventName,
        string $message,
        array $extra,
        string $oldLogName = null,
        $source = self::SOURCE_BACKEND
    ): void {
        if ($oldLogName) {
            $this->logManager->create($oldLogName)->info($message, $extra);
        }

        $extra = $this->dataMaskService->maskSensitiveData($extra);

        $extra['source'] = $source;
        $extra['event_name'] = $eventName;

        $this->logManager->create(self::LOGGER_NAME)->info($message, $extra);
    }

    public function collectRateLimited(
        string $queueName,
        string $eventName,
        string $message,
        array $extra,
        string $oldLogName = null,
        $source = self::SOURCE_BACKEND
    ) {
        $channel = $this->rabbit->getChannel();
        $message = new AMQPMessage($message);
        $channel->basic_publish($message, '', $queueName);
        $channel->close();
    }

    public function collectError(array $events, int $code, string $error): void
    {
        $channel = $this->rabbit->getChannel();
        $bodyData = [
            'events' => $events,
            'error' => 'Status code: ' . $code . ', error message: ' . $error,
        ];
        $body = json_encode($bodyData);
        if (empty($body)) {
            error_log(print_r($bodyData, true));
        }

        $message = new AMQPMessage($body);
        $channel->basic_publish($message, '', PusherService::ROBO_TRUCK_QUEUE_ERROR_NAME);
        $channel->close();
    }
}
