<?php

namespace STRoboTruck\Service;

use Exception;
use Rs\JsonLines\Exception\InvalidJson;
use Rs\JsonLines\Exception\NonTraversable;
use Rs\JsonLines\JsonLines;
use STRoboTruck\Service\DataCollection\DataCollector;
use STRoboTruck\Service\HttpClient\ClientFactory;

class PusherService
{
    public const string ROBO_TRUCK_QUEUE_NAME = 'robo-truck';
    public const string ROBO_TRUCK_QUEUE_ERROR_NAME = 'robo-truck-error';

    public function __construct(
        private readonly ClientFactory $clientFactory,
        private readonly DataCollector $dataCollector
    ) {
    }

    /**
     * @throws InvalidJson
     * @throws NonTraversable
     */
    public function push(array $events): void
    {
        $client = $this->clientFactory->create();
        $client->setRawBody((new JsonLines())->enline($events));
        try {
            $response = $client->send();
            if (!$response->isSuccess()) {
                $this->dataCollector->collectError($events, $response->getStatusCode(), $response->getBody());
            }
        } catch (Exception $e) {
            $this->dataCollector->collectError($events, $e->getCode(), $e->getMessage());
        }
    }
}
