<?php

namespace STRoboTruck\Service;

use Laminas\Http\PhpEnvironment\RemoteAddress;
use <PERSON><PERSON>\Mvc\MvcEvent;
use STCompany\Entity\Company;
use STLib\Expand\Collection;
use STRoboTruck\Service\DataCollection\DataCollector;
use STUser\Entity\User;

class RequestCollectorService
{
    public function __construct(
        private readonly DataCollector $dataCollector,
        private readonly RemoteAddress $remoteAddress,
    ) {
    }

    /**
     * @param string $controllerName
     * @param MvcEvent $event
     * @param Collection $params
     * @param User|null $user
     * @param Company|null $company
     * @return void
     */
    public function collect(
        string $controllerName,
        MvcEvent $event,
        Collection $params,
        ?User $user = null,
        ?Company $company = null,
    ): void {
        $request = $event->getRequest();
        $extra = [
            'controller_name' => $controllerName,
            'action' => $event->getRouteMatch()->getParam('action'),
            'user_id' => $user?->getId(),
            'company_id' => $company?->getId(),
            'parameters' => $params->toArray(),
        ];

        $clientIp = $this->remoteAddress->getIpAddress();

        if (!empty($clientIp)) {
            $extra['client_ip'] = $clientIp;
        }

        $this->dataCollector->collect(
            DataCollector::EVENT_CONTROLLER_API_REQUEST,
            $request->getMethod() . ' request',
            $extra,
        );
    }
}
