<?php

namespace STRoboTruck\Service\HttpClient;

use Laminas\Http\Client;
use STConfiguration\Service\ConfigurationService;

class ClientFactory
{
    public function __construct(private readonly ConfigurationService $configuration)
    {
    }

    public function create(): Client
    {
        $roboTruckConfig = $this->configuration->get('robo-truck');

        $client = new Client();
        $client->setUri($roboTruckConfig['url']);
        $client->setHeaders([
            'Content-Type' => 'application/json',
            'x-api-key' => $roboTruckConfig['api-key'],
        ]);
        $client->setMethod('POST');

        return $client;
    }
}
