<?php

declare(strict_types=1);

namespace STRoboTruck\Service;

class DataMaskService
{
    /**
     * @param array $data
     * @return array
     */
    public function maskSensitiveData(array $data): array
    {
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                foreach ($value as $innerKey => $innerValue) {
                    $data[$key][$innerKey] = $this->maskValue($innerKey, $innerValue);
                }
            } else {
                $data[$key] = $this->maskValue($key, $value);
            }
        }

        return $data;
    }

    /**
     * @param mixed $key
     * @param mixed $value
     * @return string
     */
    private function maskValue(mixed $key, mixed $value): mixed
    {
        if (!is_string($value) || !is_string($key)) {
            return $value;
        }

        if ($key === 'password' || $key === 'password-confirm') {
            return str_repeat('*', strlen($value));
        }

        if (filter_var($value, FILTER_VALIDATE_EMAIL)) {
            $emailParts = explode('@', $value);

            return str_repeat('*', strlen($emailParts[0])) . '@' . $emailParts[1];
        }

        return $value;
    }
}
