<?php

namespace STRoboTruck\Service;

use STRoboTruck\Service\DataCollection\DataCollector;
use STUser\Entity\User;

class ExternalLogCollectorService
{
    public function __construct(private readonly DataCollector $dataCollector)
    {
    }

    public function collect(string $source, array $params, ?User $user = null): void
    {
        $this->dataCollector->collect(
            DataCollector::EVENT_EXTERNAL_LOG,
            'External log',
            [
                'user_id' => $user?->getId(),
                'parameters' => $params,
            ],
            source: $source
        );
    }
}
