<?php

declare(strict_types=1);

namespace STRoboTruck\Controller\Plugin;

use Api\Controller\V0\BaseController;
use <PERSON>inas\Mvc\Controller\Plugin\AbstractPlugin;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use ST<PERSON><PERSON>\IdGenerator\IdGeneratorService;
use STRoboTruck\Service\ExceptionCollectorService;
use STRoboTruck\Service\ExternalLogCollectorService;
use STRoboTruck\Service\PusherService;
use STRoboTruck\Service\RequestCollectorService;

/**
 * @method BaseController getController()
 */
class RoboTruck extends AbstractPlugin
{
    /**
     * @return PusherService
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function pusher(): PusherService
    {
        return $this->getController()->getServiceManager()->get(PusherService::class);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function requestCollector(): RequestCollectorService
    {
        return $this->getController()->getServiceManager()->get(RequestCollectorService::class);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function exceptionCollector(): ExceptionCollectorService
    {
        return $this->getController()->getServiceManager()->get(ExceptionCollectorService::class);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function exceptionIdGenerator(): IdGeneratorService
    {
        return $this->getController()->getServiceManager()->get(IdGeneratorService::class);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function externalLogCollector(): ExternalLogCollectorService
    {
        return $this->getController()->getServiceManager()->get(ExternalLogCollectorService::class);
    }
}
