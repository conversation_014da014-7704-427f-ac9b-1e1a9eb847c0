<?php

namespace STRoboTruck\Validator;

use <PERSON><PERSON>\Validator\InArray;
use Lam<PERSON>\Validator\NotEmpty;
use STLib\Validator\Validator;
use STRoboTruck\Service\DataCollection\DataCollector;

class RoboTruckValidator extends Validator
{
    protected const INCORRECT_SOURCE = 'Incorrect source. Available sources is: front.';
    protected const INCORRECT_PARAMETERS = 'Incorrect parameters. Parameters should be non empty array.';

    public function run(): void
    {
        /** @var array $input */
        $input = $this->getInstance();

        $source = $input['source'] ?? null;
        $sourceValidator = new InArray([
            'haystack' => [DataCollector::SOURCE_BACKEND, DataCollector::SOURCE_FRONT],
            'strict' => true,
        ]);
        if (!$sourceValidator->isValid($source)) {
            $this->addError('source', static::INCORRECT_SOURCE);
        }

        $parameters = $input['parameters'] ?? null;
        $nameValidator = new NotEmpty();
        if (!$nameValidator->isValid($parameters)) {
            $this->addError('parameters', self::INCORRECT_PARAMETERS);
        }
    }
}
