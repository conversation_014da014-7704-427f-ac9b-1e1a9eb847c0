<?php

declare(strict_types=1);

namespace STRedis\Controller\Plugin;

use Laminas\Mvc\Controller\Plugin\AbstractPlugin;

class Redis extends AbstractPlugin
{
    /**
     *
     * @param string $name
     * @param array $arguments
     * @return mixed
     */
    public function __call(string $name, array $arguments): mixed
    {
        $service = $this->getController()->getServiceManager()->get(\Predis\Client::class);
        return call_user_func_array([
            $service,
            $name
        ], $arguments);
    }
}
