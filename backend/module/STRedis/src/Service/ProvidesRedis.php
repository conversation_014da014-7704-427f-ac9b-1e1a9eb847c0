<?php

declare(strict_types=1);

namespace STRedis\Service;

trait ProvidesRedis
{
    /**
     *
     * @var RedisWrapper|null
     */
    private ?RedisWrapper $redisWrapper = null;

    /**
     *
     * @return \Predis\Client
     */
    protected function redis(): \Predis\Client
    {
        if (is_null($this->redisWrapper)) {
            $this->redisWrapper = new RedisWrapper();
        }
        return $this->redisWrapper->getStaticRedisClient();
    }
}
