<?php

declare(strict_types=1);

namespace STRedis\Service;

class RedisClientFactory implements \Laminas\ServiceManager\Factory\FactoryInterface
{
    /**
     *
     * @param \Interop\Container\ContainerInterface $container
     * @param string $requestedName
     * @param array $options
     * @return \Predis\Client
     */
    public function createService(\Interop\Container\ContainerInterface $container, $requestedName, array $options = null): \Predis\Client
    {
        return $this($container, $requestedName ?: PatternPluginManager::class, $this->creationOptions);
    }

    /**
     *
     * @param \Interop\Container\ContainerInterface $container
     * @param string $requestedName
     * @param array $options
     * @return \Predis\Client|null
     */
    public function __invoke(\Interop\Container\ContainerInterface $container, $requestedName, array $options = null): ?\Predis\Client
    {
        $config = $container->get('config');
        $settings = [];
        if (isset($config['predis_settings']) && is_array($config['predis_settings'])) {
            $settings = $config['predis_settings'];
            return new \Predis\Client($settings);
        }
        return null;
    }
}
