<?php

declare(strict_types=1);

namespace STRedis\Service;

use Predis\Response\Status;

class RedisService
{
    use ProvidesRedis;

    public function get(string $key): ?string
    {
        return $this->redis()->get($key);
    }

    public function delete(string ...$keys): int
    {
        return $this->redis()->del(...$keys);
    }

    public function set(string $key, $value, $expireResolution = null, $expireTTL = null, $flag = null): ?Status
    {
        return $this->redis()->set($key, $value, $expireResolution, $expireTTL, $flag);
    }

    public function expire(string $key, int $seconds): int
    {
        return $this->redis()->expire($key, $seconds);
    }

    public function incr(string $key): int
    {
        return $this->redis()->incr($key);
    }
}
