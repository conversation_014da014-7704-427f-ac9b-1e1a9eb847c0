<?php

declare(strict_types=1);

namespace STRedis\Service;

final class RedisWrapper
{
    /**
     *
     * @var \Predis\Client
     */
    private static ?\Predis\Client $staticRedisClient = null;

    /**
     *
     * @param \Predis\Client $redisClient
     * @return void
     */
    public static function setStaticRedisClient(\Predis\Client $redisClient): void
    {
        self::$staticRedisClient = $redisClient;
    }

    /**
     *
     * @return \Predis\Client
     */
    public function getStaticRedisClient(): \Predis\Client
    {
        return self::$staticRedisClient;
    }
}
