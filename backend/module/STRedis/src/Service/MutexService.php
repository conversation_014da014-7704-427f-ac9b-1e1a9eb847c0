<?php

namespace STRedis\Service;

use Symfony\Component\Lock\LockFactory;
use Symfony\Component\Lock\LockInterface;
use Symfony\Component\Lock\Store\RedisStore;

class MutexService
{
    use ProvidesRedis;

    public function getLock(string $name): LockInterface
    {
        $store = new RedisStore($this->redis());
        $factory = new LockFactory($store);

        return $factory->createLock($name);
    }
}
