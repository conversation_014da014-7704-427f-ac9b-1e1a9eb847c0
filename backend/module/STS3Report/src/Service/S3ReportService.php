<?php

declare(strict_types=1);

namespace STS3Report\Service;

class S3ReportService
{
    /**
     *
     * @var \STS3Report\Data\S3ReportRepository
     */
    protected \STS3Report\Data\S3ReportRepository $s3ReportRepository;

    /**
     *
     * @param \STS3Report\Data\S3ReportRepository $s3ReportRepository
     */
    public function __construct(
        \STS3Report\Data\S3ReportRepository $s3ReportRepository,
    ) {
        $this->s3ReportRepository = $s3ReportRepository;
    }

    /**
     *
     * @param int $companyId
     * @param string $companyEncryptionKey
     * @param \Carbon\Carbon $callDate
     * @return array
     */
    public function getParagraphsReport(int $companyId, string $companyEncryptionKey, \Carbon\Carbon $callDate): array
    {
        return $this->s3ReportRepository->getParagraphsReport($companyId, $callDate, $companyEncryptionKey);
    }

    /**
     *
     * @param int $companyId
     * @param \Carbon\Carbon $callDate
     * @return array
     */
    public function getEventsReport(
        int $companyId,
        \Carbon\Carbon $callDate,
    ): array {
        return $this->s3ReportRepository->getEventsReport($companyId, $callDate);
    }
}
