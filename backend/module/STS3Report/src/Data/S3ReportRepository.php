<?php

declare(strict_types=1);

namespace STS3Report\Data;

use Carbon\Carbon;

class S3ReportRepository extends \STClickhouse\Entity\BaseTable
{
    use \STClickhouse\Data\QueriesTrait;

    /**
     *
     * @param int $companyId
     * @param Carbon $callDate
     * @param string $key
     * @return array
     */
    public function getParagraphsReport(int $companyId, Carbon $callDate, string $key): array
    {
        $sql = '
            SELECT
                call_id,
                paragraph_number,
                start_time,
                speaker_role,
                decrypt(\'aes-256-ofb\', text, \'' . $key . '\') text,
                decrypt(\'aes-256-ofb\', en_text, \'' . $key . '\') en_text
            FROM
            (
                ' .
                // phpcs:disable
                $this->getFinalTableSqlUsingGroupBy(
                    'calls_paragraphs',
                    [
                        'company_id',
                        'call_id',
                        'paragraph_number',
                    ],
                    'created',
                    [
                        'speaker_role',
                        'start_time',
                        'text',
                        'en_text',
                    ],
                    [
                        'company_id' => $companyId,
                        [
                            'type' => 'expression',
                            'value' => '
                                call_id IN
                                (
                                    SELECT DISTINCT
                                        call_id
                                    FROM
                                        calls
                                    WHERE
                                        company_id = ' . $companyId . '
                                        AND call_time >= toDateTime(\'' . $callDate->startOfDay() . '\')
                                        AND call_time <= toDateTime(\'' . $callDate->endOfDay() . '\')
                                )
                            ',
                        ],
                    ]
                )
                // phpcs:enable
                . '
            )
            ORDER BY
                call_id,
                paragraph_number
        ';
        return $this->getClient()->selectAll($sql);
    }

    /**
     *
     * @param int $companyId
     * @param Carbon $callDate
     * @return array
     */
    public function getEventsReport(
        int $companyId,
        Carbon $callDate,
    ): array {
        $sql = <<<SQL
            SELECT 
                pce.call_id call_id, 
                paragraph paragraph_number, 
                event_name, 
                event_highlight,
                event_en_highlight
            FROM (
                {$this->getFinalTableSqlUsingGroupBy(
                'precalculated_calls_events',
                [
                    'company_id',
                    'role_id',
                    'call_id',
                    'paragraph',
                    'event_id',
                ],
                'created',
                [
                    'event_highlight',
                    'event_en_highlight',
                    'event_name',
                    'event_is_deleted',
                ],
                [
                    [
                        'type' => 'expression',
                        'value' => '
                            call_id IN
                            (
                                SELECT DISTINCT
                                    call_id
                                FROM
                                    calls
                                WHERE
                                    company_id = ' . $companyId . '
                                    AND call_time >= toDateTime(\'' . $callDate->startOfDay() . '\')
                                    AND call_time <= toDateTime(\'' . $callDate->endOfDay() . '\')
                            )
                        ',
                    ],
                    'company_id' => $companyId,
                ]
            )}) pce
            WHERE pce.event_is_deleted = 0
        SQL;

        return $this->getClient()->selectAll($sql);
    }
}
