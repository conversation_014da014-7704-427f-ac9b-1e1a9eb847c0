<?php

declare(strict_types=1);

namespace STS3Report\Daemon;

use STCall\Service\AwsTrait;
use STCompany\Service\CompanyService;
use STS3Report\Service\S3ReportService;

class S3ReportDaemon extends \STRabbit\Entity\AbstractDaemon
{
    use AwsTrait;
    use \STLib\Expand\CsvConverter;

    public const QUEUE = 's3-report';
    public const QUEUE_ERROR = 's3-report-error';

    /**
     *
     * @param string $message
     * @return void
     * @throws \Exception
     */
    public function handle(string $message): void
    {
        $data = json_decode($message);

        $awsConfig = $this->params()->offsetGet('aws_config');
        /** @var CompanyService $companyService */
        $companyService = $this->params()->offsetGet(CompanyService::class);
        /** @var S3ReportService $s3ReportService */
        $s3ReportService = $this->params()->offsetGet(S3ReportService::class);

        $company = $companyService->getCompany((int) $data->company_id);

        $this->setAwsConfig($awsConfig['api']);
        $this->setEnv($awsConfig['env']);
        $this->setCompany($company);

        $callDate = \Carbon\Carbon::parse($data->date);

        $paragraphs = $s3ReportService->getParagraphsReport($company->getId(), $company->getEncryptionKey(), $callDate);
        $events = $s3ReportService->getEventsReport($company->getId(), $callDate);

        if (!empty($paragraphs)) {
            $this->saveFileToBucket(
                $this->convertArrayToCsv($paragraphs),
                $this->getReportFileS3Path('transcribing', $data->date),
                $company->getAwsS3ReportBucketName(),
                $company->getAwsS3ReportBucketRegion()
            );
        }

        if (!empty($events)) {
            $this->saveFileToBucket(
                $this->convertArrayToCsv($events),
                $this->getReportFileS3Path('events', $data->date),
                $company->getAwsS3ReportBucketName(),
                $company->getAwsS3ReportBucketRegion()
            );
        }
    }

    /**
     * @param string $reportType
     * @param string $date
     * @return string
     */
    private function getReportFileS3Path(string $reportType, string $date): string
    {
        return sprintf(
            'report/%s/%s.csv',
            $reportType,
            $date
        );
    }
}
