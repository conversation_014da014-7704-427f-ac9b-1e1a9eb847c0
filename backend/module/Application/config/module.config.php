<?php

declare(strict_types=1);

namespace Application;

return [
    'router' => [
        'routes' => [
            'index' => [
                'type' => \Laminas\Router\Http\Literal::class,
                'options' => [
                    'route' => '/',
                    'defaults' => [
                        'controller' => Controller\IndexController::class,
                        'action' => 'index',
                    ],
                ],
            ],
            'get-user-avatar' => [
                'type' => \Laminas\Router\Http\Regex::class,
                'options' => [
                    'regex' => '/user/avatar/([\d]*)/(?<user_id>[\d]*).png',
                    'spec' => '/user/avatar/%user_id%.png',
                    'defaults' => [
                        'controller' => Controller\IndexController::class,
                        'action' => 'get-user-avatar',
                    ],
                ],
            ],
            'get-company-avatar' => [
                'type' => \Laminas\Router\Http\Regex::class,
                'options' => [
                    'regex' => '/company/avatar/(?<company_id>[\d]*).png',
                    'spec' => '/company/avatar/%user_id%.png',
                    'defaults' => [
                        'controller' => Controller\IndexController::class,
                        'action' => 'get-company-avatar',
                    ],
                ],
            ],
            'health-check' => [
                'type' => \Laminas\Router\Http\Segment::class,
                'options' => [
                    'route' => '/health-check[/]',
                ],
                'child_routes' => [
                    'get' => [
                        'type' => \Laminas\Router\Http\Method::class,
                        'options' => [
                            'verb' => 'get',
                            'defaults' => [
                                'controller' => Controller\IndexController::class,
                                'action' => 'get-health-check',
                            ],
                        ],
                    ],
                ],
            ],
        ],
    ],
    'controllers' => [
        'factories' => [
            Controller\IndexController::class => \STLib\Mvc\Controller\ControllerFactory::class,
        ],
    ],
    'view_manager' => [
        'display_not_found_reason' => true,
        'display_exceptions' => true,
        'doctype' => 'HTML5',
        'not_found_template' => 'error/404',
        'exception_template' => 'error/index',
        'template_map' => [
            'layout/layout' => getcwd() . '/template/layout/application.phtml',
            'error/404' => getcwd() . '/template/error/404.phtml',
            'error/index' => getcwd() . '/template/error/index.phtml',
        ],
        'template_path_stack' => [
            getcwd() . '/template',
        ],
    ],
];
