<?php

declare(strict_types=1);

namespace Application\Controller;

class BaseController extends \STLib\Mvc\Controller\AbstractController
{
    /**
     *
     * @param array $vars
     * @return \Laminas\View\Model\ViewModel
     */
    protected function output($data = [], int $statusCode = \Laminas\Http\Response::STATUS_CODE_200, $format = self::JSON_FORMAT, ?array $options = []): \Laminas\Http\Response
    {
        $defaultVars = [

        ];
        return parent::output(array_merge($defaultVars, $data));
    }
}
