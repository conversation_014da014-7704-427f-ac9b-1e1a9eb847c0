<?php

declare(strict_types=1);

namespace Application\Controller;

class IndexController extends BaseController
{
    public function indexAction()
    {
        exit;
    }

    /**
     *
     * @return \Laminas\Http\Response
     */
    public function getHealthCheckAction(): \Laminas\Http\Response
    {
        $this
            ->getResponse()
            ->setContent(json_encode([
                'status' => 'available',
            ]))
            ->getHeaders()
            ->addHeaderLine('Content-Type: application/json');
        return $this->getResponse();
    }

    /**
     *
     * @return \Laminas\Http\Response
     */
    public function getUserAvatarAction(): \Laminas\Http\Response
    {
        $userId = (int) $this->getServiceManager()->get('application')->getMvcEvent()->getRouteMatch()->getParam('user_id');
        $user = $this->user()->getUserById($userId);
        $userAvatarFile = $this->user()->avatar()->saveAvatar($user);
        $avatar = file_get_contents($userAvatarFile);
        $this
                ->getResponse()
                ->setContent($avatar)
                ->getHeaders()
                ->addHeaderLine('Content-Type: image/png')
                ->addHeaderLine('Pragma: no-cache')
                ->addHeaderLine('Cache-Control: no-cache')
                ->addHeaderLine('Pragma-Directive: no-cache')
                ->addHeaderLine('Cache-Directive: no-cache')
                ->addHeaderLine('Expires: 0');
        return $this->getResponse();
    }

    /**
     *
     * @return \Laminas\Http\Response
     */
    public function getCompanyAvatarAction(): \Laminas\Http\Response
    {
        $companyId = (int) $this->getServiceManager()->get('application')->getMvcEvent()->getRouteMatch()->getParam('company_id');
        $company = $this->company()->getCompany($companyId);
        $companyAvatarFile = $this->company()->avatar()->saveAvatar($company);
        $avatar = file_get_contents($companyAvatarFile);
        $this
                ->getResponse()
                ->setContent($avatar)
                ->getHeaders()
                ->addHeaderLine('Content-Type: image/png')
                ->addHeaderLine('Pragma: no-cache')
                ->addHeaderLine('Cache-Control: no-cache')
                ->addHeaderLine('Pragma-Directive: no-cache')
                ->addHeaderLine('Cache-Directive: no-cache')
                ->addHeaderLine('Expires: 0');
        return $this->getResponse();
    }
}
