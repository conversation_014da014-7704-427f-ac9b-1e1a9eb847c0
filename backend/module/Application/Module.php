<?php

declare(strict_types=1);

namespace Application;

use <PERSON>inas\Mvc\MvcEvent;
use Laminas\Mvc\ModuleRouteListener;

class Module extends \STLib\ModuleManager\MultiConfigModule
{
    /**
     *
     * @param MvcEvent $event
     * @return void
     */
    public function onBootstrap(MvcEvent $event): void
    {
        $eventManager = $event->getApplication()->getEventManager();
        $moduleRouteListener = new ModuleRouteListener();
        $moduleRouteListener->attach($eventManager);

        if ($event->getResponse() instanceof \Laminas\Http\PhpEnvironment\Response) {
            $config = $event->getApplication()->getServiceManager()->get('config');
            $accessControlAllowOriginDomains = $config['api']['cors']['access-control-allow-origin'] ?? [];
            $referer = $event->getRequest()->getHeaders()->has('referer') ? $event->getRequest()->getHeaders()->get('referer')->getFieldValue() : null;
            if (!is_null($referer)) {
                $refererUri = new \Laminas\Uri\Uri($referer);
                $refererUri->setFragment(null);
                $refererUri->setPath(null);
                $refererUri->setQuery(null);
                $refererUri->setUserInfo(null);
                if (in_array($refererUri->toString(), $accessControlAllowOriginDomains)) {
                    $event
                            ->getResponse()
                            ->getHeaders()
                            ->addHeaderLine('Access-Control-Allow-Origin', $refererUri->toString())
                            ->addHeaderLine('Access-Control-Allow-Credentials', 'true');
                }
            }
        }
    }
}
