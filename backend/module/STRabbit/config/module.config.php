<?php

declare(strict_types=1);

namespace STRabbit;

return [
    'controller_plugins' => [
        'invokables' => [
            'rabbit' => Controller\Plugin\Rabbit::class,
        ],
    ],
    'service_manager' => [
        'aliases' => [
            'rabbit' => Service\RabbitService::class,
        ],
        'factories' => [
            Service\RabbitService::class => Service\RabbitServiceFactory::class,
            Service\DaemonService::class => \STLib\Mvc\DependencyInjection\DefaultFactory::class,
        ]
    ]
];
