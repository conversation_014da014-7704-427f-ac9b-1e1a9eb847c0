<?php

declare(strict_types=1);

namespace STRabbit\Service;

use PhpAmqpLib\Message\AMQPMessage;
use PhpAmqpLib\Wire\AMQPTable;
use STRabbit\Entity\AbstractDaemon;
use STRoboTruck\Service\ExceptionCollectorService;
use Throwable;

class DaemonService
{
    use ProvidesRabbit;

    public function __construct(private readonly ExceptionCollectorService $exceptionCollectorService)
    {
    }

    /**
     *
     * @var AbstractDaemon|null
     */
    protected ?AbstractDaemon $daemon = null;

    /**
     *
     * @return AbstractDaemon
     */
    public function getDaemon(): AbstractDaemon
    {
        return $this->daemon;
    }

    /**
     *
     * @param AbstractDaemon $daemon
     * @return DaemonService
     */
    public function setDaemon(AbstractDaemon $daemon): DaemonService
    {
        $this->daemon = $daemon;
        return $this;
    }

    /**
     *
     * @param AbstractDaemon $daemon
     * @return void
     */
    public function run(AbstractDaemon $daemon): void
    {
        $queue = $daemon->getQueueName();
        $channel = $this->rabbit()->getChannel();
        $daemon->setChannel($channel);
        $daemon->init();
        $channel->basic_qos(null, 1, false);
        $channel->basic_consume(
            $queue,
            '',
            false,
            false,
            false,
            false,
            function ($message) use ($channel, $daemon) {
                // main rabbit consume callback
                $data = json_decode($message->body);

                try {
                    $daemon->handle($message->body);
                    $channel->basic_ack($message->delivery_info['delivery_tag']);
                } catch (Throwable $e) {
                    $data->attempt = $data->attempt ?? 0;
                    $channel->basic_ack($message->delivery_info['delivery_tag']);
                    if (++$data->attempt < $daemon->getAttemptsNumber() && !$daemon->doNotMakeNextAttempt()) {
                        $message = new AMQPMessage(json_encode($data), [
                            'application_headers' => new AMQPTable([
                                'x-delay' => $daemon->getDelay(),
                            ]),
                        ]);
                        $channel->basic_publish($message, $daemon->getExchangeName(), $daemon->getQueueName());
                    } elseif (!$daemon->doNotAddToErrorQueue()) {
                        $error = [
                            'code' => $e->getCode(),
                            'message' => $e->getMessage(),
                            'file' => $e->getFile(),
                            'line' => $e->getLine(),
                        ];

                        $message = new AMQPMessage(
                            json_encode(
                                array_merge(['error' => $error], (array) $data)
                            ),
                        );
                        $channel->basic_publish($message, '', $daemon->getErrorQueueName());
                    }
                    $daemon->catch();
                    if (!($e instanceof Daemon\NotThrowableException)) {
                        throw $e;
                    }
                } finally {
                    $daemon->finally();
                }
            }
        );

        while (count($channel->callbacks)) {
            $channel->wait(timeout: 30 * 60);
        }
    }
}
