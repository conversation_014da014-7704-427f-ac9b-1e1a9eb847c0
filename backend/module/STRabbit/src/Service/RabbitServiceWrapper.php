<?php

declare(strict_types=1);

namespace STRabbit\Service;

final class RabbitServiceWrapper
{
    /**
     *
     * @var \STRabbit\Service\RabbitService
     */
    private static ?\STRabbit\Service\RabbitService $staticRabbitService = null;

    /**
     *
     * @param \STRabbit\Service\RabbitService $rabbitService
     * @return void
     */
    public static function setStaticRabbitService(\STRabbit\Service\RabbitService $rabbitService): void
    {
        self::$staticRabbitService = $rabbitService;
    }

    /**
     *
     * @return \STRabbit\Service\RabbitService|null
     */
    public function getStaticRabbitService(): ?\STRabbit\Service\RabbitService
    {
        return self::$staticRabbitService;
    }
}
