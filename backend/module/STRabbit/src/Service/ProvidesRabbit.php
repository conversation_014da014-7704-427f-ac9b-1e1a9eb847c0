<?php

declare(strict_types=1);

namespace STRabbit\Service;

trait ProvidesRabbit
{
    /**
     *
     * @var RabbitServiceWrapper
     */
    private ?RabbitServiceWrapper $rabbitServiceWrapper = null;

    /**
     *
     * @return \STRabbit\Service\RabbitService|null
     */
    protected function rabbit(): ?\STRabbit\Service\RabbitService
    {
        if (is_null($this->rabbitServiceWrapper)) {
            $this->rabbitServiceWrapper = new RabbitServiceWrapper();
        }
        return $this->rabbitServiceWrapper->getStaticRabbitService();
    }
}
