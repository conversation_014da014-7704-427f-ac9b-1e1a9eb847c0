<?php

declare(strict_types=1);

namespace STRabbit\Service;

class RabbitServiceFactory implements \Laminas\ServiceManager\Factory\FactoryInterface
{
    /**
     *
     * @param \Interop\Container\ContainerInterface $container
     * @param string $requestedName
     * @param array $options
     * @return RabbitService
     */
    public function createService(\Interop\Container\ContainerInterface $container, $requestedName, array $options = null): RabbitService
    {
        return $this($container, $requestedName ?: PatternPluginManager::class, $this->creationOptions);
    }

    /**
     *
     * @param \Interop\Container\ContainerInterface $container
     * @param string $requestedName
     * @param array $options
     * @return RabbitService|null
     */
    public function __invoke(\Interop\Container\ContainerInterface $container, $requestedName, array $options = null): ?RabbitService
    {
        $config = $container->get('config');
        if (isset($config['rabbit']['connection'])) {
            $service = new RabbitService($config['rabbit']['connection']);
            return $service;
        }
        return null;
    }
}
