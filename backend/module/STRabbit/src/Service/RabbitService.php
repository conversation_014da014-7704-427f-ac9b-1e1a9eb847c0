<?php

declare(strict_types=1);

namespace STRabbit\Service;

class RabbitService
{
    /**
     *
     * @var array
     */
    private array $connectionSettings = [];

    /**
     *
     * @var \PhpAmqpLib\Connection\AMQPStreamConnection
     */
    private ?\PhpAmqpLib\Connection\AMQPStreamConnection $connection = null;

    /**
     *
     * @param array $connectionSettings
     */
    public function __construct(array $connectionSettings)
    {
        $this->connectionSettings = $connectionSettings;
    }

    /**
     *
     * @param string $channelId
     * @return \PhpAmqpLib\Channel\AMQPChannel
     */
    public function getChannel(string $channelId = null): \PhpAmqpLib\Channel\AMQPChannel
    {
        if (!($this->connection instanceof \PhpAmqpLib\Connection\AMQPStreamConnection)) {
            $this->initConnection();
        }
        return $this->connection->channel($channelId);
    }

    /**
     *
     * @return \PhpAmqpLib\Connection\AMQPStreamConnection
     * @throws \OutOfRangeException
     */
    public function initConnection(): \PhpAmqpLib\Connection\AMQPStreamConnection
    {
        if (
                !isset($this->connectionSettings['host'])
                || !isset($this->connectionSettings['port'])
                || !isset($this->connectionSettings['user'])
                || !isset($this->connectionSettings['password'])
        ) {
            throw new \OutOfRangeException('RabbitMQ connection settings host, port, user, password are required');
        }
        $this->connection = new \PhpAmqpLib\Connection\AMQPStreamConnection($this->connectionSettings['host'], $this->connectionSettings['port'], $this->connectionSettings['user'], $this->connectionSettings['password']);
        return $this->connection;
    }

    /**
     *
     * @return void
     */
    public function closeConnection(): void
    {
        if ($this->connection instanceof \PhpAmqpLib\Connection\AMQPStreamConnection) {
            $this->connection->close();
        }
    }
}
