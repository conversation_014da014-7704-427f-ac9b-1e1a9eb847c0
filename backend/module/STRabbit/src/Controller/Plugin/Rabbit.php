<?php

declare(strict_types=1);

namespace STRabbit\Controller\Plugin;

use Laminas\Mvc\Controller\Plugin\AbstractPlugin;

class Rabbit extends AbstractPlugin
{
    /**
     *
     * @param string $name
     * @param array $arguments
     * @return mixed
     * @throws \BadMethodCallException
     */
    public function __call(string $name, array $arguments): mixed
    {
        $rabbitService = $this->getController()->getServiceManager()->get(\STRabbit\Service\RabbitService::class);
        if (!method_exists($rabbitService, $name)) {
            throw new \BadMethodCallException('Invalid RabbitService method: ' . $name);
        }
        return call_user_func_array([
            $rabbitService,
            $name
        ], $arguments);
    }

    /**
     *
     * @return \STRabbit\Service\DaemonService
     */
    public function daemon(): \STRabbit\Service\DaemonService
    {
        return $this->getController()->getServiceManager()->get(\STRabbit\Service\DaemonService::class);
    }
}
