<?php

declare(strict_types=1);

namespace STApi\Entity\Exception;

class ThirdPartyApiException extends BaseApiException implements ErrorTypeProviderInterface
{
    public const string ERROR_TYPE = 'third-party-api-error';

    /**
     *
     * @var int
     */
    protected $code = \Laminas\Http\Response::STATUS_CODE_500;

    /**
     *
     * @var string
     */
    protected $message = 'Unexpected response from 3rd party API';

    public function getErrorType(): string
    {
        return self::ERROR_TYPE;
    }
}
