<?php

declare(strict_types=1);

namespace STApi\Entity\Exception\CallUpload;

use STApi\Entity\Exception\ErrorTypeProviderInterface;
use STApi\Entity\Exception\ValidationApiException;

class InvalidContentException extends ValidationApiException implements ErrorTypeProviderInterface
{
    /**
     *
     * @var string
     */
    protected $message = 'Uploaded file has invalid content';

    public function getErrorType(): string
    {
        return 'file-invalid-content';
    }
}
