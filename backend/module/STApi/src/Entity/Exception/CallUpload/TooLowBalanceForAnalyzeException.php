<?php

declare(strict_types=1);

namespace STApi\Entity\Exception\CallUpload;

use STApi\Entity\Exception\ErrorTypeProviderInterface;
use STRabbit\Service\Daemon\NotThrowableException;

class TooLowBalanceForAnalyzeException extends NotThrowableException implements ErrorTypeProviderInterface
{
    /**
     *
     * @var string
     */
    protected $message = 'Too low balance for analyze';

    public function getErrorType(): string
    {
        return 'too-low-balance-for-analyze';
    }
}
