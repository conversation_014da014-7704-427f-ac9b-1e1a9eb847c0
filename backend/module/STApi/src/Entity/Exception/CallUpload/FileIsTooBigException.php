<?php

declare(strict_types=1);

namespace STApi\Entity\Exception\CallUpload;

use STApi\Entity\Exception\ErrorTypeProviderInterface;
use STApi\Entity\Exception\ValidationApiException;

class FileIsTooBigException extends ValidationApiException implements ErrorTypeProviderInterface
{
    protected $message = 'The file is too big.';

    public function getErrorType(): string
    {
        return 'file-is-too-big';
    }
}
