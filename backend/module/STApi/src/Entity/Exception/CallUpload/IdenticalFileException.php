<?php

declare(strict_types=1);

namespace STApi\Entity\Exception\CallUpload;

use STApi\Entity\Exception\ErrorTypeProviderInterface;
use STApi\Entity\Exception\ValidationApiException;

class IdenticalFileException extends ValidationApiException implements ErrorTypeProviderInterface
{
    /**
     *
     * @var string
     */
    protected $message = 'An identical file has been already added';

    public function getErrorType(): string
    {
        return 'identical-file-already-added';
    }
}
