<?php

declare(strict_types=1);

namespace STApi\Entity\Exception;

class NoAccessToFileApiException extends BaseApiException implements ErrorTypeProviderInterface
{
    public const string ERROR_TYPE = 'no-access-to-file';

    protected $code = \Laminas\Http\Response::STATUS_CODE_422;
    protected $message = 'There is no access to file';

    public function getErrorType(): string
    {
        return self::ERROR_TYPE;
    }
}
