<?php

declare(strict_types=1);

namespace STFront;

return [
    'controller_plugins' => [
        'invokables' => [
            'front' => Controller\Plugin\Front::class,
        ],
    ],
    'service_manager' => [
        'factories' => [
            Data\FrontsTable::class => \STLib\Mvc\Data\TableFactory::class,
            Service\FrontService::class => \STLib\Mvc\DependencyInjection\DefaultFactory::class,
        ],
    ],
];
