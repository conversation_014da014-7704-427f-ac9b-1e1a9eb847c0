<?php

declare(strict_types=1);

namespace STFront\Data;

class FrontsTable extends \STLib\Db\AbstractTable
{
    /**
     *
     * @param int $frontId
     * @return \Laminas\Db\ResultSet\ResultSetInterface
     * @throws \STApi\Entity\Exception\NotFoundApiException
     */
    public function getFrontById(int $frontId): \Laminas\Db\ResultSet\ResultSetInterface
    {
        $select = $this->tableGateway->getSql()->select();
        $select
                ->where([
                    'front_id' => $frontId,
                ]);
        $result = $this->tableGateway->selectWith($select);
        if ($result->count() === 0) {
            throw new \STApi\Entity\Exception\NotFoundApiException('Front not found');
        }
        return $result;
    }

    /**
     *
     * @param string|null $domain
     * @return \Laminas\Db\ResultSet\ResultSetInterface
     * @throws \STApi\Entity\Exception\NotFoundApiException
     */
    public function getFrontByDomain(?string $domain): \Laminas\Db\ResultSet\ResultSetInterface
    {
        $select = $this->tableGateway->getSql()->select();
        $select
                ->where([
                    'domain' => $domain,
                ]);
        $result = $this->tableGateway->selectWith($select);
        if ($result->count() === 0) {
            throw new \STApi\Entity\Exception\NotFoundApiException('Front not found');
        }
        return $result;
    }

    /**
     *
     * @return \Laminas\Db\ResultSet\ResultSetInterface
     * @throws \STApi\Entity\Exception\NotFoundApiException
     */
    public function getDefaultFront(): \Laminas\Db\ResultSet\ResultSetInterface
    {
        $select = $this->tableGateway->getSql()->select();
        $select
                ->where([
                    'is_default' => true,
                ]);
        $result = $this->tableGateway->selectWith($select);
        if ($result->count() === 0) {
            throw new \STApi\Entity\Exception\NotFoundApiException('Front not found');
        }
        return $result;
    }
}
