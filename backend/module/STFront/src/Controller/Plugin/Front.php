<?php

declare(strict_types=1);

namespace STFront\Controller\Plugin;

use Laminas\Mvc\Controller\Plugin\AbstractPlugin;

class Front extends AbstractPlugin
{
    /**
     *
     * @param string $name
     * @param array $arguments
     * @return mixed
     */
    public function __call(string $name, array $arguments): mixed
    {
        $service = $this->getController()->getServiceManager()->get(\STFront\Service\FrontService::class);
        return call_user_func_array([
            $service,
            $name
        ], $arguments);
    }
}
