<?php

declare(strict_types=1);

namespace STFront\Entity;

class Front
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    /**
     *
     * @var int
     */
    protected int $id;

    /**
     *
     * @var string
     */
    protected string $domain;

    /**
     *
     * @var string
     */
    protected string $title;

    /**
     *
     * @var string
     */
    protected string $logo;

    /**
     *
     * @var string
     */
    protected string $emailLogoLink;

    /**
     *
     * @var string
     */
    protected string $favicon;

    /**
     *
     * @var string|null
     */
    protected ?string $sendingEmail;

    /**
     *
     * @var string|null
     */
    protected ?string $sendingEmailSender;

    /**
     *
     * @var bool
     */
    protected bool $isDefault = false;

    /**
     *
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     *
     * @return string
     */
    public function getDomain(): string
    {
        return $this->domain;
    }

    /**
     *
     * @return string
     */
    public function getTitle(): string
    {
        return $this->title;
    }

    /**
     *
     * @return string
     */
    public function getLogo(): string
    {
        return $this->logo;
    }

    /**
     *
     * @return string
     */
    public function getEmailLogoLink(): string
    {
        return $this->emailLogoLink;
    }

    /**
     *
     * @return string
     */
    public function getFavicon(): string
    {
        return $this->favicon;
    }

    /**
     *
     * @return string|null
     */
    public function getSendingEmail(): ?string
    {
        return $this->sendingEmail;
    }

    /**
     *
     * @return string|null
     */
    public function getSendingEmailSender(): ?string
    {
        return $this->sendingEmailSender;
    }

    /**
     *
     * @return bool
     */
    public function getIsDefault(): bool
    {
        return $this->isDefault;
    }

    /**
     *
     * @param int $id
     * @return Front
     */
    public function setId(int $id): Front
    {
        $this->id = $id;
        return $this;
    }

    /**
     *
     * @param string $domain
     * @return Front
     */
    public function setDomain(string $domain): Front
    {
        $this->domain = $domain;
        return $this;
    }

    /**
     *
     * @param string $title
     * @return Front
     */
    public function setTitle(string $title): Front
    {
        $this->title = $title;
        return $this;
    }

    /**
     *
     * @param string $logo
     * @return Front
     */
    public function setLogo(string $logo): Front
    {
        $this->logo = $logo;
        return $this;
    }

    /**
     *
     * @param string $emailLogoLink
     * @return Front
     */
    public function setEmailLogoLink(string $emailLogoLink): Front
    {
        $this->emailLogoLink = $emailLogoLink;
        return $this;
    }

    /**
     *
     * @param string $favicon
     * @return Front
     */
    public function setFavicon(string $favicon): Front
    {
        $this->favicon = $favicon;
        return $this;
    }

    /**
     *
     * @param string|null $sendingEmail
     * @return Front
     */
    public function setSendingEmail(?string $sendingEmail): Front
    {
        $this->sendingEmail = $sendingEmail;
        return $this;
    }

    /**
     *
     * @param string|null $sendingEmailSender
     * @return Front
     */
    public function setSendingEmailSender(?string $sendingEmailSender): Front
    {
        $this->sendingEmailSender = $sendingEmailSender;
        return $this;
    }

    /**
     *
     * @param bool $isDefault
     * @return Front
     */
    public function setIsDefault(bool $isDefault): Front
    {
        $this->isDefault = $isDefault;
        return $this;
    }

    /**
     *
     * @param bool|null $isDefault
     * @return bool|Front
     */
    public function isDefault(?bool $isDefault = null): bool|Front
    {
        if (is_null($isDefault)) {
            return $this->isDefault;
        }
        $this->isDefault = $isDefault;
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        return $this->extract($this);
    }
}
