<?php

declare(strict_types=1);

namespace STFront\Service;

trait ProvidesFrontService
{
    /**
     *
     * @var FrontServiceWrapper|null
     */
    private ?FrontServiceWrapper $frontServiceWrapper = null;

    /**
     *
     * @return FrontServiceWrapper
     */
    protected function frontService(): FrontService
    {
        if (is_null($this->frontServiceWrapper)) {
            $this->frontServiceWrapper = new FrontServiceWrapper();
        }
        return $this->frontServiceWrapper->getStaticFrontService();
    }
}
