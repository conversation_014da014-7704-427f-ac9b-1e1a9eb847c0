<?php

declare(strict_types=1);

namespace STFront\Service;

use STFront\Entity\Front;

class FrontService
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    /**
     *
     * @var \STFront\Data\FrontsTable
     */
    protected \STFront\Data\FrontsTable $frontsTable;

    /**
     *
     * @var \STFront\Entity\Front|null
     */
    protected ?\STFront\Entity\Front $activeFront;

    /**
     *
     * @var string|null
     */
    protected ?string $domain = null;

    /**
     *
     * @param \STFront\Data\FrontsTable $frontsTable
     */
    public function __construct(\STFront\Data\FrontsTable $frontsTable)
    {
        $this->frontsTable = $frontsTable;
    }

    /**
     *
     * @param int $frontId
     * @return \STFront\Entity\Front
     */
    public function getFrontById(int $frontId): \STFront\Entity\Front
    {
        $frontData = $this->frontsTable->getFrontById($frontId);
        return $this->hydrate((array) $frontData->current(), \STFront\Entity\Front::class);
    }

    /**
     *
     * @return Front
     * @throws \RuntimeException
     */
    public function getActiveFront(): \STFront\Entity\Front
    {
        try {
            $frontData = $this->frontsTable->getFrontByDomain($this->domain);
        } catch (\STApi\Entity\Exception\NotFoundApiException $e) {
            $frontData = $this->frontsTable->getDefaultFront();
        }
        $this->activeFront = $this->hydrate((array) $frontData->current(), \STFront\Entity\Front::class);
        return $this->activeFront;
    }

    public function getDefaultFront(): Front
    {
        return $this->hydrate(
            (array) $this->frontsTable->getDefaultFront()->current(),
            Front::class
        );
    }

    /**
     *
     * @param string|null $domain
     * @return FrontService
     */
    public function setDomain(?string $domain): FrontService
    {
        $this->domain = $domain;
        return $this;
    }

    /**
     *
     * @param string $url
     * @return string
     */
    public function makeAbsolutUrl(string $url): string
    {
        $uri = new \Laminas\Uri\Uri($url);
        $uri->setHost($this->getActiveFront()->getDomain());
        return $uri->toString();
    }
}
