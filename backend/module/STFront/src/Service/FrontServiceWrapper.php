<?php

declare(strict_types=1);

namespace STFront\Service;

class FrontServiceWrapper
{
    /**
     *
     * @var FrontService|null
     */
    private static ?FrontService $frontService = null;

    /**
     *
     * @param FrontService $frontService
     * @return void
     */
    public static function setStaticFrontService(FrontService $frontService): void
    {
        self::$frontService = $frontService;
    }

    /**
     *
     * @return FrontService
     */
    public function getStaticFrontService(): FrontService
    {
        return self::$frontService;
    }
}
