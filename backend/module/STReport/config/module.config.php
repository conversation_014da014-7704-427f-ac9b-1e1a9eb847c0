<?php

declare(strict_types=1);

namespace STReport;

return [
    'controller_plugins' => [
        'invokables' => [
            'report' => Controller\Plugin\Report::class,
        ]
    ],
    'service_manager' => [
        'factories' => [
            Data\ReportTemplatesTable::class => \STLib\Mvc\Data\TableFactory::class,
            Data\ClientReportRepository::class => \STClickhouse\Entity\TableFactory::class,
            Data\AgentReportRepository::class => \STClickhouse\Entity\TableFactory::class,
            Service\ReportTemplateService::class => \STLib\Mvc\DependencyInjection\DefaultFactory::class,
            Service\ClientReportService::class => \STLib\Mvc\DependencyInjection\DefaultFactory::class,
            Service\AgentReportService::class => \STLib\Mvc\DependencyInjection\DefaultFactory::class,
            Service\CallParagraphReportService::class => \STLib\Mvc\DependencyInjection\DefaultFactory::class,
        ]
    ]
];
