<?php

declare(strict_types=1);

namespace STReport\Data;

use Carbon\Carbon;
use ST<PERSON>lickhouse\Entity\Pagination\CandidatePagination;
use STCompany\Entity\Role;

class AgentReportRepository extends \STClickhouse\Entity\BaseTable
{
    use \STClickhouse\Data\QueriesTrait;

    public const DAY_CALENDAR_DATE_DIMENSIONS = 'day';
    public const WEEK_CALENDAR_DATE_DIMENSIONS = 'week';
    public const MONTH_CALENDAR_DATE_DIMENSIONS = 'month';

    /**
     *
     * @param int $companyId
     * @param string $dateDimension
     * @param array $teamIds
     * @param \Carbon\Carbon|null $startDate
     * @param \Carbon\Carbon|null $endDate
     * @param int|null $agentId
     * @return array
     */
    public function getCallsCount(
        int $companyId,
        string $dateDimension,
        array $teamIds,
        ?\Carbon\Carbon $startDate,
        ?\Carbon\Carbon $endDate,
        ?int $agentId,
    ): array {
        $dimensionFucntion = match ($dateDimension) {
            static::DAY_CALENDAR_DATE_DIMENSIONS => 'toDate',
            static::WEEK_CALENDAR_DATE_DIMENSIONS => 'toMonday',
            static::MONTH_CALENDAR_DATE_DIMENSIONS => 'toStartOfMonth',
        };
        $internalFilters = $this->getInternalFilters($companyId, $startDate, $endDate);

        $sql = '
            SELECT
                ' . $dimensionFucntion . '(date) date,
                sum(total_calls_count) calls_count,
                sum(total_chats_count) chats_count
            FROM
                (
                ' .
                    $this->getFinalTableSqlUsingGroupBy(
                        'precalculated_agents',
                        [
                            'date',
                            'company_id',
                            'agent_id',
                        ],
                        'created',
                        [
                            'total_calls_count',
                            'total_chats_count',
                            'created',
                        ],
                        $internalFilters,
                    ) . '
                    ORDER BY
                        created
                ) pa
        ';
        if (count($teamIds) > 0) {
            $sql .= '
            LEFT JOIN
                dictionary(users_teams) ut
                ON pa.agent_id = ut.user_id
            ';
        }

        $whereConditions = [];
        if (count($teamIds) > 0) {
            $whereConditions[] = '
                ut.team_id IN (' . implode(',', $teamIds) . ')
            ';
        }
        if (!is_null($agentId)) {
            $whereConditions [] = '
                pa.agent_id = ' . $agentId . '
            ';
        }
        if (count($whereConditions)) {
            $sql .= '
            WHERE ' . implode(' AND ', $whereConditions) . '
            ';
        }

        $sql .= '
            GROUP BY
                date
        ';
        return $this->getClient()->selectAsTree($sql, 'date');
    }

    /**
     * @param \STCompany\Entity\Company $company
     * @param int $roleId
     * @param \Carbon\Carbon|null $startDate
     * @param \Carbon\Carbon|null $endDate
     * @param array $filters
     * @return array
     */
    public function getAgentsReport(
        \STCompany\Entity\Company $company,
        int $roleId,
        ?\Carbon\Carbon $startDate = null,
        ?\Carbon\Carbon $endDate = null,
        array $filters = [],
    ): array {
        $precalculatedAgentsInternalFilters = $this->getInternalFilters($company->getId(), $startDate, $endDate, $roleId, $filters);
        $precalculatedCallsInternalFilters = $this->getInternalFilters($company->getId(), $startDate, $endDate, $roleId, $filters, 'call_time');
        $eventIds = $filters['event_ids'] ?? [];

        // phpcs:disable
        $sql = '
            SELECT
                pa.company_id company_id,
                pa.role_id role_id,
                pa.agent_id agent_id,
                pa.agent_name agent_name,
                pa.total_calls_duration total_calls_duration,
                pa.total_calls_count total_calls_count,
                pa.effective_calls_duration effective_calls_duration,
                pa.not_effective_calls_duration not_effective_calls_duration,
                pa.effective_calls_count effective_calls_count,
                pa.not_effective_calls_count not_effective_calls_count,
                pa.reviewed_calls_count reviewed_calls_count,
                pa.total_chats_count total_chats_count,
                pa.reviewed_chats_count reviewed_chats_count,
                pa.score score,
                pa.unique_clients_count unique_clients_count,
                pa.teams teams,
        ';
        if (count($eventIds) > 0) {
            $sql .= '
                ade.detailed_events detailed_events,
            ';
        }
        $sql .= '
                ace.events events
            FROM
                (
                    SELECT
                        toInt32(pab.company_id) company_id,
                        toInt32(pab.role_id) role_id,
                        toInt32(pab.agent_id) agent_id,
                        a.agent_name agent_name,
                        pab.total_calls_count total_calls_count,
                        pab.total_calls_duration total_calls_duration,
                        pab.effective_calls_duration effective_calls_duration,
                        pab.not_effective_calls_duration not_effective_calls_duration,
                        pab.effective_calls_count effective_calls_count,
                        pab.not_effective_calls_count not_effective_calls_count,
                        pab.reviewed_calls_count reviewed_calls_count,
                        pab.total_chats_count total_chats_count,
                        pab.reviewed_chats_count reviewed_chats_count,
                        pab.score score,
                        pab.unique_clients_count unique_clients_count,
                        at.teams teams
                    FROM
                        (
                            SELECT
                                company_id company_id,
                                role_id role_id,
                                agent_id agent_id,
                                toInt32(sum(total_calls_count)) total_calls_count,
                                toInt32(sum(total_calls_duration)) total_calls_duration,
                                toInt32(sum(effective_calls_duration)) effective_calls_duration,
                                toInt32(sum(not_effective_calls_duration)) not_effective_calls_duration,
                                toInt32(sum(effective_calls_count)) effective_calls_count,
                                toInt32(sum(not_effective_calls_count)) not_effective_calls_count,
                                toInt32(sum(reviewed_calls_count)) reviewed_calls_count,
                                toInt32(sum(total_chats_count)) total_chats_count,
                                toInt32(sum(reviewed_chats_count)) reviewed_chats_count,
                                toInt32(sum(score)) score,
                                arrayUniq(flatten(groupArray(unique_clients))) unique_clients_count
                            FROM
                                (
                                ' .
                                    $this->getFinalTableSqlUsingGroupBy(
                                        'precalculated_agents',
                                        [
                                            'date',
                                            'company_id',
                                            'role_id',
                                            'agent_id',
                                        ],
                                        'created',
                                        [
                                            'total_calls_count',
                                            'total_calls_duration',
                                            'effective_calls_duration',
                                            'not_effective_calls_duration',
                                            'effective_calls_count',
                                            'not_effective_calls_count',
                                            'reviewed_calls_count',
                                            'total_chats_count',
                                            'reviewed_chats_count',
                                            'score',
                                            'unique_clients',
                                            'created',
                                        ],
                                        $precalculatedAgentsInternalFilters,
                                    ) . '
                                    ORDER BY
                                        created
                                )
                            GROUP BY
                                company_id,
                                role_id,
                                agent_id
                        ) pab
                        LEFT JOIN
                            (
                                SELECT 
                                    user_id user_id, 
                                    any(user_name) agent_name, 
                                    groupArray(
                                        map(
                                            \'team_id\', toString(team_id), 
                                            \'team_name\', team_name
                                        )
                                    ) teams
                                FROM 
                                    dictionary(users_teams) 
                                WHERE 
                                    company_id = ' . $company->getId() . '
                                GROUP BY 
                                    user_id
                            ) at
                            ON at.user_id = pab.agent_id
                        LEFT JOIN
                            (
                                SELECT 
                                    user_id user_id, 
                                    user_name agent_name
                                FROM 
                                    dictionary(users)
                            ) a
                            ON a.user_id = pab.agent_id
                ) pa
            LEFT JOIN
                (
                    SELECT
                        agent_id,
                        groupArray(
                            map
                            (
                                \'event_id\', toString(event_id),
                                \'category_id\', toString(category_id),
                                \'event_name\', event_name,
                                \'category_name\', category_name,
                                \'event_icon\', event_icon,
                                \'color_id\', toString(color_id),
                                \'fill_color_hex\', fill_color_hex,
                                \'outline_color_hex\', outline_color_hex,
                                \'count\', toString(count)
                            )
                        ) events
                    FROM
                        (
                            SELECT
                                agent_id,
                                event_id,
                                event_category_id category_id,
                                any(event_name) event_name,
                                any(event_category_name) category_name,
                                any(event_icon) event_icon,
                                any(event_color_id) color_id,
                                any(event_fill_color_hex) fill_color_hex,
                                any(event_outline_color_hex) outline_color_hex,
                                COUNT(*) count
                            FROM
                                (
                                     ' . $this->getFinalTableSqlUsingGroupBy(
                                        'precalculated_calls_events',
                                        [
                                            'company_id',
                                            'role_id',
                                            'call_id',
                                            'paragraph',
                                            'event_id',
                                        ],
                                        'created',
                                        [
                                            'agent_id',
                                            'event_category_id',
                                            'event_category_name',
                                            'event_name',
                                            'event_icon',
                                            'event_color_id',
                                            'event_fill_color_hex',
                                            'event_outline_color_hex',
                                            'event_is_deleted',
                                        ],
                                        $precalculatedCallsInternalFilters,
                                    ) . '
                                )
                            WHERE
                                event_is_deleted = 0
                            GROUP BY
                                agent_id,
                                event_category_id,
                                event_id
                        )
                    GROUP BY
                        agent_id
                ) ace
                ON ace.agent_id = pa.agent_id
        ';
        if (count($eventIds) > 0) {
            $sql .= '
            LEFT JOIN
                (
                    SELECT
                        agent_id,
                        groupArray(
                            map
                            (
                                \'call_id\', call_id,
                                \'call_time\', toString(call_time),
                                \'start_time\', toString(paragraph_start_time),
                                \'paragraph_number\', toString(paragraph),
                                \'speaker_role\', toString(paragraph_speaker_role),
                                \'event_id\', toString(event_id),
                                \'event_name\', event_name,
                                \'event_text\', event_text,
                                \'event_en_text\', event_en_text,
                                \'event_highlight\', event_highlight,
                                \'event_en_highlight\', event_en_highlight,
                                \'event_icon\', event_icon,
                                \'color_id\', toString(event_color_id),
                                \'fill_color_hex\', event_fill_color_hex,
                                \'outline_color_hex\', event_outline_color_hex,
                                \'category_id\', toString(event_category_id),
                                \'category_name\', event_category_name
                            )
                        ) detailed_events
                    FROM
                        (
                             ' . $this->getFinalTableSqlUsingGroupBy(
                                'precalculated_calls_events',
                                [
                                    'company_id',
                                    'role_id',
                                    'call_id',
                                    'paragraph',
                                    'event_id',
                                ],
                                'created',
                                [
                                    'agent_id',
                                    'call_time',
                                    'paragraph_start_time',
                                    'paragraph_speaker_role',
                                    'event_name',
                                    'event_text',
                                    'event_en_text',
                                    'event_highlight',
                                    'event_en_highlight',
                                    'event_icon',
                                    'event_color_id',
                                    'event_fill_color_hex',
                                    'event_outline_color_hex',
                                    'event_is_deleted',
                                    'event_category_id',
                                    'event_category_name',
                                ],
                                array_merge(
                                    $precalculatedCallsInternalFilters,
                                    [
                                        'event_id' => $eventIds,
                                    ]
                                ),
                            ) . '
                        )
                    WHERE
                        event_is_deleted = 0
                    GROUP BY
                        agent_id
                ) ade
                ON ade.agent_id = pa.agent_id
            ';
        }
        // phpcs:enable

        return $this->getClient()->selectAll($sql);
    }

    /**
     * @param \STCompany\Entity\Company $company
     * @param int $roleId
     * @param int $agentId
     * @param \Carbon\Carbon|null $startDate
     * @param \Carbon\Carbon|null $endDate
     * @return ?array
     */
    public function getAgentReport(
        \STCompany\Entity\Company $company,
        int $roleId,
        int $agentId,
        ?\Carbon\Carbon $startDate = null,
        ?\Carbon\Carbon $endDate = null,
    ): ?array {
        $filters['agent_ids'] = [$agentId];
        $precalculatedAgentsInternalFilters = $this->getInternalFilters($company->getId(), $startDate, $endDate, $roleId, $filters);
        $precalculatedCallsInternalFilters = $this->getInternalFilters($company->getId(), $startDate, $endDate, $roleId, $filters, 'call_time');

        // phpcs:disable
        $sql = '
            SELECT
                pa.company_id company_id,
                pa.role_id role_id,
                pa.agent_id agent_id,
                pa.agent_name agent_name,
                pa.total_calls_duration total_calls_duration,
                pa.total_calls_count total_calls_count,
                pa.effective_calls_duration effective_calls_duration,
                pa.not_effective_calls_duration not_effective_calls_duration,
                pa.effective_calls_count effective_calls_count,
                pa.not_effective_calls_count not_effective_calls_count,
                pa.reviewed_calls_count reviewed_calls_count,
                pa.total_chats_count total_chats_count,
                pa.reviewed_chats_count reviewed_chats_count,
                pa.score score,
                pa.unique_clients_count unique_clients_count,
                pa.teams teams,
                ade.detailed_events detailed_events,
                ace.events events
            FROM
                (
                    SELECT
                        toInt32(pab.company_id) company_id,
                        toInt32(pab.role_id) role_id,
                        toInt32(pab.agent_id) agent_id,
                        aa.agent_name agent_name,
                        pab.total_calls_count total_calls_count,
                        pab.total_calls_duration total_calls_duration,
                        pab.effective_calls_duration effective_calls_duration,
                        pab.not_effective_calls_duration not_effective_calls_duration,
                        pab.effective_calls_count effective_calls_count,
                        pab.not_effective_calls_count not_effective_calls_count,
                        pab.reviewed_calls_count reviewed_calls_count,
                        pab.total_chats_count total_chats_count,
                        pab.reviewed_chats_count reviewed_chats_count,
                        pab.score score,
                        pab.unique_clients_count unique_clients_count,
                        aa.teams teams
                    FROM
                        (
                            SELECT 
                                user_id user_id, 
                                any(user_name) agent_name, 
                                groupArray(
                                    map(
                                        \'team_id\', toString(team_id), 
                                        \'team_name\', team_name
                                    )
                                ) teams
                            FROM 
                                dictionary(users_teams) 
                            WHERE 
                                company_id = ' . $company->getId() . '
                                AND user_id = ' . $agentId . '
                            GROUP BY 
                                user_id
                        ) aa
                    LEFT JOIN
                        (
                            SELECT
                                company_id company_id,
                                role_id role_id,
                                agent_id agent_id,
                                toInt32(sum(total_calls_count)) total_calls_count,
                                toInt32(sum(total_calls_duration)) total_calls_duration,
                                toInt32(sum(effective_calls_duration)) effective_calls_duration,
                                toInt32(sum(not_effective_calls_duration)) not_effective_calls_duration,
                                toInt32(sum(effective_calls_count)) effective_calls_count,
                                toInt32(sum(not_effective_calls_count)) not_effective_calls_count,
                                toInt32(sum(reviewed_calls_count)) reviewed_calls_count,
                                toInt32(sum(total_chats_count)) total_chats_count,
                                toInt32(sum(reviewed_chats_count)) reviewed_chats_count,
                                toInt32(sum(score)) score,
                                arrayUniq(flatten(groupArray(unique_clients))) unique_clients_count
                            FROM
                                (
                                ' .
                                    $this->getFinalTableSqlUsingGroupBy(
                                        'precalculated_agents',
                                        [
                                            'date',
                                            'company_id',
                                            'role_id',
                                            'agent_id',
                                        ],
                                        'created',
                                        [
                                            'total_calls_count',
                                            'total_calls_duration',
                                            'effective_calls_duration',
                                            'not_effective_calls_duration',
                                            'effective_calls_count',
                                            'not_effective_calls_count',
                                            'reviewed_calls_count',
                                            'total_chats_count',
                                            'reviewed_chats_count',
                                            'score',
                                            'unique_clients',
                                            'created',
                                        ],
                                        $precalculatedAgentsInternalFilters,
                                    ) . '
                                    ORDER BY
                                        created
                                )
                            GROUP BY
                                company_id,
                                role_id,
                                agent_id
                        ) pab
                    ON aa.user_id = pab.agent_id
                ) pa
            LEFT JOIN
                (
                    SELECT
                        agent_id,
                        groupArray(
                            map
                            (
                                \'event_id\', toString(event_id),
                                \'category_id\', toString(category_id),
                                \'event_name\', event_name,
                                \'category_name\', category_name,
                                \'event_icon\', event_icon,
                                \'color_id\', toString(color_id),
                                \'fill_color_hex\', fill_color_hex,
                                \'outline_color_hex\', outline_color_hex,
                                \'count\', toString(count)
                            )
                        ) events
                    FROM
                        (
                            SELECT
                                agent_id,
                                event_id,
                                event_category_id category_id,
                                any(event_name) event_name,
                                any(event_category_name) category_name,
                                any(event_icon) event_icon,
                                any(event_color_id) color_id,
                                any(event_fill_color_hex) fill_color_hex,
                                any(event_outline_color_hex) outline_color_hex,
                                COUNT(*) count
                            FROM
                                (
                                     ' . $this->getFinalTableSqlUsingGroupBy(
                                        'precalculated_calls_events',
                                        [
                                            'company_id',
                                            'role_id',
                                            'call_id',
                                            'paragraph',
                                            'event_id',
                                        ],
                                        'created',
                                        [
                                            'agent_id',
                                            'event_category_id',
                                            'event_category_name',
                                            'event_name',
                                            'event_icon',
                                            'event_color_id',
                                            'event_fill_color_hex',
                                            'event_outline_color_hex',
                                            'event_is_deleted',
                                        ],
                                        $precalculatedCallsInternalFilters,
                                    ) . '
                                )
                            WHERE
                                event_is_deleted = 0
                            GROUP BY
                                agent_id,
                                event_category_id,
                                event_id
                        )
                    GROUP BY
                        agent_id
                ) ace
                ON ace.agent_id = pa.agent_id
            LEFT JOIN
                (
                    SELECT
                        agent_id,
                        groupArray(
                            map
                            (
                                \'call_id\', call_id,
                                \'call_time\', toString(call_time),
                                \'start_time\', toString(paragraph_start_time),
                                \'paragraph_number\', toString(paragraph),
                                \'speaker_role\', toString(paragraph_speaker_role),
                                \'event_id\', toString(event_id),
                                \'event_name\', event_name,
                                \'event_text\', event_text,
                                \'event_en_text\', event_en_text,
                                \'event_highlight\', event_highlight,
                                \'event_en_highlight\', event_en_highlight,
                                \'event_icon\', event_icon,
                                \'color_id\', toString(event_color_id),
                                \'fill_color_hex\', event_fill_color_hex,
                                \'outline_color_hex\', event_outline_color_hex,
                                \'category_id\', toString(event_category_id),
                                \'category_name\', event_category_name
                            )
                        ) detailed_events
                    FROM
                        (
                             ' . $this->getFinalTableSqlUsingGroupBy(
                                'precalculated_calls_events',
                                [
                                    'company_id',
                                    'role_id',
                                    'call_id',
                                    'paragraph',
                                    'event_id',
                                ],
                                'created',
                                [
                                    'agent_id',
                                    'call_time',
                                    'paragraph_start_time',
                                    'paragraph_speaker_role',
                                    'event_name',
                                    'event_text',
                                    'event_en_text',
                                    'event_highlight',
                                    'event_en_highlight',
                                    'event_icon',
                                    'event_color_id',
                                    'event_fill_color_hex',
                                    'event_outline_color_hex',
                                    'event_is_deleted',
                                    'event_category_id',
                                    'event_category_name',
                                ],
                                array_merge(
                                    $precalculatedCallsInternalFilters
                                ),
                            ) . '
                        )
                    WHERE
                        event_is_deleted = 0
                    GROUP BY
                        agent_id
                ) ade
                ON ade.agent_id = pa.agent_id
            ';
        // phpcs:enable

        return $this->getClient()->selectOne($sql);
    }

    /**
     * @param CandidatePagination $pagination
     * @param int $companyId
     * @param string $search
     * @return CandidatePagination
     */
    public function searchAgents(
        CandidatePagination $pagination,
        int $companyId,
        string $search,
    ): CandidatePagination {
        $this->paginate(
            $this->getAgentsCandidatesSql($companyId, $search),
            'user_id',
            $pagination,
        );

        if ($pagination->getCount() === 0) {
            return $pagination;
        }

        $agentsCandidatesIdsString = implode(',', $pagination->getCandidateIds());

        $sql = <<<SQL
                SELECT
                    toInt32(agents.user_id) user_id,
                    agents.user_name user_name,
                    agents.user_email user_email,
                    ut.teams teams,
                    patc.total_calls_count today_calls_count
                FROM (
                    SELECT 
                        *
                    FROM dictionary(users)
                    WHERE user_id IN ({$agentsCandidatesIdsString})
                ) agents  
                LEFT JOIN ({$this->getFinalTableSqlUsingGroupBy(
                    'precalculated_agents',
                    [
                        'company_id',
                        'agent_id',
                    ],
                    'created',
                    [
                        'total_calls_count',
                    ],
                    [
                        'agent_id' => $pagination->getCandidateIds(),
                        'date' => (new Carbon())->toDateString(),
                    ],
                )}) 
                patc ON patc.agent_id = agents.user_id            
                LEFT JOIN (
                    SELECT
                        user_id user_id, 
                        groupArray(
                            map(
                                'team_id', toString(team_id), 
                                'team_name', team_name
                            )
                        ) teams
                    FROM dictionary(users_teams)
                    GROUP BY
                        user_id
                ) ut
                ON ut.user_id = agents.user_id
        SQL;

        $sql .= $this->getClient()->getSortSql($pagination->getParams());

        $result = $this->getClient()->selectAll($sql);
        $pagination->setResult($result);

        return $pagination;
    }

    /**
     * @param int $companyId
     * @param string $search
     * @return string
     */
    protected function getAgentsCandidatesSql(
        int $companyId,
        string $search,
    ): string {
        $agentRoleType = Role::AGENT_ROLE_TYPE;

        return <<<SQL
            SELECT
                 DISTINCT user_id
            FROM dictionary(users) users
            INNER JOIN (
                SELECT
                    user_id
                FROM dictionary(users_roles)
                WHERE 
                    company_id = {$companyId}
                    AND role_type = {$agentRoleType}
                GROUP BY
                    user_id
            ) ur ON ur.user_id = users.user_id
            WHERE 
                users.user_name LIKE '%{$search}%' 
                OR toString(users.user_id) LIKE '%{$search}%' 
                OR users.user_email LIKE '%{$search}%'
        SQL;
    }

    /**
     * @param int $companyId
     * @param \Carbon\Carbon|null $startDate
     * @param \Carbon\Carbon|null $endDate
     * @param int|null $roleId
     * @param array $filters
     * @param string $dateColumn
     * @return int[]
     */
    protected function getInternalFilters(
        int $companyId,
        ?\Carbon\Carbon $startDate,
        ?\Carbon\Carbon $endDate,
        ?int $roleId = null,
        array $filters = [],
        string $dateColumn = 'date'
    ): array {
        $internalFilter = [
            'company_id' => $companyId,
        ];
        if (is_int($roleId)) {
            $internalFilter['role_id'] = $roleId;
        }
        if ($startDate instanceof \Carbon\Carbon) {
            $internalFilter['start_date'] = [
                'column' => $dateColumn,
                'value' => $startDate,
                'type' => 'date',
                'compare' => '>=',
            ];
        }
        if ($endDate instanceof \Carbon\Carbon) {
            $internalFilter['end_date'] = [
                'column' => $dateColumn,
                'value' => $endDate,
                'type' => 'date',
                'compare' => '<=',
            ];
        }
        if (isset($filters['agent_ids']) && is_array($filters['agent_ids']) && count($filters['agent_ids']) > 0) {
            $internalFilter['agent_id'] = $filters['agent_ids'];
        }
        if (isset($filters['team_ids']) && is_array($filters['team_ids']) && count($filters['team_ids']) > 0) {
            $internalFilter['team_ids'] = [
                    'column' => 'agent_id',
                    'value' => 'agent_id IN
                        (
                            SELECT 
                                user_id
                            FROM 
                                dictionary(users_teams) 
                            WHERE
                                company_id = ' . $companyId . '
                                AND team_id IN (' . implode(',', $filters['team_ids']) . ')
                        )',
                    'type' => 'expression',
            ];
        }
        return $internalFilter;
    }
}
