<?php

declare(strict_types=1);

namespace STReport\Data;

use Carbon\Carbon;

class ReportRepository extends \STClickhouse\Entity\BaseTable
{
    use \STClickhouse\Data\QueriesTrait;
    use \STClickhouse\Data\CallsWhereCondition;

    /**
     *
     * @param int $companyId
     * @param ?Carbon $startDate
     * @param ?Carbon $endDate
     * @return array
     */
    public function getFilterValuesRanges(
        int $companyId,
        ?Carbon $startDate,
        ?Carbon $endDate,
    ): array {
        $sql = <<<SQL
            SELECT 
                min(c.call_duration) min_call_duration,
                max(c.call_duration) max_call_duration,
                min(cl.value) min_client_value,
                max(cl.value) max_client_value,
                min(score) min_event_score_value,
                max(score) max_event_score_value
            FROM ({$this->getFinalTableSqlUsingGroupBy(
                'calls',
                [
                    'company_id',
                    'call_id',
                ],
                'created',
                [
                    'call_duration',
                    'call_time',
                    'client_id'
                ],
                $this->getCallsWhereConditionsFromMainParams($companyId, $startDate, $endDate),
            )}) c
            LEFT JOIN ({$this->getFinalTableSqlUsingGroupBy(
                'precalculated_calls',
                [
                    'company_id',
                    'call_id',
                    'role_id',
                ],
                'created',
                [
                    'score',
                    'call_time'
                ],
                $this->getCallsWhereConditionsFromMainParams($companyId, $startDate, $endDate),
            )}) pc ON pc.call_id = c.call_id
            LEFT JOIN ({$this->getFinalTableSqlUsingGroupBy(
                'clients',
                [
                    'company_id',
                    'client_id',
                ],
                'created',
                [
                    'value'
                ],
                $this->getCallsWhereConditionsFromMainParams($companyId),
            )}) cl ON cl.client_id = c.client_id  
        SQL;

        return $this->getClient()->selectOne($sql);
    }
}
