<?php

declare(strict_types=1);

namespace STReport\Data;

use Carbon\Carbon;
use STClickhouse\Data\QueriesTrait;
use STClickhouse\Entity\BaseTable;
use STClickhouse\Entity\Pagination\CandidatePagination;

class ClientReportRepository extends BaseTable
{
    use QueriesTrait;

    /**
     * @param CandidatePagination $pagination
     * @param int $companyId
     * @param int $roleId
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @return CandidatePagination
     */
    public function getClientsReport(
        CandidatePagination $pagination,
        int $companyId,
        int $roleId,
        ?Carbon $startDate = null,
        ?Carbon $endDate = null,
    ): CandidatePagination {
        $filteredEventIds = $pagination->getFilter()['event_ids'] ?? [];

        $processedFilters = $this->getProcessedFilters($companyId, $pagination->getFilter());
        $pagination->setFilter($processedFilters);

        $this->paginate(
            $this->getClientCandidatesSql($companyId, $roleId, $startDate, $endDate),
            'client_id',
            $pagination,
        );

        if ($pagination->getCount() === 0) {
            return $pagination;
        }

        // phpcs:disable
        $sql = '
            SELECT
                pc.company_id company_id,
                pc.role_id role_id,
                pc.client_id client_id,
                pc.agent_name agent_name,
                pc.team_name team_name,
                pc.total_calls_duration total_calls_duration,
                pc.effective_calls_duration effective_calls_duration,
                pc.not_effective_calls_duration not_effective_calls_duration,
                pc.effective_calls_count effective_calls_count,
                pc.not_effective_calls_count not_effective_calls_count,
                pc.reviewed_calls_count reviewed_calls_count,
                pc.reviewed_chats_count reviewed_chats_count,
                pc.score score,
                pc.risk_rank risk_rank,
                pc.last_call_time last_call_time,
                pc.client_name client_name,
                pc.country country,
                pc.status status,
                pc.source source,
                pc.acquisition_date acquisition_date,
                pc.is_converted is_converted,
                pc.converted_date converted_date,
                pc.last_transaction_date last_transaction_date,
                pc.campaign_id campaign_id,
                pc.value value,
        ';
        if (count($filteredEventIds) > 0) {
            $sql .= '
                cde.detailed_events detailed_events,
            ';
        }
        $sql .= '
                cce.events events
            FROM
                (
                    SELECT
                        client_id,
                        any(pci.company_id) company_id,
                        any(pci.role_id) role_id,
                        any(pci.total_calls_duration) total_calls_duration,
                        any(pci.effective_calls_duration) effective_calls_duration,
                        any(pci.not_effective_calls_duration) not_effective_calls_duration,
                        any(pci.effective_calls_count) effective_calls_count,
                        any(pci.not_effective_calls_count) not_effective_calls_count,
                        any(pci.reviewed_calls_count) reviewed_calls_count,
                        any(pci.reviewed_chats_count) reviewed_chats_count,
                        any(pci.score) score,
                        any(pci.risk_rank) risk_rank,
                        any(pci.last_call_time) last_call_time,
                        any(pci.client_name) client_name,
                        any(pci.country) country,
                        any(pci.status) status,
                        any(pci.source) source,
                        any(pci.acquisition_date) acquisition_date,
                        any(pci.is_converted) is_converted,
                        any(pci.converted_date) converted_date,
                        any(pci.last_transaction_date) last_transaction_date,
                        any(pci.campaign_id) campaign_id,
                        any(pci.value) value,
                        arrayReduce(\'groupUniqArray\',
                            arrayFilter(
                                team_name -> trim(team_name) <> \'\',
                                groupArray(ut.team_name)
                            )
                        ) team_name,
                        arrayReduce(\'groupUniqArray\',
                            arrayFilter(
                                user_name -> trim(user_name) <> \'\',
                                groupArray(u.user_name)
                            )
                        ) agent_name
                    FROM
                        (
                            SELECT
                                *,
                                agent_ids agent_id
                            FROM
                                (
                                    SELECT
                                        company_id company_id,
                                        role_id role_id,
                                        client_id client_id,
                                        arrayReduce(\'groupUniqArray\', flatten(groupArray(agent_ids))) agent_ids,
                                        toInt32(sum(total_calls_duration)) total_calls_duration,
                                        toInt32(sum(effective_calls_duration)) effective_calls_duration,
                                        toInt32(sum(not_effective_calls_duration)) not_effective_calls_duration,
                                        toInt32(sum(effective_calls_count)) effective_calls_count,
                                        toInt32(sum(not_effective_calls_count)) not_effective_calls_count,
                                        toInt32(sum(reviewed_calls_count)) reviewed_calls_count,
                                        toInt32(sum(reviewed_chats_count)) reviewed_chats_count,
                                        toInt32(sum(score)) score,
                                        toInt32(sum(risk_rank)) risk_rank,
                                        last_value_respect_nulls(last_call_time) last_call_time,
                                        last_value_respect_nulls(client_name) client_name,
                                        last_value_respect_nulls(country) country,
                                        last_value_respect_nulls(status) status,
                                        last_value_respect_nulls(source) source,
                                        last_value_respect_nulls(acquisition_date) acquisition_date,
                                        last_value_respect_nulls(is_converted) is_converted,
                                        last_value_respect_nulls(converted_date) converted_date,
                                        last_value_respect_nulls(last_transaction_date) last_transaction_date,
                                        last_value_respect_nulls(campaign_id) campaign_id,
                                        last_value_respect_nulls(value) value
                                    FROM
                                        (
                                        ' .
                                            $this->getFinalTableSqlUsingGroupBy(
                                                'precalculated_clients',
                                                [
                                                    'date',
                                                    'company_id',
                                                    'role_id',
                                                    'client_id',
                                                ],
                                                'created',
                                                [
                                                    'agent_ids',
                                                    'total_calls_duration',
                                                    'effective_calls_duration',
                                                    'not_effective_calls_duration',
                                                    'effective_calls_count',
                                                    'not_effective_calls_count',
                                                    'reviewed_calls_count',
                                                    'reviewed_chats_count',
                                                    'score',
                                                    'risk_rank',
                                                    'last_call_time',
                                                    'client_name',
                                                    'country',
                                                    'status',
                                                    'source',
                                                    'acquisition_date',
                                                    'is_converted',
                                                    'converted_date',
                                                    'last_transaction_date',
                                                    'campaign_id',
                                                    'value',
                                                    'created',
                                                ],
                                                array_merge(
                                                    ['client_id' => $pagination->getCandidateIds()],
                                                    $this->getInternalFilters($companyId, $roleId, $startDate, $endDate),
                                                ),
                                            ) . '
                                            ORDER BY
                                                created
                                        )
                                    GROUP BY
                                        company_id,
                                        role_id,
                                        client_id
                                )
                            ARRAY JOIN
                                agent_ids
                        ) pci
                        LEFT JOIN
                            (
                                SELECT
                                    user_id,
                                    user_name,
                                    team_name
                                FROM
                                    dictionary(users_teams)
                                WHERE
                                    company_id = ' . $companyId . '
                            ) ut
                            ON ut.user_id = pci.agent_id
                        LEFT JOIN dictionary(users) u ON u.user_id = pci.agent_id
                        GROUP BY
                            pci.client_id
                ) pc
            LEFT JOIN
                (
                    SELECT
                        client_id,
                        groupArray(
                            map
                            (
                                \'event_id\', toString(event_id),
                                \'category_id\', toString(category_id),
                                \'event_name\', event_name,
                                \'category_name\', category_name,
                                \'event_icon\', event_icon,
                                \'color_id\', toString(color_id),
                                \'fill_color_hex\', fill_color_hex,
                                \'outline_color_hex\', outline_color_hex,
                                \'count\', toString(count)
                            )
                        ) events
                    FROM
                        (
                            SELECT
                                client_id,
                                event_id,
                                event_category_id category_id,
                                any(event_name) event_name,
                                any(event_category_name) category_name,
                                any(event_icon) event_icon,
                                any(event_color_id) color_id,
                                any(event_fill_color_hex) fill_color_hex,
                                any(event_outline_color_hex) outline_color_hex,
                                COUNT(*) count
                            FROM
                                (
                                     ' . $this->getFinalTableSqlUsingGroupBy(
                                        'precalculated_calls_events',
                                        [
                                            'company_id',
                                            'role_id',
                                            'call_id',
                                            'paragraph',
                                            'event_id',
                                        ],
                                        'created',
                                        [
                                            'client_id',
                                            'event_category_id',
                                            'event_category_name',
                                            'event_name',
                                            'event_icon',
                                            'event_color_id',
                                            'event_fill_color_hex',
                                            'event_outline_color_hex',
                                            'event_is_deleted',
                                        ],
                                        [
                                            'company_id' => $companyId,
                                            'role_id' => $roleId,
                                            'client_id' => $pagination->getCandidateIds(),
                                        ],
                                    ) . '
                                )
                            WHERE
                                event_is_deleted = 0
                            GROUP BY
                                client_id,
                                event_category_id,
                                event_id
                        )
                    GROUP BY
                        client_id
                ) cce
                ON cce.client_id = pc.client_id
        ';
        if (count($filteredEventIds) > 0) {
            $sql .= '
            LEFT JOIN
                (
                    SELECT
                        client_id,
                        groupArray(
                            map
                            (
                                \'call_id\', call_id,
                                \'call_time\', toString(call_time),
                                \'start_time\', toString(paragraph_start_time),
                                \'paragraph_number\', toString(paragraph),
                                \'speaker_role\', toString(paragraph_speaker_role),
                                \'event_id\', toString(event_id),
                                \'event_name\', event_name,
                                \'event_text\', event_text,
                                \'event_en_text\', event_en_text,
                                \'event_highlight\', event_highlight,
                                \'event_en_highlight\', event_en_highlight,
                                \'event_icon\', event_icon,
                                \'color_id\', toString(event_color_id),
                                \'fill_color_hex\', event_fill_color_hex,
                                \'outline_color_hex\', event_outline_color_hex,
                                \'category_id\', toString(event_category_id),
                                \'category_name\', event_category_name
                            )
                        ) detailed_events
                    FROM
                        (
                             ' . $this->getFinalTableSqlUsingGroupBy(
                                'precalculated_calls_events',
                                [
                                    'company_id',
                                    'role_id',
                                    'call_id',
                                    'paragraph',
                                    'event_id',
                                ],
                                'created',
                                [
                                    'client_id',
                                    'call_time',
                                    'paragraph_start_time',
                                    'paragraph_speaker_role',
                                    'event_name',
                                    'event_text',
                                    'event_en_text',
                                    'event_highlight',
                                    'event_en_highlight',
                                    'event_icon',
                                    'event_color_id',
                                    'event_fill_color_hex',
                                    'event_outline_color_hex',
                                    'event_is_deleted',
                                    'event_category_id',
                                    'event_category_name',
                                ],
                                [
                                    'company_id' => $companyId,
                                    'role_id' => $roleId,
                                    'client_id' => $pagination->getCandidateIds(),
                                    'event_id' => $filteredEventIds,
                                ],
                            ) . '
                        )
                    WHERE
                        event_is_deleted = 0
                    GROUP BY
                        client_id
                ) cde
                ON cde.client_id = pc.client_id
            ';
        }
        // phpcs:enable
        $sql .= $this->getClient()->getSortSql($pagination->getParams());

        $result = $this->getClient()->selectAll($sql);
        $pagination->setResult($result);

        return $pagination;
    }

    /**
     * @param int $companyId
     * @param int $roleId
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @param array $filters
     * @param array $columns
     * @return \Iterator
     */
    public function getClientsCsvReport(
        int $companyId,
        int $roleId,
        ?Carbon $startDate = null,
        ?Carbon $endDate = null,
        array $filters = [],
        array $columns = [],
    ): \Iterator {
        $filteredEventIds = $filters['event_ids'] ?? [];
        $processedFilters = $this->getProcessedFilters($companyId, $filters);
        $candidateIdsSql = $this->getClientCandidatesSql($companyId, $roleId, $startDate, $endDate) . $this->getClient()->convertFilterToSql($processedFilters);
        $candidateIds = $this->getClient()->selectColumn($candidateIdsSql, 'client_id');
        if (count($candidateIds) === 0) {
            return new \EmptyIterator();
        }

        // phpcs:disable
        $sql = '
            SELECT
                pc.company_id company_id,
                pc.role_id role_id,
                pc.client_id client_id,
                pc.agent_name agent_name,
                pc.team_name team_name,
                pc.total_calls_duration total_calls_duration,
                pc.effective_calls_duration effective_calls_duration,
                pc.not_effective_calls_duration not_effective_calls_duration,
                pc.effective_calls_count effective_calls_count,
                pc.not_effective_calls_count not_effective_calls_count,
                pc.reviewed_calls_count reviewed_calls_count,
                pc.reviewed_chats_count reviewed_chats_count,
                pc.score score,
                pc.risk_rank risk_rank,
                pc.last_call_time last_call_time,
                pc.client_name client_name,
                pc.country country,
                pc.status status,
                pc.source source,
                pc.acquisition_date acquisition_date,
                pc.is_converted is_converted,
                pc.converted_date converted_date,
                pc.last_transaction_date last_transaction_date,
                pc.campaign_id campaign_id,
                pc.value value,
        ';
        if (count($filteredEventIds) > 0) {
            $sql .= '
                cde.detailed_events detailed_events,
            ';
        }
        $sql .= '
                cce.events events
            FROM
                (
                    SELECT
                        client_id,
                        any(pci.company_id) company_id,
                        any(pci.role_id) role_id,
                        any(pci.total_calls_duration) total_calls_duration,
                        any(pci.effective_calls_duration) effective_calls_duration,
                        any(pci.not_effective_calls_duration) not_effective_calls_duration,
                        any(pci.effective_calls_count) effective_calls_count,
                        any(pci.not_effective_calls_count) not_effective_calls_count,
                        any(pci.reviewed_calls_count) reviewed_calls_count,
                        any(pci.reviewed_chats_count) reviewed_chats_count,
                        any(pci.score) score,
                        any(pci.risk_rank) risk_rank,
                        any(pci.last_call_time) last_call_time,
                        any(pci.client_name) client_name,
                        any(pci.country) country,
                        any(pci.status) status,
                        any(pci.source) source,
                        any(pci.acquisition_date) acquisition_date,
                        any(pci.is_converted) is_converted,
                        any(pci.converted_date) converted_date,
                        any(pci.last_transaction_date) last_transaction_date,
                        any(pci.campaign_id) campaign_id,
                        any(pci.value) value,
                        arrayStringConcat(
                            arrayReduce(\'groupUniqArray\',
                                arrayFilter(
                                    team_name -> trim(team_name) <> \'\',
                                    groupArray(ut.team_name)
                                )
                            )
                            , \', \'
                        ) team_name,
                        arrayStringConcat(
                            arrayReduce(\'groupUniqArray\',
                                arrayFilter(
                                    user_name -> trim(user_name) <> \'\',
                                    groupArray(u.user_name)
                                )
                            )
                            , \', \'
                        ) agent_name
                    FROM
                        (
                            SELECT
                                *,
                                agent_ids agent_id
                            FROM
                                (
                                    SELECT
                                        company_id company_id,
                                        role_id role_id,
                                        client_id client_id,
                                        arrayReduce(\'groupUniqArray\', flatten(groupArray(agent_ids))) agent_ids,
                                        toInt32(sum(total_calls_duration)) total_calls_duration,
                                        toInt32(sum(effective_calls_duration)) effective_calls_duration,
                                        toInt32(sum(not_effective_calls_duration)) not_effective_calls_duration,
                                        toInt32(sum(effective_calls_count)) effective_calls_count,
                                        toInt32(sum(not_effective_calls_count)) not_effective_calls_count,
                                        toInt32(sum(reviewed_calls_count)) reviewed_calls_count,
                                        toInt32(sum(reviewed_chats_count)) reviewed_chats_count,
                                        toInt32(sum(score)) score,
                                        toInt32(sum(risk_rank)) risk_rank,
                                        last_value_respect_nulls(last_call_time) last_call_time,
                                        last_value_respect_nulls(client_name) client_name,
                                        last_value_respect_nulls(country) country,
                                        last_value_respect_nulls(status) status,
                                        last_value_respect_nulls(source) source,
                                        last_value_respect_nulls(acquisition_date) acquisition_date,
                                        last_value_respect_nulls(is_converted) is_converted,
                                        last_value_respect_nulls(converted_date) converted_date,
                                        last_value_respect_nulls(last_transaction_date) last_transaction_date,
                                        last_value_respect_nulls(campaign_id) campaign_id,
                                        last_value_respect_nulls(value) value
                                    FROM
                                        (
                                        ' .
                                            $this->getFinalTableSqlUsingGroupBy(
                                                'precalculated_clients',
                                                [
                                                    'date',
                                                    'company_id',
                                                    'role_id',
                                                    'client_id',
                                                ],
                                                'created',
                                                [
                                                    'agent_ids',
                                                    'total_calls_duration',
                                                    'effective_calls_duration',
                                                    'not_effective_calls_duration',
                                                    'effective_calls_count',
                                                    'not_effective_calls_count',
                                                    'reviewed_calls_count',
                                                    'reviewed_chats_count',
                                                    'score',
                                                    'risk_rank',
                                                    'last_call_time',
                                                    'client_name',
                                                    'country',
                                                    'status',
                                                    'source',
                                                    'acquisition_date',
                                                    'is_converted',
                                                    'converted_date',
                                                    'last_transaction_date',
                                                    'campaign_id',
                                                    'value',
                                                    'created',
                                                ],
                                                array_merge(
                                                    ['client_id' => $candidateIds],
                                                    $this->getInternalFilters($companyId, $roleId, $startDate, $endDate),
                                                ),
                                            ) . '
                                            ORDER BY
                                                created
                                        )
                                    GROUP BY
                                        company_id,
                                        role_id,
                                        client_id
                                )
                            ARRAY JOIN
                                agent_ids
                        ) pci
                        LEFT JOIN
                            (
                                SELECT
                                    user_id,
                                    user_name,
                                    team_name
                                FROM
                                    dictionary(users_teams)
                                WHERE
                                    company_id = ' . $companyId . '
                            ) ut
                            ON ut.user_id = pci.agent_id
                        LEFT JOIN dictionary(users) u ON u.user_id = pci.agent_id
                        GROUP BY
                            pci.client_id
                ) pc
            LEFT JOIN
                (
                    SELECT
                        client_id,
                        arrayStringConcat(
                            groupArray(
                                concat(
                                    \'category: \', category_name
                                    , \'\n\'
                                    , \'event: \', event_name
                                    , \'\n\'
                                    , \'count: \', count
                                )
                            ), \'\n\n\'
                        ) events
                    FROM
                        (
                            SELECT
                                client_id,
                                event_id,
                                any(event_name) event_name,
                                any(event_category_name) category_name,
                                COUNT(*) count
                            FROM
                                (
                                     ' . $this->getFinalTableSqlUsingGroupBy(
                                        'precalculated_calls_events',
                                        [
                                            'company_id',
                                            'role_id',
                                            'call_id',
                                            'paragraph',
                                            'event_id',
                                        ],
                                        'created',
                                        [
                                            'client_id',
                                            'event_name',
                                            'event_category_name',
                                            'event_is_deleted',
                                        ],
                                        [
                                            'company_id' => $companyId,
                                            'role_id' => $roleId,
                                            'client_id' => $candidateIds,
                                        ],
                                    ) . '
                                )
                            WHERE
                                event_is_deleted = 0
                            GROUP BY
                                client_id,
                                event_id
                        )
                    GROUP BY
                        client_id
                ) cce
                ON cce.client_id = pc.client_id
        ';
        if (count($filteredEventIds) > 0) {
            $sql .= '
            LEFT JOIN
                (
                    SELECT
                        client_id,
                        arrayStringConcat(
                            groupArray(
                                concat(
                                    \'call id: \', call_id
                                    , \'\n\'
                                    , \'category name: \', event_category_name
                                    , \'\n\'
                                    , \'event name: \', event_name
                                    , \'\n\'
                                    , \'event time: \', formatDateTime(CAST(toInt64(paragraph_start_time) AS DATETIME), \'%H:%i:%s\')
                                    , \'\n\'
                                    , \'text: \', event_highlight
                                    , \'\n\'
                                    , \'en text: \', event_en_highlight
                                )
                            ), \'\n\n\'
                        ) detailed_events
                    FROM
                        (
                             ' . $this->getFinalTableSqlUsingGroupBy(
                                'precalculated_calls_events',
                                [
                                    'company_id',
                                    'role_id',
                                    'call_id',
                                    'paragraph',
                                    'event_id',
                                ],
                                'created',
                                [
                                    'client_id',
                                    'paragraph_start_time',
                                    'event_name',
                                    'event_highlight',
                                    'event_en_highlight',
                                    'event_is_deleted',
                                    'event_category_name',
                                ],
                                [
                                    'company_id' => $companyId,
                                    'role_id' => $roleId,
                                    'client_id' => $candidateIds,
                                    'event_id' => $filteredEventIds,
                                ],
                            ) . '
                        )
                    WHERE
                        event_is_deleted = 0
                    GROUP BY
                        client_id
                ) cde
                ON cde.client_id = pc.client_id
            ';
        }
        // phpcs:enable

        $sql = '
            SELECT
                ' . $this->getClient()->getSelectString($columns) . '
            FROM
            (
                ' . $sql . '
            )
        ';

        return $this->getClient()->selectIterator($sql);
    }

    /**
     * @param int $companyId
     * @param int $roleId
     * @param string $clientId
     * @return array|null
     */
    public function getClientReport(int $companyId, int $roleId, string $clientId): ?array
    {
        $sql = '
            SELECT
                pc.company_id company_id,
                pc.role_id role_id,
                pc.client_id client_id,
                pc.total_calls_duration total_calls_duration,
                pc.effective_calls_duration effective_calls_duration,
                pc.not_effective_calls_duration not_effective_calls_duration,
                pc.effective_calls_count effective_calls_count,
                pc.not_effective_calls_count not_effective_calls_count,
                pc.reviewed_calls_count reviewed_calls_count,
                pc.reviewed_chats_count reviewed_chats_count,
                pc.score score,
                pc.risk_rank risk_rank,
                pc.last_call_time last_call_time,
                pc.client_name client_name,
                pc.country country,
                pc.status status,
                pc.source source,
                pc.acquisition_date acquisition_date,
                pc.is_converted is_converted,
                pc.converted_date converted_date,
                pc.last_transaction_date last_transaction_date,
                pc.campaign_id campaign_id,
                pc.value value,
                cde.detailed_events detailed_events,
                cce.events events,
                map
                    (
                        \'user_id\', toString(la.user_id),
                        \'user_name\', la.user_name,
                        \'team_names\', la.team_names
                    ) last_agent
            FROM
                (
                    SELECT
                        company_id company_id,
                        role_id role_id,
                        client_id client_id,
                        toInt32(sum(total_calls_duration)) total_calls_duration,
                        toInt32(sum(effective_calls_duration)) effective_calls_duration,
                        toInt32(sum(not_effective_calls_duration)) not_effective_calls_duration,
                        toInt32(sum(effective_calls_count)) effective_calls_count,
                        toInt32(sum(not_effective_calls_count)) not_effective_calls_count,
                        toInt32(sum(reviewed_calls_count)) reviewed_calls_count,
                        toInt32(sum(reviewed_chats_count)) reviewed_chats_count,
                        toInt32(sum(score)) score,
                        toInt32(sum(risk_rank)) risk_rank,
                        last_value_respect_nulls(last_call_time) last_call_time,
                        last_value_respect_nulls(client_name) client_name,
                        last_value_respect_nulls(country) country,
                        last_value_respect_nulls(status) status,
                        last_value_respect_nulls(source) source,
                        last_value_respect_nulls(acquisition_date) acquisition_date,
                        last_value_respect_nulls(is_converted) is_converted,
                        last_value_respect_nulls(converted_date) converted_date,
                        last_value_respect_nulls(last_transaction_date) last_transaction_date,
                        last_value_respect_nulls(campaign_id) campaign_id,
                        last_value_respect_nulls(value) value
                    FROM
                        (
                        ' .
                            $this->getFinalTableSqlUsingGroupBy(
                                'precalculated_clients',
                                [
                                    'date',
                                    'company_id',
                                    'role_id',
                                    'client_id',
                                ],
                                'created',
                                [
                                    'total_calls_duration',
                                    'effective_calls_duration',
                                    'not_effective_calls_duration',
                                    'effective_calls_count',
                                    'not_effective_calls_count',
                                    'reviewed_calls_count',
                                    'reviewed_chats_count',
                                    'score',
                                    'risk_rank',
                                    'last_call_time',
                                    'client_name',
                                    'country',
                                    'status',
                                    'source',
                                    'acquisition_date',
                                    'is_converted',
                                    'converted_date',
                                    'last_transaction_date',
                                    'campaign_id',
                                    'value',
                                    'created',
                                ],
                                [
                                    'company_id' => $companyId,
                                    'role_id' => $roleId,
                                    'client_id' => $clientId,
                                ],
                            ) . '
                            ORDER BY
                                created
                        )
                    GROUP BY
                        company_id,
                        role_id,
                        client_id
                ) pc
            LEFT JOIN
                (
                    SELECT
                        \'' . $clientId .  '\' client_id,
                        u.user_id user_id,
                        any(u.user_name) user_name,
                        arrayStringConcat(groupArray(ut.team_name), \', \') team_names
                    FROM 
                        dictionary(users) u
                    LEFT JOIN
                        dictionary(users_teams) ut
                        ON ut.user_id = u.user_id
                        AND ut.company_id = ' . $companyId . '
                    WHERE
                        u.user_id =
                            (
                                SELECT
                                    last_value_respect_nulls(agent_id)
                                FROM
                                    (
                                        SELECT
                                            agent_id
                                        FROM 
                                            calls
                                        WHERE 
                                            company_id = ' . $companyId . '
                                            AND client_id = \'' . $clientId . '\'
                                        ORDER BY
                                            call_time DESC
                                    )
                            )
                    GROUP BY 
                        u.user_id
                ) la
                ON la.client_id = pc.client_id
            LEFT JOIN
                (
                    SELECT
                        client_id,
                        groupArray(
                            map
                            (
                                \'event_id\', toString(event_id),
                                \'category_id\', toString(category_id),
                                \'event_name\', event_name,
                                \'category_name\', category_name,
                                \'event_icon\', event_icon,
                                \'color_id\', toString(color_id),
                                \'fill_color_hex\', fill_color_hex,
                                \'outline_color_hex\', outline_color_hex,
                                \'count\', toString(count)
                            )
                        ) events
                    FROM
                        (
                            SELECT
                                client_id,
                                event_id,
                                event_category_id category_id,
                                any(event_name) event_name,
                                any(event_category_name) category_name,
                                any(event_icon) event_icon,
                                any(event_color_id) color_id,
                                any(event_fill_color_hex) fill_color_hex,
                                any(event_outline_color_hex) outline_color_hex,
                                COUNT(*) count
                            FROM
                                (
                                     ' . $this->getFinalTableSqlUsingGroupBy(
                                                'precalculated_calls_events',
                                                [
                                                'company_id',
                                                'role_id',
                                                'call_id',
                                                'paragraph',
                                                'event_id',
                                                ],
                                                'created',
                                                [
                                                'client_id',
                                                'event_category_id',
                                                'event_category_name',
                                                'event_name',
                                                'event_icon',
                                                'event_color_id',
                                                'event_fill_color_hex',
                                                'event_outline_color_hex',
                                                'event_is_deleted',
                                                ],
                                                [
                                                'company_id' => $companyId,
                                                'role_id' => $roleId,
                                                'client_id' => $clientId,
                                                ],
                                            ) . '
                                )
                            WHERE
                                event_is_deleted = 0
                            GROUP BY
                                client_id,
                                event_category_id,
                                event_id
                        )
                    GROUP BY
                        client_id
                ) cce
                ON cce.client_id = pc.client_id
            LEFT JOIN
                (
                    SELECT
                        client_id,
                        groupArray(
                            map
                            (
                                \'call_id\', call_id,
                                \'call_time\', toString(call_time),
                                \'start_time\', toString(paragraph_start_time),
                                \'paragraph_number\', toString(paragraph),
                                \'speaker_role\', toString(paragraph_speaker_role),
                                \'event_id\', toString(event_id),
                                \'event_name\', event_name,
                                \'event_text\', event_text,
                                \'event_en_text\', event_en_text,
                                \'event_highlight\', event_highlight,
                                \'event_en_highlight\', event_en_highlight,
                                \'event_icon\', event_icon,
                                \'color_id\', toString(event_color_id),
                                \'fill_color_hex\', event_fill_color_hex,
                                \'outline_color_hex\', event_outline_color_hex,
                                \'category_id\', toString(event_category_id),
                                \'category_name\', event_category_name
                            )
                        ) detailed_events
                    FROM
                        (
                             ' .
                                    $this->getFinalTableSqlUsingGroupBy(
                                        'precalculated_calls_events',
                                        [
                                            'company_id',
                                            'role_id',
                                            'call_id',
                                            'paragraph',
                                            'event_id',
                                        ],
                                        'created',
                                        [
                                            'client_id',
                                            'call_time',
                                            'paragraph_start_time',
                                            'paragraph_speaker_role',
                                            'event_name',
                                            'event_text',
                                            'event_en_text',
                                            'event_highlight',
                                            'event_en_highlight',
                                            'event_icon',
                                            'event_color_id',
                                            'event_fill_color_hex',
                                            'event_outline_color_hex',
                                            'event_is_deleted',
                                            'event_category_id',
                                            'event_category_name',
                                        ],
                                        [
                                            'company_id' => $companyId,
                                            'role_id' => $roleId,
                                            'client_id' => $clientId,
                                        ],
                                    ) . '
                        )
                    WHERE
                        event_is_deleted = 0
                    GROUP BY
                        client_id
                ) cde
                ON cde.client_id = pc.client_id
        ';
        // phpcs:enable

        return $this->getClient()->selectOne($sql);
    }

    /**
     * @param CandidatePagination $pagination
     * @param int $companyId
     * @param int $roleId
     * @param string $search
     * @return CandidatePagination
     */
    public function searchClients(
        CandidatePagination $pagination,
        int $companyId,
        int $roleId,
        string $search,
    ): CandidatePagination {
        $processedFilters = $this->getProcessedFilters($companyId, $pagination->getFilter());
        $pagination->setFilter($processedFilters);

        $this->paginate(
            $this->getClientCandidatesSql($companyId, $roleId, search: $search),
            'client_id',
            $pagination,
        );

        if ($pagination->getCount() === 0) {
            return $pagination;
        }

        // phpcs:disable
        $sql = '
            SELECT
                pc.company_id company_id,
                pc.role_id role_id,
                pc.client_id client_id,
                pc.agent_name agent_name,
                pc.team_name team_name,
                pc.total_calls_duration total_calls_duration,
                pc.effective_calls_duration effective_calls_duration,
                pc.not_effective_calls_duration not_effective_calls_duration,
                pc.effective_calls_count effective_calls_count,
                pc.not_effective_calls_count not_effective_calls_count,
                pc.reviewed_calls_count reviewed_calls_count,
                pc.reviewed_chats_count reviewed_chats_count,
                pc.score score,
                pc.risk_rank risk_rank,
                pc.last_call_time last_call_time,
                pc.client_name client_name,
                pc.country country,
                pc.status status,
                pc.source source,
                pc.acquisition_date acquisition_date,
                pc.is_converted is_converted,
                pc.converted_date converted_date,
                pc.last_transaction_date last_transaction_date,
                pc.campaign_id campaign_id,
                pc.value value
            FROM
                (
                    SELECT
                        client_id,
                        any(pci.company_id) company_id,
                        any(pci.role_id) role_id,
                        any(pci.total_calls_duration) total_calls_duration,
                        any(pci.effective_calls_duration) effective_calls_duration,
                        any(pci.not_effective_calls_duration) not_effective_calls_duration,
                        any(pci.effective_calls_count) effective_calls_count,
                        any(pci.not_effective_calls_count) not_effective_calls_count,
                        any(pci.reviewed_calls_count) reviewed_calls_count,
                        any(pci.reviewed_chats_count) reviewed_chats_count,
                        any(pci.score) score,
                        any(pci.risk_rank) risk_rank,
                        any(pci.last_call_time) last_call_time,
                        any(pci.client_name) client_name,
                        any(pci.country) country,
                        any(pci.status) status,
                        any(pci.source) source,
                        any(pci.acquisition_date) acquisition_date,
                        any(pci.is_converted) is_converted,
                        any(pci.converted_date) converted_date,
                        any(pci.last_transaction_date) last_transaction_date,
                        any(pci.campaign_id) campaign_id,
                        any(pci.value) value,
                        arrayReduce(\'groupUniqArray\',
                            arrayFilter(
                                team_name -> trim(team_name) <> \'\',
                                groupArray(ut.team_name)
                            )
                        ) team_name,
                        arrayReduce(\'groupUniqArray\',
                            arrayFilter(
                                user_name -> trim(user_name) <> \'\',
                                groupArray(u.user_name)
                            )
                        ) agent_name
                    FROM
                        (
                            SELECT
                                *,
                                agent_ids agent_id
                            FROM
                                (
                                    SELECT
                                        company_id company_id,
                                        role_id role_id,
                                        client_id client_id,
                                        arrayReduce(\'groupUniqArray\', flatten(groupArray(agent_ids))) agent_ids,
                                        toInt32(sum(total_calls_duration)) total_calls_duration,
                                        toInt32(sum(effective_calls_duration)) effective_calls_duration,
                                        toInt32(sum(not_effective_calls_duration)) not_effective_calls_duration,
                                        toInt32(sum(effective_calls_count)) effective_calls_count,
                                        toInt32(sum(not_effective_calls_count)) not_effective_calls_count,
                                        toInt32(sum(reviewed_calls_count)) reviewed_calls_count,
                                        toInt32(sum(reviewed_chats_count)) reviewed_chats_count,
                                        toInt32(sum(score)) score,
                                        toInt32(sum(risk_rank)) risk_rank,
                                        last_value_respect_nulls(last_call_time) last_call_time,
                                        last_value_respect_nulls(client_name) client_name,
                                        last_value_respect_nulls(country) country,
                                        last_value_respect_nulls(status) status,
                                        last_value_respect_nulls(source) source,
                                        last_value_respect_nulls(acquisition_date) acquisition_date,
                                        last_value_respect_nulls(is_converted) is_converted,
                                        last_value_respect_nulls(converted_date) converted_date,
                                        last_value_respect_nulls(last_transaction_date) last_transaction_date,
                                        last_value_respect_nulls(campaign_id) campaign_id,
                                        last_value_respect_nulls(value) value
                                    FROM
                                        (
                                        ' .
                                            $this->getFinalTableSqlUsingGroupBy(
                                                'precalculated_clients',
                                                [
                                                    'date',
                                                    'company_id',
                                                    'role_id',
                                                    'client_id',
                                                ],
                                                'created',
                                                [
                                                    'agent_ids',
                                                    'total_calls_duration',
                                                    'effective_calls_duration',
                                                    'not_effective_calls_duration',
                                                    'effective_calls_count',
                                                    'not_effective_calls_count',
                                                    'reviewed_calls_count',
                                                    'reviewed_chats_count',
                                                    'score',
                                                    'risk_rank',
                                                    'last_call_time',
                                                    'client_name',
                                                    'country',
                                                    'status',
                                                    'source',
                                                    'acquisition_date',
                                                    'is_converted',
                                                    'converted_date',
                                                    'last_transaction_date',
                                                    'campaign_id',
                                                    'value',
                                                    'created',
                                                ],
                                                [
                                                    'company_id' => $companyId,
                                                    'role_id' => $roleId,
                                                    'client_id' => $pagination->getCandidateIds(),
                                                ],
                                            ) . '
                                            ORDER BY
                                                created
                                        )
                                    GROUP BY
                                        company_id,
                                        role_id,
                                        client_id
                                )
                            ARRAY JOIN
                                agent_ids
                        ) pci
                        LEFT JOIN
                            (
                                SELECT
                                    user_id,
                                    user_name,
                                    team_name
                                FROM
                                    dictionary(users_teams)
                                WHERE
                                    company_id = ' . $companyId . '
                            ) ut
                            ON ut.user_id = pci.agent_id
                        LEFT JOIN dictionary(users) u ON u.user_id = pci.agent_id
                        GROUP BY
                            pci.client_id
                ) pc
        ';
        $sql .= $this->getClient()->getSortSql($pagination->getParams());
        // phpcs:enable

        $result = $this->getClient()->selectAll($sql);
        $pagination->setResult($result);

        return $pagination;
    }

    /**
     * @param int $companyId
     * @param array $filters
     * @return array
     */
    protected function getProcessedFilters(int $companyId, array $filters): array
    {
        if (isset($filters['min_value'])) {
            $filters['min_value'] = [
                'column' => 'value',
                'value' => (int) $filters['min_value'],
                'type' => 'compare',
                'compare' => '>=',
            ];
        }
        if (isset($filters['max_value'])) {
            $filters['max_value'] = [
                'column' => 'value',
                'value' => (int) $filters['max_value'],
                'type' => 'compare',
                'compare' => '<=',
            ];
        }
        if (isset($filters['min_last_transaction_date'])) {
            $filters['min_last_transaction_date'] = [
                'column' => 'last_transaction_date',
                'value' => Carbon::parse($filters['min_last_transaction_date'])->startOfDay(),
                'type' => 'date',
                'compare' => '>=',
            ];
        }
        if (isset($filters['max_last_transaction_date'])) {
            $filters['max_last_transaction_date'] = [
                'column' => 'last_transaction_date',
                'value' => Carbon::parse($filters['max_last_transaction_date'])->endOfDay(),
                'type' => 'date',
                'compare' => '<=',
            ];
        }
        if (isset($filters['min_converted_date'])) {
            $filters['min_converted_date'] = [
                'column' => 'converted_date',
                'value' => Carbon::parse($filters['min_converted_date'])->startOfDay(),
                'type' => 'date',
                'compare' => '>=',
            ];
        }
        if (isset($filters['max_converted_date'])) {
            $filters['max_converted_date'] = [
                'column' => 'converted_date',
                'value' => Carbon::parse($filters['max_converted_date'])->endOfDay(),
                'type' => 'date',
                'compare' => '<=',
            ];
        }
        if (isset($filters['min_acquisition_date'])) {
            $filters['min_acquisition_date'] = [
                'column' => 'acquisition_date',
                'value' => Carbon::parse($filters['min_acquisition_date'])->startOfDay(),
                'type' => 'date',
                'compare' => '>=',
            ];
        }
        if (isset($filters['max_acquisition_date'])) {
            $filters['max_acquisition_date'] = [
                'column' => 'acquisition_date',
                'value' => Carbon::parse($filters['max_acquisition_date'])->endOfDay(),
                'type' => 'date',
                'compare' => '<=',
            ];
        }
        $filters['event_ids'] = isset($filters['event_ids']) && is_array($filters['event_ids']) && count($filters['event_ids']) > 0
            ? [
                'column' => 'event_ids',
                'value' => 'hasAny(event_ids, [' . implode(',', $filters['event_ids']) . '])',
                'type' => 'expression',
            ]
            : [];
        $filters['event_category_ids'] = isset($filters['event_category_ids']) && is_array($filters['event_category_ids']) && count($filters['event_category_ids']) > 0
            ? [
                'value' => 'hasAny(event_category_ids, [' . implode(',', $filters['event_category_ids']) . '])',
                'type' => 'expression',
            ]
            : [];
        $filters['agent_ids'] = isset($filters['agent_ids']) && is_array($filters['agent_ids']) && count($filters['agent_ids']) > 0
            ? [
                'value' => 'hasAny(agent_ids, [' . implode(',', $filters['agent_ids']) . '])',
                'type' => 'expression',
            ]
            : [];
        $filters['team_ids'] = isset($filters['team_ids']) && is_array($filters['team_ids']) && count($filters['team_ids']) > 0
            ? [
                'value' => 'hasAny(
                    agent_ids,
                    (
                        SELECT 
                            groupArray(user_id)
                        FROM 
                            dictionary(users_teams) 
                        WHERE
                            company_id = ' . $companyId . '
                            AND team_id IN (' . implode(',', $filters['team_ids']) . ')
                    )
                )',
                'type' => 'expression',
            ]
            : [];
        return $filters;
    }

    /**
     * @param int $companyId
     * @param int $roleId
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @param string|null $search
     * @return string
     */
    protected function getClientCandidatesSql(
        int $companyId,
        int $roleId,
        ?Carbon $startDate = null,
        ?Carbon $endDate = null,
        string $search = null
    ): string {
        $internalFilter = $this->getInternalFilters($companyId, $roleId, $startDate, $endDate, $search);
        return '
            SELECT DISTINCT
                client_id
            FROM
                (
                    SELECT
                        company_id company_id,
                        role_id role_id,
                        client_id client_id,
                        arrayFlatten(groupArray(event_ids)) event_ids,
                        arrayFlatten(groupArray(agent_ids)) agent_ids,
                        arrayFlatten(groupArray(event_category_ids)) event_category_ids,
                        toInt32(sum(total_calls_duration)) total_calls_duration,
                        toInt32(sum(effective_calls_duration)) effective_calls_duration,
                        toInt32(sum(not_effective_calls_duration)) not_effective_calls_duration,
                        toInt32(sum(effective_calls_count)) effective_calls_count,
                        toInt32(sum(not_effective_calls_count)) not_effective_calls_count,
                        toInt32(sum(reviewed_calls_count)) reviewed_calls_count,
                        toInt32(sum(reviewed_chats_count)) reviewed_chats_count,
                        toInt32(sum(score)) score,
                        toInt32(sum(risk_rank)) risk_rank,
                        last_value_respect_nulls(last_call_time) last_call_time,
                        last_value_respect_nulls(client_name) client_name,
                        last_value_respect_nulls(country) country,
                        last_value_respect_nulls(status) status,
                        last_value_respect_nulls(source) source,
                        last_value_respect_nulls(acquisition_date) acquisition_date,
                        last_value_respect_nulls(is_converted) is_converted,
                        last_value_respect_nulls(converted_date) converted_date,
                        last_value_respect_nulls(last_transaction_date) last_transaction_date,
                        last_value_respect_nulls(campaign_id) campaign_id,
                        last_value_respect_nulls(value) value
                    FROM
                        (
                        ' .
                            $this->getFinalTableSqlUsingGroupBy(
                                'precalculated_clients',
                                [
                                    'date',
                                    'company_id',
                                    'role_id',
                                    'client_id',
                                ],
                                'created',
                                [
                                    'event_ids',
                                    'agent_ids',
                                    'event_category_ids',
                                    'total_calls_duration',
                                    'effective_calls_duration',
                                    'not_effective_calls_duration',
                                    'effective_calls_count',
                                    'not_effective_calls_count',
                                    'reviewed_calls_count',
                                    'reviewed_chats_count',
                                    'score',
                                    'risk_rank',
                                    'last_call_time',
                                    'client_name',
                                    'country',
                                    'status',
                                    'source',
                                    'acquisition_date',
                                    'is_converted',
                                    'converted_date',
                                    'last_transaction_date',
                                    'campaign_id',
                                    'value',
                                    'created',
                                ],
                                $internalFilter,
                            ) . '
                            ORDER BY
                                created
                        )
                    GROUP BY
                        company_id,
                        role_id,
                        client_id
                )
        ';
    }

    /**
     * @param int $companyId
     * @param int $roleId
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @param string|null $search
     * @return int[]
     */
    protected function getInternalFilters(int $companyId, int $roleId, ?Carbon $startDate, ?Carbon $endDate, string $search = null): array
    {
        $internalFilter = [
            'company_id' => $companyId,
            'role_id' => $roleId,
        ];
        if ($startDate instanceof Carbon) {
            $internalFilter['start_date'] = [
                'column' => 'date',
                'value' => $startDate,
                'type' => 'date',
                'compare' => '>=',
            ];
        }
        if ($endDate instanceof Carbon) {
            $internalFilter['end_date'] = [
                'column' => 'date',
                'value' => $endDate,
                'type' => 'date',
                'compare' => '<=',
            ];
        }
        if (!empty($search)) {
            $internalFilter['client'] = [
                'value' => 'client_name LIKE \'%' . $search . '%\' OR client_id LIKE \'%' . $search . '%\'',
                'type' => 'expression',
            ];
        }
        return $internalFilter;
    }
}
