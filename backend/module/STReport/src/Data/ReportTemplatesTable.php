<?php

declare(strict_types=1);

namespace STReport\Data;

use <PERSON>inas\Db\ResultSet\ResultSetInterface;
use ST<PERSON>pi\Entity\Exception\NotFoundApiException;
use STLib\Db\AbstractTable;
use STReport\Entity\ReportTemplate;

class ReportTemplatesTable extends AbstractTable
{
    /**
     * @param int $reportTemplateId
     * @return ResultSetInterface
     * @throws NotFoundApiException
     */
    public function getTemplate(int $reportTemplateId, int $userId = null): ResultSetInterface
    {
        $where = [
            'report_template_id' => $reportTemplateId,
        ];
        if (is_int($userId)) {
            $where['user_id'] = $userId;
        }
        $result = $this->tableGateway->select($where);

        if ($result->count() === 0) {
            throw new NotFoundApiException('Report template not found');
        }

        return $result;
    }

    /**
     * @param int $userId
     * @return ResultSetInterface
     * @throws NotFoundApiException
     */
    public function getTemplates(int $userId): ResultSetInterface
    {
        return $this->tableGateway->select(['user_id' => $userId]);
    }

    /**
     * @param ReportTemplate $template
     * @return int
     */
    public function saveTemplate(ReportTemplate $template): int
    {
        $templateData =   [
            'report' => $template->getReport(),
            'filters' => json_encode($template->getFilters()),
            'icon' => $template->getIcon(),
            'name' => $template->getName(),
        ];

        if ($template->getId() > 0) {
            $this->tableGateway->update(
                $templateData,
                ['report_template_id' => $template->getId()]
            );
        } else {
            $this->tableGateway->insert(
                array_merge($templateData, ['user_id' => $template->getUserId()])
            );
            $template->setId((int) $this->tableGateway->lastInsertValue);
        }
        return $template->getId();
    }

    /**
     * @param int $templateId
     * @return int
     */
    public function deleteTemplate(int $templateId): int
    {
        return $this->tableGateway->delete(['report_template_id' => $templateId]);
    }
}
