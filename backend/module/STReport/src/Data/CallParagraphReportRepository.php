<?php

declare(strict_types=1);

namespace STReport\Data;

use Carbon\Carbon;
use STClickhouse\Data\QueriesTrait;
use STClickhouse\Entity\BaseTable;
use STClickhouse\Entity\Pagination\CandidatePagination;
use STCompany\Entity\Company;

class CallParagraphReportRepository extends BaseTable
{
    use QueriesTrait;

    /**
     * @param CandidatePagination $pagination
     * @param Company $company
     * @param string $search
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return CandidatePagination
     */
    public function searchCallsParagraphs(
        CandidatePagination $pagination,
        Company $company,
        string $search,
        Carbon $startDate,
        Carbon $endDate,
    ): CandidatePagination {
        $this->paginate(
            $this->getCallsParagraphsCandidatesSql($company, $search, $startDate, $endDate),
            'paragraph_id',
            $pagination,
        );

        if ($pagination->getCount() === 0) {
            return $pagination;
        }

        $candidatesIdsString = "'" . implode("', '", $pagination->getCandidateIds()) . "'";

        $sql = <<<SQL
            SELECT
                paragraph_id,
                company_id,
                call_id,
                call_time,
                paragraph_number,
                speaker_number,
                speaker_role,
                start_time,
                end_time,
                timestamp,
                decrypt('aes-256-ofb', text,  '{$company->getEncryptionKey()}') text,
                decrypt('aes-256-ofb', en_text,  '{$company->getEncryptionKey()}') en_text,
                events,
                created
            FROM
            (
                SELECT 
                    company_id,
                    call_id,
                    paragraph_number,                    
                    concatWithSeparator('_', company_id, call_id, paragraph_number) paragraph_id,
                    last_value_respect_nulls(call_time) call_time,
                    last_value_respect_nulls(speaker_number) speaker_number,
                    last_value_respect_nulls(speaker_role) speaker_role,
                    last_value_respect_nulls(start_time) start_time,
                    last_value_respect_nulls(end_time) end_time,
                    last_value_respect_nulls(timestamp) timestamp,
                    last_value_respect_nulls(text) text,
                    last_value_respect_nulls(en_text) en_text,
                    last_value_respect_nulls(created) created
                FROM calls_paragraphs
                WHERE 
                    company_id = {$company->getId()}
                    AND paragraph_id IN ({$candidatesIdsString})
                GROUP BY company_id, call_id, paragraph_number, paragraph_id
                ORDER BY created
            ) cp
            LEFT JOIN
                (
                    SELECT
                        paragraph_id,
                        groupArray(
                            map(
                                'event_id', toString(event_id),
                                'category_id', toString(category_id),
                                'event_name', event_name,
                                'category_name', category_name,
                                'event_icon', event_icon,
                                'event_highlight', event_highlight,
                                'event_en_highlight', event_en_highlight,
                                'color_id', toString(color_id),
                                'fill_color_hex', fill_color_hex,
                                'outline_color_hex', outline_color_hex
                            )
                        ) events
                    FROM
                    (
                        SELECT
                            concatWithSeparator('_', company_id, call_id, paragraph) paragraph_id,
                            last_value_respect_nulls(event_id) event_id,
                            last_value_respect_nulls(event_category_id) category_id,
                            last_value_respect_nulls(event_name) event_name,
                            last_value_respect_nulls(event_category_name) category_name,
                            last_value_respect_nulls(event_icon) event_icon,
                            last_value_respect_nulls(event_highlight) event_highlight,
                            last_value_respect_nulls(event_en_highlight) event_en_highlight,
                            last_value_respect_nulls(event_color_id) color_id,
                            last_value_respect_nulls(event_fill_color_hex) fill_color_hex,
                            last_value_respect_nulls(event_outline_color_hex) outline_color_hex
                        FROM
                            precalculated_calls_events
                        WHERE
                            company_id = {$company->getId()}
                            AND paragraph_id IN ({$candidatesIdsString})
                            AND event_is_deleted = 0
                        GROUP BY
                            company_id, call_id, paragraph
                    )
                    GROUP BY
                        paragraph_id
                ) pe
            ON pe.paragraph_id = cp.paragraph_id
            ORDER BY call_time DESC
        SQL;

        $sql .= $this->getClient()->getSortSql($pagination->getParams());
        $result = $this->getClient()->selectAll($sql);
        $pagination->setResult($result);

        return $pagination;
    }

    /**
     * @param Company $company
     * @param string $search
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return string
     */
    protected function getCallsParagraphsCandidatesSql(
        Company $company,
        string $search,
        Carbon $startDate,
        Carbon $endDate,
    ): string {
        return <<<SQL
            SELECT
                concatWithSeparator('_', company_id, call_id, paragraph_number) paragraph_id
            FROM
            (
                {$this->getFinalTableSqlUsingGroupBy(
                    'calls_paragraphs',
                    [
                        'company_id',
                        'call_id',
                        'paragraph_number',
                    ],
                    'created',
                    [],
                    [
                        'company_id' => $company->getId(),
                        [
                            'column' => 'decrypt(\'aes-256-ofb\', text, \'' . $company->getEncryptionKey() . '\')',
                            'value' => $search,
                            'type' => 'like',
                        ],                 
                        [
                            'column' => 'call_time',
                            'value' => $startDate,
                            'type' => 'date',
                            'compare' => '>=',
                        ],                
                        [
                            'column' => 'call_time',
                            'value' => $endDate,
                            'type' => 'date',
                            'compare' => '<=',
                        ],
                    ]
                )}
            ) cp
            INNER JOIN (
                {$this->getFinalTableSqlUsingGroupBy(
                    'calls',
                    [
                        'company_id',
                        'call_id',
                    ],
                    'created',
                    [],
                    [
                        'company_id' => $company->getId(),
                        'is_deleted' => 0,
                        [
                            'column' => 'call_time',
                            'value' => $startDate,
                            'type' => 'date',
                            'compare' => '>=',
                        ],
                        [
                            'column' => 'call_time',
                            'value' => $endDate,
                            'type' => 'date',
                            'compare' => '<=',
                        ],
                    ]
                )}
            ) c
            ON cp.call_id = c.call_id
        SQL;
    }
}
