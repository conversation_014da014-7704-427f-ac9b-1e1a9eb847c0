<?php

declare(strict_types=1);

namespace STReport\Controller\Plugin;

use Laminas\Mvc\Controller\Plugin\AbstractPlugin;

class Report extends AbstractPlugin
{
    /**
     *
     * @return \STReport\Service\ClientReportService
     */
    public function client(): \STReport\Service\ClientReportService
    {
        return $this->getController()->getServiceManager()->get(\STReport\Service\ClientReportService::class);
    }

    /**
     *
     * @return \STReport\Service\AgentReportService
     */
    public function agent(): \STReport\Service\AgentReportService
    {
        return $this->getController()->getServiceManager()->get(\STReport\Service\AgentReportService::class);
    }

    /**
     *
     * @return \STReport\Service\CallParagraphReportService
     */
    public function callParagpaph(): \STReport\Service\CallParagraphReportService
    {
        return $this->getController()->getServiceManager()->get(\STReport\Service\CallParagraphReportService::class);
    }

    /**
     *
     * @return \STReport\Service\CallReportService
     */
    public function call(): \STReport\Service\CallReportService
    {
        return $this->getController()->getServiceManager()->get(\STReport\Service\CallReportService::class);
    }

    /**
     *
     * @return \STReport\Service\ReportTemplateService
     */
    public function template(): \STReport\Service\ReportTemplateService
    {
        return $this->getController()->getServiceManager()->get(\STReport\Service\ReportTemplateService::class);
    }
}
