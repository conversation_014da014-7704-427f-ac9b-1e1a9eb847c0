<?php

declare(strict_types=1);

namespace STReport\Validator;

use <PERSON><PERSON>\Validator\NotEmpty;
use <PERSON><PERSON>\Validator\StringLength;
use <PERSON>inas\Validator\InArray;
use STLib\Validator\Validator;

class ReportTemplateValidator extends Validator
{
    protected const INCORRECT_REPORT = 'Incorrect value for report';
    protected const INCORRECT_NAME = 'Template name must be not empty and less than 255 symbols';
    protected const INCORRECT_ICON = 'Template icon must be not empty and less than 255 symbols';
    protected const INCORRECT_FILTERS = 'Filters field is empty or content is not valid JSON';

    /**
     *
     * @see \STLib\Validator\ValidatorInterface::run()
     */
    public function run(): void
    {
        /** @var \STReport\Entity\ReportTemplate $templateData */
        $reportTemplate = $this->getInstance();

        $this->checkReport($reportTemplate->getReport());
        $this->checkName($reportTemplate->getName());
        $this->checkIcon($reportTemplate->getIcon());
        $this->checkFilters($reportTemplate->getFilters());
    }

    /**
     * @param string|null $report
     * @return void
     */
    private function checkReport(?string $report): void
    {
        $reportValidator = new InArray([
            'haystack' => \STReport\Entity\ReportTemplate::REPORTS_VALUES,
            'strict' => true,
        ]);
        if (!$reportValidator->isValid($report)) {
            $this->addError('report', static::INCORRECT_REPORT . ', possible values are "' . implode('","', \STReport\Entity\ReportTemplate::REPORTS_VALUES) . '"');
        }
    }

    /**
     * @param string|null $name
     * @return void
     */
    private function checkName(?string $name): void
    {
        $lengthValidator = new StringLength([
            'min' => 1,
            'max' => 255,
        ]);
        if (!$lengthValidator->isValid($name)) {
            $this->addError('name', static::INCORRECT_NAME);
        }
    }

    /**
     * @param string|null $icon
     * @return void
     */
    private function checkIcon(?string $icon): void
    {
        $lengthValidator = new StringLength([
            'min' => 1,
            'max' => 255,
        ]);
        if (!$lengthValidator->isValid($icon)) {
            $this->addError('icon', static::INCORRECT_ICON);
        }
    }

    /**
     * @param $filters
     * @return void
     */
    private function checkFilters($filters): void
    {
        $validator = new NotEmpty();

        if (!$validator->isValid($filters) || !is_array($filters)) {
            $this->addError('filters', static::INCORRECT_FILTERS);
        }
    }
}
