<?php

namespace STReport\Validator;

use Api\Controller\V0\SearchEngineController;
use <PERSON><PERSON>\Validator\DateComparison;
use Lam<PERSON>\Validator\StringLength;
use STLib\Validator\Validator;

class CallParagraphsSearchValidator extends Validator
{
    public function run(): void
    {
        /** @var array $input */
        $input = $this->getInstance();
        $search = $input['search'];
        $startDate = $input['start_date'];
        $endDate = $input['end_date'];

        $threeMonthsAgoEndDate = (clone $endDate)->subMonths(SearchEngineController::SEARCH_PARAGRAPHS_MAX_INTERVAL_IN_MONTHS);
        $startDateValidator = new DateComparison([
            'min' => $threeMonthsAgoEndDate,
            'inclusiveMax' => true,
        ]);

        if (!empty($startDate) && !$startDateValidator->isValid($startDate)) {
            $this->addError('start_date', 'start_date should be no longer than 3 months');
        }

        $searchValidator = new StringLength([
            'min' => 3,
        ]);

        if (!$searchValidator->isValid($search)) {
            $this->addError('search', 'search string should have at least 3 symbols');
        }
    }
}
