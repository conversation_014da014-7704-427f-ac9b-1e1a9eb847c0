<?php

namespace STReport\Service\Calendar;

class Month implements PeriodDimensionInterface
{
    /**
     *
     * @param \Carbon\Carbon $date
     * @return \Carbon\Carbon
     */
    public function normalizeStartDate(\Carbon\Carbon $date): \Carbon\Carbon
    {
        return $date->startOfMonth();
    }

    /**
     *
     * @param \Carbon\Carbon $date
     * @return \Carbon\Carbon
     */
    public function normalizeEndDate(\Carbon\Carbon $date): \Carbon\Carbon
    {
        return $date->endOfMonth();
    }

    /**
     *
     * @param \Carbon\Carbon $date
     * @return \Carbon\Carbon
     */
    public function addPeriod(\Carbon\Carbon $date): \Carbon\Carbon
    {
        return $date->addMonth();
    }

    /**
     *
     * @param \Carbon\Carbon $date
     * @return string
     */
    public function toMajorString(\Carbon\Carbon $date): string
    {
        return $date->format('F');
    }

    /**
     *
     * @param \Carbon\Carbon $date
     * @return string|null
     */
    public function toMinorString(\Carbon\Carbon $date): ?string
    {
        return null;
    }
}
