<?php

namespace STReport\Service\Calendar;

interface PeriodDimensionInterface
{
    /**
     *
     * @param \Carbon\Carbon $date
     * @return \Carbon\Carbon
     */
    public function normalizeStartDate(\Carbon\Carbon $date): \Carbon\Carbon;

    /**
     *
     * @param \Carbon\Carbon $date
     * @return \Carbon\Carbon
     */
    public function normalizeEndDate(\Carbon\Carbon $date): \Carbon\Carbon;

    /**
     *
     * @param \Carbon\Carbon $date
     * @return \Carbon\Carbon
     */
    public function addPeriod(\Carbon\Carbon $date): \Carbon\Carbon;

    /**
     *
     * @param \Carbon\Carbon $date
     * @return string
     */
    public function toMajorString(\Carbon\Carbon $date): string;

    /**
     *
     * @param \Carbon\Carbon $date
     * @return string|null
     */
    public function toMinorString(\Carbon\Carbon $date): ?string;
}
