<?php

namespace STReport\Service\Calendar;

class Day implements PeriodDimensionInterface
{
    /**
     *
     * @param \Carbon\Carbon $date
     * @return \Carbon\Carbon
     */
    public function normalizeStartDate(\Carbon\Carbon $date): \Carbon\Carbon
    {
        return $date->startOfDay();
    }

    /**
     *
     * @param \Carbon\Carbon $date
     * @return \Carbon\Carbon
     */
    public function normalizeEndDate(\Carbon\Carbon $date): \Carbon\Carbon
    {
        return $date->endOfDay();
    }

    /**
     *
     * @param \Carbon\Carbon $date
     * @return \Carbon\Carbon
     */
    public function addPeriod(\Carbon\Carbon $date): \Carbon\Carbon
    {
        return $date->addDay();
    }

    /**
     *
     * @return string
     */
    public function toMajorString(\Carbon\Carbon $date): string
    {
        if ($date->isCurrentMonth()) {
            return $date->format('d');
        }
        return $date->format('d/m');
    }

    /**
     *
     * @param \Carbon\Carbon $date
     * @return string|null
     */
    public function toMinorString(\Carbon\Carbon $date): ?string
    {
        return $date->format('D');
    }
}
