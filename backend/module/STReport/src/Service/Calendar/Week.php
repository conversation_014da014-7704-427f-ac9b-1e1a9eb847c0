<?php

namespace STReport\Service\Calendar;

class Week implements PeriodDimensionInterface
{
    /**
     *
     * @param \Carbon\Carbon $date
     * @return \Carbon\Carbon
     */
    public function normalizeStartDate(\Carbon\Carbon $date): \Carbon\Carbon
    {
        return $date->startOfWeek();
    }

    /**
     *
     * @param \Carbon\Carbon $date
     * @return \Carbon\Carbon
     */
    public function normalizeEndDate(\Carbon\Carbon $date): \Carbon\Carbon
    {
        return $date->endOfWeek();
    }

    /**
     *
     * @param \Carbon\Carbon $date
     * @return \Carbon\Carbon
     */
    public function addPeriod(\Carbon\Carbon $date): \Carbon\Carbon
    {
        return $date->addWeek();
    }

    /**
     *
     * @param \Carbon\Carbon $date
     * @return string
     */
    public function toMajorString(\Carbon\Carbon $date): string
    {
        $clonedDate = clone $date;
        $result = $clonedDate->startOfWeek()->format('d/m') . ' - ' . $clonedDate->endOfWeek()->format('d/m');
        unset($clonedDate);
        return $result;
    }

    /**
     *
     * @param \Carbon\Carbon $date
     * @return string|null
     */
    public function toMinorString(\Carbon\Carbon $date): ?string
    {
        return null;
    }
}
