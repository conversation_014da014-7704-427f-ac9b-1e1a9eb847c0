<?php

declare(strict_types=1);

namespace STReport\Service;

use Carbon\Carbon;
use STCompany\Entity\Company;
use STClickhouse\Entity\Pagination\CandidatePagination;
use STLib\Mvc\Hydrator\BaseHydratorTrait;
use STReport\Data\CallParagraphReportRepository;

class CallParagraphReportService
{
    use BaseHydratorTrait;

    public function __construct(
        protected CallParagraphReportRepository $callParagraphReportRepository
    ) {
    }

    public function searchCallParagraphs(
        CandidatePagination $pagination,
        Company $company,
        string $search,
        Carbon $startDate,
        Carbon $endDate,
    ): CandidatePagination {
        return $this->callParagraphReportRepository->searchCallsParagraphs(
            $pagination,
            $company,
            $search,
            $startDate,
            $endDate,
        );
    }
}
