<?php

declare(strict_types=1);

namespace STReport\Service;

class ClientReportService
{
    /**
     * @var \STReport\Data\ClientReportRepository
     */
    protected \STReport\Data\ClientReportRepository $clientReportRepository;

    /**
     * @param \STReport\Data\ClientReportRepository $clientReportRepository
     */
    public function __construct(\STReport\Data\ClientReportRepository $clientReportRepository)
    {
        $this->clientReportRepository = $clientReportRepository;
    }

    /**
     * @param \STClickhouse\Entity\Pagination\CandidatePagination $pagination
     * @param int $companyId
     * @param int $roleId
     * @param \Carbon\Carbon|null $startDate
     * @param \Carbon\Carbon|null $endDate
     * @return \STClickhouse\Entity\Pagination\CandidatePagination
     */
    public function getClientsReport(
        \STClickhouse\Entity\Pagination\CandidatePagination $pagination,
        int $companyId,
        int $roleId,
        ?\Carbon\Carbon $startDate = null,
        ?\Carbon\Carbon $endDate = null,
    ): \STClickhouse\Entity\Pagination\CandidatePagination {
        return $this->clientReportRepository->getClientsReport($pagination, $companyId, $roleId, $startDate, $endDate);
    }

    /**
     * @param int $companyId
     * @param int $roleId
     * @param \Carbon\Carbon|null $startDate
     * @param \Carbon\Carbon|null $endDate
     * @param array $filters
     * @param array $columns
     * @return \Iterator
     */
    public function getClientsCsvReport(
        int $companyId,
        int $roleId,
        ?\Carbon\Carbon $startDate = null,
        ?\Carbon\Carbon $endDate = null,
        array $filters = [],
        array $columns = [],
    ): \Iterator {
        return $this->clientReportRepository->getClientsCsvReport($companyId, $roleId, $startDate, $endDate, $filters, $columns);
    }

    /**
     * @param int $companyId
     * @param int $roleId
     * @param string $clientId
     * @return array|null
     */
    public function getClientReport(int $companyId, int $roleId, string $clientId): ?array
    {
        return $this->clientReportRepository->getClientReport($companyId, $roleId, $clientId);
    }

    /**
     * @param \STClickhouse\Entity\Pagination\CandidatePagination $pagination
     * @param int $companyId
     * @param int $roleId
     * @param string $search
     * @return \STClickhouse\Entity\Pagination\CandidatePagination
     */
    public function searchClients(
        \STClickhouse\Entity\Pagination\CandidatePagination $pagination,
        int $companyId,
        int $roleId,
        string $search,
    ): \STClickhouse\Entity\Pagination\CandidatePagination {
        return $this->clientReportRepository->searchClients($pagination, $companyId, $roleId, $search);
    }
}
