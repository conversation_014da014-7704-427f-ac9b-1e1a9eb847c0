<?php

declare(strict_types=1);

namespace STReport\Service;

use STLib\Mvc\Hydrator\BaseHydratorTrait;
use STReport\Data\ReportTemplatesTable;
use STReport\Entity\ReportTemplate;
use STReport\Entity\ReportTemplateCollection;

class ReportTemplateService
{
    use BaseHydratorTrait;

    /**
     * @var ReportTemplatesTable
     */
    protected ReportTemplatesTable $reportTemplateTable;

    /**
     * @param ReportTemplatesTable $reportTemplateTable
     */
    public function __construct(ReportTemplatesTable $reportTemplateTable)
    {
        $this->reportTemplateTable = $reportTemplateTable;
    }

    /**
     * @param int $userId
     * @return ReportTemplateCollection
     * @throws \STApi\Entity\Exception\NotFoundApiException
     */
    public function getReportTemplates(int $userId): ReportTemplateCollection
    {
        $collection = new ReportTemplateCollection();
        $data = $this->reportTemplateTable->getTemplates($userId);

        foreach ($data as $templateData) {
            $collection->add($this->hydrateTemplateReport((array) $templateData));
        }

        return $collection;
    }

    /**
     * @param int $templateId
     * @param int|null $userId
     * @return ReportTemplate
     * @throws \STApi\Entity\Exception\NotFoundApiException
     */
    public function getReportTemplate(int $templateId, int $userId = null): ReportTemplate
    {
        return $this->hydrateTemplateReport(
            (array) $this->reportTemplateTable->getTemplate($templateId, $userId)->current()
        );
    }

    /**
     * @param ReportTemplate $template
     * @return int
     */
    public function saveReportTemplate(ReportTemplate $template): int
    {
        return $this->reportTemplateTable->saveTemplate($template);
    }

    /**
     * @param int $reportTemplateId
     * @return int
     */
    public function deleteTemplate(int $reportTemplateId): int
    {
        return $this->reportTemplateTable->deleteTemplate($reportTemplateId);
    }

    /**
     * @param array $templateData
     * @return ReportTemplate
     */
    private function hydrateTemplateReport(array $templateData): ReportTemplate
    {
        $templateData['filters'] = json_decode($templateData['filters'], true);
        /** @var ReportTemplate $reportTemplate */
        $reportTemplate = $this->hydrate($templateData, ReportTemplate::class);
        return $reportTemplate;
    }
}
