<?php

declare(strict_types=1);

namespace STReport\Service;

use Carbon\Carbon;
use Laminas\Filter\Word\DashToCamelCase;
use LogicException;
use <PERSON><PERSON><PERSON>house\Entity\Pagination\CandidatePagination;
use STCompany\Entity\Company;
use STReport\Data\AgentReportRepository;
use STReport\Service\Calendar\PeriodDimensionInterface;

class AgentReportService
{
    /**
     * @var AgentReportRepository
     */
    protected AgentReportRepository $agentReportRepository;

    /**
     * @param AgentReportRepository $agentReportRepository
     */
    public function __construct(AgentReportRepository $agentReportRepository)
    {
        $this->agentReportRepository = $agentReportRepository;
    }

    /**
     *
     * @param int $companyId
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @param string $dateDimension
     * @param array $teamIds
     * @param int|null $agentId
     * @return array
     */
    public function getCalendar(
        int $companyId,
        Carbon $startDate,
        Carbon $endDate,
        string $dateDimension,
        array $teamIds,
        ?int $agentId,
    ): array {
        $periodDimension = $this->getPeriodDeminsionObject($dateDimension);
        $periodDimension->normalizeStartDate($startDate);
        $periodDimension->normalizeEndDate($endDate);

        $callsCountData = $this->agentReportRepository->getCallsCount($companyId, $dateDimension, $teamIds, $startDate, $endDate, $agentId);

        $calendar = [];
        while ($startDate->lessThanOrEqualTo($endDate)) {
            $calendar[] = [
                'major' => $periodDimension->toMajorString($startDate),
                'minor' => $periodDimension->toMinorString($startDate),
                'calls_count' => (int) ($callsCountData[$startDate->format('Y-m-d')]['calls_count'] ?? 0),
                'chats_count' => (int) ($callsCountData[$startDate->format('Y-m-d')]['chats_count'] ?? 0),
                'start_date' => $startDate->toISOString(),
            ];
            $periodDimension->addPeriod($startDate);
        }
        return $calendar;
    }

    /**
     * @param Company $company
     * @param int $roleId
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @param array $filters
     * @return array
     */
    public function getAgentsReport(
        Company $company,
        int $roleId,
        ?Carbon $startDate = null,
        ?Carbon $endDate = null,
        array $filters = [],
    ): array {
        return $this->agentReportRepository->getAgentsReport($company, $roleId, $startDate, $endDate, $filters);
    }

    /**
     * @param Company $company
     * @param int $roleId
     * @param int $agentId
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @return ?array
     */
    public function getAgentReport(
        Company $company,
        int $roleId,
        int $agentId,
        ?Carbon $startDate = null,
        ?Carbon $endDate = null,
    ): ?array {
        return $this->agentReportRepository->getAgentReport($company, $roleId, $agentId, $startDate, $endDate);
    }

    /**
     *
     * @param string $dimension
     * @return Calendar\PeriodDimensionInterface
     * @throws LogicException
     */
    protected function getPeriodDeminsionObject(string $dimension): PeriodDimensionInterface
    {
        $dashToCamelCase = new DashToCamelCase();
        $namespace = '\STReport\Service\Calendar\\' . $dashToCamelCase->filter($dimension);
        if (!class_exists($namespace)) {
            throw new LogicException('Can\'t find period dimension class "' . $namespace . '"');
        }
        return new $namespace();
    }

    /**
     * @param CandidatePagination $pagination
     * @param int $companyId
     * @param string $search
     * @return CandidatePagination
     */
    public function searchAgents(
        CandidatePagination $pagination,
        int $companyId,
        string $search,
    ): CandidatePagination {
        return $this->agentReportRepository->searchAgents($pagination, $companyId, $search);
    }
}
