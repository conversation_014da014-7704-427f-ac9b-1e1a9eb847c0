<?php

declare(strict_types=1);

namespace STReport\Service;

use Carbon\Carbon;
use STClickhouse\Entity\Pagination\CandidatePagination;

class CallReportService
{
    /**
     * @var \STReport\Data\CallReportRepository
     */
    protected \STReport\Data\CallReportRepository $callReportRepository;

    /**
     * @param \STReport\Data\CallReportRepository $callReportRepository
     */
    public function __construct(\STReport\Data\CallReportRepository $callReportRepository)
    {
        $this->callReportRepository = $callReportRepository;
    }

    /**
     * @param CandidatePagination $pagination
     * @param int $companyId
     * @param int $roleId
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @return CandidatePagination
     */
    public function getCalls(
        CandidatePagination $pagination,
        int $companyId,
        int $roleId,
        ?Carbon $startDate = null,
        ?Carbon $endDate = null,
    ): CandidatePagination {
        return $this->callReportRepository->getCalls($pagination, $companyId, $roleId, $startDate, $endDate);
    }

    /**
     * @param CandidatePagination $pagination
     * @param int $companyId
     * @param int $roleId
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @return CandidatePagination
     */
    public function getCallsReport(
        CandidatePagination $pagination,
        int $companyId,
        int $roleId,
        ?Carbon $startDate = null,
        ?Carbon $endDate = null,
    ): CandidatePagination {
        return $this->callReportRepository->getCallsReport($pagination, $companyId, $roleId, $startDate, $endDate);
    }

    /**
     * @param int $companyId
     * @param int $roleId
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @param array $filters
     * @param array $columns
     * @param string $baseCallUrl
     * @return string
     */
    public function getCallsCsvReport(
        int $companyId,
        int $roleId,
        ?Carbon $startDate = null,
        ?Carbon $endDate = null,
        array $filters = [],
        array $columns = [],
        string $baseCallUrl = '',
    ): string {
        return $this->callReportRepository->getCallsCsvReport($companyId, $roleId, $startDate, $endDate, $filters, $columns, $baseCallUrl);
    }

    /**
     * @param CandidatePagination $pagination
     * @param int $companyId
     * @param int $roleId
     * @param string $search
     * @return CandidatePagination
     */
    public function searchCalls(
        CandidatePagination $pagination,
        int $companyId,
        int $roleId,
        string $search,
    ): CandidatePagination {
        return $this->callReportRepository->searchCalls($pagination, $companyId, $roleId, $search);
    }

    /**
     * @param int $companyId
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @return array
     */
    public function getFilterValuesRanges(
        int $companyId,
        ?Carbon $startDate,
        ?Carbon $endDate,
    ): array {
        return $this->callReportRepository->getFilterValuesRanges($companyId, $startDate, $endDate);
    }
}
