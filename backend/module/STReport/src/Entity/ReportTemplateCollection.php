<?php

declare(strict_types=1);

namespace STReport\Entity;

use STLib\Expand\Collection;

class ReportTemplateCollection extends Collection
{
    /**
     * @param ReportTemplate $reportTemplate
     * @param mixed $key
     * @return Collection
     * @throws \RuntimeException
     */
    public function add(mixed $reportTemplate, string|int|null $key = null): Collection
    {
        if (!($reportTemplate instanceof ReportTemplate)) {
            throw new \RuntimeException('Report template must be an instace of "\STReport\Entity\ReportTemplate"');
        }

        parent::add($reportTemplate, $key ?? $reportTemplate->getId());

        return $this;
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        $result = [];

        /** @var ReportTemplate $reportTemplate */
        foreach ($this as $reportTemplate) {
            $result[] = $reportTemplate->toArray();
        }

        return $result;
    }
}
