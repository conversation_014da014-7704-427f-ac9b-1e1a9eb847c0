<?php

declare(strict_types=1);

namespace STReport\Entity;

use STLib\Mvc\Hydrator\BaseHydratorTrait;

class ReportTemplate
{
    use BaseHydratorTrait;

    public const REPORTS_VALUES = [
        'calls',
        'teams',
        'clients',
    ];
    public const DEFAULT_REPORT = 'calls';

    /**
     * @var int|null
     */
    protected ?int $id = null;

    /**
     * @var string
     */
    protected string $report = self::DEFAULT_REPORT;

    /**
     * @var string
     */
    protected string $name;

    /**
     * @var string
     */
    protected string $icon;

    /**
     * @var array
     */
    protected array $filters;

    /**
     * @var int
     */
    protected int $userId;

    /**
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @param int|null $id
     * @return ReportTemplate
     */
    public function setId(?int $id): ReportTemplate
    {
        $this->id = $id;
        return $this;
    }

    /**
     * @return string
     */
    public function getReport(): string
    {
        return $this->report;
    }

    /**
     * @param string $report
     * @return ReportTemplate
     */
    public function setReport(string $report): ReportTemplate
    {
        $this->report = $report;
        return $this;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @param string $name
     * @return ReportTemplate
     */
    public function setName(string $name): ReportTemplate
    {
        $this->name = $name;
        return $this;
    }

    /**
     * @return string
     */
    public function getIcon(): string
    {
        return $this->icon;
    }

    /**
     * @param string $icon
     * @return ReportTemplate
     */
    public function setIcon(string $icon): ReportTemplate
    {
        $this->icon = $icon;
        return $this;
    }

    /**
     * @return array
     */
    public function getFilters(): array
    {
        return $this->filters;
    }

    /**
     * @param array $filters
     * @return ReportTemplate
     */
    public function setFilters(array $filters): ReportTemplate
    {
        $this->filters = $filters;
        return $this;
    }

    /**
     * @return int
     */
    public function getUserId(): int
    {
        return $this->userId;
    }

    /**
     * @param int $userId
     * @return ReportTemplate
     */
    public function setUserId(int $userId): ReportTemplate
    {
        $this->userId = $userId;
        return $this;
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return $this->extract($this);
    }
}
