<?php

declare(strict_types=1);

namespace STConfiguration\Controller\Plugin;

use BadMethodCallException;
use Laminas\Mvc\Controller\Plugin\AbstractPlugin;
use STConfiguration\Service\ConfigurationService;

class Configuration extends AbstractPlugin
{
    /**
     *
     * @param string $name
     * @param array $arguments
     * @return mixed
     * @throws BadMethodCallException
     */
    public function __call(string $name, array $arguments): mixed
    {
        $configurationService = $this->getController()->getServiceManager()->get(ConfigurationService::class);
        if (!method_exists($configurationService, $name)) {
            throw new BadMethodCallException('Invalid \STConfiguration\Service\ConfigurationService method: ' . $name);
        }
        return call_user_func_array([
            $configurationService,
            $name
        ], $arguments);
    }
}
