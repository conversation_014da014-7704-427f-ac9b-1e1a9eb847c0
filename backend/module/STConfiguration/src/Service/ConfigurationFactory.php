<?php

namespace STConfiguration\Service;

use Interop\Container\ContainerInterface;
use Laminas\Di\Container\ServiceManager\AutowireFactory;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;

class ConfigurationFactory extends AutowireFactory
{
    /**
     *
     * @param ContainerInterface $container
     * @param $requestedName
     * @param array|null $options
     * @return ConfigurationService
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function __invoke(ContainerInterface $container, $requestedName, array $options = null): ConfigurationService
    {
        $config = $container->get('config');

        return parent::__invoke($container, $requestedName, ['config' => $config]);
    }
}
