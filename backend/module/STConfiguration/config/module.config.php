<?php

declare(strict_types=1);

namespace STConfiguration\Service;

use STAlgo\Service\Interfaces\ConfigurationInterface as AlgoConfigurationInterface;
use STOnboarding\Service\Interfaces\ConfigurationInterface as OnboardingConfigurationInterface;
use STConfiguration\Controller\Plugin\Configuration as ConfigurationPlugin;
use STConfiguration\ServiceProviding\ConfigurationServiceProvider;
use STSlack\Service\Interfaces\ConfigurationInterface as SlackConfigurationInterface;
use STCall\Service\Interfaces\ConfigurationInterface as CallConfigurationInterface;
use STTranslation\Service\Interfaces\ConfigurationInterface as TranslationConfigurationInterface;
use STUser\Service\Interfaces\ConfigurationInterface as UserConfigurationInterface;
use STCompany\Service\Interfaces\ConfigurationInterface as CompanyConfigurationInterface;

return [
    'controller_plugins' => [
        'invokables' => [
            'configuration' => ConfigurationPlugin::class,
        ],
    ],
    'service_manager' => [
        'factories' => [
            ConfigurationService::class => ConfigurationFactory::class,
        ],
        'aliases' => [
            AlgoConfigurationInterface::class => ConfigurationServiceProvider::class,
            OnboardingConfigurationInterface::class => ConfigurationServiceProvider::class,
            SlackConfigurationInterface::class => ConfigurationServiceProvider::class,
            CallConfigurationInterface::class => ConfigurationServiceProvider::class,
            TranslationConfigurationInterface::class => ConfigurationServiceProvider::class,
            UserConfigurationInterface::class => ConfigurationServiceProvider::class,
            CompanyConfigurationInterface::class => ConfigurationServiceProvider::class,
        ],
    ],
];
