<?php

declare(strict_types=1);

namespace Console;

use Console\Command\Algo\ConnectCompaniesAndAlgoApisToIndustriesCommand;
use Console\Command\Algo\UpdateAlgoApisCommand;
use Console\Command\Billing\NotifyTrialPeriodEndsSoonCommand;
use Console\Command\Call\ClientSummary\ClientSummaryDaemonCommand;
use Console\Command\Call\ClientSummary\ClientSummaryUpdateCommand;
use Console\Command\Billing\SetTrialExpiredStatusCommand;
use Console\Command\Call\Export\CallExportCommand;
use Console\Command\Call\Export\CallExportDaemonCommand;
use Console\Command\Call\Webhooks\WebhooksDaemonCommand;
use Console\Command\Queue\ConsumeMessagesCommand;
use STLib\Mvc\Command\CommandFactory;
use Console\Command\Database\ClickhouseRunScriptCommand;

return [
    'laminas-cli' => [
        'commands' => [
            'algo:algo:save-api' => Command\Algo\SaveAlgoApiCommand::class,
            'algo:update-events' => Command\Algo\UpdateAlgoEventsCommand::class,
            'algo:update-event-hint' => Command\Algo\UpdateAlgoEventHintCommand::class,
            'mail:daemon' => Command\MailDaemonCommand::class,
            'call:analysis:daemon' => Command\Call\AnalysisStepCommand::class,
            'call:download-waiting-room:daemon' => Command\Call\WaitingRoomCommand::class,
            'call:repeat-analysis' => Command\Call\RepeatCallAnalysisCommand::class,
            'call:repeat-failed-to-download-calls' => Command\Call\RepeatFileDownloadCallsCommand::class,
            'call:send-statuses-to-client-api' => Command\Call\SendCallsStatusesToClientApi::class,
            'call:precalculate:daemon' => Command\Precalculation\PrecalculateDaemonCommand::class,
            'call:precalculate:bulk' => Command\Precalculation\BulkPrecalculateCommand::class,
            'call:precalculate:changed' => Command\Precalculation\PrecalculateChangedCommand::class,
            'call:s3:company:daemon' => Command\S3Integration\S3IntegrationCompanyDaemonCommand::class,
            'call:s3:call:daemon' => Command\S3Integration\S3IntegrationCallDaemonCommand::class,
            'call:s3:add-jobs' => Command\S3Integration\S3IntegrationCommand::class,
            's3:report:daemon' => Command\S3Report\S3ReportDaemonCommand::class,
            's3:report:add-jobs' => Command\S3Report\S3ReportCommand::class,
            'call:client-api-request:daemon' => Command\Call\ClientApiRequest::class,
            'call:test-analysis' => Command\Call\TestAnalysisCommand::class,
            'client:precalculate' => Command\Client\ClientPrecalculationCommand::class,
            'client:precalculate:daemon' => Command\Client\ClientPrecalculationDaemonCommand::class,
            'agent:precalculate' => Command\Agent\AgentPrecalculationCommand::class,
            'agent:precalculate:daemon' => Command\Agent\AgentPrecalculationDaemonCommand::class,
            'company:clone-role' => Command\Company\CloneRoleCommand::class,
            'company:add-balance' => Command\Company\AddBalanceCommand::class,
            'company:change-algo-apis' => Command\Company\ChangeAlgoApisCommand::class,
            'company:change-rate' => Command\Company\ChangeRateCommand::class,
            'company:delete-data' => Command\Company\DeleteDataCommand::class,
            'company:delete-calls' => Command\Company\DeleteCallsCommand::class,
            'company:delete-calls:daemon' => Command\Company\DeleteCallsDaemonCommand::class,
            'transcribe-driver:set-language' => Command\LanguageDriver\SetTranscribeDriverLanguageCommand::class,
            'translation-driver:set-language' => Command\LanguageDriver\SetTranslationDriverLanguageCommand::class,
            'dashboard:statistics-create:calls' => Command\Dashboard\CreateCallsDashboardStatisticsCommand::class,
            'dashboard:statistics-create:calls-languages' => Command\Dashboard\CreateCallsLanguagesDashboardStatisticsCommand::class,
            'dashboard:statistics-create:reviewed-events' => Command\Dashboard\CreateReviewedEventsDashboardStatisticsCommand::class,
            'dashboard:statistics-create:events-categories' => Command\Dashboard\CreateEventsCategoriesDashboardStatisticsCommand::class,
            'dashboard:statistics-create:managers-kpi' => Command\Dashboard\CreateManagersKPIDashboardStatisticsCommand::class,
            'dashboard:statistics-create:reviewed-clients' => Command\Dashboard\CreateReviewedClientsDashboardStatisticsCommand::class,
            'queue:messages:consume' => ConsumeMessagesCommand::class,
            'queue:messages:repeat-error' => Command\Queue\RepeatQueueProcessingCommand::class,
            'queue:messages:remove-duplicates' => Command\Queue\RemoveDuplicatesCommand::class,
            'queue:messages:drop' => Command\Queue\DropMessagesCommand::class,
            'queue:messages:read' => Command\Queue\ReadMessagesCommand::class,
            'queue:messages:add' => Command\Queue\AddMessagesCommand::class,
            'user:add-as-hidden-admin-to-companies' => Command\User\AddAsHiddenAdminToCompanies::class,
            'user:remove-hidden-admin-from-companies' => Command\User\RemoveHiddenAdminFromCompanies::class,
            'ems:data-set-reviewed-calls-examples-search-daemon' => Command\EMS\EmsDataSetReviewedCallsExamplesSearchDaemonCommand::class,
            'ems:data-set-analyzed-calls-examples-search-daemon' => Command\EMS\EmsDataSetAnalyzedCallsExamplesSearchDaemonCommand::class,
            'ems:data-set-analyzed-calls-examples-sorting-daemon' => Command\EMS\EmsDataSetAnalyzedCallsExamplesSortingDaemonCommand::class,
            'ems:data-set-examples-add-to-export' => Command\EMS\EmsDataSetExamplesAddToExportCommand::class,
            'ems:data-set-export-daemon' => Command\EMS\EmsDataSetExportDaemonCommand::class,
            'notifications:companies-with-decreasing-calls-amount' => Command\Notification\CompaniesWithDecreasingCallsAmount::class,
            'robo-truck:push' => Command\RoboTruck\PushCommand::class,
            'algo:update-algo-apis' => UpdateAlgoApisCommand::class,
            'industries:connect-companies-and-algo-apis' => ConnectCompaniesAndAlgoApisToIndustriesCommand::class,
            'call:export' => CallExportCommand::class,
            'call:export:daemon' => CallExportDaemonCommand::class,
            'call:client-summary:daemon' => ClientSummaryDaemonCommand::class,
            'call:client-summary:update' => ClientSummaryUpdateCommand::class,
            'db:clickhouse:run-script' => ClickhouseRunScriptCommand::class,
            'webhooks:daemon' => WebhooksDaemonCommand::class,
            'billing:set-trial-expired-status' => SetTrialExpiredStatusCommand::class,
            'billing:notify-trial-period-ends-soon' => NotifyTrialPeriodEndsSoonCommand::class,
        ],
    ],
    'service_manager' => [
        'factories' => [
            Command\Algo\SaveAlgoApiCommand::class => CommandFactory::class,
            Command\Algo\UpdateAlgoEventsCommand::class => CommandFactory::class,
            Command\Algo\UpdateAlgoEventHintCommand::class => CommandFactory::class,
            Command\MailDaemonCommand::class => CommandFactory::class,
            Command\Company\CloneRoleCommand::class => CommandFactory::class,
            Command\Company\AddBalanceCommand::class => CommandFactory::class,
            Command\Company\ChangeAlgoApisCommand::class => CommandFactory::class,
            Command\Company\ChangeRateCommand::class => CommandFactory::class,
            Command\Company\DeleteDataCommand::class => CommandFactory::class,
            Command\Company\DeleteCallsCommand::class => CommandFactory::class,
            Command\Company\DeleteCallsDaemonCommand::class => CommandFactory::class,
            Command\Call\AnalysisStepCommand::class => CommandFactory::class,
            Command\Call\WaitingRoomCommand::class => CommandFactory::class,
            Command\Call\RepeatCallAnalysisCommand::class => CommandFactory::class,
            Command\Call\RepeatFileDownloadCallsCommand::class => CommandFactory::class,
            Command\Call\SendCallsStatusesToClientApi::class => CommandFactory::class,
            Command\Call\TestAnalysisCommand::class => CommandFactory::class,
            Command\Client\ClientPrecalculationDaemonCommand::class => CommandFactory::class,
            Command\Client\ClientPrecalculationCommand::class => CommandFactory::class,
            Command\Agent\AgentPrecalculationDaemonCommand::class => CommandFactory::class,
            Command\Agent\AgentPrecalculationCommand::class => CommandFactory::class,
            Command\Precalculation\PrecalculateDaemonCommand::class => CommandFactory::class,
            Command\Precalculation\BulkPrecalculateCommand::class => CommandFactory::class,
            Command\Precalculation\PrecalculateChangedCommand::class => CommandFactory::class,
            Command\S3Integration\S3IntegrationCompanyDaemonCommand::class => CommandFactory::class,
            Command\S3Integration\S3IntegrationCallDaemonCommand::class => CommandFactory::class,
            Command\S3Integration\S3IntegrationCommand::class => CommandFactory::class,
            Command\S3Report\S3ReportDaemonCommand::class => CommandFactory::class,
            Command\S3Report\S3ReportCommand::class => CommandFactory::class,
            Command\Call\ClientApiRequest::class => CommandFactory::class,
            Command\LanguageDriver\SetTranscribeDriverLanguageCommand::class => CommandFactory::class,
            Command\LanguageDriver\SetTranslationDriverLanguageCommand::class => CommandFactory::class,
            Command\Dashboard\CreateCallsDashboardStatisticsCommand::class => CommandFactory::class,
            Command\Dashboard\CreateManagersKPIDashboardStatisticsCommand::class => CommandFactory::class,
            Command\Dashboard\CreateCallsLanguagesDashboardStatisticsCommand::class => CommandFactory::class,
            Command\Dashboard\CreateReviewedClientsDashboardStatisticsCommand::class => CommandFactory::class,
            Command\Dashboard\CreateReviewedEventsDashboardStatisticsCommand::class => CommandFactory::class,
            Command\Dashboard\CreateEventsCategoriesDashboardStatisticsCommand::class => CommandFactory::class,
            Command\Queue\RepeatQueueProcessingCommand::class => CommandFactory::class,
            Command\Queue\RemoveDuplicatesCommand::class => CommandFactory::class,
            Command\Queue\DropMessagesCommand::class => CommandFactory::class,
            Command\Queue\ReadMessagesCommand::class => CommandFactory::class,
            Command\Queue\AddMessagesCommand::class => CommandFactory::class,
            Command\Queue\ConsumeMessagesCommand::class => CommandFactory::class,
            Command\User\AddAsHiddenAdminToCompanies::class => CommandFactory::class,
            Command\User\RemoveHiddenAdminFromCompanies::class => CommandFactory::class,
            Command\EMS\EmsDataSetReviewedCallsExamplesSearchDaemonCommand::class => CommandFactory::class,
            Command\EMS\EmsDataSetAnalyzedCallsExamplesSortingDaemonCommand::class => CommandFactory::class,
            Command\EMS\EmsDataSetAnalyzedCallsExamplesSearchDaemonCommand::class => CommandFactory::class,
            Command\EMS\EmsDataSetExamplesAddToExportCommand::class => CommandFactory::class,
            Command\EMS\EmsDataSetExportDaemonCommand::class => CommandFactory::class,
            Command\Notification\CompaniesWithDecreasingCallsAmount::class => CommandFactory::class,
            Command\RoboTruck\PushCommand::class => CommandFactory::class,
            UpdateAlgoApisCommand::class => CommandFactory::class,
            ConnectCompaniesAndAlgoApisToIndustriesCommand::class => CommandFactory::class,
            CallExportCommand::class => CommandFactory::class,
            ClickhouseRunScriptCommand::class => CommandFactory::class,
            CallExportDaemonCommand::class => CommandFactory::class,
            ClientSummaryUpdateCommand::class => CommandFactory::class,
            ClientSummaryDaemonCommand::class => CommandFactory::class,
            WebhooksDaemonCommand::class => CommandFactory::class,
            SetTrialExpiredStatusCommand::class => CommandFactory::class,
            NotifyTrialPeriodEndsSoonCommand::class => CommandFactory::class,
        ],
    ],
];
