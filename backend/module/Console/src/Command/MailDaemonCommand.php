<?php

declare(strict_types=1);

namespace Console\Command;

use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class MailDaemonCommand extends BaseCommand
{
    /**
     *
     * @var string
     */
    protected static $defaultName = 'mail:daemon';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'E-mail sending daemon';

    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $daemon = (new \STMail\Daemon\MailDaemon())
                ->setQueueName(\STMail\Service\MailService::MAIL_QUEUE_NAME)
                ->setErrorQueueName(\STMail\Service\MailService::ERROR_MAIL_QUEUE_NAME);
        $daemon->params()->add($this->mail()->getMailServiceInstance(), \STMail\Service\MailService::class);
        $this->rabbit()->daemon()->run($daemon);
        return static::FAILURE;
    }
}
