<?php

declare(strict_types=1);

namespace Console\Command\User;

use Exception;
use STCompany\Data\UsersCompaniesRolesTable;
use STCompany\Entity\Role;
use Symfony\Component\Console\Output\OutputInterface;

class AddAsHiddenAdminToCompanies extends HiddenAdminOperationsCommand
{
    /**
     *
     * @var string
     */
    protected static string $defaultName = 'user:add-as-hidden-admin-to-companies';

    /**
     *
     * @var string
     */
    protected static string $defaultDescription = 'Add user as hidden admin to companies';

    /**
     * @throws Exception
     */
    protected function executeCommand(
        int $userId,
        int $companyId,
        Role $role,
        UsersCompaniesRolesTable $usersCompaniesRolesTable,
        OutputInterface $output
    ): void {
        $usersCompaniesRolesTable->saveUserData($userId, $companyId, $role->getId());
        $output->writeln(
            'User has been added to company id=' . $companyId . ' with role "' . $role->getName(
            ) . '" (id: "' . $role->getId() . ')"'
        );
    }
}
