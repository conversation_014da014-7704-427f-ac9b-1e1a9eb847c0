<?php

declare(strict_types=1);

namespace Console\Command\User;

use Console\Command\BaseCommand;
use Exception;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use ReflectionException;
use RuntimeException;
use STCompany\Data\UsersCompaniesRolesTable;
use STCompany\Entity\Role;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

abstract class HiddenAdminOperationsCommand extends BaseCommand
{
    abstract protected function executeCommand(
        int $userId,
        int $companyId,
        Role $role,
        UsersCompaniesRolesTable $usersCompaniesRolesTable,
        OutputInterface $output
    ): void;

    /**
     * @return void
     */
    protected function configure(): void
    {
        parent::configure();
        $this->addOption('user_id', null, InputOption::VALUE_REQUIRED, 'User id');
        $this->addOption('company_id', null, InputOption::VALUE_OPTIONAL, 'Company id');
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface|ReflectionException
     * @throws Exception
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $userId = (int) $input->getOption('user_id');
        if ($userId <= 0) {
            throw new RuntimeException('Param "user_id" is required');
        }
        $companyId = $input->getOption('company_id');
        if (is_null($companyId)) {
            $companyIds = $this->company()->getCompanyIds();
        } else {
            $companyId = (int) $companyId;
            if ($companyId <= 0) {
                throw new RuntimeException('Wrong param "company_id", it can be positive integer.');
            }
            $companyIds = [$companyId];
        }

        /**
         * @var UsersCompaniesRolesTable $usersCompaniesRolesTable
         */
        $usersCompaniesRolesTable = $this->getServiceManager()->get(UsersCompaniesRolesTable::class);
        foreach ($companyIds as $companyId) {
            $roles = $this->company()->role()->getRoles($companyId);
            foreach ($roles as $role) {
                if ($role->isAdmin()) {
                    $this->executeCommand($userId, $companyId, $role, $usersCompaniesRolesTable, $output);
                }
            }
        }

        return static::SUCCESS;
    }
}
