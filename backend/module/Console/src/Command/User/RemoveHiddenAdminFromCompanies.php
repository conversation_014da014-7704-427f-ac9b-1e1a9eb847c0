<?php

declare(strict_types=1);

namespace Console\Command\User;

use Exception;
use STCompany\Data\UsersCompaniesRolesTable;
use STCompany\Entity\Role;
use Symfony\Component\Console\Output\OutputInterface;

class RemoveHiddenAdminFromCompanies extends HiddenAdminOperationsCommand
{
    /**
     *
     * @var string
     */
    protected static string $defaultName = 'user:remove-hidden-admin-from-companies';

    /**
     *
     * @var string
     */
    protected static string $defaultDescription = 'Remove user as hidden admin from companies';

    /**
     * @throws Exception
     */
    protected function executeCommand(
        int $userId,
        int $companyId,
        Role $role,
        UsersCompaniesRolesTable $usersCompaniesRolesTable,
        OutputInterface $output
    ): void {
        $usersCompaniesRolesTable->deleteByUserIdCompanyIdAndRoleId($userId, $companyId, $role->getId());
        $output->writeln(
            'User has been removed as hidden admin from company id=' . $companyId . ' with role "' . $role->getName(
            ) . '" (id: "' . $role->getId() . ')"'
        );
    }
}
