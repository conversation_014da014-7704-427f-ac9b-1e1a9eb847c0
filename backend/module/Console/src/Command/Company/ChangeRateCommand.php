<?php

declare(strict_types=1);

namespace Console\Command\Company;

use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Input\InputOption;

class ChangeRateCommand extends \Console\Command\BaseCommand
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    /**
     *
     * @var string
     */
    protected static $defaultName = 'company:change-rate';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'Change rate';

    /**
     *
     * @return void
     */
    protected function configure()
    {
        $this->addOption('company_id', 'c', InputOption::VALUE_REQUIRED, 'company id (required)');
        $this->addOption('rate', 'r', InputOption::VALUE_REQUIRED, 'Rate');
    }

    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $this->company()->saveCompanyRate(
            (int) $input->getOption('company_id'),
            (float) $input->getOption('rate')
        );
        $output->writeln('Rate is updated');

        return static::SUCCESS;
    }
}
