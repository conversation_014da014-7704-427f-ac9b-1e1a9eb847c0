<?php

declare(strict_types=1);

namespace Console\Command\Company;

use Console\Command\BaseCommand;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use STCall\Service\CallService;
use ST<PERSON><PERSON>pany\Daemon\DeleteCallsDaemon;
use STCompany\Service\CompanyService;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'company:delete-calls:daemon',
    description: 'company:delete-calls:daemon',
)]
class DeleteCallsDaemonCommand extends BaseCommand
{
    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        /** @var CallService $callService */
        $callService = $this->getServiceManager()->get(CallService::class);
        /** @var CompanyService $companyService */
        $companyService = $this->getServiceManager()->get(CompanyService::class);

        $daemon = $this->getServiceManager()->get(DeleteCallsDaemon::class);
        $daemon
            ->setQueueName(DeleteCallsDaemon::QUEUE)
            ->setErrorQueueName(DeleteCallsDaemon::QUEUE_ERROR);
        $daemon->params()->add($callService, CallService::class);
        $daemon->params()->add($companyService, CompanyService::class);
        $daemon->params()->add($this->getServiceManager()->get('config')['aws'], 'aws_config');

        $this->rabbit()->daemon()->run($daemon);

        return self::FAILURE;
    }
}
