<?php

declare(strict_types=1);

namespace Console\Command\Company;

use PhpAmqpLib\Message\AMQPMessage;
use STCompany\Daemon\DeleteCallsDaemon;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'company:delete-calls',
    description: 'Company calls removing',
)]
class DeleteCallsCommand extends \Console\Command\BaseCommand
{
    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $channel = $this->rabbit()->getChannel();

        foreach ($this->company()->getCompanyIdsWithCallsRemovingEnabled() as $companyId) {
            $message = new AMQPMessage(json_encode([
                'company_id' => $companyId,
            ]));
            $channel->basic_publish($message, '', routing_key: DeleteCallsDaemon::QUEUE);
        }

        $channel->close();

        return self::SUCCESS;
    }
}
