<?php

declare(strict_types=1);

namespace Console\Command\Company;

use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Input\InputOption;

class ChangeAlgoApisCommand extends \Console\Command\BaseCommand
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    /**
     *
     * @var string
     */
    protected static $defaultName = 'company:change-algo-apis';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'Change algo APIs list';

    /**
     *
     * @return void
     */
    protected function configure()
    {
        $this->addOption('company_id', 'c', InputOption::VALUE_REQUIRED, 'company id (required)');
        $this->addOption('api_id', 'a', InputOption::VALUE_IS_ARRAY | InputOption::VALUE_REQUIRED, 'APIs list', []);
    }

    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $companyId = (int) $input->getOption('company_id');
        $algoApiIds = array_filter($input->getOption('api_id'), function ($apiId) {
            return is_numeric($apiId) && (int) $apiId > 0;
        });

        $existedAlgoApiIds = $this->algo()->api()->getCompanyAlgoApiIds($companyId);
        $output->writeln('Existed algo ids are ' . implode(',', $existedAlgoApiIds));

        $this->algo()->api()->saveCompanyAlgoApiIds($companyId, $algoApiIds);

        $updatedAlgoApiIds = $this->algo()->api()->getCompanyAlgoApiIds($companyId);
        $output->writeln('Current algo ids are ' . implode(',', $updatedAlgoApiIds));

        return static::SUCCESS;
    }
}
