<?php

declare(strict_types=1);

namespace Console\Command\Company;

use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Input\InputOption;

class DeleteDataCommand extends \Console\Command\BaseCommand
{
    use \STCall\Service\AwsTrait;

    protected const S3_DELETION_BUTCH_SIZE = 1000;

    /**
     * @var string
     */
    protected static $defaultName = 'company:delete-data';

    /**
     * @var string
     */
    protected static $defaultDescription = 'Delete company private data';

    /**
     * @var InputInterface
     */
    protected InputInterface $input;

    /**
     * @var OutputInterface
     */
    protected OutputInterface $output;

    /**
     * @return void
     */
    protected function configure()
    {
        $this->addOption('company_ids', 'c', InputOption::VALUE_OPTIONAL | InputOption::VALUE_IS_ARRAY, '', null);
        $this->addOption('start_date', 's', InputOption::VALUE_OPTIONAL, '', null);
        $this->addOption('end_date', 'e', InputOption::VALUE_OPTIONAL, '', null);
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $this->input = $input;
        $this->output = $output;

        $companyIds = array_filter(array_map('intval', $input->getOption('company_ids')));
        $startDate = \Carbon\Carbon::parse($input->getOption('start_date'))->startOfDay();
        $endDate = \Carbon\Carbon::parse($input->getOption('end_date'))->endOfDay();

        $awsConfig = $this->getServiceManager()->get('config')['aws'];
        $this->setAwsConfig($awsConfig['api']);
        $this->setEnv($awsConfig['env']);

        foreach ($companyIds as $companyId) {
            $this->output->writeln('Company id: ' . $companyId);
            $company = $this->company()->getCompany($companyId);
            $this->setCompany($company);

            $s3DeleteCount = $this->deleteS3($companyId, $startDate, $endDate);
            $this->output->writeln('Total files deleted from s3: ' . $s3DeleteCount);

            $this->company()->removeData()->removeCallsData($companyId, $startDate, $endDate);
            $this->output->writeln('Calls deleted');

            $this->company()->removeData()->removeAlgoEventsData($companyId, $startDate, $endDate);
            $this->output->writeln('Algo events deleted');

            $this->company()->removeData()->removeCallsParagraphsData($companyId, $startDate, $endDate);
            $this->output->writeln('Calls paragraphs deleted');

            $this->company()->removeData()->removeCallsReviewsData($companyId, $startDate, $endDate);
            $this->output->writeln('Calls reviews deleted');

            $this->company()->removeData()->removePrecalculatedAgentsData($companyId, $startDate, $endDate);
            $this->output->writeln('Precalculated agents deleted');

            $this->company()->removeData()->removePrecalculatedCallsEventsData($companyId, $startDate, $endDate);
            $this->output->writeln('Precalculated calls events deleted');

            $this->company()->removeData()->removePrecalculatedCallsData($companyId, $startDate, $endDate);
            $this->output->writeln('Precalculated calls deleted');

            $this->company()->removeData()->removePrecalculatedClientsData($companyId, $startDate, $endDate);
            $this->output->writeln('Precalculated clients events deleted');

            $this->company()->removeData()->removeCallsLogsData($companyId);
            $this->output->writeln('Logs deleted');

            $this->company()->removeData()->removeNotConsistentClientsData($companyId);
            $this->output->writeln('Clients deleted');

            $this->company()->removeData()->removeNotConsistentCallsCommentsData($companyId);
            $this->output->writeln('Calls comments deleted');

            $this->company()->removeData()->removeNotConsistentCallsCommentsNotificationsData($companyId);
            $this->output->writeln('Calls comments notifications deleted');
        }

        return static::SUCCESS;
    }

    /**
     * @param int $companyId
     * @param \Carbon\Carbon $startDate
     * @param \Carbon\Carbon $endDate
     * @return int
     */
    protected function deleteS3(int $companyId, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): int
    {
        $s3Files = $this->call()->getS3Files($companyId, $startDate, $endDate);

        $s3FilesDeleteButch = [];
        $s3DeleteCount = 0;

        foreach ($s3Files as $s3File) {
            $s3FilesDeleteButch[] = $s3File;
            if (count($s3FilesDeleteButch) >= static::S3_DELETION_BUTCH_SIZE) {
                $this->deleteFilesFromS3($s3FilesDeleteButch);
                $s3DeleteCount += count($s3FilesDeleteButch);
                $this->output->writeln('---- Deleted files: ' . $s3DeleteCount);
                $s3FilesDeleteButch = [];
            }
        }

        if (count($s3FilesDeleteButch) > 0) {
            $this->deleteFilesFromS3($s3FilesDeleteButch);
            $s3DeleteCount += count($s3FilesDeleteButch);
        }
        return $s3DeleteCount;
    }
}
