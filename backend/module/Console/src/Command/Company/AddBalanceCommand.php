<?php

declare(strict_types=1);

namespace Console\Command\Company;

use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Input\InputOption;

class AddBalanceCommand extends \Console\Command\BaseCommand
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    /**
     *
     * @var string
     */
    protected static $defaultName = 'company:add-balance';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'Add balance';

    /**
     *
     * @return void
     */
    protected function configure()
    {
        parent::configure();
        $this->addOption('company-id', 'c', InputOption::VALUE_REQUIRED, 'company id (required)');
        $this->addOption('balance', 'b', InputOption::VALUE_REQUIRED, 'balance change in seconds (required)');
    }

    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $companyId = (int) $input->getOption('company-id');
        $balanceChange = (int) $input->getOption('balance');

        $company = $this->company()->getCompany($companyId);

        $output->writeln(
            'Current balance of company is '
                . $company->getPaidTranscribingTime()
                . ' seconds (~' . $company->getPaidTranscribingTime() / 60 / 60 . ' hours)'
        );

        $company->addPaidTranscribingTime($balanceChange);
        $this->company()->saveCompany($company);

        $output->writeln(
            'New balance of company is '
                . $company->getPaidTranscribingTime()
                . ' seconds (~' . $company->getPaidTranscribingTime() / 60 / 60 . ' hours)'
        );

        return static::SUCCESS;
    }
}
