<?php

declare(strict_types=1);

namespace Console\Command\Company;

use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class CloneRoleCommand extends \Console\Command\BaseCommand
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    /**
     *
     * @var string
     */
    protected static $defaultName = 'company:clone-role';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'Clone role';

    /**
     *
     * @return void
     */
    protected function configure()
    {
        parent::configure();
        $this->addOption('source-company-id', 'sc', \Symfony\Component\Console\Input\InputOption::VALUE_REQUIRED, 'source company Id (required)');
        $this->addOption('source-role-id', 'sr', \Symfony\Component\Console\Input\InputOption::VALUE_REQUIRED, 'source role Id (required)');
        $this->addOption('destination-company-id', 'dc', \Symfony\Component\Console\Input\InputOption::VALUE_REQUIRED, 'destination company Id (required)');
        $this->addOption('destination-role-id', 'dr', \Symfony\Component\Console\Input\InputOption::VALUE_OPTIONAL, 'destination role Id (optional)');
    }

    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $sourceCompanyId = (int) $input->getOption('source-company-id');
        $sourceRoleId = (int) $input->getOption('source-role-id');
        $destinationCompanyId = (int) $input->getOption('destination-company-id');
        $destinationRoleId = (int) $input->getOption('destination-role-id');

        $sourceRole = $this->company()->role()->getRole($sourceCompanyId, $sourceRoleId);
        if ($destinationRoleId > 0) {
            $destinationRole = $this->company()->role()->getRole($destinationCompanyId, $destinationRoleId);

            $output->writeln('overwriting event algo on role "' . $destinationRole->getName() . '"');

            $destinationCategories = $this->company()->event()->getCategories($destinationCompanyId, $destinationRole->getId());
            $categoryIds = array_column($destinationCategories->toArray(), 'id');

            $output->writeln('old categories to delete: ' . implode(', ', $categoryIds));

            $this->company()->event()->deleteCategory($destinationRole, $categoryIds);
        } else {
            $output->writeln('creating role "' . $sourceRole->getName() . '" and associated algo inside company #' . $destinationCompanyId);

            $destinationRole = $sourceRole;
            $destinationRole->setCompanyId($destinationCompanyId)
                            ->setId(null);
            $this->company()->role()->saveRole($destinationRole);
        }
        $output->writeln('role ready!');

        $sourceCategories = $this->company()->event()->getCategories($sourceCompanyId, $sourceRoleId);

        /**
         * Ids need to be reassigned one by one, as of date of creation
         * this is the best way to duplicate data from role to role
         */
        foreach ($sourceCategories as $sourceCategory) {
            $output->writeln('cloning category "' . $sourceCategory->getName() . '"...');

            $sourceCategoryArray = $sourceCategory->toArray();
            unset($sourceCategoryArray['role']);
            unset($sourceCategoryArray['color']);
            unset($sourceCategoryArray['events']);

            $destinationCategory = $this->hydrate($sourceCategoryArray, \STCompany\Entity\Event\Category::class);
            $destinationCategory
                    ->setId(null)
                    ->setRole($destinationRole)
                    ->setColor($sourceCategory->getColor())
                    ->setEvents($sourceCategory->getEvents());
            if ($sourceCategory->getId() > 0) {
                $destinationCategory->setId($this->company()->event()->saveCategory($destinationCategory));
            }

            foreach ($destinationCategory->getEvents() as $destinationEvent) {
                $output->writeln('cloning event "' . $destinationEvent->getName() . '"...');
                $destinationEvent
                        ->setId(null)
                        ->setRole($destinationRole)
                        ->setCategoryId($destinationCategory->getId());
                $this->company()->event()->saveEvent($destinationEvent);
            }
            $output->writeln('category "' . $sourceCategory->getName() . '" successfully cloned!');
        }

        $output->writeln('the process was successful!');

        return static::SUCCESS;
    }
}
