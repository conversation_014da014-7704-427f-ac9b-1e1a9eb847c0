<?php

declare(strict_types=1);

namespace Console\Command;

/**
 *
 * @method \STAlgo\Controller\Plugin\Algo algo()
 * @method \STApi\Controller\Plugin\Api api()
 * @method \STApi\Controller\Plugin\ApiPermissionChecker apiPermissionChecker()
 * @method \STCall\Controller\Plugin\Call|\STCall\Service\CallService call()
 * @method \STCall\Controller\Plugin\Translator|\STTranslation\Service\TranslatorService translator()
 * @method \STClickhouse\Controller\Plugin\Clickhouse clickhouse()
 * @method \STConfiguration\Controller\Plugin\Configuration configuration()
 * @method \STCodeManagement\Controller\Plugin\CodeManagement codeManagement()
 * @method \STCompany\Controller\Plugin\Company|\STCompany\Service\CompanyService company()
 * @method \STCompany\Controller\Plugin\CompanyPermissionChecker companyPermissionChecker()
 * @method \STCompany\Controller\Plugin\TeamPermissionChecker teamPermissionChecker()
 * @method \STCompany\Controller\Plugin\UserNotification|\STCompany\Service\Notification\UserNotificationService userNotification()
 * @method \STDashboard\Controller\Plugin\Dashboard dashboard()
 * @method \STEms\Controller\Plugin\Ems ems()
 * @method \STFront\Controller\Plugin\Front|\STFront\Service\FrontService front()
 * @method \STLog\Controller\Plugin\Logger logger()
 * @method \STMail\Controller\Plugin\Mail mail()
 * @method \STRabbit\Controller\Plugin\Rabbit|\STRabbit\Service\RabbitService rabbit()
 * @method \STRedis\Controller\Plugin\Redis|\Predis\Client redis()
 * @method \STRoboTruck\Controller\Plugin\RoboTruck roboTruck()
 * @method \STLlmEvent\Controller\Plugin\LlmEvent llmEvent()
 * @method \STUser\Controller\Plugin\Auth|\STUser\Service\AuthService auth()
 * @method \STUser\Controller\Plugin\User|\STUser\Service\UserService user()
 * @method \STUser\Controller\Plugin\UserPermissionChecker userPermissionChecker()
 */
abstract class BaseCommand extends \STLib\Mvc\Command\AbstractCommand
{
}
