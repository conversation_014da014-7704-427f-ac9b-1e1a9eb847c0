<?php

declare(strict_types=1);

namespace Console\Command\LanguageDriver;

use ST<PERSON>all\Data\AbstractDriversLanguagesTable;
use STLib\Mvc\Command\AbstractCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

abstract class AbstractSetDriverLanguageCommand extends \Console\Command\BaseCommand
{
    /**
     *
     * @var InputInterface
     */
    protected InputInterface $input;

    /**
     *
     * @var OutputInterface
     */
    protected OutputInterface $output;

    /**
     *
     * @return void
     */
    protected function configure()
    {
        parent::configure();
        $this->addOption('language', null, \Symfony\Component\Console\Input\InputOption::VALUE_REQUIRED, 'language (required)');
        $this->addOption('driver', null, \Symfony\Component\Console\Input\InputOption::VALUE_REQUIRED, 'driver (required)');
    }

    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $this->input = $input;
        $this->output = $output;

        $language = $input->getOption('language');
        $driver = $input->getOption('driver');
        $validationResult = $this->validateInput($language, $driver);

        if ($validationResult !== self::SUCCESS) {
            return $validationResult;
        }

        $driverLanguageTable = $this->getDriverLanguageTable();

        if ($driverLanguageTable->saveLanguageDriver($language, $driver)) {
            $output->writeln("{$language} language successfully associated with {$driver} driver");
        } else {
            $output->writeln('Error while update language driver association');
        }

        return self::SUCCESS;
    }

    /**
     *
     * @param string|null $language
     * @param string|null $driver
     * @return int
     */
    private function validateInput(?string $language, ?string $driver): int
    {
        $result = self::SUCCESS;

        if ($language === null) {
            $this->output->writeln('Parameter "language" is required');
            $result = self::INVALID;
        }

        if ($driver === null) {
            $this->output->writeln('Parameter "driver" is required');
            $result = self::INVALID;
        } else {
            $validDrivers = $this->getValidDriverOptions();

            if (!in_array($driver, $validDrivers, true)) {
                $this->output->writeln(
                    'Unacceptable driver, available drivers: ' .
                    implode(', ', $validDrivers)
                );
                $result = self::INVALID;
            }
        }

        return $result;
    }

    /**
     * @return array
     */
    abstract protected function getValidDriverOptions(): array;

    /**
     * @return AbstractDriversLanguagesTable
     */
    abstract protected function getDriverLanguageTable(): AbstractDriversLanguagesTable;
}
