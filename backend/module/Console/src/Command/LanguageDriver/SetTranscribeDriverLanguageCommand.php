<?php

declare(strict_types=1);

namespace Console\Command\LanguageDriver;

use ST<PERSON>all\Data\AbstractDriversLanguagesTable;
use STCall\Data\TranscribeDriversLanguagesTable;
use STCall\Service\CallAnalysis\TranscribingDistributionStep;

class SetTranscribeDriverLanguageCommand extends AbstractSetDriverLanguageCommand
{
    /**
     *
     * @var string
     */
    protected static $defaultName = 'transcribe-driver:set-language';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'Create or update association between call language and transcribe driver';

    /**
     *
     * @return array
     */
    protected function getValidDriverOptions(): array
    {
        return TranscribingDistributionStep::AVAILABLE_TRANSCRIBING_DRIVERS;
    }

    /**
     *
     * @return AbstractDriversLanguagesTable
     */
    protected function getDriverLanguageTable(): AbstractDriversLanguagesTable
    {
        return $this->getServiceManager()->get(TranscribeDriversLanguagesTable::class);
    }
}
