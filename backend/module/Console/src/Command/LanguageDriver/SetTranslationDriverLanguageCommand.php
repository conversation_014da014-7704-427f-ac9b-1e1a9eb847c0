<?php

declare(strict_types=1);

namespace Console\Command\LanguageDriver;

use ST<PERSON>all\Data\AbstractDriversLanguagesTable;
use STCall\Data\TranslationDriversLanguagesTable;

class SetTranslationDriverLanguageCommand extends AbstractSetDriverLanguageCommand
{
    /**
     *
     * @var string
     */
    protected static $defaultName = 'translation-driver:set-language';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'Create or update association between call language and translation driver';


    /**
     * @return array
     */
    protected function getValidDriverOptions(): array
    {
        return TranslationDriversLanguagesTable::AVAILABLE_DRIVERS;
    }

    /**
     * @return AbstractDriversLanguagesTable
     */
    protected function getDriverLanguageTable(): AbstractDriversLanguagesTable
    {
        return $this->getServiceManager()->get(TranslationDriversLanguagesTable::class);
    }
}
