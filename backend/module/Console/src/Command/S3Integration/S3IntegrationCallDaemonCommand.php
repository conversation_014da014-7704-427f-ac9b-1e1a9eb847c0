<?php

declare(strict_types=1);

namespace Console\Command\S3Integration;

use ST<PERSON>all\Daemon\S3\S3IntegrationCallDaemon;
use STCall\Service\CallAnalysisService;
use STCall\Service\Import\UploadService;
use STCompany\Service\CompanyService;
use STLib\Mvc\Command\AbstractCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class S3IntegrationCallDaemonCommand extends \Console\Command\BaseCommand
{
    /**
     *
     * @var string
     */
    protected static $defaultName = 'call:s3:call:daemon';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'Runs daemon for s3 calls';

    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $uploadService = $this->getServiceManager()->get(UploadService::class);
        /** @var CompanyService $companyService */
        $companyService = $this->getServiceManager()->get(CompanyService::class);
        $callAnalysisService = $this->getServiceManager()->get(CallAnalysisService::class);
        $awsConfig = $this->getServiceManager()->get('config')['aws'];

        $daemon = $this->getServiceManager()->get(S3IntegrationCallDaemon::class);
        $daemon
            ->setQueueName(S3IntegrationCallDaemon::QUEUE)
            ->setErrorQueueName(S3IntegrationCallDaemon::QUEUE_ERROR);
        $daemon->params()->add($awsConfig, 'aws_config');
        $daemon->params()->add($companyService, CompanyService::class);
        $daemon->params()->add($uploadService, UploadService::class);
        $daemon->params()->add($callAnalysisService, CallAnalysisService::class);

        $this->rabbit()->daemon()->run($daemon);

        return self::FAILURE;
    }
}
