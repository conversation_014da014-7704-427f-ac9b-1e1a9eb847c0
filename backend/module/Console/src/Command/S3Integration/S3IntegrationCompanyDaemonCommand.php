<?php

declare(strict_types=1);

namespace Console\Command\S3Integration;

use ST<PERSON>all\Daemon\S3\S3IntegrationCompanyDaemon;
use STCompany\Service\CompanyService;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class S3IntegrationCompanyDaemonCommand extends \Console\Command\BaseCommand
{
    /**
     *
     * @var string
     */
    protected static $defaultName = 'call:s3:company:daemon';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'Runs daemon for companies with s3 integration';

    /**
     *
     * @var OutputInterface
     */
    private OutputInterface $output;

    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        /** @var CompanyService $companyService */
        $companyService = $this->getServiceManager()->get(CompanyService::class);
        $awsConfig = $this->getServiceManager()->get('config')['aws'];

        $daemon = $this->getServiceManager()->get(S3IntegrationCompanyDaemon::class);
        $daemon
            ->setQueueName(S3IntegrationCompanyDaemon::QUEUE)
            ->setErrorQueueName(S3IntegrationCompanyDaemon::QUEUE_ERROR);
        $daemon->params()->add($awsConfig, 'aws_config');
        $daemon->params()->add($companyService, CompanyService::class);

        $this->rabbit()->daemon()->run($daemon);

        return self::FAILURE;
    }
}
