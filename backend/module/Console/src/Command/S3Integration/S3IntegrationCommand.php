<?php

declare(strict_types=1);

namespace Console\Command\S3Integration;

use PhpAmqpLib\Message\AMQPMessage;
use STCall\Daemon\S3\S3IntegrationCompanyDaemon;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class S3IntegrationCommand extends \Console\Command\BaseCommand
{
    /**
     *
     * @var string
     */
    protected static $defaultName = 'call:s3:add-jobs';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'S3 calls integration';

    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $channel = $this->rabbit()->getChannel();

        foreach ($this->company()->getCompanyIdsWithS3Integration() as $companyId) {
            $message = new AMQPMessage(json_encode([
                'company_id' => $companyId,
            ]));
            $channel->basic_publish($message, '', routing_key: S3IntegrationCompanyDaemon::QUEUE);
        }

        $channel->close();

        return self::SUCCESS;
    }
}
