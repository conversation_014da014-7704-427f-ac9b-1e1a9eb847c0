<?php

declare(strict_types=1);

namespace Console\Command\EMS;

use PhpAmqpLib\Message\AMQPMessage;
use STEms\Daemon\EmsDataSetExportDaemon;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class EmsDataSetExamplesAddToExportCommand extends \Console\Command\BaseCommand
{
    /**
     *
     * @var string
     */
    protected static $defaultName = 'ems:data-set-examples-add-to-export';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'Add data sets to export';

    /**
     *
     * @return void
     */
    protected function configure()
    {
        $this->addOption('status', 's', InputOption::VALUE_IS_ARRAY | InputOption::VALUE_OPTIONAL, 'Status');
        $this->addOption('events_ids', 'e', InputOption::VALUE_IS_ARRAY | InputOption::VALUE_REQUIRED, 'Events ids (required)', []);
    }

    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     * @throws \JsonException
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $eventsIds = array_filter($input->getOption('events_ids'), function ($eventId) {
            return is_numeric($eventId) && (int) $eventId > 0;
        });
        if (empty($eventsIds)) {
            throw new \RuntimeException('Param "events_ids" is reqiured');
        }
        $statuses = !empty($input->getOption('status')) ? $input->getOption('status') : null;

        $dataSetsIds = $this->ems()->dataSet()->getDataSetsIdsByEventsIds($eventsIds);

        if (empty($dataSetsIds)) {
            $output->writeln('There are no data sets for the provided events ids');

            return self::FAILURE;
        }

        $channel = $this->rabbit()->getChannel();
        $message = new AMQPMessage(json_encode([
            'data_set_ids' => $dataSetsIds,
            'statuses' => $statuses,
        ], JSON_THROW_ON_ERROR));
        $channel->basic_publish($message, exchange: '', routing_key: EmsDataSetExportDaemon::QUEUE);
        $channel->close();

        return self::SUCCESS;
    }
}
