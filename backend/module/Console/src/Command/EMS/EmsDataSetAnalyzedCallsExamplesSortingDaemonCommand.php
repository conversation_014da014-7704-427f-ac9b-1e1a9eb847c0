<?php

declare(strict_types=1);

namespace Console\Command\EMS;

use STCompany\Service\CompanyService;
use STEms\Daemon\EmsDataSetAnalyzedCallsExamplesSortingDaemon;
use STEms\Service\EmsDataSetExampleService;
use STEms\Service\EmsDataSetService;
use STEms\Service\EmsEventsService;
use STMail\Service\MailService;
use STUser\Service\UserService;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class EmsDataSetAnalyzedCallsExamplesSortingDaemonCommand extends \Console\Command\BaseCommand
{
    /**
     *
     * @var string
     */
    protected static $defaultName = 'ems:data-set-analyzed-calls-examples-search-daemon';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'Runs daemon for ems data set analyzed calls examples search';

    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $daemon = $this->getDaemon();

        $daemon->params()->add($this->getServiceManager()->get(EmsDataSetExampleService::class), EmsDataSetExampleService::class);
        $daemon->params()->add($this->getServiceManager()->get(EmsDataSetService::class), EmsDataSetService::class);
        $daemon->params()->add($this->getServiceManager()->get(MailService::class), MailService::class);
        $daemon->params()->add($this->getServiceManager()->get(UserService::class), UserService::class);
        $daemon->params()->add($this->getServiceManager()->get(EmsEventsService::class), EmsEventsService::class);
        $daemon->params()->add($this->getServiceManager()->get(CompanyService::class), CompanyService::class);

        $this->rabbit()->daemon()->run($daemon);

        return self::FAILURE;
    }

    /**
     * @return EmsDataSetAnalyzedCallsExamplesSortingDaemon
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    protected function getDaemon(): EmsDataSetAnalyzedCallsExamplesSortingDaemon
    {
        $daemon = $this->getServiceManager()->get(EmsDataSetAnalyzedCallsExamplesSortingDaemon::class);
        $daemon
            ->setQueueName(EmsDataSetAnalyzedCallsExamplesSortingDaemon::QUEUE)
            ->setErrorQueueName(EmsDataSetAnalyzedCallsExamplesSortingDaemon::QUEUE_ERROR);

        return $daemon;
    }
}
