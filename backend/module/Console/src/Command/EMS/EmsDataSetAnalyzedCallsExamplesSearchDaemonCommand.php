<?php

declare(strict_types=1);

namespace Console\Command\EMS;

use STCompany\Service\CompanyService;
use STCompany\Service\EventService as CompanyEventService;
use STCall\Service\EventService as CallEventService;
use STEms\Daemon\EmsDataSetAnalyzedCallsExamplesSearchDaemon;
use STEms\Service\EmsDataSetService;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class EmsDataSetAnalyzedCallsExamplesSearchDaemonCommand extends \Console\Command\BaseCommand
{
    /**
     *
     * @var string
     */
    protected static $defaultName = 'ems:data-set-analyzed-calls-examples-search-daemon';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'Runs daemon for ems data set analyzed calls examples search';

    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $daemon = $this->getDaemon();

        $daemon->params()->add($this->getServiceManager()->get(CompanyEventService::class), CompanyEventService::class);
        $daemon->params()->add($this->getServiceManager()->get(CallEventService::class), CallEventService::class);
        $daemon->params()->add($this->getServiceManager()->get(CompanyService::class), CompanyService::class);
        $daemon->params()->add($this->getServiceManager()->get(EmsDataSetService::class), EmsDataSetService::class);

        $this->rabbit()->daemon()->run($daemon);

        return self::FAILURE;
    }

    /**
     * @return EmsDataSetAnalyzedCallsExamplesSearchDaemon
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    protected function getDaemon(): EmsDataSetAnalyzedCallsExamplesSearchDaemon
    {
        $daemon = $this->getServiceManager()->get(EmsDataSetAnalyzedCallsExamplesSearchDaemon::class);
        $daemon
            ->setQueueName(EmsDataSetAnalyzedCallsExamplesSearchDaemon::QUEUE)
            ->setErrorQueueName(EmsDataSetAnalyzedCallsExamplesSearchDaemon::QUEUE_ERROR);

        return $daemon;
    }
}
