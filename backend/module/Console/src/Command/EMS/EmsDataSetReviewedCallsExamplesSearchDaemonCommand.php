<?php

declare(strict_types=1);

namespace Console\Command\EMS;

use STCall\Service\Precalculation\CallPrecalculationService;
use STCompany\Service\CompanyService;
use STCompany\Service\EventService;
use STEms\Daemon\EmsDataSetReviewedCallsExamplesSearchDaemon;
use STEms\Service\EmsDataSetExampleService;
use STEms\Service\EmsDataSetService;
use STMail\Service\MailService;
use STUser\Service\UserService;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class EmsDataSetReviewedCallsExamplesSearchDaemonCommand extends \Console\Command\BaseCommand
{
    /**
     *
     * @var string
     */
    protected static $defaultName = 'ems:data-set-reviewed-calls-examples-search-daemon';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'Runs daemon for ems data set reviewed calls examples search';

    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $daemon = $this->getDaemon();

        $daemon->params()->add($this->getServiceManager()->get(EmsDataSetService::class), EmsDataSetService::class);
        $daemon->params()->add($this->getServiceManager()->get(EmsDataSetExampleService::class), EmsDataSetExampleService::class);
        $daemon->params()->add($this->getServiceManager()->get(CallPrecalculationService::class), CallPrecalculationService::class);
        $daemon->params()->add($this->getServiceManager()->get(MailService::class), MailService::class);
        $daemon->params()->add($this->getServiceManager()->get(UserService::class), UserService::class);
        $daemon->params()->add($this->getServiceManager()->get(CompanyService::class), CompanyService::class);
        $daemon->params()->add($this->getServiceManager()->get(EventService::class), EventService::class);

        $this->rabbit()->daemon()->run($daemon);

        return self::FAILURE;
    }

    /**
     * @return EmsDataSetReviewedCallsExamplesSearchDaemon
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    protected function getDaemon(): EmsDataSetReviewedCallsExamplesSearchDaemon
    {
        $daemon = $this->getServiceManager()->get(EmsDataSetReviewedCallsExamplesSearchDaemon::class);
        $daemon
            ->setQueueName(EmsDataSetReviewedCallsExamplesSearchDaemon::QUEUE)
            ->setErrorQueueName(EmsDataSetReviewedCallsExamplesSearchDaemon::QUEUE_ERROR);

        return $daemon;
    }
}
