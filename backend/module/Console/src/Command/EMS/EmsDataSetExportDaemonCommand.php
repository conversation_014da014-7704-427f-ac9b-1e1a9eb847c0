<?php

declare(strict_types=1);

namespace Console\Command\EMS;

use STCompany\Service\EventService;
use STEms\Daemon\EmsDataSetExportDaemon;
use STEms\Service\EmsDataSetService;
use STSlack\Service\SlackService;
use STEms\Service\EmsDataSetExampleService;
use STCompany\Service\CompanyService;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class EmsDataSetExportDaemonCommand extends \Console\Command\BaseCommand
{
    /**
     *
     * @var string
     */
    protected static $defaultName = 'ems:data-set-export-daemon';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'Daemon to export data sets';

    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $awsConfig = $this->getServiceManager()->get('config')['aws'];

        $daemon = $this->getServiceManager()->get(EmsDataSetExportDaemon::class);
        $daemon
            ->setQueueName(EmsDataSetExportDaemon::QUEUE)
            ->setErrorQueueName(EmsDataSetExportDaemon::QUEUE_ERROR);
        $daemon->params()->add($this->getServiceManager()->get(CompanyService::class), CompanyService::class);
        $daemon->params()->add($this->getServiceManager()->get(EventService::class), EventService::class);
        $daemon->params()->add($this->getServiceManager()->get(EmsDataSetService::class), EmsDataSetService::class);
        $daemon->params()->add($this->getServiceManager()->get(EmsDataSetExampleService::class), EmsDataSetExampleService::class);
        $daemon->params()->add($this->getServiceManager()->get(SlackService::class), SlackService::class);
        $daemon->params()->add($awsConfig, 'aws_config');

        $this->rabbit()->daemon()->run($daemon);

        return self::FAILURE;
    }
}
