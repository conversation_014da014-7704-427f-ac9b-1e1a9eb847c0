<?php

declare(strict_types=1);

namespace Console\Command\Notification;

use ST<PERSON>ront\Service\FrontService;
use STMail\Service\MailService;
use STUser\Service\UserService;

class CompaniesWithDecreasingCallsAmount extends \Console\Command\BaseCommand
{
    private const EMAIL_TO_SEND_NOTIFICATION = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
    ];

    /**
     *
     * @var string
     */
    protected static $defaultName = 'notifications:companies-with-decreasing-calls-amount';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'Command gets companies with decreasing calls amount and sends notification';

    /**
     *
     * @param \Symfony\Component\Console\Input\InputInterface $input
     * @param \Symfony\Component\Console\Output\OutputInterface $output
     * @return int
     */
    protected function launch(\Symfony\Component\Console\Input\InputInterface $input, \Symfony\Component\Console\Output\OutputInterface $output): int
    {
        $companiesWithLowerCallsVolume = $this->call()->getCompaniesWithDecreasingCallsAmount();

        if (!empty($companiesWithLowerCallsVolume)) {
            $this->sendEmailNotification($companiesWithLowerCallsVolume);
        }

        return static::SUCCESS;
    }

    /**
     *
     * @param array $companies
     * @return void
     */
    private function sendEmailNotification(array $companies): void
    {
        /** @var MailService $mailService */
        $mailService = $this->getServiceManager()->get(MailService::class);
        /** @var UserService $userService */
        $userService = $this->getServiceManager()->get(UserService::class);
        /** @var FrontService $frontService */
        $frontService = $this->getServiceManager()->get(FrontService::class);

        foreach (static::EMAIL_TO_SEND_NOTIFICATION as $email) {
            $mailService->addToQueue(
                $frontService->getDefaultFront()->getId(),
                $userService->getUserByEmail($email),
                [
                    'template_id' => 'companies-with-low-calls-volume',
                    'substitutions' => [
                        'companies' => $companies,
                    ],
                ]
            );
        }
    }
}
