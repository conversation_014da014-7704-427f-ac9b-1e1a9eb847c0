<?php

declare(strict_types=1);

namespace Console\Command\S3Report;

use STS3Report\Daemon\S3ReportDaemon;
use STS3Report\Service\S3ReportService;
use STCompany\Service\CompanyService;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 's3:report:daemon',
    description: 'Runs daemon for s3 report',
)]
class S3ReportDaemonCommand extends \Console\Command\BaseCommand
{
    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $daemon = $this->getServiceManager()->get(S3ReportDaemon::class);
        $daemon
            ->setQueueName(S3ReportDaemon::QUEUE)
            ->setErrorQueueName(S3ReportDaemon::QUEUE_ERROR);
        $daemon->params()->add($this->getServiceManager()->get('config')['aws'], 'aws_config');
        $daemon->params()->add($this->getServiceManager()->get(CompanyService::class), CompanyService::class);
        $daemon->params()->add($this->getServiceManager()->get(S3ReportService::class), S3ReportService::class);

        $this->rabbit()->daemon()->run($daemon);

        return self::FAILURE;
    }
}
