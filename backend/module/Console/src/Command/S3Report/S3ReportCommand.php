<?php

declare(strict_types=1);

namespace Console\Command\S3Report;

use PhpAmqpLib\Message\AMQPMessage;
use STS3Report\Daemon\S3ReportDaemon;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 's3:report:add-jobs',
    description: 'S3 reports jobs creating',
)]
class S3ReportCommand extends \Console\Command\BaseCommand
{
    protected function configure(): void
    {
        parent::configure();

        $this->addOption(
            'date',
            shortcut: 'd',
            mode: InputOption::VALUE_OPTIONAL,
            description: 'Date in format YYYY-MM-DD',
            default: 'yesterday',
        );
        $this->addOption(
            'company_id',
            shortcut: 'c',
            mode: InputOption::VALUE_OPTIONAL,
        );
    }

    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $channel = $this->rabbit()->getChannel();

        $companyIds = $input->getOption('company_id')
            ? [$input->getOption('company_id')]
            : $this->company()->getCompanyIdsWithS3ReportIntegration()
        ;

        foreach ($companyIds as $companyId) {
            $message = new AMQPMessage(json_encode([
                'company_id' => $companyId,
                'date' => \Carbon\Carbon::parse($input->getOption('date'))->toDateString(),
            ]));
            $channel->basic_publish($message, '', routing_key: S3ReportDaemon::QUEUE);
        }

        $channel->close();

        return self::SUCCESS;
    }
}
