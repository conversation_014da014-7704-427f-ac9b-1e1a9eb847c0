<?php

declare(strict_types=1);

namespace Console\Command\Queue;

use Symfony\Component\Console\Input\InputOption;
use Console\Command\BaseCommand;
use PhpAmqpLib\Message\AMQPMessage;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class ReadMessagesCommand extends BaseCommand
{
    use QueueCommandFilterableTrait;
    use QueueCommandLimitableTrait;

    /**
     * @return int
     */
    public function getDefaultLimit(): int
    {
        return 1000;
    }

    /**
     * @var string
     */
    protected static string $defaultName = 'queue:read-messages';

    /**
     * @var string
     */
    protected static string $defaultDescription = 'Read messages from queue by condition without removing them';

    /**
     * @return void
     */
    protected function configure()
    {
        parent::configure();
        $this->addOption('queue', 'u', InputOption::VALUE_REQUIRED, 'Queue name');
        $this->addOption('jsonOutput', 'j', InputOption::VALUE_OPTIONAL, 'JSON output format', false);
        $this->addConfigureCallsForQueueCommandLimitableTrait();
        $this->addConfigureCallsForQueueCommandFilterableTrait();
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $queue = $input->getOption('queue');
        $jsonOutput = (bool) $input->getOption('jsonOutput');

        if (empty($queue)) {
            throw new \RuntimeException('Parameter "queue" is required');
        }

        $conditions = $this->getConditions($input);
        $parsedConditions = $this->parseConditions($conditions);

        $limit = $this->getLimit($input);
        $processLimit = $this->getProcessLimit($input);


        $messagesSuitable = 0;
        $messagesRead = 0;


        $channel = $this->rabbit()->getChannel();
        if ($jsonOutput) {
            $output->writeln('[');
        }

        while ($messagesRead < $limit) {
            // basic_get has an error, 2nd argument isn't no_ack but auto_ack
            $data = $channel->basic_get($queue, false);

            if (is_null($data)) {
                if ($messagesRead === 0) {
                    $output->writeln('Queue is empty');
                }
                break;
            }

            try {
                $messageBody = json_decode($data->body);

                if (!$messageBody instanceof \stdClass) {
                    $messagesRead++;
                    continue;
                }

                if ($this->isMessageSuitable($messageBody, $parsedConditions)) {
                    if ($jsonOutput && $messagesSuitable > 0) {
                        $output->writeln(',');
                    }
                    $output->write(json_encode($messageBody, JSON_PRETTY_PRINT));
                    if (!$jsonOutput) {
                        $output->writeln('');
                    }
                    $messagesSuitable++;
                    if ($messagesSuitable >= $processLimit) {
                        break;
                    }
                }

                $messagesRead++;
            } catch (\Exception $e) {
                throw $e;
            }
        }

        if (!$jsonOutput && $messagesSuitable > 0) {
            $output->writeln(
                sprintf("\nFound %d matching messages out of %d messages read", $messagesSuitable, $messagesRead)
            );
        }
        if ($jsonOutput) {
            $output->writeln('');
            $output->writeln(']');
        }

        return static::SUCCESS;
    }
}
