<?php

declare(strict_types=1);

namespace Console\Command\Queue;

use Console\Command\BaseCommand;
use PhpAmqpLib\Message\AMQPMessage;
use RuntimeException;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class AddMessagesCommand extends BaseCommand
{
    /**
     * @var string
     */
    protected static $defaultName = 'queue:messages:add';

    /**
     * @var string
     */
    protected static $defaultDescription = 'Adds messages to a queue from a JSON file.';

    /**
     * @return void
     */
    protected function configure()
    {
        parent::configure();
        $this->addOption('queue', 'u', InputOption::VALUE_REQUIRED, 'The name of the queue to publish messages to');
        $this->addOption('file', 'f', InputOption::VALUE_REQUIRED, 'Path to the JSON file containing an array of message objects');
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $queue = $input->getOption('queue');
        $filePath = $input->getOption('file');

        if (empty($queue)) {
            throw new RuntimeException('The "queue" option is required.');
        }

        if (empty($filePath)) {
            throw new RuntimeException('The "file" option is required.');
        }

        if (!file_exists($filePath) || !is_readable($filePath)) {
            throw new RuntimeException(sprintf('The file "%s" does not exist or is not readable.', $filePath));
        }

        $fileContent = file_get_contents($filePath);
        if ($fileContent === false) {
            throw new RuntimeException(sprintf('Could not read the content of the file "%s".', $filePath));
        }

        $messages = json_decode($fileContent);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new RuntimeException(sprintf('Invalid JSON in file "%s": %s', $filePath, json_last_error_msg()));
        }

        if (!is_array($messages)) {
            throw new RuntimeException(sprintf('The JSON file "%s" must contain a root-level array of message objects.', $filePath));
        }

        $channel = $this->rabbit()->getChannel();
        $messagesAdded = 0;

        foreach ($messages as $messageBody) {
            if (!is_object($messageBody)) {
                $output->writeln('<warning>Skipping an item in the file that is not a valid JSON object.</warning>');
                continue;
            }

            $messageString = json_encode($messageBody);
            $amqpMessage = new AMQPMessage($messageString);
            $channel->basic_publish($amqpMessage, '', $queue);
            $messagesAdded++;
        }

        $output->writeln(sprintf(
            "<info>Successfully added %d messages from '%s' to the '%s' queue.</info>",
            $messagesAdded,
            $filePath,
            $queue
        ));

        return static::SUCCESS;
    }
}
