<?php

namespace Console\Command\Queue;

use Symfony\Component\Console\Input\InputOption;
use Console\Command\BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use PhpAmqpLib\Message\AMQPMessage;
use RuntimeException;

class RemoveDuplicatesCommand extends BaseCommand
{
    use QueueCommandLimitableTrait;

    public function getProcessLimit(InputInterface $input): int
    {
        return 200000;
    }


    /**
     *
     * @var string
     */
    protected static string $defaultName = 'queue:messages:remove-duplicates';

    /**
     *
     * @var string
     */
    protected static string $defaultDescription = 'Removes all duplicates from queue';

    /**
     *
     * @return void
     */
    protected function configure(): void
    {
        $this->addOption('queue', 'u', InputOption::VALUE_REQUIRED, 'Queue name');
        $this->addConfigureCallsForQueueCommandLimitableTrait();
    }

    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $queue = $input->getOption('queue');
        if (empty($queue)) {
            throw new RuntimeException('Params "queue" are required');
        }

        $limit = $this->getLimit($input);
        $processLimit = $this->getProcessLimit($input);

        // here if only valid payload it's suitable
        $messagesSuitable = 0;
        $messagesRead = 0;
        $duplicatesFound = 0;

        $messagesToRepublishInQueue = [];
        $channel = $this->rabbit()->getChannel();

        $spottedRequestIds = [];

        for ($i = 0; $i < $limit; $i++) {
            // basic_get has an error, 2nd argument isn't no_ack but auto_ack
            $data = $channel->basic_get($queue, true);
            if (is_null($data)) {
                $output->writeln(sprintf('Read %d messages from queue. Queue is now empty.', $messagesRead));
                break;
            }
            $messagesRead++;

            $messageBody = json_decode($data->body, true);

            $reqId = $messageBody['options']['request_id'] ?? '';
            if (!empty($reqId)) {
                if (array_key_exists($reqId, $spottedRequestIds)) {
                    $duplicatesFound++;
                    continue;
                }
            }

            $spottedRequestIds[$reqId] = true;

            $messagesToRepublishInQueue[] = new AMQPMessage(json_encode($messageBody));
            $messagesSuitable++;

            if ($messagesSuitable >= $processLimit) {
                break;
            }
        }

        if (!empty($messagesToRepublishInQueue)) {
            foreach ($messagesToRepublishInQueue as $messageToRepublishInQueue) {
                $channel->basic_publish($messageToRepublishInQueue, '', $queue);
            }
            $output->writeln(sprintf('Re-queued %d back to the queue.', $messagesSuitable));
        }

        $output->writeln(
            sprintf(
                "Finished reading %s. Read %d messages, %d duplicates found. Left %d'. ",
                $queue,
                $messagesRead,
                $duplicatesFound,
                $messagesSuitable,
            )
        );

        return static::SUCCESS;
    }
}
