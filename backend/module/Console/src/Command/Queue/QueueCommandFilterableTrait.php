<?php

namespace Console\Command\Queue;

use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;

trait QueueCommandFilterableTrait
{
    protected function addConfigureCallsForQueueCommandFilterableTrait(): void
    {
        $this->addOption(
            'conditions',
            'c',
            InputOption::VALUE_IS_ARRAY | InputOption::VALUE_OPTIONAL,
            'conditions'
        );
    }

    protected function getConditions(InputInterface $input): array
    {
        return $input->getOption('conditions') ?? [];
    }

    /**
     * @param array|null $conditions
     * @return array
     */
    private function parseConditions(?array $conditions): array
    {
        $parsedConditions = [];
        if ($conditions) {
            foreach ($conditions as $condition) {
                preg_match('/([^=]+)=(.+)/', $condition, $matches); // Changed regex to capture everything after first '='
                if (!isset($matches[1]) || !isset($matches[2]) || empty($matches[1])) { // $matches[2] can be empty for pattern like "key="
                    throw new \RuntimeException('Invalid condition "' . $condition . '", correct format is param=value');
                }
                $param = $matches[1];
                $value = $matches[2];
                $negation = false;
                if (substr($value, 0, 1) === '!') {
                    $value = substr($value, 1, strlen($value) - 1);
                    $negation = true;
                }

                $arrayOfValues = explode('|', $value);
                if (
                    count($arrayOfValues) > 1 &&
                    (str_starts_with($value, '%') || str_ends_with($value, '%') || $negation)
                ) {
                    throw new \InvalidArgumentException('Can not do array or negation with wildcards');
                }
                if (count($arrayOfValues) > 1) {
                    $value = $arrayOfValues;
                }

                $parsedConditions[] = [
                    'param' => $param,
                    'value' => $value,
                    'negation' => $negation,
                ];
            }
        }
        return $parsedConditions;
    }

    /**
     * Checks if a message body satisfies a list of conditions, supporting wildcard matching.
     *
     * @param \stdClass $messageBody The decoded JSON message body.
     * @param array $conditions The parsed conditions to check against.
     * @return bool True if the message is suitable, false otherwise.
     */
    private function isMessageSuitable(\stdClass $messageBody, array $conditions): bool
    {
        if (empty($conditions)) {
            return true;
        }

        foreach ($conditions as $condition) {
            $paramParts = explode('.', $condition['param']);
            $expectedPattern = $condition['value']; // Renamed for clarity

            $actualValue = null;

            // 1. Retrieve the actual value from the message body and catch nulls
            // @todo refactor this please
            if (count($paramParts) === 3) {
                $key1 = $paramParts[0];
                $key2 = $paramParts[1];
                $key3 = $paramParts[2];

                if (!is_array($condition['value']) && strtolower($condition['value']) === 'null') {
                    if (
                        property_exists($messageBody, $key1) && property_exists($messageBody->{$key1}, $key2) &&
                        property_exists($messageBody->{$key1}->{$key2}, $key3) &&
                        $messageBody->{$key1}->{$key2}->{$key3} === null
                    ) {
                        return true;
                    } else {
                        return false;
                    }
                }

                // Check if the nested properties exist
                if (
                    !isset($messageBody->{$key1}) ||
                    !isset($messageBody->{$key1}->{$key2}) ||
                    !isset($messageBody->{$key1}->{$key2}->{$key3})
                ) {
                    return false;
                }
                $actualValue = (string) $messageBody->{$key1}->{$key2}->{$key3};
            } elseif (count($paramParts) === 2) {
                $key1 = $paramParts[0];
                $key2 = $paramParts[1];

                if (!is_array($condition['value']) && strtolower($condition['value']) === 'null') {
                    if (
                        property_exists($messageBody, $key1) && property_exists($messageBody->{$key1}, $key2)
                        && $messageBody->{$key1}->{$key2} === null
                    ) {
                        return true;
                    } else {
                        return false;
                    }
                }

                // Check if the nested properties exist
                if (!isset($messageBody->{$key1}) || !isset($messageBody->{$key1}->{$key2})) {
                    return false;
                }
                $actualValue = (string) $messageBody->{$key1}->{$key2};
            } else {
                $key = $condition['param'];

                // we want nulls and it's not null
                if (!is_array($condition['value']) && strtolower($condition['value']) === 'null') {
                    if (property_exists($messageBody, $key) && $messageBody->{$key} === null) {
                        return true;
                    } else {
                        return false;
                    }
                }

                if (!isset($messageBody->{$key})) {
                    return false;
                }
                $actualValue = (string) $messageBody->{$key};
            }

            $startsWildcard = $endsWildcard = false;
            if (!is_array($condition['value'])) {
                // 2. Perform pattern matching based on wildcards
                $startsWildcard = str_starts_with($expectedPattern, '%');
                $endsWildcard = str_ends_with($expectedPattern, '%');
            }

            if ($startsWildcard && $endsWildcard) {
                // Scenario 1: "%value%" (contains)
                $searchString = trim($expectedPattern, '%');
                if (!str_contains($actualValue, $searchString)) {
                    return false;
                }
            } elseif ($startsWildcard) {
                // Scenario 3: "%value" (ends with)
                $searchString = ltrim($expectedPattern, '%');
                if (!str_ends_with($actualValue, $searchString)) {
                    return false;
                }
            } elseif ($endsWildcard) {
                // Scenario 2: "value%" (starts with)
                $searchString = rtrim($expectedPattern, '%');
                if (!str_starts_with($actualValue, $searchString)) {
                    return false;
                }
            } else {
                // Scenario 4: "value" (exact phrase)
                if (is_array($expectedPattern)) {
                    if (!in_array($actualValue, $expectedPattern)) {
                        return $condition['negation'] ? true : false;
                    }
                } elseif ($actualValue !== $expectedPattern) {
                    return $condition['negation'] ? true : false;
                }
            }
        }

        return $condition['negation'] ? false : true;
    }
}
