<?php

declare(strict_types=1);

namespace Console\Command\Queue;

use Laminas\ServiceManager\Factory\FactoryInterface;
use STCall\Service\CallAnalysis\BaseStep;
use Symfony\Component\Console\Input\InputOption;
use Console\Command\BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use STCall\Controller\Plugin\Call;
use STCall\Service\CallService;

class ConsumeMessagesCommand extends BaseCommand
{
    protected const DEFAULT_LIMIT = 50;

    /**
     * @var string
     */
    protected static $defaultName = 'queue:messages:consume';

    /**
     * @var string
     */
    protected static $defaultDescription = 'Consumes and acknowledges N messages from a queue, outputting them to the console.';

    /**
     * @return void
     */
    protected function configure()
    {
        parent::configure();
        $this->addOption('queue', 'u', InputOption::VALUE_REQUIRED, 'The name of the queue to consume from');
        $this->addOption('limit', 'l', InputOption::VALUE_REQUIRED, 'Maximum number of messages to consume', static::DEFAULT_LIMIT);
        $this->addOption('step', 's', InputOption::VALUE_REQUIRED, 'Step name');
        $this->addOption('jsonOutput', 'j', InputOption::VALUE_NONE, 'Output messages in a JSON array format');
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $queue = $input->getOption('queue');
        /** @var Call|CallService $call */
        $stepName = $input->getOption('step');
        if (!$stepName) {
            throw  new \InvalidArgumentException(
                sprintf(
                    'Invalid step option, `%s` given, available: %s  - %s ',
                    $stepName,
                    PHP_EOL,
                    implode(PHP_EOL . "  - ", $call->getAvailableAnalysisSteps())
                )
            );
        }


        $limit = max((int) $input->getOption('limit'), 0);
        $jsonOutput = (bool) $input->getOption('jsonOutput');

        if (empty($queue)) {
            throw new \RuntimeException('The "queue" option is required.');
        }

        /** @var FactoryInterface $stepFactory */

        $channel = $this->rabbit()->getChannel();
        $messagesConsumed = 0;

        if ($jsonOutput) {
            $output->writeln('[');
        }

        for ($i = 0; $i < $limit; $i++) {
            // The second parameter `auto_ack` is set to `true`.
            // This tells RabbitMQ to immediately consider the message acknowledged and remove it from the queue.
            $data = $channel->basic_get($queue, true);
            if (is_null($data)) {
                break;
            }

            $parsedData = json_decode($data->body);
            $arrayData = json_decode($data->body, true);
            $options = $arrayData['options'] ?? [];
            /** @var BaseStep $step */
            $step = $this->call()->getAnalysisStep($stepName);
            $step->applyOptions($options);

            $call = $step->launch($parsedData->call_id, $parsedData->company_id);
            $step->run($parsedData->company_id);

            if (is_null($data)) {
                if ($messagesConsumed === 0 && !$jsonOutput) {
                    $output->writeln('<info>Queue is empty.</info>');
                }
                break;
            }

            try {
                $messageBody = json_decode($data->body);

                if (!$messageBody instanceof \stdClass) {
                    $output->writeln(sprintf('<error>Consumed a message with invalid JSON body. Raw: %s</error>', $data->body));
                    continue;
                }

                if ($jsonOutput && $messagesConsumed > 0) {
                    $output->writeln(',');
                }

                $output->write(json_encode($messageBody, JSON_PRETTY_PRINT));

                if (!$jsonOutput) {
                    $output->writeln('');
                }

                $messagesConsumed++;
            } catch (\Exception $e) {
                $output->writeln(sprintf('<error>An error occurred while processing a consumed message: %s</error>', $e->getMessage()));
            }
        }

        if ($jsonOutput) {
            $output->writeln('');
            $output->writeln(']');
        }

        if (!$jsonOutput && $messagesConsumed > 0) {
            $output->writeln(sprintf(
                "\n<info>Finished. Consumed a total of %d messages from the '%s' queue.</info>",
                $messagesConsumed,
                $queue
            ));
        }

        return static::SUCCESS;
    }
}
