<?php

declare(strict_types=1);

namespace Console\Command\Queue;

use PhpAmqpLib\Message\AMQPMessage;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Console\Command\BaseCommand;
use stdClass;

class DropMessagesCommand extends BaseCommand
{
    use QueueCommandFilterableTrait;
    use QueueCommandLimitableTrait;

    public function getDefaultLimit(): int
    {
        return 1000;
    }

    /**
     *
     * @var string
     */
    protected static string $defaultName = 'queue:drop-messages';

    /**
     *
     * @var string
     */
    protected static string $defaultDescription = 'Delete messages from queue by condition';

    /**
     *
     * @return void
     */
    protected function configure()
    {
        parent::configure();
        $this->addOption('queue', 'u', InputOption::VALUE_REQUIRED, 'The name of the queue to read from');
        $this->addOption('dryRun', 'd', InputOption::VALUE_OPTIONAL, 'Dry run mode, do not delete', false);
        $this->addConfigureCallsForQueueCommandLimitableTrait();
        $this->addConfigureCallsForQueueCommandFilterableTrait();
    }

    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $queue = $input->getOption('queue');
        $dryRun = (bool) $input->getOption('dryRun');

        if ($dryRun) {
            $output->writeln('Dry run mode enabled');
        }
        $conditions = $this->getConditions($input);
        $parsedConditions = $this->parseConditions($conditions);

        $limit = $this->getLimit($input);
        $processLimit = $this->getProcessLimit($input);


        if (empty($queue) || empty($conditions)) {
            throw new \RuntimeException('Parameters "queue" and "conditions" are required');
        }

        $channel = $this->rabbit()->getChannel();

        $messagesSuitable = 0;
        $messagesRead = 0;
        $messagesReQueued = 0;

        for ($i = 0; $i < $limit; $i++) {
            // second parameter is auto_ack=true, message is removed immediately
            // false, message is not removed
            $data = $channel->basic_get($queue, $dryRun ? false : true);

            if (is_null($data)) {
                $output->writeln('Queue is empty.');
                break;
            }

            try {
                $messageBody = json_decode($data->body);

                if (!$messageBody instanceof stdClass) {
                    $output->writeln('<error>Invalid JSON in message body, requeueing.</error>');
                    $message = new AMQPMessage($data->body);
                    $channel->basic_publish($message, '', $queue);
                    $messagesReQueued++;
                    continue;
                }

                if ($this->isMessageSuitable($messageBody, $parsedConditions)) {
                    $output->writeln('<info>Dropped message:</info> ' . json_encode($messageBody));
                    $messagesSuitable++;
                    if ($messagesSuitable >= $processLimit) {
                        break;
                    }
                } else {
                    $message = new AMQPMessage(json_encode($messageBody));
                    $channel->basic_publish($message, '', $queue);
                    $messagesReQueued++;
                }
            } catch (\Exception $e) {
                $output->writeln(sprintf('<error>An error occurred: %s. Requeueing original message.</error>', $e->getMessage()));
                $message = new AMQPMessage($data->body);
                $channel->basic_publish($message, '', $queue);
                $messagesReQueued++;
            }
        }

        $output->writeln(sprintf(
            $dryRun ? "\n<info>Dry run mode. Finished. %d matching messages to drop. %d non-matching or invalid messages to re-queue. Total %d.</info>" :
            "\n<info>Finished. Dropped %d matching messages. Re-queued %d non-matching or invalid messages. Total %d.</info>",
            $messagesSuitable,
            $messagesReQueued,
            $messagesRead,
        ));

        return static::SUCCESS;
    }
}
