<?php

declare(strict_types=1);

namespace Console\Command\Queue;

use Symfony\Component\Console\Input\InputOption;
use Console\Command\BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use PhpAmqpLib\Message\AMQPMessage;
use RuntimeException;
use stdClass;

class RepeatQueueProcessingCommand extends BaseCommand
{
    use QueueCommandFilterableTrait;
    use QueueCommandLimitableTrait;

    /**
     *
     * @var string
     */
    protected static string $defaultName = 'queue:messages:repeat-error';

    /**
     *
     * @var string
     */
    protected static string $defaultDescription = 'Repeat processing of all messages in error queue';

    /**
     *
     * @return void
     */
    protected function configure(): void
    {
        $this->addOption('error_queue', 'e', InputOption::VALUE_REQUIRED, 'Error queue name');
        $this->addOption('queue', 'u', InputOption::VALUE_REQUIRED, 'Queue name');
        $this->addConfigureCallsForQueueCommandFilterableTrait();
        $this->addConfigureCallsForQueueCommandLimitableTrait();
    }

    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $queue = $input->getOption('queue');
        $errorQueue = $input->getOption('error_queue');
        if (empty($queue) || empty($errorQueue)) {
            throw new RuntimeException('Params "queue" and "error_queue" are required');
        }

        $conditions = $this->getConditions($input);
        $parsedConditions = $this->parseConditions($conditions);

        $limit = $this->getLimit($input);
        $processLimit = $this->getProcessLimit($input);

        $messagesToRepublishInErrorQueue = [];

        $messagesSuitable = 0;
        $messagesRead = 0;
        $messagesReQueued = 0;

        $channel = $this->rabbit()->getChannel();

        for ($i = 0; $i < $limit; $i++) {
            // basic_get has an error, 2nd argument isn't no_ack but auto_ack
            $data = $channel->basic_get($errorQueue, true);
            if (is_null($data)) {
                $output->writeln(sprintf('Read %d messages from queue. Queue is now empty.', $messagesRead));
                break;
            }
            $messagesRead++;

            $messageBody = json_decode($data->body);

            if (!$messageBody instanceof stdClass) {
                $output->writeln('<error>Invalid JSON in message body, requeueing in error queue.</error>');
                $messagesToRepublishInErrorQueue[] = new AMQPMessage($data->body);
                $messagesReQueued++;
                continue;
            }

            if ($this->isMessageSuitable($messageBody, $parsedConditions)) {
                $messageBody->attempt = 0;
                unset($messageBody->error);
                $message = new AMQPMessage(json_encode($messageBody));
                $channel->basic_publish($message, '', $queue);
                $output->writeln('Moved message: ' . json_encode($messageBody));
                $messagesSuitable++;
                if ($messagesSuitable >= $processLimit) {
                    break;
                }
            } else {
                $messagesToRepublishInErrorQueue[] = new AMQPMessage(json_encode($messageBody));
            }
        }

        if (!empty($messagesToRepublishInErrorQueue)) {
            foreach ($messagesToRepublishInErrorQueue as $messageToRepublishInErrorQueue) {
                $channel->basic_publish($messageToRepublishInErrorQueue, '', $errorQueue);
            }
            $output->writeln(sprintf('Re-queued %d non-matching or invalid messages back to the error queue.', $messagesReQueued));
        }

        $output->writeln(
            sprintf(
                "Finished. Moved %d matching messages from '%s' to '%s'. ",
                $messagesSuitable,
                $errorQueue,
                $queue
            )
        );
        $output->writeln(
            sprintf(
                "Re-queued %d non-matching or invalid messages back to the error queue. Total %d.",
                $messagesReQueued,
                $messagesRead
            )
        );

        return static::SUCCESS;
    }
}
