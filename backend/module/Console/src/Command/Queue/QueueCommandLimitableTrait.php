<?php

namespace Console\Command\Queue;

use RuntimeException;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;

trait QueueCommandLimitableTrait
{
    /**
     * @var int
     */
    protected const int DEFAULT_PROCESS_LIMIT = 1000;
    /**
     * @var int
     */
    protected const int DEFAULT_LIMIT = 200000;

    protected const int MAX_LIMIT = 300000;

    protected const int MAX_PROCESS_LIMIT = 100000;

    /**
     * @return int
     */
    public function getDefaultLimit(): int
    {
        return self::DEFAULT_LIMIT;
    }

    protected function addConfigureCallsForQueueCommandLimitableTrait(): void
    {
        $this->addOption(
            'limit',
            'l',
            InputOption::VALUE_OPTIONAL,
            'limit number of messages process',
            $this->getDefaultLimit(),
        );
        $this->addOption(
            'process_limit',
            'p',
            InputOption::VALUE_OPTIONAL,
            'stop processing after this number of messages',
            $this->getDefaultProcessLimit(),
        );
    }

    /**
     * @return int
     */
    public function getDefaultProcessLimit(): int
    {
        return self::DEFAULT_PROCESS_LIMIT;
    }


    /**
     * @param InputInterface $input
     * @return int
     */
    public function getLimit(InputInterface $input): int
    {
        $limit = max((int) $input->getOption('limit', self::DEFAULT_LIMIT), 0);
        if ($limit > self::MAX_LIMIT || $limit <= 0) {
            throw new RuntimeException(sprintf(
                'Params "limit" cannot be greater than %d and less than 1',
                self::MAX_LIMIT
            ));
        }

        return $limit;
    }

    /**
     * @param InputInterface $input
     * @return int
     */
    public function getProcessLimit(InputInterface $input): int
    {
        $processLimit = max((int) $input->getOption('process_limit', self::DEFAULT_PROCESS_LIMIT), 0);
        if ($processLimit > self::MAX_PROCESS_LIMIT || $processLimit <= 0) {
            throw new RuntimeException(sprintf(
                'Params "process_limit" cannot be greater than %d and less than 1',
                self::MAX_PROCESS_LIMIT
            ));
        }

        return $processLimit;
    }
}
