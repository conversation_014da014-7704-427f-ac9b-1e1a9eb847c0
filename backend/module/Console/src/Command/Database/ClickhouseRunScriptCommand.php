<?php

declare(strict_types=1);

namespace Console\Command\Database;

use Console\Command\BaseCommand;
use S<PERSON>fony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use STClickhouse\Client\Client as ClickHouseClient;
use RuntimeException;

#[AsCommand(
    name: 'db:clickhouse:run-script',
    description: 'Executes a SQL script file against the ClickHouse database.',
)]
final class ClickhouseRunScriptCommand extends BaseCommand
{
    protected function configure(): void
    {
        parent::configure();
        $this->addOption(
            'file',
            'f',
            InputOption::VALUE_REQUIRED,
            'Path to the SQL script file to execute.'
        );
    }

    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $filePath = $input->getOption('file');

        if (!$filePath || !file_exists($filePath) || !is_readable($filePath)) {
            throw new RuntimeException(sprintf('The file "%s" does not exist or is not readable.', $filePath ?? ''));
        }

        /**
         * This assumes a ClickHouseDB\Client is available in the service manager.
         * If it's not, you will need to create a factory for it that uses your
         * CLICKHOUSE_* environment variables.
         * @var ClickHouseClient $clickhouseClient
         */
        $clickhouseClient = $this->getServiceManager()->get(ClickHouseClient::class);

        $sqlContent = file_get_contents($filePath);
        if ($sqlContent === false) {
            throw new RuntimeException(sprintf('Could not read the content of the file "%s".', $filePath));
        }

        // Split statements by semicolon. This is a simple approach that works for the provided script.
        $statements = explode(';', $sqlContent);

        $output->writeln("<info>Executing script:</info> {$filePath}");
        $executedCount = 0;

        foreach ($statements as $statement) {
            $statement = trim($statement);

            // Skip empty statements that result from the final semicolon in the file
            if (empty($statement)) {
                continue;
            }

            try {
                // The `write` method is used for non-SELECT statements like INSERT, CREATE, etc.
                $clickhouseClient->write($statement);
                $output->writeln("  <fg=green>✔</> Executed statement.");
                $executedCount++;
            } catch (\Exception $e) {
                $output->writeln("<error>Error executing statement: {$e->getMessage()}</error>");
                $output->writeln("<error>Statement: {$statement}</error>");
                return self::FAILURE;
            }
        }

        $output->writeln(sprintf("\n<info>Finished. Successfully executed %d statements.</info>", $executedCount));

        return self::SUCCESS;
    }
}
