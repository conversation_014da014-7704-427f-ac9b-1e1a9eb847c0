<?php

declare(strict_types=1);

namespace Console\Command\Algo;

use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class UpdateAlgoEventHintCommand extends \Console\Command\BaseCommand
{
    /**
     *
     * @var string
     */
    protected static $defaultName = 'algo:update-event-hint';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'Update algo event hint';

    /**
     *
     * @return void
     */
    protected function configure()
    {
        parent::configure();
        $this->addOption('algo_api_id', null, \Symfony\Component\Console\Input\InputOption::VALUE_REQUIRED, 'algo_api_id (required)');
        $this->addOption('algo_event', null, \Symfony\Component\Console\Input\InputOption::VALUE_REQUIRED, 'algo_event (required)');
        $this->addOption('algo_event_hint', null, \Symfony\Component\Console\Input\InputOption::VALUE_REQUIRED, 'algo_event_hint (required)');
    }

    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $algoApiId = (int) $input->getOption('algo_api_id');
        $algoEvent = (string) $input->getOption('algo_event');
        $algoEventHint = (string) $input->getOption('algo_event_hint');

        if ($algoApiId <= 0) {
            throw new \InvalidArgumentException('Incorrect param "algo_api_id"');
        }
        if (empty($algoEvent)) {
            throw new \InvalidArgumentException('Incorrect param "algo_event"');
        }

        $this->algo()->algoEvent()->updateAlgoEventHint($algoApiId, $algoEvent, $algoEventHint);

        return static::SUCCESS;
    }
}
