<?php

declare(strict_types=1);

namespace Console\Command\Algo;

use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class SaveAlgoApiCommand extends \Console\Command\BaseCommand
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    /**
     *
     * @var string
     */
    protected static $defaultName = 'algo:save-api';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'Save algo api';

    /**
     *
     * @return void
     */
    protected function configure()
    {
        parent::configure();
        $this->addOption('id', null, \Symfony\Component\Console\Input\InputOption::VALUE_OPTIONAL, 'id (optional)');
        $this->addOption('path', null, \Symfony\Component\Console\Input\InputOption::VALUE_OPTIONAL, 'path (optional)');
        $this->addOption('analyze_method', null, \Symfony\Component\Console\Input\InputOption::VALUE_OPTIONAL, 'analyze method (optional)');
        $this->addOption('is_default', null, \Symfony\Component\Console\Input\InputOption::VALUE_OPTIONAL, 'is_default (optional)');
        $this->addOption('category_id', null, \Symfony\Component\Console\Input\InputOption::VALUE_OPTIONAL, 'category_id (optional)');
    }

    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $algoApiId = (int) $input->getOption('id');
        $algoApiPath = (string) $input->getOption('path');
        $analyzeMethod = (string) $input->getOption('analyze_method');
        $isDefaultApi = !is_null($input->getOption('is_default')) ? (bool) $input->getOption('is_default') : null;
        $industryId = (int) $input->getOption('category_id');

        if ($algoApiId > 0) {
            $algoApi = $this->algo()->api()->getAlgoApi($algoApiId);
            if (is_bool($isDefaultApi)) {
                $algoApi->isDefaultApi($isDefaultApi);
            }
            if ($industryId > 0) {
                $algoApi->setIndustryId($industryId);
            }
            if (!empty($analyzeMethod)) {
                $algoApi->setAnalyzeMethod($analyzeMethod);
            }
        } else {
            if (empty($algoApiPath)) {
                throw new \InvalidArgumentException('path is required param');
            }
            if ($industryId <= 0) {
                throw new \InvalidArgumentException('category_id is required param');
            }
            if (empty($analyzeMethod)) {
                throw new \InvalidArgumentException('analyze_method is required param');
            }
            $algoApi = $this->hydrate([
                'path' => $algoApiPath,
                'is_default_api' => $isDefaultApi,
                'industry_id' => $industryId,
                'analyze_method' => $analyzeMethod,
            ], \STAlgo\Entity\AlgoApi::class);
        }

        $this->algo()->api()->saveAlgoApi($algoApi);

        return static::SUCCESS;
    }
}
