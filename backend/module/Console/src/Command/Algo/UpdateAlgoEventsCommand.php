<?php

declare(strict_types=1);

namespace Console\Command\Algo;

use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class UpdateAlgoEventsCommand extends \Console\Command\BaseCommand
{
    /**
     *
     * @var string
     */
    protected static $defaultName = 'algo:update-events';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'Update algo events';

    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $algoApis = $this->algo()->api()->getAlgoApis();
        foreach ($algoApis as $algoApi) {
            $count = $this->algo()->algoEvent()->updateAlgoEvents($algoApi);
            $output->writeln('Added ' . $count . ' algo events for API "' . $algoApi->getPath() . '"');
        }
        return static::SUCCESS;
    }
}
