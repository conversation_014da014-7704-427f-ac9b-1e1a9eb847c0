<?php

declare(strict_types=1);

namespace Console\Command\Algo;

use Console\Command\BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class UpdateAlgoApisCommand extends BaseCommand
{
    protected static $defaultName = 'algo:update-algo-apis';

    protected static $defaultDescription = 'Update algo apis';

    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $updater = $this->algo()->algoApiUpdater();
        $updater->update();

        return self::SUCCESS;
    }
}
