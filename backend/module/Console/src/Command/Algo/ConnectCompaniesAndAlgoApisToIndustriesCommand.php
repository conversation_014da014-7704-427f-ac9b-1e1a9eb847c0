<?php

declare(strict_types=1);

namespace Console\Command\Algo;

use Console\Command\BaseCommand;
use Laminas\Db\Adapter\Adapter;
use <PERSON>inas\Db\Sql\Insert;
use Laminas\Db\Sql\Sql;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use STAlgo\Data\CompaniesAlgoApisTable;
use STCompany\Service\CompanyService;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class ConnectCompaniesAndAlgoApisToIndustriesCommand extends BaseCommand
{
    protected static $defaultName = 'industries:connect-companies-and-algo-apis';

    protected static $defaultDescription = 'Connect companies and algo apis to industries';

    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $this->algoApisToIndustries();

        /**
         * @var CompanyService $companyService
         */
        $companyService = $this->serviceManager->get(CompanyService::class);
        $companiesIds = $companyService->getCompanyIds();
        /**
         * @var CompaniesAlgoApisTable $companiesAlgoApisTable
         */
        $companiesAlgoApisTable = $this->serviceManager->get(CompaniesAlgoApisTable::class);

        $algoApiIds = [97, 118, 56, 64];
        $algoApiCompanyIds = [];
        foreach ($algoApiIds as $algoApiId) {
            $companiesData = $companiesAlgoApisTable->getCompaniesAlgoApisByAlgoApiId($algoApiId);
            $algoApiCompanyIds[$algoApiId] = array_flip(array_column($companiesData->toArray(), 'company_id'));
        }

        $qcIndustryId = 1;
        $salesIndustryId = 2;

        $alreadyAdded = [];
        $this->companiesOfAlgoApisIntoCompaniesIndustriesForIndustry(
            $companiesIds,
            [97, 118],
            $algoApiCompanyIds,
            $qcIndustryId,
            $alreadyAdded
        );
        $this->companiesOfAlgoApisIntoCompaniesIndustriesForIndustry(
            $companiesIds,
            [56, 97, 118],
            $algoApiCompanyIds,
            $qcIndustryId,
            $alreadyAdded
        );
        $this->companiesOfAlgoApisIntoCompaniesIndustriesForIndustry(
            $companiesIds,
            [56, 97, 118],
            $algoApiCompanyIds,
            $salesIndustryId,
            $alreadyAdded
        );
        $this->companiesOfAlgoApisIntoCompaniesIndustriesForIndustry(
            $companiesIds,
            [56, 64, 97, 118],
            $algoApiCompanyIds,
            $qcIndustryId,
            $alreadyAdded
        );
        $this->companiesOfAlgoApisIntoCompaniesIndustriesForIndustry(
            $companiesIds,
            [56, 64, 97, 118],
            $algoApiCompanyIds,
            $salesIndustryId,
            $alreadyAdded
        );

        return self::SUCCESS;
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    private function algoApisToIndustries(): void
    {
        $dbAdapter = $this->serviceManager->get(Adapter::class);
        $sql = new Sql($dbAdapter);

        $insert = new Insert('algo_apis_industries');

        $values = [
            ['industry_id' => 1, 'algo_api_id' => 97],
            ['industry_id' => 1, 'algo_api_id' => 118],

            ['industry_id' => 2, 'algo_api_id' => 56]
        ];

        foreach ($values as $data) {
            $insert->values($data);
            $statement = $sql->prepareStatementForSqlObject($insert);
            $statement->execute();
        }
    }

    /**
     * @param array $companyIds
     * @param array $algoApiIds
     * @param array $algoApisCompanyIds
     * @param int $industryId
     * @param $alreadyAdded
     * @return void
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    private function companiesOfAlgoApisIntoCompaniesIndustriesForIndustry(
        array $companyIds,
        array $algoApiIds,
        array $algoApisCompanyIds,
        int $industryId,
        &$alreadyAdded
    ): void {
        $companiesIdsToAdd = [];
        foreach ($companyIds as $companyId) {
            $key = $companyId . $industryId;
            if (array_key_exists($key, $alreadyAdded)) {
                continue;
            }
            if ($this->isConnectToAlgoApis($companyId, $algoApiIds, $algoApisCompanyIds)) {
                $companiesIdsToAdd[] = $companyId;
                $alreadyAdded[$key] = 1;
            }
        }

        $dbAdapter = $this->serviceManager->get(Adapter::class);
        $sql = new Sql($dbAdapter);
        $insert = new Insert('companies_industries');

        foreach ($companiesIdsToAdd as $companyToAddId) {
            $insert->values(['industry_id' => $industryId, 'company_id' => $companyToAddId]);
            $statement = $sql->prepareStatementForSqlObject($insert);
            $statement->execute();
        }
    }

    private function isConnectToAlgoApis(int $companyId, array $algoApiIds, array $algoApisCompanyIds): bool
    {
        foreach ($algoApiIds as $algoApiId) {
            $algoApiCompanyIds = $algoApisCompanyIds[$algoApiId];
            if (!array_key_exists($companyId, $algoApiCompanyIds)) {
                return false;
            }
        }

        return true;
    }
}
