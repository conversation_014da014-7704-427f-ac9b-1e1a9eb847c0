<?php

declare(strict_types=1);

namespace Console\Command\Agent;

use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class AgentPrecalculationCommand extends \Console\Command\BaseCommand
{
    /**
     *
     * @var string
     */
    protected static $defaultName = 'agent:precalculate';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'Move agent to precalculations command';

    /**
     *
     * @return void
     */
    protected function configure()
    {
        parent::configure();
        $this->addOption('company_id', mode: InputOption::VALUE_REQUIRED);
        $this->addOption('agent_id', mode: InputOption::VALUE_REQUIRED | InputOption::VALUE_IS_ARRAY);
    }

    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $companyId = (int) $input->getOption('company_id');
        $agentIds = array_filter($input->getOption('agent_id'));
        if ($companyId <= 0 && !empty($agentIds)) {
            throw new \InvalidArgumentException('Company id is null or empty');
        }
        $needToPrecalculateAllAgents = empty($agentIds);
        $companyIds = ($companyId > 0) ? [$companyId] : $this->company()->getCompanyIds();
        foreach ($companyIds as $companyId) {
            if ($needToPrecalculateAllAgents) {
                $agentIds = array_column($this->company()->user()->getUsersByRoleType($companyId, [
                    \STCompany\Entity\Role::AGENT_ROLE_TYPE,
                ])->toArray(), 'id');
            }
            $this->company()->agentPrecalculationManager()->addAgentsToPrecalculateQueue($companyId, $agentIds, priority: \STCompany\Service\AgentPrecalculation\AgentPrecalculationManagerService::MANUAL_LAUNCH_PRIORITY);
        }

        return static::SUCCESS;
    }
}
