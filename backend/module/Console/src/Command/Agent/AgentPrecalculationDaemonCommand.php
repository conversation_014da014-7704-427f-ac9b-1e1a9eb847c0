<?php

declare(strict_types=1);

namespace Console\Command\Agent;

use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class AgentPrecalculationDaemonCommand extends \Console\Command\BaseCommand
{
    /**
     *
     * @var string
     */
    protected static $defaultName = 'agent:precalculate:daemon';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'Precalculate agents daemon';

    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $daemon = new \STCompany\Daemon\PrecalculateAgentsDaemon();
        $daemon
            ->setQueueName(\STCompany\Daemon\PrecalculateAgentsDaemon::AGENT_PRECALCULATION_QUEUE)
            ->setErrorQueueName(\STCompany\Daemon\PrecalculateAgentsDaemon::AGENT_PRECALCULATION_QUEUE_ERROR);
        $agentPrecalculationService = $this->getServiceManager()->get(\STCompany\Service\AgentPrecalculation\AgentPrecalculationService::class);
        $daemon->params()->add($agentPrecalculationService, \STCompany\Service\AgentPrecalculation\AgentPrecalculationService::class);
        $this->rabbit()->daemon()->run($daemon);
        return static::FAILURE;
    }
}
