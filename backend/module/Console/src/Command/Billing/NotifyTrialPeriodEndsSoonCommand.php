<?php

declare(strict_types=1);

namespace Console\Command\Billing;

use Console\Command\BaseCommand;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use STCompany\Service\CompanyService;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'billing:notify-trial-period-ends-soon',
    description: 'Notify trial period ends soon',
)]
class NotifyTrialPeriodEndsSoonCommand extends BaseCommand
{
    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        /**
         * @var CompanyService $companyService
         */
        $companyService = $this->getServiceManager()->get(CompanyService::class);
        $companyService->notifyTrialPeriodEndsSoon();

        return self::SUCCESS;
    }
}
