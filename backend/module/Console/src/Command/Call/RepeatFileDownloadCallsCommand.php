<?php

declare(strict_types=1);

namespace Console\Command\Call;

use Console\Command\BaseCommand;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use ReflectionException;
use <PERSON><PERSON><PERSON>\Entity\Exception\NotFoundApiException;
use ST<PERSON>all\Daemon\Analysis\FileDownloadStepDaemon;
use ST<PERSON>all\Entity\Call;
use ST<PERSON>all\Entity\CallCollection;
use STCall\Service\CallAnalysis\FileDownloadStep;
use STCall\Service\CallAnalysisService;
use STCompany\Entity\Client;
use STCompany\Entity\ClientCollection;
use STUser\Entity\User;
use STUser\Entity\UserCollection;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'call:repeat-failed-to-download-calls',
    description: 'Try to repeat download for calls which are stuck',
)]
class RepeatFileDownloadCallsCommand extends BaseCommand
{
    protected const int UPLOADED_LATER_THAN_IN_HOURS = 6;
    protected const int UPLOADED_BEFORE_THAN_IN_HOURS = 1;

    /**
     *
     * @return void
     */
    protected function configure(): void
    {
        parent::configure();
        $this->addOption(
            'uploaded_later_than_in_hours',
            'l',
            InputOption::VALUE_OPTIONAL,
            '',
            static::UPLOADED_LATER_THAN_IN_HOURS
        );
        $this->addOption(
            'uploaded_before_than_in_hours',
            'b',
            InputOption::VALUE_OPTIONAL,
            '',
            static::UPLOADED_BEFORE_THAN_IN_HOURS
        );
        $this->addOption('company_id', 'c', InputOption::VALUE_OPTIONAL, '', null);
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     * @throws ContainerExceptionInterface
     * @throws NotFoundApiException
     * @throws NotFoundExceptionInterface
     * @throws ReflectionException
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $uploadedLaterThanInHours = (int) $input->getOption('uploaded_later_than_in_hours');
        $uploadedBeforeThanInHours = (int) $input->getOption('uploaded_before_than_in_hours');
        $companyId = (int) $input->getOption('company_id');

        $companyIds = ($companyId > 0) ? [$companyId] : $this->company()->getCompanyIds();

        $callsCount = 0;
        foreach ($companyIds as $companyId) {
            $output->writeln('Company id is ' . $companyId);

            $company = $this->company()->getCompany($companyId);

            if (!$company->isPaidTranscribingTimeEnoughForAnalyze()) {
                $output->writeln('Company ' . $companyId . ' has no enough paid balance');
                continue;
            }

            $unprocessedCalls = $this->call()->getFailedToDownloadCalls(
                $companyId,
                $uploadedLaterThanInHours,
                $uploadedBeforeThanInHours
            );

            $clientIds = $this->getClientIds($unprocessedCalls);
            $clients = $this->company()->client()->getClientsByIds($clientIds, $companyId);
            $agentIds = $this->getAgentIds($unprocessedCalls);
            $agents = $this->user()->getUsersById($agentIds);

            $callsCount += $unprocessedCalls->count();
            foreach ($unprocessedCalls as $call) {
                $queue = FileDownloadStep::CALL_FILE_DOWNLOAD_QUEUE;
                $output->writeln('-- Adding call "' . $call->getId() . '" to queue "' . $queue . '"');

                $this->call()->analysis()->addToQueue(
                    $call->getCompanyId(),
                    null,
                    $queue,
                    exchange: FileDownloadStepDaemon::RABBITMQ_EXCHANGE_NAME,
                    options: $this->getOptions($call, $clients, $agents),
                    priority: CallAnalysisService::TOP_PRIORITY
                );
            }
        }

        $output->writeln('Calls count: ' . $callsCount);

        return static::SUCCESS;
    }

    private function getClientIds(CallCollection $calls): array
    {
        $clientIds = [];
        foreach ($calls as $call) {
            $clientIds[] = $call->getClientId();
        }

        return $clientIds;
    }

    private function getAgentIds(CallCollection $calls): array
    {
        $agentIds = [];
        foreach ($calls as $call) {
            $agentIds[] = $call->getAgentId();
        }

        return $agentIds;
    }

    private function getOptions(Call $call, ClientCollection $clients, UserCollection $agents): array
    {
        /** @var Client $client */
        $client = $clients->offsetGet($call->getClientId());

        /** @var User $agent */
        $agent = $agents->offsetGet($call->getAgentId());

        return [
            'params' => [
                'agent_id' => (string) $call->getAgentId(),
                'agent_name' => $agent?->getName(),
                'call_id' => $call->getId(),
                'call_status' => $call->getCallStatus(),
                'client_acquisition_date' => $client?->getAcquisitionDate()?->format('Y-m-d H:i:s'),
                'client_campaign_id' => $client?->getCampaignId(),
                'client_converted_date' => $client?->getConvertedDate()?->format('Y-m-d H:i:s'),
                'client_country' => $client?->getCountry(),
                'client_id' => $call->getClientId(),
                'client_is_converted' => $client?->getIsConverted(),
                'client_last_transaction_date' => $client?->getLastTransactionDate()?->format('Y-m-d H:i:s'),
                'client_name' => $client?->getName(),
                'client_source' => $client?->getSource(),
                'client_status' => $client?->getStatus(),
                'client_value' => $client?->getValue(),
                'original_file_name' => $call->getOriginalFileName(),
                'is_manual' => $call->getIsRunManually(),
                'recording_file' => $call->getRecordingFileUrl(),
                'time' => $call->getTime()->format('Y-m-d H:i:s')
            ],
            'request_id' => uniqid('', true)
        ];
    }
}
