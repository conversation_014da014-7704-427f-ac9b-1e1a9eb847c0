<?php

declare(strict_types=1);

namespace Console\Command\Call;

use ST<PERSON>all\Data\TranscribeDriversLanguagesTable;
use STCall\Entity\Paragraph;
use STCall\Service\CallAnalysis\BaseStep;
use STCall\Service\CallAnalysis\FileDownloadStep;
use STCall\Service\CallAnalysis\LanguageDetectionStep;
use STCall\Service\CallAnalysis\TranscribingDistributionStep;
use STCall\Service\CallService;
use STCall\Service\Import\UploadService;
use STCall\Service\Precalculation\CallPrecalculationService;
use STCompany\Entity\Company;
use STCompany\Service\CompanyService;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class TestAnalysisCommand extends \Console\Command\BaseCommand
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    /**
     *
     * @var string
     */
    protected static $defaultName = 'call:test-analysis';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'Test analysis steps';

    /**
     *
     * @return void
     */
    protected function configure()
    {
        parent::configure();

        $this->addOption(
            'driver',
            'd',
            \Symfony\Component\Console\Input\InputOption::VALUE_OPTIONAL,
            '',
            TranscribingDistributionStep::TRANSCRIBING_DEEPGRAM_NOVA_2_DRIVER
        );
        $this->addOption(
            'company_id',
            'c',
            \Symfony\Component\Console\Input\InputOption::VALUE_OPTIONAL,
            '',
            18
        );
        $this->addOption(
            'file',
            'f',
            \Symfony\Component\Console\Input\InputOption::VALUE_OPTIONAL,
            '',
            'https://www.eslfast.com/easydialogs/audio/dailylife001.mp3'
        );
        $this->addOption(
            'type',
            't',
            \Symfony\Component\Console\Input\InputOption::VALUE_OPTIONAL,
            '',
            'call'
        );
    }

    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        /** @var CompanyService $companyService */
        $companyService = $this->company();
        $companyId = (int) $input->getOption('company_id');
        $company = $companyService->getCompany($companyId);
        $steps = $this->getStepsServices();

        $this->switchTranscribingDriver($input->getOption('driver'));

        $step = $this->launchFirstStep($input, $company);
        $callId = $step->getCall()->getId();

        $output->writeln($step->getQueueName() . ' has been finished: Call ID: ' . $callId);

        while (true) {
            $nextStepQueue = $step->getNextStepQueue();
            $stepOptions = $step->getNextStepOptions();
            $step = $steps[$nextStepQueue];

            try {
                $step
                    ->applyOptions($stepOptions)
                    ->launch($callId, $companyId);
            } catch (\STCall\Service\CallAnalysis\Transcribing\TwoSteps\NotReadyException $e) {
                $output->writeln('Waiting when transcribing is ready...');
                sleep(60);

                $step
                    ->applyOptions($stepOptions)
                    ->launch($callId, $companyId);
            }

            $output->writeln($nextStepQueue . ' has been finished.');

            if (!$step->hasNextStep()) {
                break;
            }
        }

        $this->checkUploadedCall($company, $callId, $output);
        $this->deleteCall($company, $callId);

        return static::SUCCESS;
    }

    /**
     * @param InputInterface $input
     * @param Company $company
     * @return BaseStep
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    private function launchFirstStep(InputInterface $input, Company $company): BaseStep
    {
        if ($input->getOption('type') === 'chat_call') {
            $call = $this->getServiceManager()->get(UploadService::class)->uploadCall(
                $this->hydrate(
                    [
                        'driver_name' => 'chat-api-upload',
                        'company' => $company,
                        'options' => $this->getUploadingParams($input),
                    ],
                    \STCall\Service\Import\UploadParams\UploadParams::class
                )
            );
            /** @var BaseStep $step */
            $step = $this->getServiceManager()->get(LanguageDetectionStep::class);
            $step->applyOptions([
                'request_id' => uniqid('', true),
            ])->launch($call->getId(), $company->getId());
        } else {
            /** @var BaseStep $step */
            $step = $this->getServiceManager()->get(FileDownloadStep::class);
            $step
                ->applyOptions([
                    'params' => $this->getUploadingParams($input),
                    'request_id' => uniqid('', true),
                ])
                ->launch('', $company->getId())
            ;
        }

        return $step;
    }

    /**
     * @return array
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    private function getStepsServices(): array
    {
        $steps = [];

        foreach (scandir(getcwd() . '/module/STCall/src/Service/CallAnalysis/') as $stepClass) {
            $namespace = 'STCall\\Service\\CallAnalysis\\' . str_replace('.php', '', $stepClass);

            if (!preg_match('/(Base|Factory)/i', $namespace) && $this->getServiceManager()->has($namespace)) {
                /** @var BaseStep $stepService */
                $stepService = $this->getServiceManager()->get($namespace);
                $steps[$stepService->getQueueName()] = $stepService;
            }
        }

        return $steps;
    }

    /**
     * @param Company $company
     * @param string $callId
     * @return void
     */
    private function deleteCall(Company $company, string $callId): void
    {
        $call = $this->call()->getBasicCalls($company->getId(), [$callId])->current();
        $call->setIsDeleted(true);
        $this->call()->saveCall($call);
    }

    /**
     * @param string $driver
     * @return void
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    private function switchTranscribingDriver(string $driver): void
    {
        /** @var TranscribeDriversLanguagesTable $table */
        $table = $this->getServiceManager()->get(TranscribeDriversLanguagesTable::class);
        $table->saveLanguageDriver('en', $driver);
    }

    /**
     * @param Company $company
     * @param string $callId
     * @param OutputInterface $output
     * @return void
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    private function checkUploadedCall(Company $company, string $callId, OutputInterface $output): void
    {
        /** @var CallService $callService */
        $callService = $this->call();
        /** @var CallPrecalculationService $callPrecalculationService */
        $callPrecalculationService = $this->getServiceManager()->get(CallPrecalculationService::class);

        $paragraphs = $callService->getParagraphs($company, $callId);

        if ($callPrecalculationService->getPrecalculatedCallsCount($callId, $company->getId()) > 0) {
            $output->writeln('Call was precalculated successfully.');
        }

        $output->writeln(
            sprintf('Call has %s algo events', $callService->getCallAlgoEventsCount($company->getId(), $callId))
        );
        $output->writeln(
            sprintf('Call has %s paragraphs', $paragraphs->count())
        );
        $output->writeln(
            sprintf(
                'Call has %s translated paragraphs',
                count($paragraphs->filter(fn(Paragraph $paragraph) => $paragraph->getEnText() !== null))
            )
        );
        $output->writeln(
            sprintf(
                'Call has %s precalculated events',
                $callPrecalculationService->getPrecalalculatedEventsCount($callId, $company->getId())
            )
        );
    }

    /**
     * @param InputInterface $input
     * @return array
     */
    private function getUploadingParams(InputInterface $input): array
    {
        $callId = uniqid('', true);
        $params = [
            'time' => '2022-12-09 12:10:10',
            'agent_id' => uniqid('', true),
            'client_id' => uniqid('', true),
        ];

        return array_merge(
            $params,
            match ($input->getOption('type')) {
                'chat_call' => [
                    'chat_call' => $this->getChatCallData(),
                    'chat_call_id' => $callId,
                ],
                'call' => [
                    'recording_file' => $input->getOption('file'),
                    'call_id' => $callId,
                ]
            }
        );
    }

    /**
     * @return array[]
     */
    private function getChatCallData(): array
    {
        return [
            [
                'is_agent' => false,
                'is_client' => true,
                'time' => '2022-12-09 12:10:10',
                'text' => 'hi'
            ],
            [
                'is_agent' => true,
                'is_client' => false,
                'time' => '2022-12-09 12:10:10',
                'text' => 'Hi, Tim. Yeah. How are you, Tim? Yesterday , you cursed me. You hang up on me, everything, but I\'m not mad.'
            ],
            [
                'is_agent' => false,
                'is_client' => true,
                'time' => '2022-12-09 12:10:10',
                'text' => 'I\'m calling you again because I want you to understand that you didn\'t understand correctly whatever you think it is. That\'s why I\'m calling you again. I don\'t mind you cursing.'
            ],
            [
                'is_agent' => true,
                'is_client' => false,
                'time' => '2022-12-09 12:10:10',
                'text' => 'Yeah. Why in the world are you doing this?'
            ],
            [
                'is_agent' => false,
                'is_client' => true,
                'time' => '2022-12-09 12:10:10',
                'text' => 'Because I want to ask people too. See, if I were a scam to your uncle, you told me if I were really a scam. When you talked to me in the first time in the beginning and you told me you want to have more money here, and you talked to me about the risk, and I told you everything on your face. I told you you have a risk . I asked you how you will react if you can lose some money. Understand. Do you don\'t think I already will take money from your card because I know it\'s the fact you agreed to deposit more funds for your account or have a better investment, that\'s something else. But if I worry, ma\'am, first of all, you got to me. You registered. I didn\'t call you from a basement.'
            ],
            [
                'is_agent' => true,
                'is_client' => false,
                'time' => '2022-12-09 12:10:10',
                'text' => 'Can I can I, tell you something? Yes. After dealing with you after dealing with you with sorry. You\'re not listening.'
            ],
            [
                'is_agent' => false,
                'is_client' => true,
                'time' => '2022-12-09 12:10:10',
                'text' => 'after Inappropriate Behaviorfucking listening, you stupid cow. Jesus. Wait. After meeting with you people for so many you you wore yourself out so many hours, hours and hours and hours trying to nail me. And, I went for it, and then my Complaintbank is on my case. There \'s eleven hundred quid There\'s an attempt of eleven and hundred eleven hundred quid being taken out of my account. And you\'re hiding behind Atomic, a boner which appears to be a boner Friday firm. And, I\'m a boner Friday bass player, and you\'re a fucking bonafide quack. Alright. You can call me all you like, but, you\'re wasting your time.'
            ],
            [
                'is_agent' => true,
                'is_client' => false,
                'time' => '2022-12-09 12:10:10',
                'text' => 'You think you think I get excited from your curse? No. I\'m not. First of all, you can curse until tomorrow. You didn\'t took nothing from your account because'
            ]
        ];
    }
}
