<?php

declare(strict_types=1);

namespace Console\Command\Call;

use Symfony\Component\Console\Input\InputOption;

class SendCallsStatusesToClientApi extends \Console\Command\BaseCommand
{
    /**
     *
     * @var string
     */
    protected static $defaultName = 'call:send-statuses-to-client-api';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'Command gets calls from DB for a specific date and company and send their statuses to client api';

    /**
     *
     * @return void
     */
    protected function configure(): void
    {
        parent::configure();

        $this->addOption('company_id', 'c', InputOption::VALUE_OPTIONAL, '', null);
        $this->addOption('start_time', 'st', InputOption::VALUE_OPTIONAL, '', null);
        $this->addOption('end_time', 'et', InputOption::VALUE_OPTIONAL, '', null);
    }

    /**
     *
     * @param \Symfony\Component\Console\Input\InputInterface $input
     * @param \Symfony\Component\Console\Output\OutputInterface $output
     * @return int
     * @throws \JsonException
     */
    protected function launch(\Symfony\Component\Console\Input\InputInterface $input, \Symfony\Component\Console\Output\OutputInterface $output): int
    {
        $startTime = $input->getOption('start_time');
        $endTime = $input->getOption('end_time');
        $companyId = (int) $input->getOption('company_id');
        $companyIds = $companyId > 0 ? [$companyId] : $this->company()->getCompanyIds();

        foreach ($companyIds as $companyId) {
            $output->writeln('Company id: ' . $companyId);
            $callIds = $this->call()->getCallIds(
                $companyId,
                $startTime !== null ? \Carbon\Carbon::parse($startTime) : null,
                $endTime !== null ? \Carbon\Carbon::parse($endTime) : null,
            );

            foreach ($callIds as $callId) {
                $this->call()->upload()->informClientApiAboutUploadedCall((int) $companyId, $callId);
            }
        }

        return static::SUCCESS;
    }
}
