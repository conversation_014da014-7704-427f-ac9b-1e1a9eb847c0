<?php

declare(strict_types=1);

namespace Console\Command\Call\Export;

use Console\Command\BaseCommand;
use STCall\Service\Export\RunCallsExportsService;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'call:export',
    description: 'Run calls exports generation',
)]
final class CallExportCommand extends BaseCommand
{
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        /**
         * @var RunCallsExportsService $runCallsExportsService
         */
        $runCallsExportsService = $this->getServiceManager()->get(RunCallsExportsService::class);
        $runCallsExportsService->run();

        return self::SUCCESS;
    }
}
