<?php

declare(strict_types=1);

namespace Console\Command\Call\Export;

use Console\Command\BaseCommand;
use ST<PERSON>all\Daemon\Export\CallExportDaemon;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'call:export:daemon',
    description: 'Run calls exports generation daemon',
)]
final class CallExportDaemonCommand extends BaseCommand
{
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        /**
         * @var CallExportDaemon $daemon
         */
        $daemon = $this->getServiceManager()->get(CallExportDaemon::class);
        $daemon
            ->setQueueName(CallExportDaemon::CALLS_EXPORTS_QUEUE_NAME)
            ->setErrorQueueName(CallExportDaemon::CALLS_EXPORTS_QUEUE_ERROR_NAME);

        $this->rabbit()->daemon()->run($daemon);

        return self::FAILURE;
    }
}
