<?php

namespace Console\Command\Call;

use Console\Command\BaseCommand;
use <PERSON><PERSON>\Db\Adapter\Adapter;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use STCall\Service\CallAnalysis\FileDownloadStep;
use STCall\Service\CallAnalysis\StepWithWaitingRoomInterface;
use STCall\Service\CallAnalysisService;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use STCall\Daemon\Analysis\FileDownloadStepWaitingRoomDaemon;

#[AsCommand(
    name: 'call:download-waiting-room:daemon',
    description: 'Watch waiting room and move to normal queue',
)]
class WaitingRoomCommand extends BaseCommand
{
    /**
     *
     * @return void
     */
    protected function configure()
    {
        parent::configure();
        $this->addOption('step', null, InputOption::VALUE_REQUIRED, 'step');
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $stepName = $input->getOption('step');
        /** @var FileDownloadStep $step */
        $step = $this->call()->analysisStep($stepName);

        $daemon = $this->getDaemon();
        $daemon
            ->setQueueName($step->getWaitingQueueName())
            ->setErrorQueueName($step->getWaitingErrorQueueName());

        $callRateLimits = [];
        if ($step instanceof StepWithWaitingRoomInterface) {
            $daemon->setWaitingQueueName($step->getWaitingQueueName());
            $daemon->setWaitingErrorQueueName($step->getWaitingErrorQueueName());
            $cacheStorage = $this->getServiceManager()->get('RateLimiter.CacheStorage');
            $daemon->params()->add($cacheStorage, 'RateLimiter.CacheStorage');
            $callRateLimits = $this->company()->getCallDownloadRateLimiterSettings();
            $daemon->setRateLimits($callRateLimits);
        }

        $daemon->params()->add($step, 'step');
        $daemon->params()->add($this->call()->analysis(), CallAnalysisService::class);
        $daemon->params()->add($this->getServiceManager()->get(Adapter::class), Adapter::class);

//        try {
            $this->rabbit()->daemon()->run($daemon);
//        } catch (\Exception $e) {
            // gracefully handle errors
//        }

//        return static::SUCCESS;
        return static::FAILURE;
    }


    /**
     * @return FileDownloadStepWaitingRoomDaemon
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    protected function getDaemon(): FileDownloadStepWaitingRoomDaemon
    {
        return $this->getServiceManager()->get(FileDownloadStepWaitingRoomDaemon::class);
    }
}
