<?php

declare(strict_types=1);

namespace Console\Command\Call\Webhooks;

use Console\Command\BaseCommand;
use <PERSON><PERSON><PERSON>\Daemon\Webhooks\WebhooksDaemon;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'webhooks:daemon',
    description: 'Run webhooks daemon',
)]
final class WebhooksDaemonCommand extends BaseCommand
{
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        /**
         * @var WebhooksDaemon $daemon
         */
        $daemon = $this->getServiceManager()->get(WebhooksDaemon::class);
        $daemon
            ->setQueueName(WebhooksDaemon::WEBHOOKS_QUEUE_NAME)
            ->setErrorQueueName(WebhooksDaemon::WEBHOOKS_QUEUE_ERROR_NAME);

        $this->rabbit()->daemon()->run($daemon);

        return self::FAILURE;
    }
}
