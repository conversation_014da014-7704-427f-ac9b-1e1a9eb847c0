<?php

declare(strict_types=1);

namespace Console\Command\Call;

use Console\Command\BaseCommand;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use STCall\Daemon\ClientApi\ClientApiRequestDaemon;
use STCall\Service\CallAnalysisService;
use STCall\Service\Import\UploadService;
use STCompany\Service\CompanyService;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class ClientApiRequest extends BaseCommand
{
    /**
     *
     * @var string
     */
    protected static $defaultName = 'call:client-api-request:daemon';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'Daemon for processings requests from client api';

    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $daemon = $this->getServiceManager()->get(ClientApiRequestDaemon::class);
        $daemon
                ->setQueueName(ClientApiRequestDaemon::QUEUE_NAME)
                ->setErrorQueueName(ClientApiRequestDaemon::ERROR_QUEUE_NAME);

        $uploadService = $this->getServiceManager()->get(UploadService::class);
        $companyService = $this->getServiceManager()->get(CompanyService::class);
        $callAnalysisService = $this->getServiceManager()->get(CallAnalysisService::class);

        $daemon->params()->add($uploadService, UploadService::class);
        $daemon->params()->add($companyService, CompanyService::class);
        $daemon->params()->add($callAnalysisService, CallAnalysisService::class);

        $this->rabbit()->daemon()->run($daemon);

        return static::FAILURE;
    }
}
