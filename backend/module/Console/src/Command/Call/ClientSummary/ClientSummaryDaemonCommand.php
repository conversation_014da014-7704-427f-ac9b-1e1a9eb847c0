<?php

declare(strict_types=1);

namespace Console\Command\Call\ClientSummary;

use Console\Command\BaseCommand;
use ST<PERSON>all\Daemon\ClientSummaryDaemon;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'call:client-summary:daemon',
    description: 'Run client summary generation daemon',
)]
final class ClientSummaryDaemonCommand extends BaseCommand
{
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        /**
         * @var ClientSummaryDaemon $daemon
         */
        $daemon = $this->getServiceManager()->get(ClientSummaryDaemon::class);
        $daemon
            ->setQueueName(ClientSummaryDaemon::QUEUE)
            ->setErrorQueueName(ClientSummaryDaemon::QUEUE_ERROR);

        $this->rabbit()->daemon()->run($daemon);

        return self::FAILURE;
    }
}
