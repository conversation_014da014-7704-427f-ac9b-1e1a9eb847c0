<?php

declare(strict_types=1);

namespace Console\Command\Call;

use Console\Command\BaseCommand;
use <PERSON><PERSON>\Db\Adapter\Adapter;
use <PERSON><PERSON>\Filter\Word\DashToCamelCase;
use LogicException;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use STCall\Daemon\Analysis\BaseAnalysisStepDaemon;
use STCall\Daemon\Analysis\FileDownloadStepDaemon;
use STCall\Service\CallAnalysis\BaseStep;
use STCall\Service\CallAnalysis\StepWithWaitingRoomInterface;
use STCall\Service\CallAnalysisService;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'call:analysis:daemon',
    description: 'Collect analysis step',
)]
class AnalysisStepCommand extends BaseCommand
{
    /**
     *
     * @return void
     */
    protected function configure()
    {
        parent::configure();
        $this->addOption('step', 's', InputOption::VALUE_REQUIRED, 'step');
    }

    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $stepName = $input->getOption('step');
        /** @var BaseStep $step */
        $step = $this->call()->analysisStep($stepName);

        $daemon = $this->getDaemon($stepName);
        $daemon
            ->setQueueName($step->getQueueName())
            ->setErrorQueueName($step->getErrorQueueName());

        $callRateLimits = [];
        if ($step instanceof StepWithWaitingRoomInterface) {
            $daemon->setWaitingQueueName($step->getWaitingQueueName());
            $daemon->setWaitingErrorQueueName($step->getWaitingErrorQueueName());
            $cacheStorage = $this->getServiceManager()->get('RateLimiter.CacheStorage');
            $daemon->params()->add($cacheStorage, 'RateLimiter.CacheStorage');
            $callRateLimits = $this->company()->getCallDownloadRateLimiterSettings();
            $daemon->setRateLimits($callRateLimits);
        }

        $daemon->params()->add($step, 'step');
        $daemon->params()->add($this->call()->analysis(), CallAnalysisService::class);
        $daemon->params()->add($this->getServiceManager()->get(Adapter::class), Adapter::class);

//        try {
            $this->rabbit()->daemon()->run($daemon);
//        } catch (\Exception $e) {
//            // gracefully handle errors
//        }

//        return static::SUCCESS;
        return static::FAILURE;
    }

    /**
     * @param string $step
     * @return BaseAnalysisStepDaemon
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    protected function getDaemon(string $step): BaseAnalysisStepDaemon
    {
        // @todo replace it with switch [mmm]
        $dashToCamelCase = new DashToCamelCase();
        $namespace = '\\STCall\\Daemon\\Analysis\\' . $dashToCamelCase->filter($step) . 'StepDaemon';
        if (!class_exists($namespace)) {
            throw new LogicException('Can\'t find daemon for "' . $namespace . '"');
        }
        return $this->getServiceManager()->get($namespace);
    }
}
