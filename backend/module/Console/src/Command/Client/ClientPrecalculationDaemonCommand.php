<?php

declare(strict_types=1);

namespace Console\Command\Client;

use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class ClientPrecalculationDaemonCommand extends \Console\Command\BaseCommand
{
    /**
     *
     * @var string
     */
    protected static $defaultName = 'client:precalculate:daemon';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'Precalculate clients daemon';

    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $daemon = new \STCompany\Daemon\PrecalculateClientsDaemon();
        $daemon
            ->setQueueName(\STCompany\Daemon\PrecalculateClientsDaemon::CLIENT_PRECALCULATION_QUEUE)
            ->setErrorQueueName(\STCompany\Daemon\PrecalculateClientsDaemon::CLIENT_PRECALCULATION_QUEUE_ERROR);
        $clientPrecalculationService = $this->getServiceManager()->get(\STCompany\Service\ClientPrecalculation\ClientPrecalculationService::class);
        $daemon->params()->add($clientPrecalculationService, \STCompany\Service\ClientPrecalculation\ClientPrecalculationService::class);
        $this->rabbit()->daemon()->run($daemon);
        return static::FAILURE;
    }
}
