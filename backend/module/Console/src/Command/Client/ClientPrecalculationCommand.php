<?php

declare(strict_types=1);

namespace Console\Command\Client;

use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class ClientPrecalculationCommand extends \Console\Command\BaseCommand
{
    /**
     *
     * @var string
     */
    protected static $defaultName = 'client:precalculate';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'Move client to precalculations command';

    /**
     *
     * @return void
     */
    protected function configure()
    {
        parent::configure();
        $this->addOption('company_id', mode: InputOption::VALUE_REQUIRED);
        $this->addOption('client_id', mode: InputOption::VALUE_REQUIRED | InputOption::VALUE_IS_ARRAY);
    }

    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $companyId = (int) $input->getOption('company_id');
        $clientIds = array_filter($input->getOption('client_id'));
        if ($companyId <= 0) {
            throw new \InvalidArgumentException('Company id is required param');
        }
        if (empty($clientIds)) {
            throw new \InvalidArgumentException('Client id is required param');
        }

        $this->company()->clientPrecalculationManager()->addClientsToPrecalculateQueue($companyId, $clientIds, priority: \STCompany\Service\ClientPrecalculation\ClientPrecalculationManagerService::MANUAL_LAUNCH_PRIORITY);
        return static::SUCCESS;
    }
}
