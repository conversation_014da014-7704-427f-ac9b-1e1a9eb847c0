<?php

declare(strict_types=1);

namespace Console\Command\Precalculation;

use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class PrecalculateDaemonCommand extends \Console\Command\BaseCommand
{
    /**
     *
     * @var string
     */
    protected static $defaultName = 'call:precalculate:daemon';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'Daemon for calls preacalculations';

    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $daemon = new \STCall\Daemon\Precalculation\PrecalculateCallsDaemon();
        $daemon
                ->setQueueName(\STCall\Daemon\Precalculation\PrecalculateCallsDaemon::PRECALCULATION_QUEUE)
                ->setErrorQueueName(\STCall\Daemon\Precalculation\PrecalculateCallsDaemon::PRECALCULATION_QUEUE_ERROR);

        $callPrecalculationService = $this->getServiceManager()->get(\STCall\Service\Precalculation\CallPrecalculationService::class);
        $callsService = $this->getServiceManager()->get(\STCall\Service\CallService::class);

        $daemon->params()->add($callPrecalculationService, \STCall\Service\Precalculation\CallPrecalculationService::class);
        $daemon->params()->add($callsService, \STCall\Service\CallService::class);

        $this->rabbit()->daemon()->run($daemon);
        return static::FAILURE;
    }
}
