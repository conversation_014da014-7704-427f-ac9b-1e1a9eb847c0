<?php

declare(strict_types=1);

namespace Console\Command\Precalculation;

use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class BulkPrecalculateCommand extends \Console\Command\BaseCommand
{
    /**
     *
     * @var string
     */
    protected static $defaultName = 'call:precalculate:bulk';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'Adding call to precalculation';

    /**
     *
     * @return void
     */
    protected function configure()
    {
        parent::configure();
        $this->addOption('uploaded_before_than_in_days', 'b', \Symfony\Component\Console\Input\InputOption::VALUE_OPTIONAL, '', 0);
        $this->addOption('uploaded_later_than_in_days', 'l', \Symfony\Component\Console\Input\InputOption::VALUE_OPTIONAL, '', 1);
        $this->addOption('company_id', 'c', \Symfony\Component\Console\Input\InputOption::VALUE_OPTIONAL, '');
        $this->addOption('delete', 'd', \Symfony\Component\Console\Input\InputOption::VALUE_OPTIONAL, '', false);
    }

    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $uploadedLaterThanInDays = (int) $input->getOption('uploaded_later_than_in_days');
        $uploadedBeforeThanInDays = (int) $input->getOption('uploaded_before_than_in_days');
        $companyId = (int) $input->getOption('company_id');

        $startDate = \Carbon\Carbon::now()->subDays($uploadedLaterThanInDays);
        $endDate = \Carbon\Carbon::now()->subDays($uploadedBeforeThanInDays);

        $companyIds = $companyId > 0 ? [$companyId] : $this->company()->getCompanyIds();
        foreach ($companyIds as $companyId) {
            $callIds = $this->call()->getCallIds($companyId, $startDate, $endDate);
            $this->call()->precalculationManager()->addCallsToPrecalculateQueue(
                $companyId,
                $callIds,
                priority: \STCall\Service\Precalculation\CallPrecalculationManagerService::PRECALCULATE_BULK_PRIORITY,
            );
        }

        return static::SUCCESS;
    }
}
