<?php

declare(strict_types=1);

namespace Console\Command\Precalculation;

use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class PrecalculateChangedCommand extends \Console\Command\BaseCommand
{
    /**
     *
     * @var string
     */
    protected static $defaultName = 'call:precalculate:changed';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'Precalculate changed calls';

    /**
     *
     * @return void
     */
    protected function configure()
    {
        parent::configure();
        $this->addOption('company_id', null, \Symfony\Component\Console\Input\InputOption::VALUE_OPTIONAL, '');
        $this->addOption('changed_after_in_minutes', null, \Symfony\Component\Console\Input\InputOption::VALUE_OPTIONAL, '', 5);
        $this->addOption('calls_uploaded_after_in_days', null, \Symfony\Component\Console\Input\InputOption::VALUE_OPTIONAL, '', 14);
        $this->addOption('delete', null, \Symfony\Component\Console\Input\InputOption::VALUE_OPTIONAL, '', false);
    }

    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $companyId = (int) $input->getOption('company_id');
        $changedAfterInMinutes = (int) $input->getOption('changed_after_in_minutes');
        $callsUploadedAfterInDays = (int) $input->getOption('calls_uploaded_after_in_days');

        $companyIds = $companyId > 0 ? [$companyId] : $this->company()->getCompanyIds();
        $changedAfter = time() - $changedAfterInMinutes * 60;

        foreach ($companyIds as $companyId) {
            // precalculate roles
            $markToPrecalculationsRoleIds = $this->call()->precalculationManager()->getRoleIdsMarkedToPrecalculations($companyId, $changedAfter);
            $callIds = $this->call()->getCallIds($companyId, \Carbon\Carbon::now()->subDays($callsUploadedAfterInDays));
            if (count($markToPrecalculationsRoleIds) > 0) {
                $this->call()->precalculationManager()->addCallsToPrecalculateQueue(
                    $companyId,
                    $callIds,
                    priority: \STCall\Service\Precalculation\CallPrecalculationManagerService::PRECALCULATE_AFTER_EVENT_CHANGE_EVENT_PRIORITY,
                    roleId: $markToPrecalculationsRoleIds,
                );
            }

            // precalculate calls
            $roleIds = $this->company()->role()->getRoleIds($companyId);
            foreach ($roleIds as $roleId) {
                $markToPrecalculationsCallIds = $this->call()->precalculationManager()->getCallIdsMarkedToPrecalculations($companyId, $roleId, $changedAfter);
                $this->call()->precalculationManager()->addCallsToPrecalculateQueue(
                    $companyId,
                    $markToPrecalculationsCallIds,
                    priority: \STCall\Service\Precalculation\CallPrecalculationManagerService::PRECALCULATE_AFTER_EVENT_HAPPENING_CHANGE_CALL_PRIORITY,
                    roleId: $roleId,
                );
            }
        }

        return static::SUCCESS;
    }
}
