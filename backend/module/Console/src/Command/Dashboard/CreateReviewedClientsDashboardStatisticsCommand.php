<?php

declare(strict_types=1);

namespace Console\Command\Dashboard;

use Carbon\CarbonInterface;

class CreateReviewedClientsDashboardStatisticsCommand extends AbstractCreateDashboardStatisticsCommand
{
    /**
     *
     * @var string
     */
    protected static $defaultName = 'dashboard:statistics-create:reviewed-clients';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'Creates reviewed clients dashboard statistics';

    /**
     *
     * @param int $companyId
     * @param CarbonInterface $startDate
     * @param CarbonInterface $endDate
     * @return void
     */
    protected function runUpdate(int $companyId, CarbonInterface $startDate, CarbonInterface $endDate): void
    {
        $this->dashboard()->updateDashboardStatisticsService()->updateReviewedClientsStatistics(
            $companyId,
            $startDate,
            $endDate,
        );
    }

    /**
     *
     * @return void
     */
    protected function runTruncate(): void
    {
        $this->dashboard()->updateDashboardStatisticsService()->truncateReviewedClientsStatistics();
    }

    /**
     *
     * @return void
     */
    protected function runSwap(): void
    {
        $this->dashboard()->updateDashboardStatisticsService()->swapReviewedClientsStatistics();
    }
}
