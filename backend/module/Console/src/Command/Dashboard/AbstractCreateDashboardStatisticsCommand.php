<?php

declare(strict_types=1);

namespace Console\Command\Dashboard;

use Carbon\CarbonInterface;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Lock\LockFactory;
use Symfony\Component\Lock\Store\FlockStore;

abstract class AbstractCreateDashboardStatisticsCommand extends \Console\Command\BaseCommand
{
    protected const MONTH_COUNT_PER_REQUEST = 1;

    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $companyIds = $this->company()->getCompanyIds();

        $store = new FlockStore();
        $factory = new LockFactory($store);
        $lock = $factory->createLock(strtolower(get_class($this)));

        if (!$lock->acquire()) {
            $output->writeln('Script is already running');
            return static::FAILURE;
        }

        $this->runTruncate();

        foreach ($companyIds as $companyId) {
            $output->writeln('Calculations for company id: ' . $companyId);

            $dateRange = $this->call()->getCallsDateRange($companyId);

            if ($dateRange['first_time'] === null) {
                continue;
            }

            $period = \Carbon\CarbonPeriod::create(
                $dateRange['first_time'],
                $dateRange['last_time'],
                'P' . static::MONTH_COUNT_PER_REQUEST . 'M'
            );

            foreach ($period as $startDate) {
                $endDate = (clone $startDate)->addMonths(static::MONTH_COUNT_PER_REQUEST)->subDay();
                $output->writeln('Period: ' . $startDate->format('Y-m-d') . ' - ' . $endDate->format('Y-m-d'));
                $this->runUpdate(
                    (int) $companyId,
                    $startDate->setTime(0, 0),
                    $endDate->setTime(23, 59, 59)
                );
            }
        }

        $this->runSwap();

        $lock->release();

        return static::SUCCESS;
    }

    /**
     *
     * @param int $companyId
     * @return void
     */
    abstract protected function runUpdate(int $companyId, CarbonInterface $startDate, CarbonInterface $endDate): void;

    /**
     *
     * @return void
     */
    abstract protected function runTruncate(): void;

    /**
     *
     * @return void
     */
    abstract protected function runSwap(): void;
}
