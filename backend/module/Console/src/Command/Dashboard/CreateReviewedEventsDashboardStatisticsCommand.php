<?php

declare(strict_types=1);

namespace Console\Command\Dashboard;

use Carbon\CarbonInterface;

class CreateReviewedEventsDashboardStatisticsCommand extends AbstractCreateDashboardStatisticsCommand
{
    /**
     *
     * @var string
     */
    protected static $defaultName = 'dashboard:statistics-create:reviewed-events';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'Creates events dashboard statistics';

    /**
     *
     * @param int $companyId
     * @param CarbonInterface $startDate
     * @param CarbonInterface $endDate
     * @return void
     */
    protected function runUpdate(int $companyId, CarbonInterface $startDate, CarbonInterface $endDate): void
    {
        $this->dashboard()->updateDashboardStatisticsService()->updateEventsStatistics(
            $companyId,
            $startDate,
            $endDate,
        );
    }

    /**
     *
     * @return void
     */
    protected function runTruncate(): void
    {
        $this->dashboard()->updateDashboardStatisticsService()->truncateEventsStatistics();
    }

    /**
     *
     * @return void
     */
    protected function runSwap(): void
    {
        $this->dashboard()->updateDashboardStatisticsService()->swapEventsStatistics();
    }
}
