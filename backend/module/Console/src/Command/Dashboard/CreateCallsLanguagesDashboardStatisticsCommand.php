<?php

declare(strict_types=1);

namespace Console\Command\Dashboard;

use Carbon\CarbonInterface;

class CreateCallsLanguagesDashboardStatisticsCommand extends AbstractCreateDashboardStatisticsCommand
{
    /**
     *
     * @var string
     */
    protected static $defaultName = 'dashboard:statistics-create:calls-languages';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'Creates calls languages dashboard statistics';

    /**
     *
     * @param int $companyId
     * @param CarbonInterface $startDate
     * @param CarbonInterface $endDate
     * @return void
     */
    protected function runUpdate(int $companyId, CarbonInterface $startDate, CarbonInterface $endDate): void
    {
        $this->dashboard()->updateDashboardStatisticsService()->updateCallsLanguagesStatistics(
            $companyId,
            $startDate,
            $endDate,
        );
    }

    /**
     *
     * @return void
     */
    protected function runTruncate(): void
    {
        $this->dashboard()->updateDashboardStatisticsService()->truncateCallsLanguagesStatistics();
    }

    /**
     *
     * @return void
     */
    protected function runSwap(): void
    {
        $this->dashboard()->updateDashboardStatisticsService()->swapCallsLanguagesStatistics();
    }
}
