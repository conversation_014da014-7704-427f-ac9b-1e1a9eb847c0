<?php

declare(strict_types=1);

namespace Console\Command\Dashboard;

use Carbon\CarbonInterface;

class CreateManagersKPIDashboardStatisticsCommand extends AbstractCreateDashboardStatisticsCommand
{
    /**
     *
     * @var string
     */
    protected static $defaultName = 'dashboard:statistics-create:managers-kpi';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'Creates managers kpi dashboard statistics';

    /**
     *
     * @param int $companyId
     * @param CarbonInterface $startDate
     * @param CarbonInterface $endDate
     * @return void
     */
    protected function runUpdate(int $companyId, CarbonInterface $startDate, CarbonInterface $endDate): void
    {
        $this->dashboard()->updateDashboardStatisticsService()->updateManagersKPIStatistics(
            $companyId,
            $startDate,
            $endDate,
        );
    }

    /**
     *
     * @return void
     */
    protected function runTruncate(): void
    {
        $this->dashboard()->updateDashboardStatisticsService()->truncateManagersKPIStatistics();
    }

    /**
     *
     * @return void
     */
    protected function runSwap(): void
    {
        $this->dashboard()->updateDashboardStatisticsService()->swapManagersKPIStatistics();
    }
}
