<?php

declare(strict_types=1);

namespace Console\Command\Dashboard;

use Carbon\CarbonInterface;

class CreateEventsCategoriesDashboardStatisticsCommand extends AbstractCreateDashboardStatisticsCommand
{
    /**
     *
     * @var string
     */
    protected static $defaultName = 'dashboard:statistics-create:events-categories';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'Creates events categories dashboard statistics';

    /**
     *
     * @param int $companyId
     * @param CarbonInterface $startDate
     * @param CarbonInterface $endDate
     * @return void
     */
    protected function runUpdate(int $companyId, CarbonInterface $startDate, CarbonInterface $endDate): void
    {
        $this->dashboard()->updateDashboardStatisticsService()->updateEventsCategoriesStatistics(
            $companyId,
            $startDate,
            $endDate,
        );
    }

    /**
     *
     * @return void
     */
    protected function runTruncate(): void
    {
        $this->dashboard()->updateDashboardStatisticsService()->truncateEventsCategoriesStatistics();
    }

    /**
     *
     * @return void
     */
    protected function runSwap(): void
    {
        $this->dashboard()->updateDashboardStatisticsService()->swapEventsCategoriesStatistics();
    }
}
