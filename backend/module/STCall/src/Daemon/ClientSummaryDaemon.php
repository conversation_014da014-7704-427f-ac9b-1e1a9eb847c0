<?php

declare(strict_types=1);

namespace STCall\Daemon;

use Carbon\Carbon;
use STAlgo\Service\AiSolutionsCommutatorService;
use STApi\Entity\Exception\ThirdPartyApiException;
use ST<PERSON>all\Entity\CallCollection;
use ST<PERSON>all\Entity\ClientSummary;
use STCall\Service\CallService;
use STCall\Service\ClientSummaryService;
use STCall\Service\Interfaces\CompanySelectorInterface;
use STCall\Service\Interfaces\FeaturesAvailableCheckerInterface;
use STCompany\Entity\Company;
use STRabbit\Entity\AbstractDaemon;
use STRoboTruck\Service\DataCollection\DataCollector;

class ClientSummaryDaemon extends AbstractDaemon
{
    public const string QUEUE = 'client-summary';
    public const string QUEUE_ERROR = 'client-summary-error';
    private const int MAX_CALLS_TO_GENERATE_SUMMARY = 50;

    public function __construct(
        private readonly FeaturesAvailableCheckerInterface $featuresAvailableChecker,
        private readonly CallService $callService,
        private readonly CompanySelectorInterface $companySelector,
        private readonly ClientSummaryService $clientSummaryService,
        private readonly AiSolutionsCommutatorService $aiSolutionsCommutator,
        private readonly DataCollector $dataCollector
    ) {
    }

    public function handle(string $message): void
    {
        $data = json_decode($message, true);

        if (!isset($data['company_id'], $data['client_id'])) {
            $this->dataCollector->collect(
                DataCollector::EVENT_CALL_CLIENT_SUMMARY_MESSAGE_FAIL,
                'Failed to get required data from message',
                ['message' => $message]
            );

            return;
        }

        $company = $this->companySelector->getCompany((int) $data['company_id']);

        if (!$this->featuresAvailableChecker->isClientSummarizationAvailable($company)) {
            return;
        }

        $clientId = (string) $data['client_id'];

        $lastSummary = $this->clientSummaryService->getLastSummary(
            $company->getId(),
            $clientId,
        );

        $this->generateSummaryFromCalls(
            $company,
            $clientId,
            $lastSummary,
        );
    }

    private function generateSummaryFromCalls(
        Company $company,
        string $clientId,
        ClientSummary $previousSummary = null,
    ): void {
        $calls = $this->callService->getClientCallsByDateRange(
            $company,
            $clientId,
            dateColumn: 'call_time',
            startDate: (new Carbon())->subDay()->startOfDay(),
            limit: self::MAX_CALLS_TO_GENERATE_SUMMARY,
        );

        foreach ($calls->chunk(5) as $callsChunk) {
            $callsChunk = new CallCollection($callsChunk);
            $lastCallTime = $callsChunk->last()->getTime();

            try {
                $summaryDTO = $this->aiSolutionsCommutator->getClientSummary(
                    $company->getId(),
                    $callsChunk,
                    $previousSummary
                );
            } catch (ThirdPartyApiException $e) {
                throw $e;
            } catch (\Throwable $e) {
                throw new ThirdPartyApiException('Failed to generate client summary: ' . $e->getMessage());
            }

            $callsChunk->usort(function ($a, $b) {
                return $a->getCreated()->lt($b->getCreated()) ? -1 : 1;
            });

            $previousSummary = $this->clientSummaryService->createClientSummaryFromDTO(
                $summaryDTO,
                $company->getId(),
                $clientId,
                $lastCallTime,
                $callsChunk->last()->getCreated()
            );
        }
    }
}
