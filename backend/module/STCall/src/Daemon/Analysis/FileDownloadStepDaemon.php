<?php

declare(strict_types=1);

namespace STCall\Daemon\Analysis;

use Exception;
use Interop\Container\Containerinterface;
use STApi\Entity\Exception\CallUpload\FileIsTooBigException;
use STA<PERSON>\Entity\Exception\CallUpload\IdenticalFileException;
use ST<PERSON><PERSON>\Entity\Exception\CallUpload\InvalidContentException;
use STApi\Entity\Exception\CallUpload\TooLowBalanceForAnalyzeException;
use STApi\Entity\Exception\InvalidCompanyApiRequestException;
use STApi\Entity\Exception\NoAccessToFileApiException;
use STApi\Entity\Exception\NotFoundApiException;
use STCall\Entity\Call;
use STCall\Service\CallAnalysis\Exception\StepIsAlreadyFinishedException;
use STCall\Service\CallAnalysis\Exception\StepIsFailedWithErrorFromTranscribingDriverException;
use STCall\Service\CallAnalysis\FileDownloadStep;
use STCall\Service\CallAnalysisService;
use STCall\Service\CallErrorService;
use STRabbit\Entity\AbstractDaemon;
use STRoboTruck\Service\DataCollection\DataCollector;
use Symfony\Component\RateLimiter\RateLimiterFactory;
use Symfony\Component\RateLimiter\Storage\CacheStorage;

class FileDownloadStepDaemon extends BaseAnalysisStepDaemon
{
    public const string RABBITMQ_EXCHANGE_NAME = 'delay';

    /**
     *
     * @var int
     */
    protected int $attemptsNumber = 5;

    /**
     *
     * @var int
     */
    protected int $delay = 5 * 60 * 1000;

    private array $rateLimits = [];
    private array $limiters = [];

    protected ContainerInterface $container;

    protected CacheStorage $cacheStorage;

    /**
     *
     * Class constructor
     */
    public function __construct(
        DataCollector $dataCollector,
        CacheStorage $cacheStorage,
        CallErrorService $callErrorService,
    ) {
        parent::__construct($dataCollector, $callErrorService);

        $this->exchangeName = static::RABBITMQ_EXCHANGE_NAME;
        $this->cacheStorage = $cacheStorage;
    }

    public function setRateLimits(array $rateLimits)
    {
        $this->rateLimits = $rateLimits;
        foreach ($this->rateLimits as $key => $value) {
            $this->limiters[$key] = (new RateLimiterFactory(
                $value,
                $this->cacheStorage,
            ))->create($value['id']);
        }

        return $this;
    }

    /**
     * @param string $message
     * @return void
     * @throws FileIsTooBigException
     * @throws IdenticalFileException
     * @throws InvalidCompanyApiRequestException
     * @throws InvalidContentException
     * @throws StepIsAlreadyFinishedException
     * @throws StepIsFailedWithErrorFromTranscribingDriverException
     * @throws TooLowBalanceForAnalyzeException
     * @throws NoAccessToFileApiException
     * @throws NotFoundApiException
     */
    public function handle(string $message): void
    {
        $data = json_decode($message);
        $callId = $data->call_id ?? '';
        $optionsCallId = $data?->options?->params?->call_id ?? '';
        $companyId = $data->company_id ?? 0;

        /** @var CallAnalysisService $callAnalysisService */
        $callAnalysisService = $this->params()->offsetGet(CallAnalysisService::class);

        /** @var FileDownloadStep $step */
        $step = $this->params()->offsetGet('step');

        $callIdsLog = sprintf(
            'step call_id: %s, optional call_id: %s',
            $step->getCall() instanceof Call ? $step->getCall()->getId() : '',
            $optionsCallId
        );

        if (isset($this->limiters[$companyId])) {
            if ($this->limiters[$companyId]->consume(1)->isAccepted() === false) {
                try {
                    $callAnalysisService->addToQueue(
                        $companyId,
                        null,
                        $step->getWaitingQueueName(),
                        $this->exchangeName,
                        (array) ($data?->options ?? []),
                        40 * 1000, //$this->delay,
                        CallAnalysisService::PRIORITY_10,
                        $data?->attempt ?? null
                    );
                    $logMessage = 'Rate limited, moved to waiting queue, ' . $callIdsLog;
                    $this->log($companyId, $callId, $logMessage, false);

                    return;
                } catch (Exception $e) {
                    $this->log(
                        $companyId,
                        $callId,
                        'Cannot move message to waiting queue, ' . $callIdsLog . ', error: ' . $e->getMessage(),
                        false
                    );
                    $this->doNotMakeNextAttempt(true);
                }
            }
        }

        parent::handle($message);
    }

    /**
     *
     * @return AbstractDaemon
     */
    public function init(): AbstractDaemon
    {
        $this->getChannel()->exchange_declare(
            exchange: static::RABBITMQ_EXCHANGE_NAME,
            type: 'x-delayed-message',
            passive: false,
            durable: true,
            auto_delete: false,
            internal: false,
            nowait: false,
            arguments: new \PhpAmqpLib\Wire\AMQPTable([
                'x-delayed-type' => \PhpAmqpLib\Exchange\AMQPExchangeType::DIRECT,
            ]),
        );
        $this->getChannel()->queue_declare(
            queue: $this->getQueueName(),
            passive: false,
            durable: true,
            exclusive: false,
            auto_delete: false,
            nowait: false,
            arguments: new \PhpAmqpLib\Wire\AMQPTable([
                'x-dead-letter-exchange' => 'delayed',
                'x-max-priority' => 100,
            ]),
        );
        $this->getChannel()->queue_declare(
            queue: $this->getErrorQueueName(),
            passive: false,
            durable: true,
            exclusive: false,
            auto_delete: false,
        );

        $this->getChannel()->queue_bind($this->getQueueName(), $this->getExchangeName(), $this->getQueueName());
        return $this;
    }
}
