<?php

declare(strict_types=1);

namespace STCall\Daemon\ClientApi\Handler;

use Exception;
use ST<PERSON>all\Daemon\Analysis\FileDownloadStepDaemon;
use ST<PERSON>all\Service\CallAnalysis\FileDownloadStep;
use STCall\Service\Import\UploadParams\UploadParams;
use STRoboTruck\Service\DataCollection\DataCollector;

class CallHandler extends AbstractHandler
{
    /**
     *
     * @param array $requestBody
     * @return bool
     * @throws Exception
     */
    public function run(array $requestBody): bool
    {
        if (!$this->featuresAvailableChecker->isCallAnalysisAvailable($this->company)) {
            $this->dataCollector->collect(
                DataCollector::EVENT_CALL_ANALYSIS_NOT_AVAILABLE,
                json_encode($requestBody),
                [
                    'id' => $this->requestId,
                    'company_id' => $this->company->getId(),
                    'call_id' => null,
                ]
            );

            return false;
        }

        $recordingFile = $requestBody['recording_file'] ?? null;
        if (!empty($recordingFile)) {
            $this->callAnalysisService->addToQueue(
                $this->company->getId(),
                null,
                FileDownloadStep::CALL_FILE_DOWNLOAD_QUEUE,
                FileDownloadStepDaemon::RABBITMQ_EXCHANGE_NAME,
                [
                    'params' => $requestBody,
                    'request_id' => $this->requestId,
                ],
                rand(0, 10 * 1000)
            );
            return true;
        }
        /** @var UploadParams $uploadParams */
        $uploadParams = $this->hydrate(
            [
                'driver_name' => 'api-upload',
                'company' => $this->company,
                'user' => null,
                'options' => $requestBody,
            ],
            UploadParams::class
        );
        $this->uploadService->uploadCall($uploadParams);

        return true;
    }
}
