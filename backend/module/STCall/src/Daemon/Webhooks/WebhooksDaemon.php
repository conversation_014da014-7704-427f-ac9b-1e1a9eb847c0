<?php

declare(strict_types=1);

namespace STCall\Daemon\Webhooks;

use ST<PERSON>pi\Entity\Exception\ThirdPartyApiException;
use STCall\Service\Webhooks\WebhookSender;
use STCall\Service\Webhooks\WebhookServiceFactory;
use STCom<PERSON>y\Service\Webhooks\WebhookSettingsSelector;
use STRabbit\Entity\AbstractDaemon;
use STRoboTruck\Service\DataCollection\DataCollector;
use Throwable;

class WebhooksDaemon extends AbstractDaemon
{
    public const string WEBHOOKS_QUEUE_NAME = 'webhooks';
    public const string WEBHOOKS_QUEUE_ERROR_NAME = 'webhooks-error';

    private const string ERROR_MESSAGE_EXTRACT_DATA = 'Fail to extract message data.';
    private const string ERROR_MESSAGE_GET_WEBHOOK_SETTINGS = 'Fail to get webhook settings.';
    private const string ERROR_MESSAGE_SEND_DATA = 'Fail to send data to company.';

    public function __construct(
        private readonly WebhookServiceFactory $webhookServiceFactory,
        private readonly WebhookSettingsSelector $webhookSettingsSelector,
        private readonly WebhookSender $webhookSender,
        private readonly DataCollector $dataCollector
    ) {
    }

    /**
     * @param string $message
     * @return void
     * @throws Throwable
     * @throws ThirdPartyApiException
     */
    public function handle(string $message): void
    {
        $messageData = json_decode($message, true);

        if (!$this->checkIfDataCorrect($messageData, $message)) {
            return;
        }

        ['company_id' => $companyId, 'call_id' => $callId, 'source' => $source, 'data' => $data] = $messageData;

        $webhookService = $this->webhookServiceFactory->create($source);

        $webhookType = $webhookService->getType();
        try {
            $webhookSettingsData = $this->webhookSettingsSelector->getCompaniesWebhooksSettingsDataByType(
                $webhookType,
                $companyId
            );
        } catch (Throwable $e) {
            $extra = [
                'source' => $source,
                'webhook_type' => $webhookType,
                'error_message' => $e->getMessage(),
                'error_code' => $e->getCode(),
            ];
            $this->dataCollector->collect(
                DataCollector::EVENT_SEND_WEBHOOK_FAIL,
                self::ERROR_MESSAGE_GET_WEBHOOK_SETTINGS,
                $extra
            );

            return;
        }

        $sendData = [
            'company_id' => $companyId,
            'call_id' => $callId,
            'data' => $webhookService->filterData($data),
        ];

        $url = $webhookSettingsData['url'];
        $headers = $webhookSettingsData['headers'] ?? null;

        try {
            $this->webhookSender->send($url, $sendData, $headers);
        } catch (Throwable $e) {
            $extra = [
                'source' => $source,
                'webhook_type' => $webhookType,
                'webhook_url' => $url,
                'headers' => $headers,
                'data' => $sendData,
                'error_message' => $e->getMessage(),
                'error_code' => $e->getCode(),
            ];
            $this->dataCollector->collect(
                DataCollector::EVENT_SEND_WEBHOOK_FAIL,
                self::ERROR_MESSAGE_SEND_DATA,
                $extra
            );
        }
    }

    private function checkIfDataCorrect(array $data, string $message): bool
    {
        if (
            !array_key_exists('company_id', $data) ||
            !array_key_exists('call_id', $data) ||
            !array_key_exists('source', $data) ||
            !array_key_exists('data', $data) ||
            !is_array($data['data'])
        ) {
            $this->dataCollector->collect(
                DataCollector::EVENT_SEND_WEBHOOK_FAIL,
                self::ERROR_MESSAGE_EXTRACT_DATA,
                ['message' => $message]
            );

            return false;
        }

        return true;
    }
}
