<?php

declare(strict_types=1);

namespace STCall\Validator;

use STLib\Validator\Validator;
use Lam<PERSON>\Validator\NotEmpty;

class CallValidator extends Validator
{
    public const EMPTY_CLIENT_NAME_ERROR = 'Empty client name';
    public const CALL_TIME_ERROR = 'Call time should not be less than today';
    public const USER_NOT_EXISTS = 'User is not exists';

    /**
     *
     * @param \STCall\Entity\Call $call
     * @return Validator
     * @throws \InvalidArgumentException
     */
    public function setInstance($call): Validator
    {
        if (!($call instanceof \STCall\Entity\Call)) {
            throw new \InvalidArgumentException('Call must be instance of "\STCall\Entity\Call"');
        }
        return parent::setInstance($call);
    }

    /**
     *
     * @see \STLib\Validator\ValidatorInterface::run()
     */
    public function run()
    {
        if ($this->hasCheck('call-time')) {
            $callTimeValidator = new \STLib\Validator\Check\Date([
                'max' => \Carbon\Carbon::now(),
            ]);
            if (!$callTimeValidator->isValid($this->getInstance()->getTime())) {
                $this->addError('call_time', static::CALL_TIME_ERROR);
            }
        }

        if ($this->hasCheck('user-exists')) {
            $agentId = $this->getInstance()->getAgentId() ?? null;
            $userExistsValidator = new \Laminas\Validator\Db\RecordExists([
                'table'   => $this->getTable('users_companies_roles')->getTable(),
                'field'   => 'user_id',
                'adapter' => $this->getTable('users_companies_roles')->getAdapter(),
                'exclude' => 'company_id = ' . $this->getInstance()->getCompanyId(),
            ]);

            if (!$userExistsValidator->isValid($agentId)) {
                $this->addError('agent_id', static::USER_NOT_EXISTS);
            }
        }
    }
}
