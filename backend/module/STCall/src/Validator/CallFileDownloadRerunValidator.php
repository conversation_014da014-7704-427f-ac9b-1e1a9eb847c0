<?php

declare(strict_types=1);

namespace STCall\Validator;

use ST<PERSON>all\Entity\Call;
use STLib\Validator\Validator;

class CallFileDownloadRerunValidator extends Validator
{
    public function setInstance($input): Validator
    {
        if (!$input instanceof Call) {
            throw new \InvalidArgumentException('Input must be an instance of \STCall\Entity\Call');
        }
        return parent::setInstance($input);
    }

    public function run(): void
    {
        /** @var Call $call */
        $call = $this->getInstance();

        if ($call->getCallType() !== Call::CALL_TYPE) {
            $this->addError('call_type', 'The call type is not supported');

            return;
        }

        if ($call->getS3FilePath()) {
            $this->addError('s3_file_path', 'The call file is already downloaded');
        }
    }
}
