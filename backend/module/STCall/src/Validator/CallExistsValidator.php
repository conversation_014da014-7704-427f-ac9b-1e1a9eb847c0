<?php

declare(strict_types=1);

namespace STCall\Validator;

use ST<PERSON>all\Data\CallsTable;
use STLib\Validator\Validator;

final class CallExistsValidator extends Validator
{
    protected const string ERROR_CALL_DOES_NOT_EXIST = 'The call does not exists.';

    public function __construct(private readonly CallsTable $callsTable)
    {
    }

    public function run(): void
    {
        /** @var array $input */
        $input = $this->getInstance();

        ['call_id' => $callId, 'company_id' => $companyId] = $input;

        if (!$this->callsTable->isCallExists($callId, $companyId)) {
            $this->addError('id', self::ERROR_CALL_DOES_NOT_EXIST);
        }
    }
}
