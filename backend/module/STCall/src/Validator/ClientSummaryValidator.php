<?php

declare(strict_types=1);

namespace STCall\Validator;

use ST<PERSON>ib\Validator\Validator;

class ClientSummaryValidator extends Validator
{
    public const string EMPTY_CLIENT_ID_ERROR = 'client_id is required';

    public function setInstance($input): Validator
    {
        if (!is_array($input)) {
            throw new \InvalidArgumentException('Input must be an array with client_id and company keys');
        }
        return parent::setInstance($input);
    }

    public function run(): void
    {
        /** @var array $input */
        $input = $this->getInstance();

        if ($this->hasCheck('client-id')) {
            $this->validateClientId($input);
        }
    }

    private function validateClientId(array $input): void
    {
        $clientId = $input['client_id'] ?? null;

        if (empty($clientId)) {
            $this->addError('client_id', self::EMPTY_CLIENT_ID_ERROR);
        }
    }
}
