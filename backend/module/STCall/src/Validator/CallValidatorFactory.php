<?php

declare(strict_types=1);

namespace STCall\Validator;

class CallValidatorFactory implements \Laminas\ServiceManager\Factory\FactoryInterface
{
    /**
     *
     * @param \Interop\Container\ContainerInterface $container
     * @param type $requestedName
     * @param array $options
     * @return CallValidator
     */
    public function createService(\Interop\Container\ContainerInterface $container, $requestedName, array $options = null): CallValidator
    {
        return $this($container, $requestedName ?: PatternPluginManager::class, $this->creationOptions);
    }

    /**
     *
     * @param \Interop\Container\ContainerInterface $container
     * @param string $requestedName
     * @param array $options
     * @return CallValidator
     */
    public function __invoke(\Interop\Container\ContainerInterface $container, $requestedName, array $options = null): CallValidator
    {
        $usersCompaniesRolesTable = $container->get(\STCompany\Data\UsersCompaniesRolesTable::class);
        $callValidator = new CallValidator();
        $callValidator->addTable('users_companies_roles', $usersCompaniesRolesTable);

        return $callValidator;
    }
}
