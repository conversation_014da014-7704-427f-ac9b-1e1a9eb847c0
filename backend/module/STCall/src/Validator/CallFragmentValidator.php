<?php

declare(strict_types=1);

namespace STCall\Validator;

use <PERSON><PERSON>\Validator\Between;
use STLib\Validator\Validator;

class CallFragmentValidator extends Validator
{
    protected const INCORRECT_START_TIME = 'Start time must be more than 0 and less than call duration';
    protected const INCORRECT_END_TIME = 'End time must be more than 1 and less than call duration';

    /**
     *
     * @var int
     */
    private int $callDuration;

    /**
     *
     * @see \STLib\Validator\ValidatorInterface::run()
     */
    public function run(): void
    {
        /** @var array $callFragmentData */
        $callFragmentData = $this->getInstance();

        $this->checkStartTime($callFragmentData['start_time'] ? (int) $callFragmentData['start_time'] : null);
        $this->checkEndTime($callFragmentData['end_time'] ? (int) $callFragmentData['end_time'] : null);
    }

    /**
     * @param int $duration
     * @return void
     */
    public function setCallDuration(int $duration): void
    {
        $this->callDuration = $duration;
    }

    /**
     * @param int|null $startTime
     * @return void
     */
    private function checkStartTime(?int $startTime): void
    {
        if ($startTime === null) {
            return;
        }

        $lengthValidator = new Between([
            'min' => 0,
            'max' => $this->callDuration,
        ]);
        if (!$lengthValidator->isValid($startTime)) {
            $this->addError('start_time', static::INCORRECT_START_TIME);
        }
    }

    /**
     * @param int|null $endTime
     * @return void
     */
    private function checkEndTime(?int $endTime): void
    {
        if ($endTime === null) {
            return;
        }

        $lengthValidator = new Between([
            'min' => 1,
            'max' => $this->callDuration,
        ]);
        if (!$lengthValidator->isValid($endTime)) {
            $this->addError('end_time', static::INCORRECT_END_TIME);
        }
    }
}
