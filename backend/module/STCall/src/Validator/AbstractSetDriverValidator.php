<?php

declare(strict_types=1);

namespace STCall\Validator;

use STLib\Validator\Validator;

abstract class AbstractSetDriverValidator extends Validator
{
    protected const string ERROR_DRIVER_IS_REQUIRED = 'Driver parameter is required';
    protected const string ERROR_LANGUAGE_IS_REQUIRED = 'Language parameter is required';
    protected const string ERROR_DRIVER_IS_NOT_VALID = 'Driver parameter is not valid';

    public function run(): void
    {
        /** @var array $input */
        $input = $this->getInstance();

        if (!isset($input['driver'])) {
            $this->addError('driver', self::ERROR_DRIVER_IS_REQUIRED);
        }

        if (!isset($input['language'])) {
            $this->addError('language', self::ERROR_LANGUAGE_IS_REQUIRED);
        }

        if (!in_array($input['driver'], $this->getValidDriverOptions(), true)) {
            $this->addError('driver', self::ERROR_DRIVER_IS_NOT_VALID);
        }
    }

    abstract protected function getValidDriverOptions(): array;
}
