<?php

declare(strict_types=1);

namespace STCall\Validator;

use ST<PERSON>all\Data\CallsTable;
use STLib\Validator\Validator;

final class ManualCallUploadValidator extends Validator
{
    public function __construct(private readonly CallsTable $callsTable)
    {
    }

    public function run(): void
    {
        /** @var array $input */
        $input = $this->getInstance();

        $fileName = $input['fileName'] ?? null;
        $companyId = $input['companyId'];

        if (empty($fileName)) {
            $this->addError('file_name', 'The file path is required');
            return;
        }

        // Derive and sanitize filename to be used as call_id and S3 object key
        $sanitizedFileName = $this->sanitizeFileName($fileName);

        if ($sanitizedFileName === '') {
            $this->addError('file_name', 'Invalid file name');
            return;
        }

        // Inject sanitized names for downstream consumers (UploadParams expects file_name and s3_file_path)
        $this->setInstance([
            'sanitizedFileName' => $sanitizedFileName,
        ]);

        if ($this->callsTable->isCallExists($sanitizedFileName, (int) $companyId)) {
            $this->addError('file_name', 'The call is already exists');
        }
    }

    private function sanitizeFileName(string $fileName): string
    {
        // Replace any character not in A-Za-z0-9_.- with underscore
        $sanitized = preg_replace('/[^A-Za-z0-9_.-]+/', '_', $fileName);
        if ($sanitized === null) {
            throw new \RuntimeException('Failed to sanitize file name');
        }

        // Collapse repeated underscores, hyphens, and dots
        $sanitized = preg_replace('/_+/', '_', $sanitized);
        $sanitized = preg_replace('/-+/', '-', $sanitized);
        $sanitized = preg_replace('/\.+/', '.', $sanitized);

        // Trim leading/trailing dots/underscores/hyphens
        $sanitized = trim($sanitized, '._-');

        return $sanitized;
    }
}
