<?php

declare(strict_types=1);

namespace STCall\Entity\Import\Result;

class Result
{
    /**
     *
     * @var \STCall\Entity\Call
     */
    protected \STCall\Entity\Call $call;

    /**
     *
     * @var \STCall\Entity\Import\Result\User|\STCompany\Entity\User
     */
    protected \STCall\Entity\Import\Result\User|\STCompany\Entity\User $agent;

    /**
     *
     * @var \STCall\Entity\Import\Result\Team
     */
    protected \STCall\Entity\Import\Result\Team $team;

    /**
     *
     * @var \STCompany\Entity\Client
     */
    protected \STCompany\Entity\Client $client;

    /**
     *
     * Class constructor
     */
    public function __construct()
    {
        $this->agent = new User();
        $this->team = new Team();
        $this->client = new \STCompany\Entity\Client();
    }

    /**
     *
     * @return \STCall\Entity\Call
     */
    public function getCall(): \STCall\Entity\Call
    {
        return $this->call;
    }

    /**
     *
     * @return \STCall\Entity\Import\Result\User|\STCompany\Entity\User
     */
    public function getAgent(): \STCall\Entity\Import\Result\User|\STCompany\Entity\User
    {
        return $this->agent;
    }

    /**
     *
     * @return \STCompany\Entity\Client
     */
    public function getClient(): \STCompany\Entity\Client
    {
        return $this->client;
    }

    /**
     *
     * @return \STCall\Entity\Import\Result\Team
     */
    public function getTeam(): \STCall\Entity\Import\Result\Team
    {
        return $this->team;
    }

    /**
     *
     * @param \STCall\Entity\Call $call
     * @return Result
     */
    public function setCall(\STCall\Entity\Call $call): Result
    {
        $this->call = $call;
        return $this;
    }

    /**
     *
     * @param \STCall\Entity\Import\Result\User|\STCompany\Entity\User $agent
     * @return Result
     */
    public function setAgent(\STCall\Entity\Import\Result\User|\STCompany\Entity\User $agent): Result
    {
        $this->agent = $agent;
        return $this;
    }

    /**
     *
     * @param \STCall\Entity\Import\Result\Team $team
     * @return Result
     */
    public function setTeam(\STCall\Entity\Import\Result\Team $team): Result
    {
        $this->team = $team;
        return $this;
    }

    /**
     *
     * @param \STCompany\Entity\Client $client
     * @return $this
     */
    public function setClient(\STCompany\Entity\Client $client)
    {
        $this->client = $client;
        return $this;
    }
}
