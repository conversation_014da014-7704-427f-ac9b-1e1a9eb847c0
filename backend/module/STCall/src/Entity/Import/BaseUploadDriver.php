<?php

declare(strict_types=1);

namespace STCall\Entity\Import;

abstract class BaseUploadDriver implements DriverInterface
{
    /**
     *
     * @var \STCall\Service\Import\UploadParams\UploadParams
     */
    protected \STCall\Service\Import\UploadParams\UploadParams $uploadParams;

    /**
     *
     * @return \STCall\Service\Import\UploadParams\UploadParams
     */
    public function getUploadParams(): \STCall\Service\Import\UploadParams\UploadParams
    {
        return $this->uploadParams;
    }

    /**
     *
     * @param \STCall\Service\Import\UploadParams\UploadParams $uploadParams
     * @return BaseUploadDriver
     */
    public function setUploadParams(\STCall\Service\Import\UploadParams\UploadParams $uploadParams): BaseUploadDriver
    {
        $this->uploadParams = $uploadParams;
        $this->setOptions($uploadParams->getOptions());
        return $this;
    }

    /**
     *
     * @return \STCompany\Entity\Company
     */
    public function getCompany(): \STCompany\Entity\Company
    {
        return $this->getUploadParams()->getCompany();
    }

    /**
     *
     * @param string|null $content
     * @return int
     */
    protected function getAudioDuration(?string $content): int
    {
        if (is_null($content)) {
            return 0;
        }

        $filePath = sys_get_temp_dir() . '/' . uniqid('audio_', true);
        file_put_contents($filePath, $content);

        try {
            $ffprobe = \FFMpeg\FFProbe::create();

            // Ensure there is at least one audio stream; otherwise treat as invalid and return 0
            $audioStreams = $ffprobe->streams($filePath)->audios();
            $hasAudioStream = method_exists($audioStreams, 'count')
                ? $audioStreams->count() > 0
                : (is_countable($audioStreams)
                ? count($audioStreams) > 0
                : !empty($audioStreams));

            if (!$hasAudioStream) {
                return 0;
            }

            $duration = (int) ceil((float) $ffprobe->format($filePath)->get('duration'));

            if ($duration <= 0) {
                // Some malformed files may have streams but no usable duration
                return 0;
            }

            return $duration;
        } catch (\Exception $e) {
            return 0;
        } finally {
            @unlink($filePath);
        }
    }

    /**
     *
     * @return string|null
     */
    protected function getFileHash(): ?string
    {
        if (is_null($this->getUploadParams()->getContent())) {
            return null;
        }
        return md5($this->getUploadParams()->getContent());
    }
}
