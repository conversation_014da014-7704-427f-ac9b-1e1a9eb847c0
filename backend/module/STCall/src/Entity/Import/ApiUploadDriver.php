<?php

declare(strict_types=1);

namespace STCall\Entity\Import;

use <PERSON><PERSON>all\Helper\AwsHelper;

class ApiUploadDriver extends BaseUploadDriver implements CallDriverInterface
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    protected const ID_PREFIX = '';

    /**
     *
     * @var array
     */
    protected array $options;

    /**
     *
     * @var array
     */
    protected array $clientOptions;

    /**
     *
     * @param array $options
     * @return DriverInterface
     * @throws \STApi\Entity\Exception\InvalidCompanyApiRequestException
     */
    public function setOptions(array $options): DriverInterface
    {
        if (!isset($options['time'])) {
            throw new \STApi\Entity\Exception\InvalidCompanyApiRequestException('Empty "time" option');
        }
        if (!isset($options['agent_id'])) {
            throw new \STApi\Entity\Exception\InvalidCompanyApiRequestException('Empty "agent_id" option');
        }
        if (!isset($options['client_id'])) {
            throw new \STApi\Entity\Exception\InvalidCompanyApiRequestException('Empty "client_id" option');
        }

        $options['company_id'] = $this->getCompany()->getId();
        $options['time'] = \Carbon\Carbon::parse($options['time']);
        $options['call_id'] = $options['call_id'] ?? uniqid(static::ID_PREFIX, true);
        $options['client_name'] = $options['client_name'] ?? $options['client_id'];
        $options['call_status'] = !isset($options['recording_file'])
                ? \STCall\Data\CallsTable::CALL_STATUS_HANG_UP
                : (isset($options['call_status']) ? strtolower($options['call_status']) : \STCall\Data\CallsTable::CALL_STATUS_END);
        $options['company_team_id'] = $options['team_id'] ?? $options['team_name'] ?? null;
        $options['team_name'] = $options['team_name'] ?? $options['company_team_id'] ?? null;
        $options['company_agent_id'] = $options['agent_id'] ?? null;
        $options['origin'] = \STCall\Data\CallsTable::CALL_ORIGIN_API;
        $options['is_run_manually'] = $options['is_manual'] ?? true;
        $options['client_acquisition_date'] = isset($options['client_acquisition_date']) && !empty($options['client_acquisition_date']) ? \Carbon\Carbon::parse($options['client_acquisition_date']) : null;
        $options['client_converted_date'] = isset($options['client_converted_date']) && !empty($options['client_converted_date']) ? \Carbon\Carbon::parse($options['client_converted_date']) : null;
        $options['client_last_transaction_date'] = isset($options['client_last_transaction_date']) && !empty($options['client_last_transaction_date']) ? \Carbon\Carbon::parse($options['client_last_transaction_date']) : null;
        $options['client_value'] = isset($options['client_value']) ? (float) $options['client_value'] : null;
        $options['s3_file_path'] = !empty($options['recording_file'])
                ? (AwsHelper::getAwsCompatibleFileName($options['call_id']) . '.' . $this->getFileExtension($options['recording_file']))
                : null;
        $options['duration'] = $this->getAudioDuration($this->getUploadParams()->getContent());
        $options['file_hash'] = $this->getFileHash();
        $options['recording_file_url'] = $options['recording_file'] ?? null;

        unset($options['team_id']);
        unset($options['agent_id']);
        // don't save original_file_name for api uploaded calls
        $options['original_file_name'] = null;
        $this->options = $options;

        $clientOptions = [];
        $clientOptions['client_id'] = $options['client_id'];
        $clientOptions['client_name'] = $options['client_name'] ?? $options['client_id'];
        $clientOptions['client_source'] = isset($options['client_source']) ? strtolower($options['client_source']) : null;
        $clientOptions['client_status'] = isset($options['client_status']) ? strtolower($options['client_status']) : null;
        $clientOptions['client_country'] = isset($options['client_country']) ? strtolower($options['client_country']) : null;
        $clientOptions['client_acquisition_date'] = isset($options['client_acquisition_date']) && !empty($options['client_acquisition_date']) ? \Carbon\Carbon::parse($options['client_acquisition_date']) : null;
        $clientOptions['client_converted_date'] = isset($options['client_converted_date']) && !empty($options['client_converted_date']) ? \Carbon\Carbon::parse($options['client_converted_date']) : null;
        $clientOptions['client_is_converted'] = isset($options['client_is_converted']) ? (bool) $options['client_is_converted'] : null;
        $clientOptions['client_last_transaction_date'] = isset($options['client_last_transaction_date']) && !empty($options['client_last_transaction_date']) ? \Carbon\Carbon::parse($options['client_last_transaction_date']) : null;
        $clientOptions['client_campaign_id'] = isset($options['client_campaign_id']) ? $options['client_campaign_id'] : null;
        $clientOptions['client_value'] = isset($options['client_value']) ? (float) $options['client_value'] : null;

        $this->clientOptions = $clientOptions;

        return $this;
    }

    /**
     *
     * @return \STCall\Entity\Import\Result\Result
     */
    public function run(): \STCall\Entity\Import\Result\Result
    {
        $result = new Result\Result();
        $result->getAgent()->setId($this->options['company_agent_id']);
        $result->getAgent()->setName($this->options['agent_name'] ?? $this->options['company_agent_id']);
        $result->getTeam()->setId($this->options['company_team_id']);
        $result->getTeam()->setName($this->options['team_name']);
        /** @var \STCall\Entity\Call $call */
        $call = $this->hydrate($this->options, \STCall\Entity\Call::class);
        $result->setCall($call);
        /** @var \STCompany\Entity\Client $client */
        $client = $this->hydrate($this->clientOptions, \STCompany\Entity\Client::class);
        $result->setClient($client);
        return $result;
    }

    /**
     *
     * @param string $fileName
     * @return string
     */
    protected function getFileExtension(string $fileName): string
    {
        $uri = new \Laminas\Uri\Uri($fileName);
        $extension = pathinfo($uri->getPath(), PATHINFO_EXTENSION);
        return !empty($extension) ? $extension : 'mp3';
    }
}
