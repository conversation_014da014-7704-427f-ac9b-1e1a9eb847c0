<?php

declare(strict_types=1);

namespace STCall\Listeners;

use <PERSON><PERSON>\EventManager\EventInterface;
use PhpAmqpLib\Message\AMQPMessage;
use ReflectionException;
use STApi\Entity\Exception\NotFoundApiException;
use ST<PERSON><PERSON>\Daemon\Webhooks\WebhooksDaemon;
use ST<PERSON>all\Entity\CallRole;
use STCall\Service\CallAnalysis\AlgoEventsStep;
use STCall\Service\CallEventService;
use STCall\Service\CallService;
use STCall\Service\Webhooks\WebhookServiceFactory;
use STCompany\Data\EventsColorsTable;
use STCompany\Service\CompanyService;
use STCompany\Service\EventService;
use STCompany\Service\RoleService;
use STCompany\Service\Webhooks\WebhookSettingsSelector;
use STRabbit\Service\RabbitService;
use STRoboTruck\Service\DataCollection\DataCollector;

final class CallAlgoEventsStepFinishedListener
{
    private const string ERROR_MESSAGE = 'Fail to extract call data.';

    public function __construct(
        private readonly WebhookServiceFactory $webhookServiceFactory,
        private readonly WebhookSettingsSelector $webhookSettingsSelector,
        private readonly CompanyService $companySelector,
        private readonly RoleService $rolesSelector,
        private readonly EventService $eventSelector,
        private readonly CallService $callService,
        private readonly RabbitService $rabbit,
        private readonly DataCollector $dataCollector,
        private readonly CallEventService $callEventService,
    ) {
    }

    /**
     * @throws ReflectionException
     * @throws NotFoundApiException
     */
    public function listen(EventInterface $event): void
    {
        $companyId = $event->getParam('company_id');
        $callId = $event->getParam('call_id');

        if (is_null($companyId) || is_null($callId)) {
            $this->dataCollector->collect(
                DataCollector::EVENT_ADD_COMPANY_EVENTS_TO_WEBHOOKS_QUEUE_FAIL,
                self::ERROR_MESSAGE,
                ['event' => json_encode($event)]
            );

            return;
        }

        $this->callEventService->createAndSave(
            $callId,
            $companyId,
            AlgoEventsStep::CALL_ALGO_EVENTS_QUEUE,
            $event->getName(),
        );

        $webhookService = $this->webhookServiceFactory->create(AlgoEventsStep::CALL_ALGO_EVENTS_QUEUE);
        if (!$this->webhookSettingsSelector->isWebhooksEnabled($webhookService->getType(), $companyId)) {
            return;
        }

        $company = $this->companySelector->getCompany($companyId);
        $eventCollection = $this->eventSelector->getEvents($companyId);
        $defaultFragmentColor = $this->eventSelector->getColor(EventsColorsTable::GREY_COLOR_ID);

        /** @var CallRole $callRole */
        $callRoleCollection = $this->callService->getCallsWithRoleIds(
            $company,
            $this->rolesSelector->getRoleIds($companyId),
            [$callId],
            $eventCollection,
            $defaultFragmentColor
        );

        $data = [];
        foreach ($callRoleCollection as $callRole) {
            foreach ($callRole->getCall()->getParagraphs() as $paragraph) {
                if ($paragraph->getEventHappenings()->isEmpty()) {
                    continue;
                }
                $data[] = $paragraph->toArray();
            }
        }

        $messageBody = [
            'company_id' => $companyId,
            'call_id' => $callId,
            'source' => AlgoEventsStep::CALL_ALGO_EVENTS_QUEUE,
            'data' => $data,
        ];

        $channel = $this->rabbit->getChannel();

        $message = new AMQPMessage(json_encode($messageBody));
        $channel->basic_publish($message, '', WebhooksDaemon::WEBHOOKS_QUEUE_NAME);
        $channel->close();
    }
}
