<?php

declare(strict_types=1);

namespace STCall\Listeners;

use <PERSON><PERSON>\EventManager\EventInterface;
use STCall\Service\ClientSummaryService;

readonly class ClientSummaryDisabledListener
{
    public const string EVENT_NAME = 'client-summary-disabled';

    public function __construct(
        private ClientSummaryService $clientSummaryService
    ) {
    }

    public function listen(EventInterface $event): void
    {
        $companyId = $event->getParam('company_id') ? (int) $event->getParam('company_id') : null;

        if (is_null($companyId)) {
            return;
        }

        $this->clientSummaryService->removeClientSummary($companyId);
    }
}
