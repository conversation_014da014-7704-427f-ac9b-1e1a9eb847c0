<?php

declare(strict_types=1);

namespace STCall\Service;

use Carbon\Carbon;
use STCall\Data\EventsRepository;
use STCompany\Entity\Company;
use STCompany\Entity\Event\Event;

class EventService
{
    /**
     * @param EventsRepository $eventsRepository
     */
    public function __construct(
        protected EventsRepository $eventsRepository,
    ) {
    }

    /**
     * @param Company $company
     * @param Event $event
     * @param Carbon|null $fromDate
     * @param int $limit
     * @return array
     */
    public function getEventsFromAnalyzedCalls(
        Company $company,
        Event $event,
        Carbon $fromDate = null,
        int $limit = 10
    ): array {
        return $this->eventsRepository->getEventsFromAnalyzedCalls(
            $company,
            $event,
            $fromDate,
            $limit
        );
    }
}
