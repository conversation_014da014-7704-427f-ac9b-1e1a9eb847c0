<?php

declare(strict_types=1);

namespace STCall\Service;

use STCall\Data\CallsReviewsTable;

class CallReviewService
{
    /**
     *
     * @var CallsReviewsTable
     */
    protected CallsReviewsTable $callsReviewsTable;

    /**
     *
     * @param CallsReviewsTable $callsReviewsTable
     */
    public function __construct(
        CallsReviewsTable $callsReviewsTable,
    ) {
        $this->callsReviewsTable = $callsReviewsTable;
    }

    /**
     * @param int $companyId
     * @param string $callId
     * @param int $roleId
     * @param int $reviewerUserId
     * @return bool
     */
    public function saveReviewed(
        int $companyId,
        string $callId,
        int $roleId,
        int $reviewerUserId
    ): bool {
        return $this->callsReviewsTable->saveReviewed($companyId, $callId, $roleId, $reviewerUserId);
    }
}
