<?php

declare(strict_types=1);

namespace STCall\Service;

use PhpAmqpLib\Message\AMQPMessage;
use PhpAmqpLib\Wire\AMQPTable;
use STCall\Daemon\Analysis\FileDownloadStepDaemon;
use STCall\Data\CallsTable;
use ST<PERSON>all\Entity\Call;
use ST<PERSON>all\Entity\CallCollection;
use STCall\Service\CallAnalysis\AlgoEventsStep;
use STCall\Service\CallAnalysis\ChecklistStep;
use STCall\Service\CallAnalysis\Exception\StepIsAlreadyFinishedException;
use STCall\Service\CallAnalysis\FileDownloadStep;
use ST<PERSON>all\Service\CallAnalysis\LanguageDetectionStep;
use STCall\Service\CallAnalysis\LlmEventsDetectionStep;
use STCall\Service\CallAnalysis\SpeakersRolesDetectionStep;
use STCall\Service\CallAnalysis\SummarizationStep;
use STCall\Service\CallAnalysis\TranscribingDistributionStep;
use STCall\Service\CallAnalysis\TranscribingJobCollectionStep;
use STCall\Service\CallAnalysis\TranscribingStep;
use STCall\Service\CallAnalysis\TranscribingWhisperStep;
use STCall\Service\CallAnalysis\TranslationStep;
use STCompany\Entity\Client;
use STCompany\Entity\ClientCollection;
use STRabbit\Service\ProvidesRabbit;

class CallAnalysisService
{
    use ProvidesRabbit;

    private const int TOP_PRIORITY_CALLS = 100;
    private const int HIGH_PRIORITY_CALLS = 200;
    private const int MEDIUM_PRIORITY_CALLS = 500;
    private const int LOW_PRIORITY_CALLS = 1000;
    private const int LOWEST_PRIORITY_CALLS = 5000;

    private const int PRIORITY_70 = 70;
    private const int PRIORITY_60 = 60;
    private const int PRIORITY_50 = 50;
    private const int PRIORITY_40 = 40;
    private const int PRIORITY_30 = 30;
    private const int PRIORITY_20 = 20;
    public const int PRIORITY_10 = 10;

    public const int TOP_PRIORITY = self::PRIORITY_70;

    public const string EVENT_CALL_TRANSCRIBING_STEP_FINISHED = 'call-transcribing-step-finished';
    public const string EVENT_CALL_TRANSCRIBING_JOB_COLLECTION_STEP_FINISHED = 'call-transcribing-job-collection-step-finished';
    public const string EVENT_CALL_TRANSLATION_STEP_FINISHED = 'call-translation-step-finished';
    public const string EVENT_CALL_ALGO_EVENTS_STEP_FINISHED = 'call-algo-events-step-finished';
    public const string EVENT_CALL_CHECKLIST_STEP_FINISHED = 'call-checklist-step-finished';
    public const string EVENT_CALL_SUMMARIZATION_STEP_FINISHED = 'call-summarization-step-finished';

    /**
     *
     * @var CallsTable
     */
    protected CallsTable $callsTable;

    /**
     *
     * @param CallsTable $callsTable
     */
    public function __construct(CallsTable $callsTable)
    {
        $this->callsTable = $callsTable;
    }

    /**
     *
     * @param Call $call
     * @return string
     * @throws CallAnalysis\Exception\StepIsAlreadyFinishedException
     */
    public function getNextAnalysisStep(Call $call): string
    {
        if (empty($call->getLanguage())) {
            return LanguageDetectionStep::CALL_LANGUAGE_DETECTION_QUEUE;
        } elseif (!$call->isTranscribed()) {
            return TranscribingDistributionStep::CALL_DISTRIBUITION_TRANSCRIBING_QUEUE;
        } elseif (!$call->isTranslated()) {
            return TranslationStep::CALL_TRANSLATION_QUEUE;
        } elseif (!$call->isSpeakersRolesDetected()) {
            return SpeakersRolesDetectionStep::CALL_SPEAKER_DETECTION_QUEUE;
        } elseif (!$call->isChecklistCompleted()) {
            return ChecklistStep::CALL_CHECKLIST_QUEUE;
        } elseif (!$call->isSummarizationCompleted()) {
            return SummarizationStep::CALL_SUMMARIZATION_QUEUE;
        } elseif (!$call->isLlmEventsDetected()) {
            return LlmEventsDetectionStep::CALL_LLM_EVENTS_DETECTION_QUEUE;
        } elseif (!$call->isAnalyzed()) {
            return AlgoEventsStep::CALL_ALGO_EVENTS_QUEUE;
        }

        throw new CallAnalysis\Exception\StepIsAlreadyFinishedException('Call is already analyzed');
    }

    /**
     *
     * @param int $companyId
     * @param Call|null $call
     * @param string $queue
     * @param string $exchange
     * @param array $options
     * @param int $delay
     * @return bool
     */
    public function addToQueue(
        int $companyId,
        ?Call $call,
        string $queue,
        string $exchange = '',
        array $options = [],
        int $delay = 0,
        ?int $priority = null,
        ?int $carryOverAttempt = null,
    ): bool {
        $channel = $this->rabbit()->getChannel();
        $message = new AMQPMessage(json_encode([
            'call_id' => $call instanceof Call ? $call->getId() : null,
            'company_id' => $companyId,
            'options' => $options,
            'attempt' => $carryOverAttempt ?? 0,
        ]), [
            'priority' => $priority ?? $this->getMessagePriorityByQueue($queue, $companyId, $call),
        ]);

        if ($delay > 0) {
            $message->set('application_headers', new AMQPTable([
                'x-delay' => $delay,
            ]));
        }

        $channel->basic_publish($message, $exchange, routing_key: $queue);
        $channel->close();
        return true;
    }

    /**
     * @throws StepIsAlreadyFinishedException
     */
    public function addToNextStepQueue(Call $call): void
    {
        $this->addToQueue(
            $call->getCompanyId(),
            $call,
            $this->getNextAnalysisStep($call)
        );
    }

    public function addToFileDownloadQueueForRerun(CallCollection $calls, ClientCollection $callsClients): void
    {
        foreach ($calls as $call) {
            if ($call->getCallType() !== Call::CALL_TYPE) {
                continue;
            }

            $client = $callsClients->offsetGet($call->getClientId());

            $this->addToQueue(
                $call->getCompanyId(),
                null,
                FileDownloadStep::CALL_FILE_DOWNLOAD_QUEUE,
                FileDownloadStepDaemon::RABBITMQ_EXCHANGE_NAME,
                [
                    'params' => [
                        'call_id' => $call->getId(),
                        'time' => $call->getTime()->format('Y-m-d H:i:s'),
                        'call_status' => $call->getCallStatus(),
                        'recording_file' => $call->getRecordingFileUrl(),
                        'agent_id' => $call->getAgentId(),
                        'agent_name' => $call->getAgentName(),
                        'client_id' => $client instanceof Client ? $client->getId() : $call->getClientId(),
                        'client_name' => $client?->getName(),
                        'client_source' => $client?->getSource(),
                        'client_status' => $client?->getStatus(),
                        'client_last_transaction_date' => $client?->getLastTransactionDate()?->format('Y-m-d H:i:s'),
                        'client_converted_date' => $client?->getConvertedDate()?->format('Y-m-d H:i:s'),
                        'client_value' => $client?->getValue(),
                        'client_country' => $client?->getCountry(),
                        'client_acquisition_date' => $client?->getAcquisitionDate()?->format('Y-m-d H:i:s'),
                        'client_is_converted' => $client?->getIsConverted(),
                        'client_campaign_id' => $client?->getCampaignId(),
                    ],
                    'request_id' => uniqid('', true),
                ],
                priority: self::TOP_PRIORITY,
            );
        }
    }

    /**
     *
     * @param string $queue
     * @param int $companyId
     * @param Call|null $call
     * @return int
     */
    private function getMessagePriorityByQueue(string $queue, int $companyId, ?Call $call): int
    {
        // phpcs:disable
        if (in_array($queue, [
            FileDownloadStep::CALL_FILE_DOWNLOAD_QUEUE,
            AlgoEventsStep::CALL_ALGO_EVENTS_QUEUE,
            LanguageDetectionStep::CALL_LANGUAGE_DETECTION_QUEUE,
            TranscribingDistributionStep::CALL_DISTRIBUITION_TRANSCRIBING_QUEUE,
            TranscribingJobCollectionStep::CALL_TRANSCRIBING_JOB_COLLECTION_QUEUE,
            TranscribingStep::CALL_TRANSCRIBING_QUEUE,
            TranscribingWhisperStep::CALL_TRANSCRIBING_QUEUE,
            TranslationStep::CALL_TRANSLATION_QUEUE,
            SpeakersRolesDetectionStep::CALL_SPEAKER_DETECTION_QUEUE,
            ChecklistStep::CALL_CHECKLIST_QUEUE,
            SummarizationStep::CALL_SUMMARIZATION_QUEUE,
            LlmEventsDetectionStep::CALL_LLM_EVENTS_DETECTION_QUEUE,
        ])) {
            return $this->getMessagePriorityByCompanyIdAndCallId($companyId, $call);
        }
        // phpcs:enable

        return 0;
    }

    /**
     *
     * @param int $companyId
     * @param Call|null $call
     * @return int
     */
    private function getMessagePriorityByCompanyIdAndCallId(int $companyId, ?Call $call = null): int
    {
        if ($call instanceof Call && $call->getOrigin() === CallsTable::CALL_ORIGIN_MANUAL) {
            return static::PRIORITY_70;
        }

        $countCallsFor24Hours = $this->callsTable->getCountOfTranscribedCallsForLast24HoursByCompanyId($companyId);

        if ($countCallsFor24Hours <= self::TOP_PRIORITY_CALLS) {
            return static::PRIORITY_60;
        }

        if ($countCallsFor24Hours <= self::HIGH_PRIORITY_CALLS) {
            return static::PRIORITY_50;
        }

        if ($countCallsFor24Hours <= self::MEDIUM_PRIORITY_CALLS) {
            return static::PRIORITY_40;
        }

        if ($countCallsFor24Hours <= self::LOW_PRIORITY_CALLS) {
            return static::PRIORITY_30;
        }

        if ($countCallsFor24Hours <= self::LOWEST_PRIORITY_CALLS) {
            return static::PRIORITY_20;
        }

        return static::PRIORITY_10;
    }
}
