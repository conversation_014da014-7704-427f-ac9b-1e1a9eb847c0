<?php

declare(strict_types=1);

namespace STCall\Service;

use STCall\Data\CallsSummarizationsTable;
use STCall\Entity\Call;

final class CallSummarizationRemoverService
{
    public function __construct(private readonly CallsSummarizationsTable $callsSummarizationsTable)
    {
    }

    public function removeCallSummarization(Call $call): void
    {
        $this->callsSummarizationsTable->removeCallSummarization($call->getId(), $call->getCompanyId());
    }
}
