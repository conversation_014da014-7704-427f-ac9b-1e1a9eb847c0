<?php

declare(strict_types=1);

namespace STCall\Service\Precalculation;

class CallPrecalculationManagerService
{
    use \STRedis\Service\ProvidesRedis;
    use \STRabbit\Service\ProvidesRabbit;

    public const int PRECALCULATE_AFTER_CALL_CHANGE_PRIORITY = 60;
    public const int PRECALCULATE_AFTER_REVIEW_CALL_PRIORITY = 50;
    public const int PRECALCULATE_NEW_CALL_PRIORITY = 40;
    public const int PRECALCULATE_AFTER_EVENT_HAPPENING_CHANGE_CALL_PRIORITY = 30;
    public const int PRECALCULATE_AFTER_EVENT_CHANGE_EVENT_PRIORITY = 20;
    public const int PRECALCULATE_BULK_PRIORITY = 10;

    protected const string PRECALCULATIONS_REDIS_PREFIX = 'precalculations:planned';

    /**
     *
     * @param int $companyId
     * @param int $roleId
     * @return void
     */
    public function markRoleAsRequiredToPrecalculations(int $companyId, int $roleId): void
    {
        $this->redis()->hset($this->getPrecalculateRedisKey($companyId), (string) $roleId, (string) time());
    }

    /**
     *
     * @param int $companyId
     * @param int $roleId
     * @param string $callId
     * @return void
     */
    public function markCallAsRequiredToPrecalculations(int $companyId, int $roleId, string $callId): void
    {
        $this->redis()->hset($this->getPrecalculateRedisKey($companyId, $roleId), $callId, (string) time());
    }

    /**
     *
     * @param int $companyId
     * @param int $changedAfter
     * @return array
     */
    public function getRoleIdsMarkedToPrecalculations(int $companyId, int $changedAfter): array
    {
        $roleIds = [];
        $redisKey = $this->getPrecalculateRedisKey($companyId);
        $result = $this->redis()->hgetall($redisKey);
        foreach ($result as $roleId => $timestamp) {
            if ($timestamp < $changedAfter) {
                $roleIds[] = $roleId;
                $this->redis()->hdel($redisKey, [$roleId]);
            }
        }
        return $roleIds;
    }

    /**
     *
     * @param int $companyId
     * @param int $roleId
     * @param int $changedAfter
     * @return array
     */
    public function getCallIdsMarkedToPrecalculations(int $companyId, int $roleId, int $changedAfter): array
    {
        $callIds = [];
        $redisKey = $this->getPrecalculateRedisKey($companyId, $roleId);
        $result = $this->redis()->hgetall($redisKey);
        foreach ($result as $callId => $timestamp) {
            if ($timestamp < $changedAfter) {
                $callIds[] = $callId;
                $this->redis()->hdel($redisKey, [$callId]);
            }
        }
        return $callIds;
    }

    /**
     *
     * @param int $companyId
     * @param string|array $callId
     * @param int $priority
     * @param int|array|null $roleId
     * @param int $batchSize
     * @return bool
     */
    public function addCallsToPrecalculateQueue(
        int $companyId,
        string|array $callId,
        int $priority = 1,
        int|array $roleId = null,
        int $batchSize = 50
    ): bool {
        $callIds = is_array($callId) ? $callId : [$callId];
        $channel = $this->rabbit()->getChannel();
        foreach (array_chunk($callIds, $batchSize) as $callIdsBatch) {
            $message = new \PhpAmqpLib\Message\AMQPMessage(json_encode([
                'call_ids' => $callIdsBatch,
                'company_id' => $companyId,
                'role_id' => $roleId,
            ]), [
                'priority' => $priority,
            ]);
            $channel->basic_publish($message, '', routing_key: \STCall\Daemon\Precalculation\PrecalculateCallsDaemon::PRECALCULATION_QUEUE);
        }
        $channel->close();
        return true;
    }

    /**
     *
     * @param int $companyId
     * @param int|null $roleId
     * @return string
     */
    protected function getPrecalculateRedisKey(int $companyId, int $roleId = null): string
    {
        return static::PRECALCULATIONS_REDIS_PREFIX . ':' . $companyId . (is_null($roleId) ? '' : ':' . $roleId);
    }
}
