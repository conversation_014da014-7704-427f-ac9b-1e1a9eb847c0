<?php

declare(strict_types=1);

namespace STCall\Service\Precalculation;

class CallPrecalculationManagerServiceWrapper
{
    /**
     *
     * @var CallPrecalculationManagerService|null
     */
    private static ?CallPrecalculationManagerService $callPrecalculationManagerService = null;

    /**
     *
     * @param CallPrecalculationManagerService $callPrecalculationManagerService
     * @return void
     */
    public static function setStaticCallPrecalculationManagerService(CallPrecalculationManagerService $callPrecalculationManagerService): void
    {
        self::$callPrecalculationManagerService = $callPrecalculationManagerService;
    }

    /**
     *
     * @return CallPrecalculationManagerService
     */
    public function getStaticCallPrecalculationManagerService(): CallPrecalculationManagerService
    {
        return self::$callPrecalculationManagerService;
    }
}
