<?php

declare(strict_types=1);

namespace STCall\Service\Precalculation;

trait CallPrecalculationManagerServiceTrait
{
    /**
     *
     * @var CallPrecalculationManagerServiceWrapper|null
     */
    private ?CallPrecalculationManagerServiceWrapper $callPrecalculationManagerServiceWrapper = null;

    /**
     *
     * @return CallPrecalculationManagerService
     */
    protected function getCallPrecalculationManager(): CallPrecalculationManagerService
    {
        if (is_null($this->callPrecalculationManagerServiceWrapper)) {
            $this->callPrecalculationManagerServiceWrapper = new CallPrecalculationManagerServiceWrapper();
        }
        return $this->callPrecalculationManagerServiceWrapper->getStaticCallPrecalculationManagerService();
    }
}
