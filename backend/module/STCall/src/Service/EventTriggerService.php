<?php

declare(strict_types=1);

namespace STCall\Service;

use Laminas\EventManager\Event;
use Laminas\EventManager\EventManager;

class EventTriggerService
{
    public function __construct(private readonly EventManager $eventManager)
    {
    }

    public function trigger(string $eventName, array $params): void
    {
        $event = new Event($eventName, null, $params);

        $this->eventManager->triggerEvent($event);
    }
}
