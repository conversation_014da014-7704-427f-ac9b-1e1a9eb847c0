<?php

declare(strict_types=1);

namespace STCall\Service\CallFragment\Slicer;

abstract class BaseSlicer
{
    public const CALL_CIRCLES_PURPOSE = 'call-circles';

    /**
     *
     * @var string
     */
    protected string $purpose;

    /**
     *
     * @var int
     */
    protected int $fragmentLength;

    /**
     *
     * @var int
     */
    protected int $timestampLength;

    /**
     *
     * @var \STCall\Entity\ParagraphCollection
     */
    protected \STCall\Entity\ParagraphCollection $paragraphCollection;

    /**
     *
     * @var \STCall\Entity\CallFragment\CallFragmentCollection|null
     */
    protected ?\STCall\Entity\CallFragment\CallFragmentCollection $callFragments = null;

    /**
     *
     * @return string
     */
    public function getPurpose(): string
    {
        return $this->purpose;
    }

    /**
     *
     * @return int
     */
    public function getFragmentLength(): int
    {
        return $this->fragmentLength;
    }

    /**
     *
     * @return int
     */
    public function getTimestampLength(): int
    {
        return $this->timestampLength;
    }

    /**
     *
     * @param string $purpose
     * @return BaseSlicer
     */
    public function setPurpose(string $purpose): BaseSlicer
    {
        $this->purpose = $purpose;
        // init timestamp and fragment length values
        $this->setFragmentLength(static::CALL_CIRCLE_FRAGMENT_LENGTH);
        $this->setTimestampLength(static::CALL_CIRCLE_TIMESTAMP_LENGTH);
        return $this;
    }

    /**
     *
     * @param int $fragmentLength
     * @return BaseSlicer
     */
    public function setFragmentLength(int $fragmentLength): BaseSlicer
    {
        $this->fragmentLength = $fragmentLength;
        return $this;
    }

    /**
     *
     * @param int $timestampLength
     * @return BaseSlicer
     */
    public function setTimestampLength(int $timestampLength): BaseSlicer
    {
        $this->timestampLength = $timestampLength;
        return $this;
    }

    /**
     *
     * @return \STCall\Entity\ParagraphCollection
     */
    public function getParagraphCollection(): \STCall\Entity\ParagraphCollection
    {
        return $this->paragraphCollection;
    }

    /**
     *
     * @param \STCall\Entity\ParagraphCollection $paragraphCollection
     * @return BaseSlicer
     */
    public function setParagraphCollection(\STCall\Entity\ParagraphCollection $paragraphCollection): BaseSlicer
    {
        $this->callFragments = null;
        $this->paragraphCollection = $paragraphCollection;
        return $this;
    }

    abstract public function getCallFragments(): \STCall\Entity\CallFragment\CallFragmentCollection;
}
