<?php

declare(strict_types=1);

namespace STCall\Service\CallFragment\Slicer;

class TimeSlicer extends BaseSlicer
{
    public const CALL_CIRCLE_FRAGMENT_LENGTH = 180;
    public const CALL_CIRCLE_TIMESTAMP_LENGTH = 180;

    /**
     *
     * @return \STCall\Entity\CallFragment\CallFragmentCollection
     * @throws \RuntimeException
     */
    public function getCallFragments(): \STCall\Entity\CallFragment\CallFragmentCollection
    {
        if ($this->fragmentLength <= 0) {
            throw new \RuntimeException('Sliced call length should be more than 0');
        }
        if ($this->timestampLength <= 0) {
            throw new \RuntimeException('Sliced call timestamp should be more than 0');
        }
        if (is_null($this->callFragments)) {
            $timestamps = $this->getTimestampsWithParagraphs();
            $this->sliceCallFragments($timestamps);
        }
        return $this->callFragments;
    }

    /**
     *
     * @return array
     */
    protected function getTimestampsWithParagraphs(): array
    {
        $timestampInterval = $this->getTimestampLength();
        $timestamps = [];
        foreach ($this->paragraphCollection as $paragraph) {
            /**
             * get timestamp start time of paragraph. If $length = 30, $timestamp value is 0, 15, 30, 45, 60, 75, etc
             */
            $timestampTime = floor($paragraph->getStartTime() / $timestampInterval) * $timestampInterval;
            if (!isset($timestamps[$timestampTime])) {
                $timestamps[$timestampTime] = new \stdClass();
                $timestamps[$timestampTime]->time = $timestampTime;
                $timestamps[$timestampTime]->paragraphNumber = null;
                $timestamps[$timestampTime]->deviation = null;
            }

            /**
             * deviation between paragraph start time and $timestamp
             */
            $deviation = abs($timestampTime - $paragraph->getStartTime());
            if (is_null($timestamps[$timestampTime]->deviation) || $timestamps[$timestampTime]->deviation > $deviation) {
                $timestamps[$timestampTime]->paragraphNumber = $paragraph->getParagraphNumber();
                $timestamps[$timestampTime]->deviation = $deviation;
            }
        }
        return $timestamps;
    }

    /**
     *
     * @param array $timestamps
     * @return \STCall\Entity\CallFragment\CallFragmentCollection
     */
    protected function sliceCallFragments(array $timestamps): \STCall\Entity\CallFragment\CallFragmentCollection
    {
        $length = $this->getFragmentLength();
        $this->callFragments = new \STCall\Entity\CallFragment\CallFragmentCollection();

        foreach ($timestamps as &$timestamp) {
            $callFragment = new \STCall\Entity\CallFragment\CallFragment();
            $firstParagraphNumber = $timestamp->paragraphNumber;
            if (isset($timestamps[$timestamp->time + $length])) {
                $lastParagraphNumber = max($timestamp->paragraphNumber, $timestamps[$timestamp->time + $length]->paragraphNumber - 1);
            } else {
                $lastParagraphNumber = max($this->paragraphCollection->keys());
            }
            for ($i = $firstParagraphNumber; $i <= $lastParagraphNumber; $i++) {
                $callFragment->getParagraphs()->add($this->paragraphCollection->offsetGet($i));
            }
            $this->callFragments->add($callFragment);
        }
        return $this->callFragments;
    }
}
