<?php

declare(strict_types=1);

namespace STCall\Service\CallFragment\Slicer;

class ParagraphCountSlicer extends BaseSlicer
{
    public const CALL_CIRCLE_FRAGMENT_LENGTH = 4;
    public const CALL_CIRCLE_TIMESTAMP_LENGTH = 4;

    /**
     *
     * @return \STCall\Entity\CallFragment\CallFragmentCollection
     * @throws \RuntimeException
     */
    public function getCallFragments(): \STCall\Entity\CallFragment\CallFragmentCollection
    {
        if ($this->fragmentLength <= 0) {
            throw new \RuntimeException('Sliced call length should be more than 0');
        }
        if (is_null($this->callFragments)) {
            $this->sliceCallFragments();
        }
        return $this->callFragments;
    }

    /**
     *
     * @return \STCall\Entity\CallFragment\CallFragmentCollection
     */
    protected function sliceCallFragments(): \STCall\Entity\CallFragment\CallFragmentCollection
    {
        $this->callFragments = new \STCall\Entity\CallFragment\CallFragmentCollection();
        /** @var \STCall\Entity\Paragraph $paragraph **/
        foreach ($this->getParagraphCollection() as $paragraph) {
            $paragraphNumber = $paragraph->getParagraphNumber();
            if ($paragraphNumber % $this->getTimestampLength() === 0) {
                $callFragment = new \STCall\Entity\CallFragment\CallFragment();
                for ($i = $paragraphNumber; $i < $paragraphNumber + $this->getFragmentLength(); $i++) {
                    if ($this->getParagraphCollection()->offsetExists($i)) {
                        $callFragment->getParagraphs()->add($this->paragraphCollection->offsetGet($i));
                    }
                }
                $this->callFragments->add($callFragment);
            }
        }
        return $this->callFragments;
    }
}
