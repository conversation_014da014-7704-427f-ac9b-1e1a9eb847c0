<?php

declare(strict_types=1);

namespace STCall\Service\CallFragment;

use ST<PERSON>all\Entity\Call;
use STCall\Entity\CallFragment\CallFragmentCollection;
use STCall\Entity\ChatCall;
use STCall\Entity\ParagraphCollection;
use STCall\Service\CallFragment\Slicer\BaseSlicer;

class ParagraphManager
{
    /**
     * @var ParagraphCollection
     */
    protected ParagraphCollection $paragraphCollection;

    /**
     * @var CallFragmentCollection|null
     */
    protected ?CallFragmentCollection $callFragments = null;

    /**
     *
     * @return ParagraphCollection
     */
    public function getParagraphCollection(): ParagraphCollection
    {
        return $this->paragraphCollection;
    }

    /**
     *
     * @param ParagraphCollection $paragraphCollection
     * @return ParagraphManager
     */
    public function setParagraphCollection(ParagraphCollection $paragraphCollection): ParagraphManager
    {
        $this->callFragments = null;
        $this->paragraphCollection = $paragraphCollection;
        return $this;
    }

    /**
     * @param string $callType
     * @param string $purpose
     * @return CallFragmentCollection
     */
    public function getCallFragments(string $callType, string $purpose): CallFragmentCollection
    {
        /** @var BaseSlicer $slicer **/
        $slicer = match ($callType) {
            Call::CALL_TYPE => new Slicer\TimeSlicer(),
            ChatCall::CHAT_CALL_TYPE => new Slicer\ParagraphCountSlicer(),
            default => throw new \RuntimeException('Invalid call type "' . $callType . '"'),
        };
        return $slicer
                ->setPurpose($purpose)
                ->setParagraphCollection($this->getParagraphCollection())
                ->getCallFragments();
    }
}
