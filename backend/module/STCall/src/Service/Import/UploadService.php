<?php

declare(strict_types=1);

namespace STCall\Service\Import;

use Exception;
use JsonException;
use Laminas\Filter\Word\DashToCamelCase;
use LogicException;
use PhpAmqpLib\Message\AMQPMessage;
use ReflectionException;
use <PERSON><PERSON><PERSON>\Data\ApiApplicationsTable;
use STA<PERSON>\Entity\Exception\NotFoundApiException;
use STCall\Data\CallsParagraphsTable;
use STCall\Data\CallsTable;
use STCall\Entity\Import\BaseUploadDriver;
use STCall\Entity\Import\CallDriverInterface;
use STCall\Entity\Import\ChatCallDriverInterface;
use STCall\Entity\Import\DriverInterface;
use STCall\Entity\Import\Result\Team;
use STCall\Entity\Import\Result\User;
use STCall\Service\CallAnalysis\CallUploadValidator;
use STCall\Service\Import\CallSaving\AbstractCallSaving;
use STCall\Service\Import\CallSaving\CallSaving;
use STCall\Service\Import\CallSaving\ChatCallSaving;
use STCall\Service\Import\CallSaving\Result\Result;
use STCompany\Data\ClientsTable;
use STCompany\Data\RolesTable;
use STCompany\Data\TeamsTable;
use STCompany\Data\UsersCompaniesRolesTable;
use STCompany\Data\UsersTeamsTable;
use STCompany\Entity\Role;
use STCompany\Service\AgentPrecalculation\AgentPrecalculationManagerService;
use STCompany\Service\AgentPrecalculation\AgentPrecalculationManagerServiceTrait;
use STCompany\Service\ClientPrecalculation\ClientPrecalculationManagerService;
use STCompany\Service\ClientPrecalculation\ClientPrecalculationManagerServiceTrait;
use STLib\Db\ProvidesTransaction;
use STLib\Mvc\Hydrator\BaseHydratorTrait;
use STRabbit\Service\ProvidesRabbit;
use STRedis\Service\ProvidesRedis;
use STUser\Data\UsersTable;
use UnexpectedValueException;

class UploadService
{
    use ProvidesTransaction;
    use BaseHydratorTrait;
    use ProvidesRedis;
    use ProvidesRabbit;
    use ClientPrecalculationManagerServiceTrait;
    use AgentPrecalculationManagerServiceTrait;

    protected const string DEFAULT_AGENTS_ROLE_NAME = 'Agents';
    protected const string CLIENT_API_UPLOADED_CALL_QUEUE_NAME = 'robo_message_bus_uploaded_calls';

    /**
     *
     * @var CallsTable
     */
    protected CallsTable $callsTable;

    /**
     *
     * @var ClientsTable
     */
    protected ClientsTable $clientsTable;

    /**
     *
     * @var UsersTable
     */
    protected UsersTable $usersTable;

    /**
     *
     * @var RolesTable
     */
    protected RolesTable $rolesTable;

    /**
     *
     * @var UsersCompaniesRolesTable
     */
    protected UsersCompaniesRolesTable $usersCompaniesRolesTable;

    /**
     *
     * @var TeamsTable
     */
    protected TeamsTable $teamsTable;

    /**
     *
     * @var UsersTeamsTable
     */
    protected UsersTeamsTable $usersTeamsTable;

    /**
     *
     * @var CallsParagraphsTable
     */
    protected CallsParagraphsTable $callsParagraphsTable;

    /**
     *
     * @var ApiApplicationsTable
     */
    protected ApiApplicationsTable $apiApplicationsTable;

    /**
     *
     * @var array
     */
    protected array $awsConfig;

    /**
     *
     * @param CallsTable $callsTable
     * @param ClientsTable $clientsTable
     * @param UsersTable $usersTable
     * @param RolesTable $rolesTable
     * @param UsersCompaniesRolesTable $usersCompaniesRolesTable
     * @param TeamsTable $teamsTable
     * @param UsersTeamsTable $usersTeamsTable
     * @param CallsParagraphsTable $callsParagraphsTable
     * @param ApiApplicationsTable $apiApplicationsTable
     * @param array $awsConfig
     * @throws UnexpectedValueException
     */
    public function __construct(
        CallsTable $callsTable,
        ClientsTable $clientsTable,
        UsersTable $usersTable,
        RolesTable $rolesTable,
        UsersCompaniesRolesTable $usersCompaniesRolesTable,
        TeamsTable $teamsTable,
        UsersTeamsTable $usersTeamsTable,
        CallsParagraphsTable $callsParagraphsTable,
        ApiApplicationsTable $apiApplicationsTable,
        array $awsConfig,
        private readonly CallUploadValidator $callUploadValidator,
    ) {
        if (!isset($awsConfig['api']) || !isset($awsConfig['env'])) {
            throw new UnexpectedValueException('AWS config must contain "api" and "env" settings');
        }
        $this->callsTable = $callsTable;
        $this->clientsTable = $clientsTable;
        $this->usersTable = $usersTable;
        $this->rolesTable = $rolesTable;
        $this->usersCompaniesRolesTable = $usersCompaniesRolesTable;
        $this->teamsTable = $teamsTable;
        $this->usersTeamsTable = $usersTeamsTable;
        $this->callsParagraphsTable = $callsParagraphsTable;
        $this->apiApplicationsTable = $apiApplicationsTable;
        $this->awsConfig = $awsConfig;
    }

    /**
     *
     * @param UploadParams\UploadParams $uploadParams
     * @return Result
     * @throws Exception
     */
    public function uploadCall(UploadParams\UploadParams $uploadParams): Result
    {
        // create and run upload driver
        /** @var BaseUploadDriver $driver **/
        $driver = $this->getDriver($uploadParams);
        $result = $driver->run();

        // get client and team or create if not exists
        $this->beginTransaction();

        try {
            $companyId = $uploadParams->getCompany()->getId();

            if ($result->getAgent() instanceof \STUser\Entity\User) {
                $agentId = $result->getAgent()->getId();
            } else {
                $this->createAgentRoleIfNotExists($companyId);
                $agentId = $this->createUserIfNotExistsAndGetUserId($companyId, $result->getAgent());
            }

            $teamId = $this->createTeamIfNotExistsAndGetTeamId($companyId, $result->getTeam());

            if ($teamId > 0) {
                $this->usersTeamsTable->deleteUsersFromAllTeams([$agentId]);
                $this->usersTeamsTable->save($agentId, $teamId);
            }

            $this->commit();
        } catch (Exception $e) {
            $this->rollback();
            throw $e;
        }

        // save client
        $client = $result->getClient();
        $client->setCompanyId($uploadParams->getCompany()->getId());
        $this->clientsTable->saveClient($client);

        $callSaving = $this->getCallSaving($driver)
            ->setCallsTable($this->callsTable)
            ->setUsersTable($this->usersTable)
            ->setCallUploadValidator($this->callUploadValidator)
            ->setCallsParagraphsTable($this->callsParagraphsTable)
            ->setDriver($driver)
            ->setCall($result->getCall())
            ->setUploadParams($uploadParams)
            ->setAgentId($agentId)
            ->setAwsS3Config($this->awsConfig);
        $callSavingResult = $callSaving->saveCall();

        // inform client API about successful call adding
        $this->informClientApiAboutUploadedCall($uploadParams->getCompany()->getId(), $callSavingResult->getCall()->getId());
        // precalculate client
        $this->getClientPrecalculationManager()->addClientsFromCallToPrecalculateQueue(
            $callSavingResult->getCall(),
            priority: ClientPrecalculationManagerService::CALL_SENT_PRIORITY,
        );
        // precalculate agent
        $this->getAgentPrecalculationManager()->addAgentsFromCallToPrecalculateQueue(
            $callSavingResult->getCall(),
            priority: AgentPrecalculationManagerService::CALL_SENT_PRIORITY,
        );
        return $callSavingResult;
    }

    /**
     * @param int $companyId
     * @return void
     * @throws ReflectionException
     */
    protected function createAgentRoleIfNotExists(int $companyId): void
    {
        try {
            $this->rolesTable->getAgentRole($companyId);
        } catch (NotFoundApiException $e) {
            /** @var Role $role */
            $role = $this->hydrate([
                'role_type' => Role::AGENT_ROLE_TYPE,
                'company_id' => $companyId,
                'name' => static::DEFAULT_AGENTS_ROLE_NAME,
            ], Role::class);
            $this->rolesTable->saveRole($role);
        }
    }

    /**
     * @param int $companyId
     * @param User $resultUser
     * @return int
     * @throws NotFoundApiException
     * @throws ReflectionException
     */
    protected function createUserIfNotExistsAndGetUserId(int $companyId, User $resultUser): int
    {
        if (!empty($resultUser->getId())) {
            $userResultSet = $this->usersCompaniesRolesTable->getUserId($companyId, $resultUser);
            if ($userResultSet->count() > 0) {
                $userId = $userResultSet->current()->offsetGet('user_id');
                if (!empty($resultUser->getName())) {
                    $existedUser = (array) $this->usersTable->getUserById($userId)->current();
                    if (!empty($resultUser->getName()) && $existedUser['user_name'] !== $resultUser->getName()) {
                        $this->usersTable->updateUserName($userId, $resultUser->getName());
                    }
                }
                return $userId;
            }
        }
        if (!empty($resultUser->getName())) {
            try {
                $userByNameResultSet = $this->usersTable->getUserByName($companyId, $resultUser->getName());
                return $userByNameResultSet->current()->offsetGet('user_id');
            } catch (NotFoundApiException $e) {
                // don't throw exception if user not found
            }
        }

        /** @var \STUser\Entity\User $user */
        $user = $this->hydrate([
            'company_id' => $companyId,
            'name' => $resultUser->getName() ?? $resultUser->getId(),
        ], \STUser\Entity\User::class);
        $user->initRegistrationDate();
        $userId = $this->usersTable->saveUser($user);
        $roleId = $this->rolesTable->getAgentRole($companyId)->current()->role_id;
        $this->usersCompaniesRolesTable->saveUserData($userId, $companyId, $roleId, $resultUser->getId());
        return $userId;
    }

    /**
     *
     * @param int $companyId
     * @param string $callId
     * @return bool
     * @throws JsonException
     */
    public function informClientApiAboutUploadedCall(int $companyId, string $callId): bool
    {
        $application = $this->apiApplicationsTable->getApplicationByCompanyId($companyId);
        $token = $application['application_token'] ?? null;
        if (!is_string($token)) {
            return false;
        }

        $channel = $this->rabbit()->getChannel();
        $channel->queue_declare(
            queue: static::CLIENT_API_UPLOADED_CALL_QUEUE_NAME,
            passive: false,
            durable: true,
            exclusive: false,
            auto_delete: false,
        );

        $message = new AMQPMessage(json_encode([
            'token' => $token,
            'call_id' => $callId,
            'company_id' => $companyId,
            'is_uploaded' => true,
        ], JSON_THROW_ON_ERROR));
        $channel->basic_publish($message, exchange: '', routing_key: static::CLIENT_API_UPLOADED_CALL_QUEUE_NAME);
        $channel->close();
        return true;
    }

    /**
     * @param int $companyId
     * @param Team $resultTeam
     * @return int|null
     * @throws ReflectionException
     */
    protected function createTeamIfNotExistsAndGetTeamId(int $companyId, Team $resultTeam): ?int
    {
        if (empty($resultTeam->getId())) {
            return null;
        }
        $teamResultSet = $this->teamsTable->getTeamIdByCompanyTeam($companyId, $resultTeam);
        if ($teamResultSet->count() > 0) {
            return $teamResultSet->current()->offsetGet('team_id');
        }
        try {
            $teamByNameResultSet = $this->teamsTable->getTeamByName($companyId, $resultTeam->getName());
            return $teamByNameResultSet->current()->offsetGet('team_id');
        } catch (NotFoundApiException $e) {
            // don't throw exception if team not found
        }
        $team = $this->hydrate([
            'company_id' => $companyId,
            'name' => $resultTeam->getName(),
            'company_team_id' => $resultTeam->getId(),
        ], \STCompany\Entity\Team::class);
        $teamId = $this->teamsTable->saveTeam($team, $resultTeam);
        return $teamId;
    }

    /**
     *
     * @param UploadParams\UploadParams $uploadParams
     * @return DriverInterface
     * @throws LogicException
     */
    protected function getDriver(UploadParams\UploadParams $uploadParams): DriverInterface
    {
        $dashToCamelCase = new DashToCamelCase();
        $namespace = '\STCall\Entity\Import\Factory\\' . $dashToCamelCase->filter($uploadParams->getDriverName()) . 'DriverFactory';
        if (!class_exists($namespace)) {
            throw new LogicException('Can\'t find upload driver "' . $namespace . '"');
        }
        return (new $namespace())
                ->create()
                ->setUploadParams($uploadParams);
    }

    /**
     * @param DriverInterface $driver
     * @return AbstractCallSaving
     */
    protected function getCallSaving(DriverInterface $driver): AbstractCallSaving
    {
        if ($driver instanceof CallDriverInterface) {
            $callSaving = new CallSaving();
        } elseif ($driver instanceof ChatCallDriverInterface) {
            $callSaving = new ChatCallSaving();
        } else {
            throw new \InvalidArgumentException('Driver must implement STCall\Entity\Import\ChatCallDriverInterface');
        }
        return $callSaving;
    }
}
