<?php

declare(strict_types=1);

namespace STCall\Service;

use STApi\Entity\Exception\ErrorTypeProviderInterface;
use STCall\Data\CallsErrorsTable;
use STCall\Entity\CallError;

class CallErrorService
{
    public function __construct(
        private readonly CallsErrorsTable $callsErrorsTable,
    ) {
    }

    public function saveError(
        CallError $callError,
    ): void {
        $this->callsErrorsTable->save($callError);
    }

    public function createErrorFromException(
        \Exception $exception,
        string $callId,
        int $companyId,
        string $errorOrigin,
    ): void {
        if (empty($callId)) {
            return;
        }

        $errorType = $exception instanceof ErrorTypeProviderInterface ? $exception->getErrorType() : 'unknown-error';

        $this->callsErrorsTable->save(new CallError(
            $callId,
            $companyId,
            $errorType,
            $errorOrigin,
            (int) $exception->getCode(),
            $this->extractCleanErrorMessage($exception),
        ));
    }

    public function deleteForErrorOrigin(string $callId, int $companyId, string $errorOrigin): void
    {
        $this->callsErrorsTable->deleteForErrorOrigin($callId, $companyId, $errorOrigin);
    }

    public function deleteForCallId(string $callId, int $companyId): void
    {
        $this->callsErrorsTable->deleteForCallId($callId, $companyId);
    }

    private function extractCleanErrorMessage(\Exception $e): string
    {
        $message = $e->getMessage();

        // For Guzzle like exceptions, extract just the request info and status
        if (preg_match('/^(Client error: .+?) response:/', $message, $matches)) {
            return trim($matches[1]);
        }

        return strlen($message) <= 255 ? $message : substr($message, 0, 255) . '...';
    }
}
