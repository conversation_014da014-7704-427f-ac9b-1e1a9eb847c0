<?php

declare(strict_types=1);

namespace STCall\Service;

use STCall\Data\TranscribeDriversLanguagesTable;
use STCall\Data\TranslationDriversLanguagesTable;

class LanguageDriverService
{
    /**
     * @param TranscribeDriversLanguagesTable $transcribeDriversLanguagesTable
     * @param TranslationDriversLanguagesTable $translationDriversLanguagesTable
     */
    public function __construct(
        protected TranscribeDriversLanguagesTable $transcribeDriversLanguagesTable,
        protected TranslationDriversLanguagesTable $translationDriversLanguagesTable,
    ) {
    }

    /**
     * @return array
     */
    public function getTranslationDriversWithLanguages(): array
    {
        return $this->translationDriversLanguagesTable->getDriversWithLanguages();
    }

    /**
     * @return array
     */
    public function getTranscribingDriversWithLanguages(): array
    {
        return $this->transcribeDriversLanguagesTable->getDriversWithLanguages();
    }

    /**
     * @param string $language
     * @param string $driver
     * @return void
     */
    public function setTranscribingDriver(string $language, string $driver): void
    {
        $this->transcribeDriversLanguagesTable->saveLanguageDriver($language, $driver);
    }

    /**
     * @param string $language
     * @param string $driver
     * @return void
     */
    public function setTranslationDriver(string $language, string $driver): void
    {
        $this->translationDriversLanguagesTable->saveLanguageDriver($language, $driver);
    }
}
