<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis\TranscribingDriver;

use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use JsonException;
use ST<PERSON>all\Entity\ParagraphCollection;
use ST<PERSON>all\Service\CallAnalysis\Exception\StepIsFailedWithErrorFromTranscribingDriverException;
use STCall\Service\CallAnalysis\Transcribing\TwoSteps\NotReadyException;
use STCall\Service\CallAnalysis\TranscribingDistributionStep;
use stdClass;
use STLib\Mvc\Hydrator\BaseHydratorTrait;

class SpeechmaticsDriver extends AbstractDriver implements TwoStepsDriverInterface
{
    use BaseHydratorTrait;

    protected const string BASE_URL = 'https://eu.asr.api.speechmatics.com/v2/';

    protected const string STATUS_COMPLETED = 'done';
    protected const string STATUS_IN_PROGRESS = 'running';
    protected const string STATUS_ERROR = 'rejected';

    protected const int FILE_ACCESS_DURATION_IN_MIN = 180;

    /**
     * @return string
     * @throws JsonException
     * @throws GuzzleException
     */
    public function createJob(): string
    {
        $params = [
            'type' => 'transcription',
            'fetch_data' => ['url' => $this->getFileLink(static::FILE_ACCESS_DURATION_IN_MIN)],
            'transcription_config' => [
                'language' => $this->getCall()->getLanguage(),
                'diarization' => 'speaker',
                'speaker_diarization_config' => [
                    'speaker_sensitivity' => 0.7
                ],
                'operating_point' => 'enhanced',
            ],
        ];

        if (!$this->getCompanyVocabulary()->isEmpty()) {
            $params['transcription_config']['additional_vocab'] = array_map(
                static fn($value): array => ['content' => $value],
                array_column($this->getCompanyVocabulary()->toArray(), 'word'),
            );
        }

        $jobId = $this->callCreateJob('post', $params)->id;
        $this->createTranscribingRequestLog($jobId, TranscribingDistributionStep::TRANSCRIBING_SPEECHMATICS_DRIVER);

        return $jobId;
    }

    /**
     * @param string $transcriptionName
     * @return ParagraphCollection
     * @throws JsonException
     * @throws StepIsFailedWithErrorFromTranscribingDriverException
     * @throws NotReadyException
     * @throws Exception
     * @throws GuzzleException
     */
    public function collectData(string $transcriptionName): ParagraphCollection
    {
        $jobResponse = $this->call('get', 'jobs' . '/' . $transcriptionName);
        $transcriptionJobStatus = $jobResponse->job->status;

        if ($transcriptionJobStatus === static::STATUS_IN_PROGRESS) {
            throw new NotReadyException();
        }
        if ($transcriptionJobStatus === static::STATUS_ERROR) {
            throw new StepIsFailedWithErrorFromTranscribingDriverException('Speechmatics transcribing error for job"' . $transcriptionName . '"');
        }
        if ($transcriptionJobStatus !== static::STATUS_COMPLETED) {
            throw new \RuntimeException('Unknown status "' . $transcriptionJobStatus . '" for job"' . $transcriptionName . '"');
        }

        $transcribingResponse = $this->call('get', sprintf('/jobs/%s/transcript', $transcriptionName));

        return $this->getResponseToCollectionConverter()->convertDriverResponseToParagraphCollection(
            $transcribingResponse,
            ResponseConverter\ResponseToCollectionConverter::SPEECHMATICS,
            $this->call
        );
    }

    /**
     * @param string $method
     * @param array $params
     * @return stdClass
     * @throws JsonException
     * @throws GuzzleException
     */
    protected function callCreateJob(string $method, array $params = []): stdClass
    {
        $client = new Client();
        $result = $client->request($method, static::BASE_URL . 'jobs', [
            'headers' => [
                'Authorization' => 'Bearer' . ' ' . $this->driverConfig['api']['token'],
            ],
            'multipart' => [
                [
                    'name' => 'config',
                    'contents' => json_encode($params, JSON_THROW_ON_ERROR),
                ],
            ],
        ]);

        return json_decode($result->getBody()->getContents(), false, 512, JSON_THROW_ON_ERROR);
    }

    /**
     * @param string $method
     * @param string $path
     * @return stdClass
     * @throws GuzzleException
     * @throws JsonException
     */
    protected function call(string $method, string $path): stdClass
    {
        $client = new Client();
        $result = $client->request($method, static::BASE_URL . $path, [
            'headers' => [
                'Authorization' => 'Bearer' . ' ' . $this->driverConfig['api']['token'],
            ],
        ]);

        return json_decode($result->getBody()->getContents(), false, 512, JSON_THROW_ON_ERROR);
    }
}
