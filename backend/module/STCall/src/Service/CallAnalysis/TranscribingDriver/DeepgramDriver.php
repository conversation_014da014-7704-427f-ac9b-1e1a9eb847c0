<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis\TranscribingDriver;

use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use RuntimeException;
use <PERSON><PERSON><PERSON>\Entity\Exception\ThirdPartyApiException;
use ST<PERSON>all\Entity\Call;
use STCall\Entity\ParagraphCollection;
use STCall\Service\CallAnalysis\TranscribingDistributionStep;
use stdClass;
use STLib\Mvc\Hydrator\BaseHydratorTrait;

class DeepgramDriver extends AbstractDriver implements OneStepDriverInterface
{
    use BaseHydratorTrait;

    protected const string TRANSCRIBE_URL = 'https://api.deepgram.com/v1/listen';
    protected const MODEL = 'general-enhanced';
    protected const USE_VOCABULARY = true;
    protected const float VOCABULARY_INTENSIFIER = 0.3;

    /**
     *
     * @return ParagraphCollection
     * @throws ThirdPartyApiException
     * @throws GuzzleException
     */
    public function transcribe(): ParagraphCollection
    {
        return match ($this->getCall()->getCallType()) {
            Call::CALL_TYPE => $this->transcribeCall(),
            default => throw new RuntimeException('Invalid call type sent to transcribing'),
        };
    }

    /**
     *
     * @return ParagraphCollection
     * @throws GuzzleException
     * @throws ThirdPartyApiException
     * @throws Exception
     */
    protected function transcribeCall(): ParagraphCollection
    {
        $fileLink = $this->getFileLink();
        $result = $this->call($fileLink);
        if (isset($result->metadata->request_id)) {
            $this->createTranscribingRequestLog(
                $result->metadata->request_id,
                TranscribingDistributionStep::TRANSCRIBING_DEEPGRAM_DRIVER
            );
        }
        if (!isset($result->results->channels[0]->alternatives[0])) {
            throw new ThirdPartyApiException('Deepgram response error');
        }
        if (!isset($result->results->channels[0]->alternatives[0]->paragraphs)) {
            return new ParagraphCollection();
        }

        return $this->getResponseToCollectionConverter()->convertDriverResponseToParagraphCollection(
            $result,
            ResponseConverter\ResponseToCollectionConverter::DEEPGRAM,
            $this->call
        );
    }

    /**
     *
     * @param string $fileLink
     * @return stdClass
     * @throws GuzzleException
     */
    protected function call(string $fileLink): stdClass
    {
        $client = new Client();
        $headers = [
            'Authorization' => 'Token' . ' ' . $this->getSecret(),
            'Content-Type' => 'application/json',
        ];
        $body = json_encode([
            'url' => $fileLink,
        ]);

        $result = $client->post(static::TRANSCRIBE_URL . '?' . $this->getQueryParamsString(), [
            'headers' => $headers,
            'body' => $body,
            'timeout' => 25 * 60,
        ]);
        return json_decode($result->getBody()->getContents());
    }

    /**
     *
     * @return string
     */
    protected function getSecret(): string
    {
        return $this->driverConfig['api']['secret'];
    }

    /**
     *
     * @return string
     */
    protected function getQueryParamsString(): string
    {
        $query = [
            'model' => static::MODEL,
            'language' => $this->getLanguage(),
            'punctuate' => true,
            'diarize' => $this->getCall()->getCallType() === Call::CALL_TYPE,
            'paragraphs' => $this->getCall()->getCallType() === Call::CALL_TYPE,
            'tag' => $this->getBucketName(),
        ];

        if ($this->isKeywordsAllowed()) {
            if ($this->getCompanyVocabulary()->count() > 0 && static::USE_VOCABULARY) {
                $query[$this->getKeyFieldsName()] = array_column($this->getCompanyVocabulary()->toArray(), 'word');
            }
        }

        return $this->getDeepgramSpecificQueryParams($query);
    }

    /**
     * Conversion to specific query string, what deepgram is able to parse
     * See doc https://developers.deepgram.com/reference/listen-remote and key fields param describing
     * They need array params as separate params, for example keywords=snuffalupagus&keywords=systrom(Model 2)
     * They need array params as separate params, for example keyterm=snuffalupagus&keyterm=systrom(Model 3)
     *
     * @param array $query
     * @return string
     */
    protected function getDeepgramSpecificQueryParams(array $query): string
    {
        $queryParts = [];
        foreach ($query as $key => $value) {
            if ($key === $this->getKeyFieldsName()) {
                foreach ($value as $item) {
                    $queryParts[] = $this->buildKeyword($key, $item);
                }
            } elseif (is_array($value)) {
                foreach ($value as $item) {
                    $queryParts[] = $key . '=' . $item;
                }
            } elseif (is_bool($value)) {
                $queryParts[] = $key . '=' . ($value ? 'true' : 'false');
            } else {
                $queryParts[] = $key . '=' . $value;
            }
        }
        return implode('&', $queryParts);
    }

    protected function getLanguage(): ?string
    {
        return $this->call->getLanguage();
    }

    protected function getKeyFieldsName(): string
    {
        return 'keywords';
    }

    protected function buildKeyword(string $key, string $item): string
    {
        return $key . '=' . $item . ':' . static::VOCABULARY_INTENSIFIER;
    }

    protected function isKeywordsAllowed(): bool
    {
        return true;
    }
}
