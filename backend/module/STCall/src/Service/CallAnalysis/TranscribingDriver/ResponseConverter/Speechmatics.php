<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis\TranscribingDriver\ResponseConverter;

class Speechmatics extends AbstractWordsToParagraphResponseConverter
{
    /**
     * @param mixed $response
     * @return iterable
     */
    protected function getWordsItems(mixed $response): iterable
    {
        return $response->results;
    }

    /**
     * @param $responseItem
     * @return ?int
     */
    protected function getSpeaker($responseItem): ?int
    {
        return $responseItem->alternatives[0]->speaker ? (int) \mb_substr($responseItem->alternatives[0]->speaker, -1) : null;
    }
}
