<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis\TranscribingDriver;

use GuzzleHttp\Exception\GuzzleException;
use Laminas\Http\Response;
use STApi\Entity\Exception\ThirdPartyApiException;
use ST<PERSON>all\Helper\LanguageHelper;
use ST<PERSON>all\Service\CallAnalysis\TranscribingDistributionStep;

class TranscriptorDriver extends AbstractDriver implements TwoStepsDriverInterface
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    protected const BASE_URL = 'https://api.transkriptor.com/';
    protected const STATUS_ACCEPTED = 'Accepted';
    protected const FILE_ACCESS_DURATION_IN_MIN = 180;

    /**
     *
     * @return string
     */
    public function createJob(): string
    {
        $jobId = $this->callUpload();
        $this->createTranscribingRequestLog($jobId, TranscribingDistributionStep::TRANSCRIBING_TRANSCRIPTOR_DRIVER);

        return $jobId;
    }

    /**
     *
     * @param string $transcriptionName
     * @return \STCall\Entity\ParagraphCollection
     * @throws \STCall\Service\CallAnalysis\Transcribing\TwoSteps\NotReadyException
     * @throws \RuntimeException
     */
    public function collectData(string $transcriptionName): \STCall\Entity\ParagraphCollection
    {
        $jobResponse = $this->callGetContent($transcriptionName);

        if (!isset($jobResponse['content'])) {
            throw new \STCall\Service\CallAnalysis\Transcribing\TwoSteps\NotReadyException();
        }

        return $this->getResponseToCollectionConverter()->convertDriverResponseToParagraphCollection(
            $jobResponse,
            ResponseConverter\ResponseToCollectionConverter::TRANSCRIPTOR,
            $this->call
        );
    }

    /**
     *
     * @param string $jobId
     * @return array|string
     * @throws \JsonException
     */
    private function callGetContent(string $jobId): array|string
    {
        $url = self::BASE_URL . '3/Get-Content' . '?' . http_build_query(['orderid' => $jobId]);

        $client = new \GuzzleHttp\Client();
        $response = $client->request('GET', $url);

        if ($response->getStatusCode() !== Response::STATUS_CODE_200) {
            throw new \STCall\Service\CallAnalysis\Exception\StepIsFailedWithErrorFromTranscribingDriverException('Transcriptor transcribing error for job"' . $jobId . '"');
        }

        return json_decode($response->getBody()->getContents(), true, 512, JSON_THROW_ON_ERROR);
    }

    /**
     * @throws GuzzleException
     * @throws \JsonException
     * @throws ThirdPartyApiException
     */
    private function callUpload(): string
    {
        $client = new \GuzzleHttp\Client();

        $result = $client->request(
            'GET',
            self::BASE_URL . '1/Upload' . "?" . http_build_query([
                'apiKey' => $this->driverConfig['api']['token'],
                'language' => LanguageHelper::getBCP47ByLanguageCode($this->call->getLanguage()),
                'fileName' => $this->call->getId()
            ]),
        );

        if ($result->getStatusCode() !== Response::STATUS_CODE_200) {
            throw new \STApi\Entity\Exception\ThirdPartyApiException(
                'Transcriptor response error: ' . $result->getBody()->getContents()
            );
        }

        $uploadResponseData = json_decode($result->getBody()->getContents(), true, 512, JSON_THROW_ON_ERROR);
        $givenOrderId = explode('-+-', $uploadResponseData['fields']['key'])[0];

        $fields = $uploadResponseData['fields'];
        $postFields = [];

        foreach ($fields as $key => $field) {
            $postFields[] = [
                'name' => $key,
                'contents' => $field,
            ];
        }

        $postFields[] =  [
            'name' => 'file',
            'contents' => $this->getFileContent(),
        ];

        $response = $client->request(
            'POST',
            $uploadResponseData['url'],
            ['multipart' => $postFields],
        );

        if ($response->getStatusCode() !== Response::STATUS_CODE_204) {
            throw new \STApi\Entity\Exception\ThirdPartyApiException(
                'Transcriptor response error: ' . $response->getBody()->getContents()
            );
        }

        return $givenOrderId;
    }
}
