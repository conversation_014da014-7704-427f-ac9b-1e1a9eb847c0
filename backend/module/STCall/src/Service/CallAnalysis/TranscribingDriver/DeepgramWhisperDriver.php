<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis\TranscribingDriver;

use STCall\Service\CallAnalysis\TranscribingDistributionStep;

class DeepgramWhisperDriver extends DeepgramDriver
{
    protected const MODEL = 'whisper';
    protected const USE_VOCABULARY = false;

    /**
     *
     * @return string
     */
    protected function getSecret(): string
    {
        return $this->driverConfig['api']['on-prem-secret'];
    }

    /**
     * @param string $jobId
     * @param string $driverName
     * @return void
     */
    protected function createTranscribingRequestLog(string $jobId, string $driverName): void
    {
        parent::createTranscribingRequestLog($jobId, TranscribingDistributionStep::TRANSCRIBING_DEEPGRAM_WHISPER_DRIVER);
    }
}
