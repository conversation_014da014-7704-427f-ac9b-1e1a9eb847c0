<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis\TranscribingDriver;

use STCall\Service\CallAnalysis\TranscribingDistributionStep;

class DeepgramNova3Driver extends DeepgramDriver
{
    private const string LANGUAGE = 'multi';

    protected const string MODEL = 'nova-3';

    protected const string LANGUAGE_ENGLISH = 'en';

    protected function getLanguage(): string
    {
        return $this->call->getLanguage() === self::LANGUAGE_ENGLISH ? $this->call->getLanguage() : self::LANGUAGE;
    }

    protected function getKeyFieldsName(): string
    {
        return 'keyterm';
    }

    protected function isKeywordsAllowed(): bool
    {
        return $this->call->getLanguage() === self::LANGUAGE_ENGLISH;
    }

    /**
     * @param string $jobId
     * @param string $driverName
     * @return void
     */
    protected function createTranscribingRequestLog(string $jobId, string $driverName): void
    {
        parent::createTranscribingRequestLog(
            $jobId,
            TranscribingDistributionStep::TRANSCRIBING_DEEPGRAM_NOVA_3_DRIVER
        );
    }

    protected function buildKeyword(string $key, string $item): string
    {
        return $key . '=' . $item;
    }
}
