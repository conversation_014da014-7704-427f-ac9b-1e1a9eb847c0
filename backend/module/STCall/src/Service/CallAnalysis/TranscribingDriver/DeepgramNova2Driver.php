<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis\TranscribingDriver;

use STCall\Service\CallAnalysis\TranscribingDistributionStep;

class DeepgramNova2Driver extends DeepgramDriver
{
    protected const MODEL = 'nova-2';

    /**
     * @param string $jobId
     * @param string $driverName
     * @return void
     */
    protected function createTranscribingRequestLog(string $jobId, string $driverName): void
    {
        parent::createTranscribingRequestLog($jobId, TranscribingDistributionStep::TRANSCRIBING_DEEPGRAM_NOVA_2_DRIVER);
    }
}
