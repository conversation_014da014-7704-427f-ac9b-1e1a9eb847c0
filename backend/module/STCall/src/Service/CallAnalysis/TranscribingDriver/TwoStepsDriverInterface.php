<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis\TranscribingDriver;

interface TwoStepsDriverInterface
{
    /**
     *
     * @return string
     */
    public function createJob(): string;

    /**
     *
     * @param string $transcriptionName
     * @return \STCall\Entity\ParagraphCollection
     */
    public function collectData(string $transcriptionName): \STCall\Entity\ParagraphCollection;
}
