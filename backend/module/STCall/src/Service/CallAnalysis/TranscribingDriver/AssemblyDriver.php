<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis\TranscribingDriver;

use GuzzleHttp\RequestOptions;
use STCall\Service\CallAnalysis\TranscribingDistributionStep;

class AssemblyDriver extends AbstractDriver implements TwoStepsDriverInterface
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    protected const BASE_URL = 'https://api.assemblyai.com/v2/';

    protected const STATUS_COMPLETED = 'completed';
    protected const STATUS_IN_PROGRESS = 'processing';
    protected const STATUS_QUEUED = 'queued';
    protected const STATUS_ERROR = 'error';

    protected const FILE_ACCESS_DURATION_IN_MIN = 180;

    /**
     *
     * @return string
     */
    public function createJob(): string
    {
        $params = [
            'audio_url' => $this->getFileLink(static::FILE_ACCESS_DURATION_IN_MIN),
            'language_code' => $this->getCall()->getLanguage(),
            'speakers_expected' => 2,
            'speaker_labels' => true,
        ];

        if (!$this->getCompanyVocabulary()->isEmpty()) {
            $params['word_boost'] = array_column($this->getCompanyVocabulary()->toArray(), 'word');
        }

        $jobId = $this->call('post', 'transcript', $params)->id;
        $this->createTranscribingRequestLog($jobId, TranscribingDistributionStep::TRANSCRIBING_ASSEMBLY_DRIVER);

        return $jobId;
    }

    /**
     *
     * @param string $transcriptionName
     * @return \STCall\Entity\ParagraphCollection
     * @throws \STCall\Service\CallAnalysis\Transcribing\TwoSteps\NotReadyException
     * @throws \RuntimeException
     */
    public function collectData(string $transcriptionName): \STCall\Entity\ParagraphCollection
    {
        $jobResponse = $this->call('get', 'transcript/' . $transcriptionName);
        $transcriptionJobStatus = $jobResponse->status;

        if (in_array($transcriptionJobStatus, [self::STATUS_IN_PROGRESS, self::STATUS_QUEUED], true)) {
            throw new \STCall\Service\CallAnalysis\Transcribing\TwoSteps\NotReadyException();
        }
        if ($transcriptionJobStatus === static::STATUS_ERROR) {
            throw new \STCall\Service\CallAnalysis\Exception\StepIsFailedWithErrorFromTranscribingDriverException('Assembly transcribing error for job"' . $transcriptionName . '"');
        }
        if ($transcriptionJobStatus !== static::STATUS_COMPLETED) {
            throw new \RuntimeException('Unknown status "' . $transcriptionJobStatus . '" for job"' . $transcriptionName . '"');
        }

        if (!isset($jobResponse->utterances)) {
            $this->logger('api-calls-logs')->info('Unable to get paragraphs for call', [
                'id' => $jobResponse->id,
                'company_id' => $this->company->getId(),
                'call_id' => $this->call->getId(),
            ]);
        }

        return $this->getResponseToCollectionConverter()->convertDriverResponseToParagraphCollection(
            $jobResponse,
            ResponseConverter\ResponseToCollectionConverter::ASSEMBLY,
            $this->call
        );
    }

    /**
     *
     * @param string $method
     * @param string $path
     * @param array $params
     * @return \ArrayObject
     */
    protected function call(string $method, string $path, array $params = []): \stdClass
    {
        $client = new \GuzzleHttp\Client();
        $options = [
            'headers' => [
                'Authorization' => $this->driverConfig['api']['token'],
            ],
        ];

        if (!empty($params)) {
            $options[RequestOptions::JSON] = $params;
        }

        $result = $client->request($method, static::BASE_URL . $path, $options);

        return json_decode($result->getBody()->getContents());
    }
}
