<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis;

use ST<PERSON>all\Data\CallsTable;
use ST<PERSON>all\Data\TranscribeDriversLanguagesTable;
use ST<PERSON>all\Entity\Call;
use ST<PERSON>all\Entity\CallFactory;
use ST<PERSON>all\Service\CallAnalysis\Exception\StepIsAlreadyFinishedException;
use STLib\Mvc\Hydrator\BaseHydratorTrait;
use STRoboTruck\Service\DataCollection\DataCollector;

class TranscribingDistributionStep extends BaseStep
{
    use BaseHydratorTrait;

    public const string TRANSCRIBING_AWS_DRIVER = 'aws';
    public const string TRANSCRIBING_DEEPGRAM_DRIVER = 'deepgram';
    public const string TRANSCRIBING_WORDCAB_DRIVER = 'wordcab';
    public const string TRANSCRIBING_DEEPGRAM_WHISPER_DRIVER = 'deepgramWhisper';
    public const string TRANSCRIBING_DEEPGRAM_NOVA_2_DRIVER = 'deepgramNova2';
    public const string TRANSCRIBING_DEEPGRAM_NOVA_3_DRIVER = 'deepgramNova3';
    public const string TRANSCRIBING_ASSEMBLY_DRIVER = 'assembly';
    public const string TRANSCRIBING_TRANSCRIPTOR_DRIVER = 'transcriptor';
    public const string TRANSCRIBING_SPEECHMATICS_DRIVER = 'speechmatics';
    public const string TRANSCRIBING_SALAD_DRIVER = 'salad';

    public const array AVAILABLE_TRANSCRIBING_DRIVERS = [
        self::TRANSCRIBING_AWS_DRIVER,
        self::TRANSCRIBING_DEEPGRAM_DRIVER,
        self::TRANSCRIBING_WORDCAB_DRIVER,
        self::TRANSCRIBING_DEEPGRAM_WHISPER_DRIVER,
        self::TRANSCRIBING_DEEPGRAM_NOVA_2_DRIVER,
        self::TRANSCRIBING_DEEPGRAM_NOVA_3_DRIVER,
        self::TRANSCRIBING_ASSEMBLY_DRIVER,
        self::TRANSCRIBING_TRANSCRIPTOR_DRIVER,
        self::TRANSCRIBING_SPEECHMATICS_DRIVER,
        self::TRANSCRIBING_SALAD_DRIVER,
    ];

    public const string CALL_DISTRIBUITION_TRANSCRIBING_QUEUE = 'call-transcribing-distribution-step';
    public const string CALL_DISTRIBUITION_TRANSCRIBING_ERROR_QUEUE = 'call-transcribing-distribution-step-error';

    /**
     *
     * @var TranscribeDriversLanguagesTable
     */
    protected TranscribeDriversLanguagesTable $transcribeDriversLanguagesTable;

    /**
     *
     * @var string
     */
    protected string $nextStepQueue;

    /**
     *
     * @param CallsTable $callsTable
     * @param CallFactory $callFactory
     * @param TranscribeDriversLanguagesTable $transcribeDriversLanguagesTable
     * @param DataCollector $dataCollector
     */
    public function __construct(
        CallsTable $callsTable,
        CallFactory $callFactory,
        TranscribeDriversLanguagesTable $transcribeDriversLanguagesTable,
        private readonly DataCollector $dataCollector,
    ) {
        parent::__construct($callsTable, $callFactory);
        $this->transcribeDriversLanguagesTable = $transcribeDriversLanguagesTable;
    }

    /**
     *
     * @param int $companyId
     * @return bool
     * @throws StepIsAlreadyFinishedException
     */
    public function run(int $companyId): bool
    {
        $call = $this->getCall();
        if ($call->isTranscribed()) {
            throw new StepIsAlreadyFinishedException('Call is already transcribed');
        }

        $driverName = $this->getDriverNameByLanguage($call);
        $this->nextStepQueue = match ($driverName) {
            static::TRANSCRIBING_DEEPGRAM_DRIVER => TranscribingStep::CALL_TRANSCRIBING_QUEUE,
            static::TRANSCRIBING_DEEPGRAM_WHISPER_DRIVER => TranscribingWhisperStep::CALL_TRANSCRIBING_QUEUE,
            static::TRANSCRIBING_DEEPGRAM_NOVA_2_DRIVER => TranscribingStep::CALL_TRANSCRIBING_QUEUE,
            static::TRANSCRIBING_DEEPGRAM_NOVA_3_DRIVER => TranscribingStep::CALL_TRANSCRIBING_QUEUE,
            static::TRANSCRIBING_AWS_DRIVER => TranscribingJobCreationStep::CALL_TRANSCRIBING_JOB_CREATION_QUEUE,
            static::TRANSCRIBING_WORDCAB_DRIVER => TranscribingJobCreationStep::CALL_TRANSCRIBING_JOB_CREATION_QUEUE,
            static::TRANSCRIBING_SPEECHMATICS_DRIVER => TranscribingJobCreationStep::CALL_TRANSCRIBING_JOB_CREATION_QUEUE,
            static::TRANSCRIBING_SALAD_DRIVER => TranscribingJobCreationStep::CALL_TRANSCRIBING_JOB_CREATION_QUEUE,
            static::TRANSCRIBING_ASSEMBLY_DRIVER => TranscribingJobCreationStep::CALL_TRANSCRIBING_JOB_CREATION_QUEUE,
            static::TRANSCRIBING_TRANSCRIPTOR_DRIVER => TranscribingJobCreationStep::CALL_TRANSCRIBING_JOB_CREATION_QUEUE,
        };
        $this->addNextStepOption('driver', $driverName);

        $this->dataCollector->collect(
            DataCollector::EVENT_CALL_ANALYZE_TRANSCRIBING_DISTRIBUTION_STEP_SUCCESS,
            'Driver is ' . $driverName,
            [
                'id' => null,
                'company_id' => $companyId,
                'call_id' => $call->getId(),
            ],
            'api-calls-logs'
        );

        return true;
    }

    /**
     *
     * @return string
     */
    public function getNextStepQueue(): string
    {
        return $this->nextStepQueue;
    }

    /**
     *
     * @return string
     */
    public function getQueueName(): string
    {
        return static::CALL_DISTRIBUITION_TRANSCRIBING_QUEUE;
    }

    /**
     *
     * @return string
     */
    public function getErrorQueueName(): string
    {
        return static::CALL_DISTRIBUITION_TRANSCRIBING_ERROR_QUEUE;
    }

    /**
     *
     * @param Call $call
     * @return string
     */
    protected function getDriverNameByLanguage(Call $call): string
    {
        $language = $call->getLanguage();
        if ($language === null) {
            return static::TRANSCRIBING_DEEPGRAM_WHISPER_DRIVER;
        }

        $driverName = $this->transcribeDriversLanguagesTable->getDriverNameByLanguage($language);

        return $driverName === null ? static::TRANSCRIBING_DEEPGRAM_WHISPER_DRIVER : $driverName;
    }
}
