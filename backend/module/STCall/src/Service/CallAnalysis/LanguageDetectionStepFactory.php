<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis;

use Interop\Container\ContainerInterface;
use <PERSON>inas\ServiceManager\Factory\FactoryInterface;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use STCall\Data\CallsParagraphsTable;
use STCall\Data\CallsTable;
use STCall\Entity\CallFactory;
use STCall\Service\Interfaces\TranslatorInterface;
use STCompany\Data\CompaniesLanguagesTable;
use STCompany\Data\CompaniesTable;
use STCompany\Data\UsersCompaniesLanguagesTable;

class LanguageDetectionStepFactory implements FactoryInterface
{
    /**
     *
     * @param ContainerInterface $container
     * @param type $requestedName
     * @param array $options
     * @return LanguageDetectionStep
     */
    public function createService(ContainerInterface $container, $requestedName, array $options = null): LanguageDetectionStep
    {
        return $this($container, $requestedName ?: PatternPluginManager::class, $this->creationOptions);
    }

    /**
     * @param ContainerInterface $container
     * @param $requestedName
     * @param array|null $options
     * @return LanguageDetectionStep
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function __invoke(ContainerInterface $container, $requestedName, array $options = null): LanguageDetectionStep
    {
        $callsTable = $container->get(CallsTable::class);
        $callFactory = $container->get(CallFactory::class);
        $companiesTable = $container->get(CompaniesTable::class);
        $companiesLanguagesTable = $container->get(CompaniesLanguagesTable::class);
        $usersLanguagesTable = $container->get(UsersCompaniesLanguagesTable::class);
        $callsParagraphsTable = $container->get(CallsParagraphsTable::class);
        $translator = $container->get(TranslatorInterface::class);
        $config = $container->get('config');
        $awsConfig = $config['aws'];
        return new LanguageDetectionStep(
            $callsTable,
            $callFactory,
            $companiesTable,
            $companiesLanguagesTable,
            $usersLanguagesTable,
            $callsParagraphsTable,
            $translator,
            $awsConfig,
        );
    }
}
