<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis;

use STCall\Data\CallsParagraphsTable;
use STCall\Data\CallsTable;
use ST<PERSON>all\Entity\CallFactory;
use STCall\Service\CallAnalysis\TranscribingDriver\Provider\DriverProvider;
use STCompany\Data\CompaniesTable;
use STCompany\Service\CompanyVocabularyService;
use STLib\Mvc\Hydrator\BaseHydratorTrait;
use STLib\Mvc\Hydrator\Hydrator;
use STRoboTruck\Service\DataCollection\DataCollector;

abstract class BaseTranscribingTwoSteps extends BaseTranscribingStep
{
    use BaseHydratorTrait;

    /**
     *
     * @var string|null
     */
    protected ?string $jobName;

    /**
     *
     * @var array
     */
    protected array $wordcabConfig = [];

    /**
     *
     * @var array
     */
    protected array $assemblyConfig = [];

    /**
     *
     * @var array
     */
    protected array $transcriptorConfig = [];

    /**
     *
     * @var array
     */
    protected array $speechmaticsConfig = [];

    /**
     *
     * @var array
     */
    protected array $saladConfig = [];

    /**
     *
     * @param CallsTable $callsTable
     * @param CallFactory $callFactory
     * @param CompaniesTable $companiesTable
     * @param Hydrator $hydrator
     * @param DriverProvider $driverProvider
     * @param CallsParagraphsTable $callsParagraphsTable
     * @param CompanyVocabularyService $companyVocabularyService
     * @param DataCollector $dataCollector
     * @param array $awsConfig
     * @param array $wordcabConfig
     * @param array $assemblyConfig
     * @param array $transcriptorConfig
     * @param array $speechmaticsConfig
     * @param array $saladConfig
     */
    public function __construct(
        CallsTable $callsTable,
        CallFactory $callFactory,
        CompaniesTable $companiesTable,
        Hydrator $hydrator,
        DriverProvider $driverProvider,
        CallsParagraphsTable $callsParagraphsTable,
        CompanyVocabularyService $companyVocabularyService,
        protected DataCollector $dataCollector,
        array $awsConfig,
        array $wordcabConfig,
        array $assemblyConfig,
        array $transcriptorConfig,
        array $speechmaticsConfig,
        array $saladConfig,
    ) {
        parent::__construct(
            $callsTable,
            $callFactory,
            $companiesTable,
            $hydrator,
            $driverProvider,
            $callsParagraphsTable,
            $companyVocabularyService,
            $awsConfig
        );
        $this->wordcabConfig = $wordcabConfig;
        $this->assemblyConfig = $assemblyConfig;
        $this->transcriptorConfig = $transcriptorConfig;
        $this->speechmaticsConfig = $speechmaticsConfig;
        $this->saladConfig = $saladConfig;
    }

    /**
     *
     * @param array $options
     * @return StepInterface
     */
    public function applyOptions(array $options = []): StepInterface
    {
        parent::applyOptions($options);
        $this->jobName = $options['job_name'] ?? null;
        return $this;
    }
}
