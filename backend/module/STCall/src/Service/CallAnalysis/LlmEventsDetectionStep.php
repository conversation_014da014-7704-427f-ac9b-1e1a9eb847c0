<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis;

use GuzzleHttp\Exception\GuzzleException;
use ReflectionException;
use STAlgo\Service\AiSolutionsCommutatorService;
use STAlgo\Service\AlgoEvents\RequestCreation\LlmEventsAlgoApiRequestCreator;
use STAlgo\Service\ParamsBuilding\RequestParamsBuilder;
use STApi\Entity\Exception\ThirdPartyApiException;
use STCall\Data\CallsAlgoEventsTable;
use STCall\Data\CallsTable;
use STCall\Entity\Call;
use STCall\Entity\CallFactory;
use STCall\Service\CallAnalysis\CallSelection\CallSelector;
use STCall\Service\CallAnalysis\Exception\StepIsAlreadyFinishedException;
use STCall\Service\Interfaces\CompanySelectorInterface;
use STCall\Service\Interfaces\TranslatorInterface;

class LlmEventsDetectionStep extends BaseEventsDetectStep
{
    public const string CALL_LLM_EVENTS_DETECTION_QUEUE = 'call-llm-events-detection-step';
    public const string CALL_LLM_EVENTS_DETECTION_ERROR_QUEUE = 'call-llm-events-detection-step-error';

    public function __construct(
        CallsTable $callsTable,
        CallFactory $callFactory,
        private readonly CallSelector $callSelector,
        private readonly CompanySelectorInterface $companySelector,
        private readonly RequestParamsBuilder $requestParamsBuilder,
        private readonly LlmEventsAlgoApiRequestCreator $llmRequestCreator,
        private readonly AiSolutionsCommutatorService $aiSolutionsCommutatorService,
        TranslatorInterface $translator,
        private readonly CallsAlgoEventsTable $callsAlgoEventsTable
    ) {
        parent::__construct($callsTable, $callFactory, $translator);
    }

    public function getQueueName(): string
    {
        return self::CALL_LLM_EVENTS_DETECTION_QUEUE;
    }

    public function getErrorQueueName(): string
    {
        return self::CALL_LLM_EVENTS_DETECTION_ERROR_QUEUE;
    }

    public function getNextStepQueue(): string
    {
        return AlgoEventsStep::CALL_ALGO_EVENTS_QUEUE;
    }

    protected function init(string $callId, int $companyId): StepInterface
    {
        $this->call = $this->callSelector->getCall($callId, $companyId);

        return $this;
    }

    /**
     * @param int $companyId
     * @return bool
     * @throws GuzzleException
     * @throws ReflectionException
     * @throws StepIsAlreadyFinishedException
     * @throws ThirdPartyApiException
     */
    public function run(int $companyId): bool
    {
        $call = $this->getCall();

        if ($call->getIsLlmEventsDetected()) {
            throw new StepIsAlreadyFinishedException('Llm events detection step is already finished');
        }

        $company = $this->companySelector->getCompany($companyId);
        $requestParams = $this->requestParamsBuilder->build($company, $call);

        $llmRequest = $this->llmRequestCreator->create($company, clone $requestParams);
        if (!$llmRequest) {
            $this->markStepAsCompleted($call);

            return true;
        }

        $algoEventsCollection = $this->aiSolutionsCommutatorService->getAlgoEventsFromCall(
            $company,
            $call,
            [$llmRequest]
        );

        $this->translateAlgoEvents($algoEventsCollection);
        $this->callsAlgoEventsTable->saveEvents($algoEventsCollection, $company->getEncryptionKey());
        $this->markStepAsCompleted($call);

        return true;
    }

    private function markStepAsCompleted(Call $call): void
    {
        $call->setIsLlmEventsDetected(true);
        $this->callsTable->saveCall($call);
    }
}
