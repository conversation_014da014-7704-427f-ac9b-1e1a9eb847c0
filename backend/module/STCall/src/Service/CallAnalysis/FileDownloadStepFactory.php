<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis;

use Interop\Container\ContainerInterface;
use Lam<PERSON>\ServiceManager\Factory\FactoryInterface;
use STCall\Data\CallsTable;
use STCall\Entity\CallFactory;
use STCall\Service\CallAnalysis\Downloading\FileDownloader;
use STCall\Service\CallErrorService;
use STCall\Service\Import\UploadService;
use STCompany\Service\CompanyService;
use STLib\Mvc\Hydrator\Hydrator;
use STRoboTruck\Service\DataCollection\DataCollector;

class FileDownloadStepFactory implements FactoryInterface
{
    /**
     * @param ContainerInterface $container
     * @param string $requestedName
     * @param array|null $options
     * @return FileDownloadStep
     */
    public function __invoke(ContainerInterface $container, $requestedName, array $options = null): FileDownloadStep
    {
        $callsTable = $container->get(CallsTable::class);
        $callFactory = $container->get(CallFactory::class);
        $fileDownloader = $container->get(FileDownloader::class);
        $hydrator = $container->get(Hydrator::class);
        $dataCollector = $container->get(DataCollector::class);
        $uploadService = $container->get(UploadService::class);
        $companyService = $container->get(CompanyService::class);

        return new FileDownloadStep(
            $callsTable,
            $callFactory,
            $fileDownloader,
            $hydrator,
            $dataCollector,
            $container->get(CallUploadValidator::class),
            $uploadService,
            $companyService
        );
    }
}
