<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis;

use Carbon\Carbon;
use GuzzleHttp\Exception\GuzzleException;
use ReflectionException;
use STAlgo\Service\AiSolutionsCommutatorService;
use STAlgo\Service\AlgoEvents\RequestCreation\NerEventsAlgoApiRequestCreator;
use STAlgo\Service\ParamsBuilding\RequestParamsBuilder;
use STApi\Entity\Exception\NotFoundApiException;
use STApi\Entity\Exception\ThirdPartyApiException;
use STCall\Data\CallsAlgoEventsTable;
use STCall\Data\CallsTable;
use STCall\Entity\AlgoEventCollection;
use STCall\Entity\CallFactory;
use STCall\Service\CallAnalysisService;
use STCall\Service\EventTriggerService;
use STCall\Service\Interfaces\TranslatorInterface;
use STCall\Service\Precalculation\CallPrecalculationManagerService;
use STCall\Service\Precalculation\CallPrecalculationManagerServiceTrait;
use STCompany\Data\CompaniesTable;
use STCompany\Entity\Company;
use STLib\Mvc\Hydrator\BaseHydratorTrait;
use STLib\Mvc\Hydrator\Hydrator;

class AlgoEventsStep extends BaseEventsDetectStep
{
    use BaseHydratorTrait;
    use CallPrecalculationManagerServiceTrait;

    public const string CALL_ALGO_EVENTS_QUEUE = 'call-algo-events-step';
    public const string CALL_ALGO_EVENTS_ERROR_QUEUE = 'call-algo-events-step-error';

    /**
     *
     * @var CompaniesTable
     */
    protected CompaniesTable $companiesTable;

    /**
     *
     * @var AiSolutionsCommutatorService
     */
    protected AiSolutionsCommutatorService $aiSolutionsCommutatorService;

    /**
     * @param CompaniesTable $companiesTable
     * @param Hydrator $hydrator
     * @param CallsTable $callsTable
     * @param CallFactory $callFactory
     * @param RequestParamsBuilder $requestParamsBuilder
     * @param NerEventsAlgoApiRequestCreator $nerRequestCreator
     * @param AiSolutionsCommutatorService $aiSolutionsCommutatorService
     * @param TranslatorInterface $translator
     * @param CallsAlgoEventsTable $callsAlgoEventsTable
     * @param EventTriggerService $eventTrigger
     */
    public function __construct(
        CompaniesTable $companiesTable,
        private Hydrator $hydrator,
        CallsTable $callsTable,
        CallFactory $callFactory,
        private readonly RequestParamsBuilder $requestParamsBuilder,
        private readonly NerEventsAlgoApiRequestCreator $nerRequestCreator,
        AiSolutionsCommutatorService $aiSolutionsCommutatorService,
        TranslatorInterface $translator,
        private readonly CallsAlgoEventsTable $callsAlgoEventsTable,
        private readonly EventTriggerService $eventTrigger
    ) {
        parent::__construct($callsTable, $callFactory, $translator);

        $this->companiesTable = $companiesTable;
        $this->aiSolutionsCommutatorService = $aiSolutionsCommutatorService;
    }

    /**
     *
     * @return string
     */
    public function getQueueName(): string
    {
        return static::CALL_ALGO_EVENTS_QUEUE;
    }

    /**
     *
     * @return string
     */
    public function getErrorQueueName(): string
    {
        return static::CALL_ALGO_EVENTS_ERROR_QUEUE;
    }

    /**
     * @return string
     */
    public function getNextStepQueue(): string
    {
        return Mp3FileConvertationStep::CALL_MP3_CONVERTATION_QUEUE;
    }

    /**
     * @param int $companyId
     * @return bool
     * @throws Exception\StepIsAlreadyFinishedException
     * @throws NotFoundApiException
     * @throws GuzzleException
     * @throws ReflectionException
     * @throws ThirdPartyApiException
     */
    public function run(int $companyId): bool
    {
        $call = $this->getCall();
        if ($call->isAnalyzed()) {
            throw new Exception\StepIsAlreadyFinishedException('Call is already analyzed');
        }

        $company = $this->getCompany($call->getCompanyId());

        $requestParams = $this->requestParamsBuilder->build($company, $call);
        $algoApiRequests = $this->nerRequestCreator->create($company, $requestParams);

        $algoEventsCollection = $this->aiSolutionsCommutatorService->getAlgoEventsFromCall(
            $company,
            $call,
            $algoApiRequests
        );

        $this->translateAlgoEvents($algoEventsCollection);

        $this->callsAlgoEventsTable->saveEvents($algoEventsCollection, $company->getEncryptionKey());

        $call->isAnalyzed(true);
        $call->setAnalyzedAt(Carbon::now());
        $this->callsTable->saveCall($call);

        // add to precalculations
        $this->getCallPrecalculationManager()->addCallsToPrecalculateQueue(
            $company->getId(),
            $call->getId(),
            CallPrecalculationManagerService::PRECALCULATE_NEW_CALL_PRIORITY,
        );

        $this->triggerStepFinishedEvent($call->getId(), $companyId, $algoEventsCollection);

        return true;
    }

    /**
     * @throws ReflectionException
     * @throws NotFoundApiException
     */
    private function getCompany(int $companyId): Company
    {
        $companyData = $this->companiesTable->getCompany($companyId);

        return $this->hydrator->hydrateClass((array) $companyData->current(), Company::class);
    }

    private function triggerStepFinishedEvent(string $callId, int $companyId, AlgoEventCollection $algoEvents): void
    {
        $eventsData = [];
        foreach ($algoEvents as $event) {
            $eventsData[] = $event->toArray();
        }

        $this->eventTrigger->trigger(
            CallAnalysisService::EVENT_CALL_ALGO_EVENTS_STEP_FINISHED,
            [
                'queue_name' => self::CALL_ALGO_EVENTS_QUEUE,
                'company_id' => $companyId,
                'call_id' => $callId,
                'data' => $eventsData
            ]
        );
    }
}
