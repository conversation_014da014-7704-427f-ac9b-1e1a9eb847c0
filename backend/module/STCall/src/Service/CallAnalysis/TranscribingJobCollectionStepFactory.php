<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis;

use Interop\Container\ContainerInterface;
use <PERSON>inas\ServiceManager\Factory\FactoryInterface;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use ST<PERSON>all\Data\CallsParagraphsTable;
use STCall\Data\CallsTable;
use ST<PERSON>all\Entity\CallFactory;
use STCall\Service\CallAnalysis\TranscribingDriver\Provider\DriverProvider;
use STCall\Service\EventTriggerService;
use STCompany\Data\CompaniesTable;
use STCompany\Service\CompanyVocabularyService;
use STLib\Mvc\Hydrator\Hydrator;
use STRoboTruck\Service\DataCollection\DataCollector;

class TranscribingJobCollectionStepFactory implements FactoryInterface
{
    /**
     * @param ContainerInterface $container
     * @param $requestedName
     * @param array|null $options
     * @return TranscribingJobCollectionStep
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function createService(
        ContainerInterface $container,
        $requestedName,
        array $options = null
    ): TranscribingJobCollectionStep {
        return $this($container, $requestedName ?: PatternPluginManager::class, $this->creationOptions);
    }

    /**
     * @param ContainerInterface $container
     * @param $requestedName
     * @param array|null $options
     * @return TranscribingJobCollectionStep
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function __invoke(
        ContainerInterface $container,
        $requestedName,
        array $options = null
    ): TranscribingJobCollectionStep {
        $callsTable = $container->get(CallsTable::class);
        $callFactory = $container->get(CallFactory::class);
        $hydrator = $container->get(Hydrator::class);
        $driverProvider = $container->get(DriverProvider::class);
        $companiesTable = $container->get(CompaniesTable::class);
        $paragraphTable = $container->get(CallsParagraphsTable::class);
        $companyVocabularyService = $container->get(CompanyVocabularyService::class);
        $dataCollector = $container->get(DataCollector::class);
        $eventTrigger = $container->get(EventTriggerService::class);
        $config = $container->get('config');
        return new TranscribingJobCollectionStep(
            $callsTable,
            $callFactory,
            $companiesTable,
            $hydrator,
            $driverProvider,
            $paragraphTable,
            $companyVocabularyService,
            $dataCollector,
            $eventTrigger,
            $config['aws'],
            $config['wordcab'],
            $config['assembly'],
            $config['transcriptor'],
            $config['speechmatics'],
            $config['salad'],
        );
    }
}
