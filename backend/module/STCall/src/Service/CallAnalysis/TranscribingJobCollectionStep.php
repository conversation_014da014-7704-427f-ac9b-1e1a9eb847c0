<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis;

use ReflectionException;
use ST<PERSON><PERSON>\Entity\Exception\BadRequestApiException;
use STApi\Entity\Exception\NotFoundApiException;
use ST<PERSON>all\Data\CallsParagraphsTable;
use ST<PERSON>all\Data\CallsTable;
use ST<PERSON>all\Entity\CallFactory;
use ST<PERSON>all\Entity\ParagraphCollection;
use STCall\Service\CallAnalysis\TranscribingDriver\Provider\DriverProvider;
use STCall\Service\CallAnalysis\TranscribingDriver\TwoStepsDriverInterface;
use STCall\Service\CallAnalysisService;
use STCall\Service\EventTriggerService;
use STCompany\Data\CompaniesTable;
use STCompany\Entity\Company;
use STCompany\Service\CompanyVocabularyService;
use STLib\Mvc\Hydrator\Hydrator;
use STRoboTruck\Service\DataCollection\DataCollector;

class TranscribingJobCollectionStep extends BaseTranscribingTwoSteps
{
    public const string CALL_TRANSCRIBING_JOB_COLLECTION_QUEUE = 'call-transcribing-job-collection-step';
    public const string CALL_TRANSCRIBING_JOB_COLLECTION_QUEUE_ERROR = 'call-transcribing-job-collection-step-error';

    public function __construct(
        CallsTable $callsTable,
        CallFactory $callFactory,
        CompaniesTable $companiesTable,
        Hydrator $hydrator,
        DriverProvider $driverProvider,
        CallsParagraphsTable $callsParagraphsTable,
        CompanyVocabularyService $companyVocabularyService,
        DataCollector $dataCollector,
        private readonly EventTriggerService $eventTrigger,
        array $awsConfig,
        array $wordcabConfig,
        array $assemblyConfig,
        array $transcriptorConfig,
        array $speechmaticsConfig,
        array $saladConfig
    ) {
        parent::__construct(
            $callsTable,
            $callFactory,
            $companiesTable,
            $hydrator,
            $driverProvider,
            $callsParagraphsTable,
            $companyVocabularyService,
            $dataCollector,
            $awsConfig,
            $wordcabConfig,
            $assemblyConfig,
            $transcriptorConfig,
            $speechmaticsConfig,
            $saladConfig
        );
    }

    /**
     *
     * @return string
     */
    public function getQueueName(): string
    {
        return static::CALL_TRANSCRIBING_JOB_COLLECTION_QUEUE;
    }

    /**
     *
     * @return string
     */
    public function getErrorQueueName(): string
    {
        return static::CALL_TRANSCRIBING_JOB_COLLECTION_QUEUE_ERROR;
    }

    /**
     *
     * @return string
     */
    public function getNextStepQueue(): string
    {
        return TranslationStep::CALL_TRANSLATION_QUEUE;
    }

    /**
     * @param int $companyId
     * @return bool
     * @throws Exception\StepIsAlreadyFinishedException
     * @throws ReflectionException
     * @throws BadRequestApiException
     * @throws NotFoundApiException
     */
    public function run(int $companyId): bool
    {
        $this->clearObject();

        $call = $this->getCall();
        if ($call->isTranscribed()) {
            throw new Exception\StepIsAlreadyFinishedException('Call is already transcribed');
        }

        /** @var TwoStepsDriverInterface $driver */
        $driver = $this->getDriver($this->driverName);
        $paragraphCollection = $driver->collectData($this->jobName);

        $this->saveTranscribedData($paragraphCollection);
        $this->reduceBalance();

        $this->triggerStepFinishedEvent($call->getId(), $companyId, $paragraphCollection);

        return true;
    }

    private function triggerStepFinishedEvent(string $callId, int $companyId, ParagraphCollection $paragraphs): void
    {
        $paragraphsData = [];
        foreach ($paragraphs as $paragraph) {
            $paragraphsData[] = $paragraph->toArray();
        }

        $this->eventTrigger->trigger(
            CallAnalysisService::EVENT_CALL_TRANSCRIBING_JOB_COLLECTION_STEP_FINISHED,
            [
                'queue_name' => self::CALL_TRANSCRIBING_JOB_COLLECTION_QUEUE,
                'company_id' => $companyId,
                'call_id' => $callId,
                'data' => $paragraphsData
            ]
        );
    }
}
