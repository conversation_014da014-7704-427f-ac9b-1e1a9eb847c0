<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis;

use Carbon\Carbon;
use ReflectionException;
use <PERSON><PERSON><PERSON>\Entity\Exception\BadRequestApiException;
use STApi\Entity\Exception\NotFoundApiException;
use ST<PERSON>all\Data\CallsParagraphsTable;
use ST<PERSON>all\Data\CallsTable;
use ST<PERSON>all\Entity\CallFactory;
use STCall\Entity\ParagraphCollection;
use STCall\Service\CallAnalysis\TranscribingDriver\AbstractDriver;
use STCall\Service\CallAnalysis\TranscribingDriver\OneStepDriverInterface;
use STCall\Service\CallAnalysis\TranscribingDriver\Provider\DriverProvider;
use STCall\Service\CallAnalysis\TranscribingDriver\TwoStepsDriverInterface;
use STCompany\Data\CompaniesTable;
use STCompany\Entity\Company;
use STCompany\Service\CompanyVocabularyService;
use STLib\Mvc\Hydrator\BaseHydratorTrait;
use STLib\Mvc\Hydrator\Hydrator;

abstract class BaseTranscribingStep extends BaseStep
{
    use BaseHydratorTrait;

    /**
     *
     * @var CompanyVocabularyService
     */
    protected CompanyVocabularyService $companyVocabularyService;

    /**
     *
     * @var CallsParagraphsTable
     */
    protected CallsParagraphsTable $callsParagraphsTable;

    /**
     *
     * @var CompaniesTable
     */
    protected CompaniesTable $companiesTable;

    /**
     *
     * @var Company|null
     */
    protected ?Company $company = null;

    /**
     *
     * @var string
     */
    protected string $driverName;

    /**
     *
     * @var array
     */
    protected array $awsConfig = [];

    /**
     *
     * @var string
     */
    protected string $nextStepQueue;

    /**
     * @param CallsTable $callsTable
     * @param CallFactory $callFactory
     * @param CompaniesTable $companiesTable
     * @param Hydrator $hydrator
     * @param DriverProvider $driverProvider
     * @param CallsParagraphsTable $callsParagraphsTable
     * @param CompanyVocabularyService $companyVocabularyService
     * @param array $awsConfig
     */
    public function __construct(
        CallsTable $callsTable,
        CallFactory $callFactory,
        CompaniesTable $companiesTable,
        private readonly Hydrator $hydrator,
        private readonly DriverProvider $driverProvider,
        CallsParagraphsTable $callsParagraphsTable,
        CompanyVocabularyService $companyVocabularyService,
        array $awsConfig,
    ) {
        parent::__construct($callsTable, $callFactory);
        $this->companyVocabularyService = $companyVocabularyService;
        $this->callsParagraphsTable = $callsParagraphsTable;
        $this->companiesTable = $companiesTable;
        $this->awsConfig = $awsConfig;
    }

    /**
     * @param string $driverName
     * @return AbstractDriver|TwoStepsDriverInterface|OneStepDriverInterface
     * @throws NotFoundApiException
     * @throws ReflectionException
     */
    protected function getDriver(string $driverName): AbstractDriver|TwoStepsDriverInterface|OneStepDriverInterface
    {
        $driverConfig = property_exists($this, $driverName . 'Config') ? $this->{$driverName . 'Config'} : [];

        return $this->driverProvider->provide(
            $driverName,
            $this->awsConfig,
            $driverConfig,
            $this->getCompany(),
            $this->getCall(),
            $this->companyVocabularyService->getCompanyVocabulary($this->getCompany()->getId())
        );
    }

    /**
     *
     * @param array $options
     * @return StepInterface
     */
    public function applyOptions(array $options = []): StepInterface
    {
        $this->driverName = $options['driver'] ?? null;
        return $this;
    }

    /**
     *
     * @return string
     */
    public function getNextStepQueue(): string
    {
        return $this->nextStepQueue;
    }

    /**
     * @return BaseTranscribingStep
     */
    protected function clearObject(): BaseTranscribingStep
    {
        $this->company = null;
        return $this;
    }

    /**
     *
     * @return Company
     * @throws NotFoundApiException
     * @throws ReflectionException
     */
    protected function getCompany(): Company
    {
        if (is_null($this->company)) {
            $companyData = $this->companiesTable->getCompany($this->getCall()->getCompanyId());
            $this->company = $this->hydrator->hydrateClass((array) $companyData->current(), Company::class);
        }
        return $this->company;
    }

    /**
     *
     * @param ParagraphCollection $paragraphCollection
     * @return bool
     * @throws BadRequestApiException
     * @throws NotFoundApiException
     * @throws ReflectionException
     */
    protected function saveTranscribedData(ParagraphCollection $paragraphCollection): bool
    {
        $this->callsParagraphsTable->saveParagraphs($paragraphCollection, $this->getCompany()->getEncryptionKey());
        $this->getCall()->isTranscribed(true);
        $this->getCall()->setTranscribingDriver($this->driverName);

        if ($this->getCall()->getTranscribedAt() === null) {
            $this->getCall()->setTranscribedAt(new Carbon());
        }

        if ($paragraphCollection->isEmpty()) {
            if ($this->getAttempt() > 0) {
                $this->getCall()
                    ->setCallStatus(CallsTable::CALL_STATUS_RINGING)
                    ->isTranslated(true)
                    ->isAnalyzed(true)
                    ->setAnalyzedAt(Carbon::now());
                $this->nextStepQueue = Mp3FileConvertationStep::CALL_MP3_CONVERTATION_QUEUE;
            } else {
                throw new BadRequestApiException('Empty response');
            }
        }
        $this->callsTable->saveCall($this->getCall());
        return true;
    }

    /**
     *
     * @return int
     * @throws NotFoundApiException
     * @throws ReflectionException
     */
    protected function reduceBalance(): int
    {
        $balance = (int) max(0, $this->getCompany()->getPaidTranscribingTime() - $this->getCall()->getDuration());
        $this->getCompany()->setPaidTranscribingTime($balance);

        $this->companiesTable
            ->partialUpdateCompany($this->getCompany()->getId(), ['paid_transcribing_time' => $balance]);

        return $balance;
    }
}
