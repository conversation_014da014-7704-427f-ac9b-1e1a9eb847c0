<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis;

use GuzzleHttp\Exception\GuzzleException;
use JsonException;
use ReflectionException;
use STAlgo\Service\AiSolutionsCommutatorService;
use <PERSON><PERSON><PERSON>\Entity\Exception\NotFoundApiException;
use STCall\Data\CallsTable;
use ST<PERSON>all\Entity\Call;
use ST<PERSON>all\Entity\CallFactory;
use STCall\Service\CallAnalysis\Checklist\ChecklistRunChecker;
use STCall\Service\CallAnalysis\Exception\StepIsAlreadyFinishedException;
use STCall\Service\CallAnalysis\Exception\StepIsFailedWithErrorFromAlgoModelException;
use STCall\Service\CallAnalysisService;
use STCall\Service\CallChecklistService;
use STCall\Service\CallService;
use STCall\Service\EventTriggerService;
use STCompany\Service\Checklist\ChecklistPointService;
use STCompany\Service\Checklist\ChecklistService;
use STCompany\Service\CompanyService;
use STRabbit\Service\ProvidesRabbit;

class ChecklistStep extends BaseStep
{
    use ProvidesRabbit;

    public const string CALL_CHECKLIST_QUEUE = 'call-checklist-step';
    public const string CALL_CHECKLIST_ERROR_QUEUE = 'call-checklist-step-error';

    protected bool $hasNextStep = true;

    public function __construct(
        protected CallService $callService,
        protected CallsTable $callsTable,
        protected CallChecklistService $callChecklistService,
        protected ChecklistService $checklistService,
        protected ChecklistPointService $checklistPointService,
        protected CallFactory $callFactory,
        protected CompanyService $companyService,
        private readonly ChecklistRunChecker $checklistRunChecker,
        protected AiSolutionsCommutatorService $aiSolutionsCommutatorService,
        private readonly EventTriggerService $eventTrigger
    ) {
        parent::__construct($callsTable, $callFactory);
    }

    /**
     *
     * @return string
     */
    public function getQueueName(): string
    {
        return static::CALL_CHECKLIST_QUEUE;
    }

    /**
     *
     * @return string
     */
    public function getErrorQueueName(): string
    {
        return static::CALL_CHECKLIST_ERROR_QUEUE;
    }

    /**
     *
     * @return string
     */
    public function getNextStepQueue(): string
    {
        return SummarizationStep::CALL_SUMMARIZATION_QUEUE;
    }

    /**
     * @param int $companyId
     * @return bool
     * @throws StepIsAlreadyFinishedException
     * @throws StepIsFailedWithErrorFromAlgoModelException
     * @throws GuzzleException
     * @throws JsonException
     * @throws ReflectionException
     * @throws NotFoundApiException
     */
    public function run(int $companyId): bool
    {
        $call = $this->getCall();

        if ($call->isChecklistCompleted()) {
            throw new Exception\StepIsAlreadyFinishedException('Checklist step is finished');
        }

        $company = $this->companyService->getCompany($companyId);

        if (!$company->isChecklistsEnabled()) {
            $this->markStepAsCompleted($call);
            return true;
        }

        $checklists = $this->checklistService->getCompanyChecklists($company->getId());
        $paragraphCollection = $this->callService->getParagraphs(
            $company,
            $call->getId(),
        );

        $data = [];
        foreach ($checklists as $checklist) {
            if (!$this->checklistRunChecker->shouldRunChecklist($call, $company, $checklist)) {
                continue;
            }

            $checklistPoints = $this->checklistPointService->getChecklistPointsByChecklistId(
                $checklist->getId(),
                $companyId
            );

            if ($checklistPoints->isEmpty()) {
                continue;
            }

            $checklistResult = $this->aiSolutionsCommutatorService->getChecklistResult(
                $call,
                $paragraphCollection,
                $checklistPoints,
                $this->companyService->getCompanyDetails($company->getId())
            );

            if (!$checklistResult->isSuccessful()) {
                throw new Exception\StepIsFailedWithErrorFromAlgoModelException();
            }

            $this->callChecklistService->saveCallChecklistPointsResults(
                $call,
                $checklistPoints,
                $checklistResult
            );

            $checklistPointsData = [];
            foreach ($checklistPoints as $checklistPoint) {
                $checklistPointsData[] = $checklistPoint->toArray();
            }

            $data[] = [
                'checklist' => $checklist->toArray(),
                'checklist_points' => $checklistPointsData,
                'checklist_result' => $checklistResult->getChecklistResult(),
            ];
        }

        $this->markStepAsCompleted($call);

        $this->triggerStepFinishedEvent($call->getId(), $companyId, $data);

        return true;
    }

    private function markStepAsCompleted(Call $call): void
    {
        $call->isChecklistCompleted(true);
        $this->callsTable->saveCall($call);
    }

    private function triggerStepFinishedEvent(string $callId, int $companyId, array $data): void
    {
        $this->eventTrigger->trigger(
            CallAnalysisService::EVENT_CALL_CHECKLIST_STEP_FINISHED,
            [
                'queue_name' => self::CALL_CHECKLIST_QUEUE,
                'company_id' => $companyId,
                'call_id' => $callId,
                'data' => $data
            ]
        );
    }

    /**
     *
     * @param array $options
     * @return StepInterface
     */
    public function applyOptions(array $options = []): StepInterface
    {
        $this->hasNextStep = !isset($options['skip_next_steps']) || !$options['skip_next_steps'];

        return $this;
    }

    public function hasNextStep(): bool
    {
        return $this->hasNextStep;
    }
}
