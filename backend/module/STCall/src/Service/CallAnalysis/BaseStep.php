<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis;

use ST<PERSON>pi\Entity\Exception\NotFoundApiException;
use STCall\Data\CallsTable;
use STCall\Entity\Call;
use ST<PERSON>all\Entity\CallFactory;

abstract class BaseStep implements StepInterface
{
    use \STLog\Service\ProviderLogger;

    /**
     *
     * @var CallsTable
     */
    protected CallsTable $callsTable;

    /**
     *
     * @var Call|null
     */
    protected ?Call $call = null;

    /**
     *
     * @var array
     */
    protected array $nextStepOptions = [];

    /**
     *
     * @var int
     */
    protected int $attempt = 0;

    /**
     *
     * @param CallsTable $callsTable
     */
    public function __construct(CallsTable $callsTable, private readonly CallFactory $callFactory)
    {
        $this->callsTable = $callsTable;
        $this->call = null;
    }

    /**
     *
     * @return Call|null
     */
    public function getCall(): ?Call
    {
        return $this->call;
    }

    /**
     *
     * @return array
     */
    public function getNextStepOptions(): array
    {
        return $this->nextStepOptions;
    }

    /**
     *
     * @return int
     */
    public function getAttempt(): int
    {
        return $this->attempt;
    }

    /**
     *
     * @param int $attempt
     * @return BaseStep
     */
    public function setAttempt(int $attempt): BaseStep
    {
        $this->attempt = $attempt;
        return $this;
    }

    /**
     *
     * @return bool
     */
    public function hasNextStep(): bool
    {
        return true;
    }

    /**
     *
     * @param string $callId
     * @param int $companyId
     * @return bool
     * @throws NotFoundApiException
     */
    public function launch(string $callId, int $companyId): bool
    {
        $this->init($callId, $companyId);
        return $this->run($companyId);
    }

    /**
     *
     * @param string $key
     * @param mixed $nextStepOption
     * @return StepInterface
     */
    public function addNextStepOption(string $key, mixed $nextStepOption): StepInterface
    {
        $this->nextStepOptions[$key] = $nextStepOption;
        return $this;
    }

    /**
     *
     * @param array $options
     * @return StepInterface
     */
    public function applyOptions(array $options = []): StepInterface
    {
        return $this;
    }

    /**
     * @param string $callId
     * @param int $companyId
     * @return StepInterface
     * @throws NotFoundApiException
     */
    protected function init(string $callId, int $companyId): StepInterface
    {
        if (!empty($callId)) {
            $callData = $this->callsTable->getCall($callId, $companyId);
            $call = $this->callFactory->createCall($callData);
            $this->call = $call;
        }

        return $this;
    }
}
