<?php

namespace STCall\Service\CallAnalysis;

use ST<PERSON>pi\Entity\Exception\CallUpload\IdenticalFileException;
use STApi\Entity\Exception\CallUpload\InvalidContentException;
use STCall\Data\CallsTable;
use <PERSON><PERSON>all\Entity\Call;

class CallUploadValidator
{
    public const int MIN_ALLOWED_CALL_DURATION = 3;
    public const int MIN_ALLOWED_CALL_SIZE_IN_BYTES = 100;

    public function __construct(
        private readonly CallsTable $callsTable,
    ) {
    }

    /**
     * @throws InvalidContentException
     * @throws IdenticalFileException
     */
    public function validate(Call $call, string $content): void
    {
        $existedCallId = $this->callsTable->getCallIdByFileHashAndDuration(
            $call->getCompanyId(),
            $call->getFileHash(),
            $call->getDuration(),
            [$call->getId()]
        );
        if ($existedCallId) {
            throw new IdenticalFileException('An identical file has been already added - ' . $existedCallId);
        }

        if ($call->getDuration() < static::MIN_ALLOWED_CALL_DURATION) {
            if ($call->getDuration() === 0) {
                throw new InvalidContentException('File is not valid');
            }

            throw new InvalidContentException('Minimal duration of file should be at least ' . static::MIN_ALLOWED_CALL_DURATION . ' sec');
        }

        if (mb_strlen($content) < static::MIN_ALLOWED_CALL_SIZE_IN_BYTES) {
            throw new InvalidContentException('Minimal size of file should be at least ' . static::MIN_ALLOWED_CALL_SIZE_IN_BYTES . ' bytes');
        }
    }
}
