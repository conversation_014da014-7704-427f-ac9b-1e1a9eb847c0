<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis;

use GuzzleHttp\Exception\GuzzleException;
use Lam<PERSON>\Hydrator\HydratorInterface;
use ReflectionException;
use ST<PERSON><PERSON>\Entity\Exception\NotFoundApiException;
use ST<PERSON><PERSON>\Entity\Exception\ThirdPartyApiException;
use STCall\Data\CallsAlgoEventsTable;
use STCall\Data\CallsParagraphsTable;
use STCall\Data\CallsTable;
use STCall\Data\TranslationDriversLanguagesTable;
use STCall\Entity\Call;
use STCall\Entity\CallFactory;
use ST<PERSON>all\Entity\Paragraph;
use STCall\Entity\ParagraphCollection;
use STCall\Service\CallAnalysis\Translation\ParagraphsTranslator;
use STCall\Service\CallAnalysisService;
use STCall\Service\EventTriggerService;
use STCompany\Data\CompaniesTable;
use STCompany\Entity\Company;
use STLib\Mvc\Hydrator\BaseHydratorTrait;
use STLib\Mvc\Hydrator\Hydrator;
use STTranslation\Service\Drivers\GoogleDriver;
use STTranslation\Service\TranslatorService;

class TranslationStep extends BaseStep
{
    use BaseHydratorTrait;

    public const string CALL_TRANSLATION_QUEUE = 'call-translation-step';
    public const string CALL_TRANSLATION_ERROR_QUEUE = 'call-translation-step-error';

    /**
     *
     * @var CompaniesTable
     */
    protected CompaniesTable $companiesTable;

    /**
     *
     * @var CallsParagraphsTable
     */
    protected CallsParagraphsTable $callsParagraphsTable;

    /**
     *
     * @var CallsAlgoEventsTable
     */
    protected CallsAlgoEventsTable $callsAlgoEventsTable;

    /**
     *
     * @var TranslationDriversLanguagesTable
     */
    protected TranslationDriversLanguagesTable $translationDriversLanguagesTable;

    /**
     *
     * @var TranslatorService
     */
    protected TranslatorService $translatorService;

    /**
     * @param CompaniesTable $companiesTable
     * @param CallsTable $callsTable
     * @param CallFactory $callFactory
     * @param HydratorInterface $hydrator
     * @param CallsParagraphsTable $callsParagraphsTable
     * @param CallsAlgoEventsTable $callsAlgoEventsTable
     * @param TranslationDriversLanguagesTable $translationDriversLanguagesTable
     * @param TranslatorService $translatorService
     * @param ParagraphsTranslator $paragraphsTranslator
     */
    public function __construct(
        CompaniesTable $companiesTable,
        CallsTable $callsTable,
        CallFactory $callFactory,
        private readonly Hydrator $hydrator,
        CallsParagraphsTable $callsParagraphsTable,
        CallsAlgoEventsTable $callsAlgoEventsTable,
        TranslationDriversLanguagesTable $translationDriversLanguagesTable,
        TranslatorService $translatorService,
        private readonly ParagraphsTranslator $paragraphsTranslator,
        private readonly EventTriggerService $eventTrigger
    ) {
        parent::__construct($callsTable, $callFactory);
        $this->companiesTable = $companiesTable;
        $this->callsParagraphsTable = $callsParagraphsTable;
        $this->callsAlgoEventsTable = $callsAlgoEventsTable;
        $this->translationDriversLanguagesTable = $translationDriversLanguagesTable;
        $this->translatorService = $translatorService;
    }

    /**
     *
     * @return string
     */
    public function getNextStepQueue(): string
    {
        return SpeakersRolesDetectionStep::CALL_SPEAKER_DETECTION_QUEUE;
    }

    /**
     *
     * @return string
     */
    public function getQueueName(): string
    {
        return static::CALL_TRANSLATION_QUEUE;
    }

    /**
     *
     * @return string
     */
    public function getErrorQueueName(): string
    {
        return static::CALL_TRANSLATION_ERROR_QUEUE;
    }

    /**
     * @param int $companyId
     * @return bool
     * @throws Exception\StepIsAlreadyFinishedException
     * @throws NotFoundApiException
     * @throws ReflectionException
     * @throws ThirdPartyApiException
     * @throws GuzzleException
     */
    public function run(int $companyId): bool
    {
        $call = $this->getCall();
        if ($call->isTranslated()) {
            throw new Exception\StepIsAlreadyFinishedException('Call is already translated');
        }
        $companyData = $this->companiesTable->getCompany($call->getCompanyId());
        /** @var Company $company */
        $company = $this->hydrator->hydrateClass((array) $companyData->current(), Company::class);

        $translationDriverName = $this->getDriverNameByLanguage($call->getLanguage());

        $paragraphCollection = $this->translateParagraphs($company, $call, $translationDriverName);

        $call->isTranslated(true);
        $call->setTranslationDriver($translationDriverName);
        $this->callsTable->saveCall($call);

        $this->triggerStepFinishedEvent($call->getId(), $companyId, $paragraphCollection);

        return true;
    }

    /**
     * @param Company $company
     * @param Call $call
     * @param string $translationDriverName
     * @return ParagraphCollection
     * @throws ReflectionException
     * @throws ThirdPartyApiException
     * @throws GuzzleException
     */
    protected function translateParagraphs(
        Company $company,
        Call $call,
        string $translationDriverName,
    ): ParagraphCollection {
        $paragraphsData = $this->callsParagraphsTable->getParagraphs($company->getId(), $call->getId(), $company->getEncryptionKey());
        $paragraphCollection = new ParagraphCollection();
        foreach ($paragraphsData as $paragraphData) {
            $paragraph = $this->hydrate($paragraphData, Paragraph::class);
            $paragraphCollection->add($paragraph);
        }

        $this->paragraphsTranslator->translate($paragraphCollection, $translationDriverName, $call);

        $this->callsParagraphsTable->saveParagraphs($paragraphCollection, $company->getEncryptionKey());

        return $paragraphCollection;
    }

    /**
     *
     * @param string|null $language
     * @return string
     */
    protected function getDriverNameByLanguage(?string $language): string
    {
        if ($language === null) {
            return GoogleDriver::DRIVER_NAME;
        }

        return $this->translationDriversLanguagesTable->getDriverNameByLanguage($language) ?? GoogleDriver::DRIVER_NAME;
    }

    private function triggerStepFinishedEvent(string $callId, int $companyId, ParagraphCollection $paragraphs): void
    {
        $paragraphsData = [];
        foreach ($paragraphs as $paragraph) {
            $paragraphsData[] = $paragraph->toArray();
        }

        $this->eventTrigger->trigger(
            CallAnalysisService::EVENT_CALL_TRANSLATION_STEP_FINISHED,
            [
                'queue_name' => self::CALL_TRANSLATION_QUEUE,
                'company_id' => $companyId,
                'call_id' => $callId,
                'data' => $paragraphsData
            ]
        );
    }
}
