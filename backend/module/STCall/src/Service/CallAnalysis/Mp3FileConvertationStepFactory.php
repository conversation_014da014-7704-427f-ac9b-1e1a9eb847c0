<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis;

use STCall\Entity\CallFactory;

class Mp3FileConvertationStepFactory implements \Laminas\ServiceManager\Factory\FactoryInterface
{
    /**
     *
     * @param \Interop\Container\ContainerInterface $container
     * @param string $requestedName
     * @param array $options
     * @return Mp3FileConvertationStep
     */
    public function createService(\Interop\Container\ContainerInterface $container, $requestedName, array $options = null): Mp3FileConvertationStep
    {
        return $this($container, $requestedName ?: PatternPluginManager::class, $this->creationOptions);
    }

    /**
     *
     * @param \Interop\Container\ContainerInterface $container
     * @param string $requestedName
     * @param array $options
     * @return FileConvertationStep
     */
    public function __invoke(\Interop\Container\ContainerInterface $container, $requestedName, array $options = null): Mp3FileConvertationStep
    {
        $callsTable = $container->get(\STCall\Data\CallsTable::class);
        $callFactory = $container->get(CallFactory::class);
        $companiesTable = $container->get(\STCompany\Data\CompaniesTable::class);
        $config = $container->get('config');
        $awsConfig = $config['aws'];
        return new Mp3FileConvertationStep(
            $callsTable,
            $callFactory,
            $companiesTable,
            $awsConfig,
        );
    }
}
