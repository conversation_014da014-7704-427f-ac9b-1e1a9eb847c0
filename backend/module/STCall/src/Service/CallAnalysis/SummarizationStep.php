<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis;

use GuzzleHttp\Exception\GuzzleException;
use JsonException;
use ReflectionException;
use ST<PERSON>pi\Entity\Exception\ThirdPartyApiException;
use ST<PERSON>all\Data\CallsTable;
use ST<PERSON>all\Entity\Call;
use ST<PERSON>all\Entity\CallFactory;
use ST<PERSON>all\Entity\CallSummarization;
use STCall\Service\CallAnalysis\CallSelection\CallSelector;
use STCall\Service\CallAnalysis\Exception\StepIsAlreadyFinishedException;
use STCall\Service\CallAnalysis\Summarization\CallSummarizationSaver;
use STCall\Service\CallAnalysis\Summarization\Interfaces\CallSummarizationGetterInterface;
use STCall\Service\CallAnalysisService;
use STCall\Service\EventTriggerService;
use STCall\Service\Interfaces\CompanySelectorInterface;
use STCall\Service\Interfaces\FeaturesAvailableCheckerInterface;
use STCompany\Entity\Company;

final class SummarizationStep extends BaseStep
{
    public const string CALL_SUMMARIZATION_QUEUE = 'call-summarization-step';
    public const string CALL_SUMMARIZATION_ERROR_QUEUE = 'call-summarization-step-error';

    public function __construct(
        CallsTable $callsTable,
        CallFactory $callFactory,
        private readonly CallSelector $callSelector,
        private readonly CompanySelectorInterface $companySelector,
        private readonly FeaturesAvailableCheckerInterface $featuresAvailableChecker,
        private readonly CallSummarizationGetterInterface $callSummarizationGetter,
        private readonly CallSummarizationSaver $callSummarizationSaver,
        private readonly EventTriggerService $eventTrigger,
    ) {
        parent::__construct($callsTable, $callFactory);
    }

    public function getQueueName(): string
    {
        return self::CALL_SUMMARIZATION_QUEUE;
    }

    public function getErrorQueueName(): string
    {
        return self::CALL_SUMMARIZATION_ERROR_QUEUE;
    }

    public function getNextStepQueue(): string
    {
        return LlmEventsDetectionStep::CALL_LLM_EVENTS_DETECTION_QUEUE;
    }

    protected function init(string $callId, int $companyId): StepInterface
    {
        $this->call = $this->callSelector->getCall($callId, $companyId);

        return $this;
    }

    /**
     * @param int $companyId
     * @return bool
     * @throws StepIsAlreadyFinishedException
     * @throws GuzzleException
     * @throws JsonException
     * @throws ReflectionException
     * @throws ThirdPartyApiException
     */
    public function run(int $companyId): bool
    {
        $call = $this->getCall();

        if ($call->isSummarizationCompleted()) {
            throw new StepIsAlreadyFinishedException('Summarization step is finished');
        }

        $company = $this->companySelector->getCompany($companyId);

        if (
            !$this->featuresAvailableChecker->isSummarizationAvailable($company)
            || ($company->getMinCallDurationForCallSummary() !== null && $call->getDuration() < $company->getMinCallDurationForCallSummary())
        ) {
            $this->markStepAsCompleted($call);

            return true;
        }

        $callSummarization = $this->callSummarizationGetter->getSummarization(
            $company,
            $call,
        );

        $this->callSummarizationSaver->saveCallSummarization($callSummarization);
        $this->markStepAsCompleted($call);

        $this->triggerStepFinishedEvent($call->getId(), $companyId, $callSummarization);

        return true;
    }

    private function markStepAsCompleted(Call $call): void
    {
        $call->isSummarizationCompleted(true);
        $this->callsTable->saveCall($call);
    }

    private function triggerStepFinishedEvent(
        string $callId,
        int $companyId,
        CallSummarization $callSummarization
    ): void {
        $this->eventTrigger->trigger(
            CallAnalysisService::EVENT_CALL_SUMMARIZATION_STEP_FINISHED,
            [
                'queue_name' => self::CALL_SUMMARIZATION_QUEUE,
                'company_id' => $companyId,
                'call_id' => $callId,
                'data' => $callSummarization->toArray()
            ]
        );
    }
}
