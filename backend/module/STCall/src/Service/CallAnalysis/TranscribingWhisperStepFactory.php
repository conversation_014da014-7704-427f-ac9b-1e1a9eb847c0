<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis;

use Interop\Container\ContainerInterface;
use <PERSON><PERSON>\ServiceManager\Factory\FactoryInterface;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use STCall\Data\CallsParagraphsTable;
use STCall\Data\CallsTable;
use ST<PERSON>all\Entity\CallFactory;
use STCall\Service\CallAnalysis\TranscribingDriver\Provider\DriverProvider;
use STCall\Service\EventTriggerService;
use STCall\Service\Interfaces\FeaturesAvailableCheckerInterface;
use STCompany\Data\CompaniesTable;
use STCompany\Service\CompanyVocabularyService;
use STLib\Mvc\Hydrator\Hydrator;
use STRoboTruck\Service\DataCollection\DataCollector;

class TranscribingWhisperStepFactory implements FactoryInterface
{
    /**
     * @param ContainerInterface $container
     * @param $requestedName
     * @param array|null $options
     * @return TranscribingWhisperStep
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function createService(ContainerInterface $container, $requestedName, array $options = null): TranscribingWhisperStep
    {
        return $this($container, $requestedName ?: PatternPluginManager::class, $this->creationOptions);
    }

    /**
     * @param ContainerInterface $container
     * @param $requestedName
     * @param array|null $options
     * @return TranscribingWhisperStep
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function __invoke(ContainerInterface $container, $requestedName, array $options = null): TranscribingWhisperStep
    {
        $callsTable = $container->get(CallsTable::class);
        $callFactory = $container->get(CallFactory::class);
        $companiesTable = $container->get(CompaniesTable::class);
        $hydrator = $container->get(Hydrator::class);
        $featuresAvailableChecker = $container->get(FeaturesAvailableCheckerInterface::class);
        $driverProvider = $container->get(DriverProvider::class);
        $paragraphTable = $container->get(CallsParagraphsTable::class);
        $companyVocabularyService = $container->get(CompanyVocabularyService::class);
        $eventTrigger = $container->get(EventTriggerService::class);
        $dataCollector = $container->get(DataCollector::class);
        $config = $container->get('config');
        $awsConfig = $config['aws'];
        $deepgramConfig = $config['deepgram'];
        return new TranscribingWhisperStep(
            $callsTable,
            $callFactory,
            $companiesTable,
            $hydrator,
            $featuresAvailableChecker,
            $driverProvider,
            $paragraphTable,
            $companyVocabularyService,
            $eventTrigger,
            $dataCollector,
            $awsConfig,
            $deepgramConfig,
        );
    }
}
