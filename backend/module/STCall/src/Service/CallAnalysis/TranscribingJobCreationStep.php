<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis;

use ReflectionException;
use ST<PERSON><PERSON>\Entity\Exception\CallUpload\TooLowBalanceForAnalyzeException;
use ST<PERSON><PERSON>\Entity\Exception\NotFoundApiException;
use ST<PERSON>all\Data\CallsParagraphsTable;
use ST<PERSON>all\Data\CallsTable;
use ST<PERSON>all\Entity\CallFactory;
use STCall\Service\CallAnalysis\TranscribingDriver\Provider\DriverProvider;
use STCall\Service\CallAnalysis\TranscribingDriver\TwoStepsDriverInterface;
use STCall\Service\Interfaces\FeaturesAvailableCheckerInterface;
use STCompany\Data\CompaniesTable;
use STCompany\Service\CompanyVocabularyService;
use STLib\Mvc\Hydrator\Hydrator;
use STRoboTruck\Service\DataCollection\DataCollector;

class TranscribingJobCreationStep extends BaseTranscribingTwoSteps
{
    public function __construct(
        CallsTable $callsTable,
        CallFactory $callFactory,
        CompaniesTable $companiesTable,
        Hydrator $hydrator,
        private readonly FeaturesAvailableCheckerInterface $featuresAvailable<PERSON>hecker,
        DriverProvider $driverProvider,
        CallsParagraphsTable $callsParagraphsTable,
        CompanyVocabularyService $companyVocabularyService,
        DataCollector $dataCollector,
        array $awsConfig,
        array $wordcabConfig,
        array $assemblyConfig,
        array $transcriptorConfig,
        array $speechmaticsConfig,
        array $saladConfig
    ) {
        parent::__construct(
            $callsTable,
            $callFactory,
            $companiesTable,
            $hydrator,
            $driverProvider,
            $callsParagraphsTable,
            $companyVocabularyService,
            $dataCollector,
            $awsConfig,
            $wordcabConfig,
            $assemblyConfig,
            $transcriptorConfig,
            $speechmaticsConfig,
            $saladConfig
        );
    }

    public const CALL_TRANSCRIBING_JOB_CREATION_QUEUE = 'call-transcribing-job-creation-step';
    public const CALL_TRANSCRIBING_JOB_CREATION_QUEUE_ERROR = 'call-transcribing-job-creation-step-error';

    /**
     *
     * @param int $companyId
     * @return bool
     * @throws Exception\StepIsAlreadyFinishedException
     * @throws TooLowBalanceForAnalyzeException
     * @throws NotFoundApiException
     * @throws ReflectionException
     */
    public function run(int $companyId): bool
    {
        $this->clearObject();
        $call = $this->getCall();

        if ($call->isTranscribed()) {
            throw new Exception\StepIsAlreadyFinishedException('Call is already transcribed');
        }

        if (!$this->featuresAvailableChecker->isCallAnalysisAvailableForCall($call, $this->getCompany())) {
            throw new TooLowBalanceForAnalyzeException('Company does not have enough time to transcribe this call');
        }

        /** @var TwoStepsDriverInterface $driver */
        $driver = $this->getDriver($this->driverName);
        $jobName = $driver->createJob();
        $this->addNextStepOption('driver', $this->driverName);
        $this->addNextStepOption('job_name', $jobName);

        $this->dataCollector->collect(
            DataCollector::EVENT_CALL_ANALYZE_TRANSCRIBING_JOB_CREATION_STEP_SUCCESS,
            'Job name is ' . $jobName,
            [
                'id' => null,
                'company_id' => $companyId,
                'call_id' => $call->getId(),
            ],
            'api-calls-logs'
        );

        return true;
    }

    /**
     *
     * @return string
     */
    public function getQueueName(): string
    {
        return static::CALL_TRANSCRIBING_JOB_CREATION_QUEUE;
    }

    /**
     *
     * @return string
     */
    public function getErrorQueueName(): string
    {
        return static::CALL_TRANSCRIBING_JOB_CREATION_QUEUE_ERROR;
    }

    /**
     *
     * @return string
     */
    public function getNextStepQueue(): string
    {
        return TranscribingJobCollectionStep::CALL_TRANSCRIBING_JOB_COLLECTION_QUEUE;
    }
}
