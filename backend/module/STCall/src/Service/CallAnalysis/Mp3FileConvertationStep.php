<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis;

use ST<PERSON>all\Entity\CallFactory;
use ST<PERSON>all\Helper\AwsHelper;

class Mp3FileConvertationStep extends BaseStep
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;
    use \STCall\Service\AwsTrait;

    public const CALL_MP3_CONVERTATION_QUEUE = 'call-mp3-file-convertation-step';
    public const CALL_MP3_CONVERTATION_ERROR_QUEUE = 'call-mp3-file-convertation-step-error';

    public const MP3_FILE_FORMAT = 'mp3';
    protected const MP3_MIME_TYPES = [
        'audio/mpeg',
        'audio/mp3',
    ];
    protected const FORCE_CONVERT_MIME_TYPES = [
        'application/octet-stream',
        'audio/x-gsm',
        'audio/x-wav',
    ];

    /**
     *
     * @var \STCompany\Data\CompaniesTable
     */
    protected \STCompany\Data\CompaniesTable $companiesTable;

    /**
     *
     * @param \STCall\Data\CallsTable $callsTable
     * @param \STCompany\Data\CompaniesTable $companiesTable
     * @param array $awsConfig
     * @throws \UnexpectedValueException
     */
    public function __construct(
        \STCall\Data\CallsTable $callsTable,
        CallFactory $callFactory,
        \STCompany\Data\CompaniesTable $companiesTable,
        array $awsConfig,
    ) {
        if (!isset($awsConfig['api']) || !isset($awsConfig['env'])) {
            throw new \UnexpectedValueException('AWS config must contain "api" and "env" settings');
        }
        parent::__construct($callsTable, $callFactory);
        $this->companiesTable = $companiesTable;
        $this->awsConfig = $awsConfig['api'];
        $this->env = $awsConfig['env'];
    }

    /**
     *
     * @param int $companyId
     * @return bool
     */
    public function run(int $companyId): bool
    {
        $call = $this->getCall();

        // skip step if chat call
        if ($call instanceof \STCall\Entity\ChatCall) {
            return true;
        }

        // skip step if call was add as s3 integration
        if ($call->getOrigin() === \STCall\Data\CallsTable::CALL_ORIGIN_S3) {
            return true;
        }

        $companyData = $this->companiesTable->getCompany($call->getCompanyId());
        $company = $this->hydrate((array) $companyData->current(), \STCompany\Entity\Company::class);
        $this->setCompany($company);

        $originalFileName = $call->getS3FilePath();
        $filePath = $this->createTemporaryFile();
        file_put_contents($filePath, $this->getFileFromS3ByFileName($originalFileName));
        $fileSize = filesize($filePath);

        $mimeType = mime_content_type($filePath);
        if (!in_array($mimeType, static::MP3_MIME_TYPES)) {
            $convertedMp3FileName = AwsHelper::getAwsCompatibleFileName($call->getId()) . '.' . static::MP3_FILE_FORMAT;
            $convertedMp3FilePath = sys_get_temp_dir() . '/' . $convertedMp3FileName;

            $ffmpeg = \FFMpeg\FFMpeg::create();
            $format = new \FFMpeg\Format\Audio\Mp3();
            $format
                ->setAudioChannels(1)
                ->setAudioKiloBitrate(16);
            $ffmpeg->open($filePath)->save($format, $convertedMp3FilePath);

            $convertedMp3FileSize = filesize($convertedMp3FilePath);
            if (in_array($mimeType, static::FORCE_CONVERT_MIME_TYPES) || $convertedMp3FileSize < $fileSize) {
                $convertedMp3FileContent = file_get_contents($convertedMp3FilePath);
                unlink($convertedMp3FilePath);

                $this->saveFileInS3($convertedMp3FileContent, $convertedMp3FileName);

                $call->setS3FilePath($convertedMp3FileName);
                $this->callsTable->saveCall($call);

                if ($convertedMp3FileName !== $originalFileName) {
                    $this->deleteFileFromS3($originalFileName);
                }
            }
        }

        unlink($filePath);

        return true;
    }

    /**
     *
     * @return string
     */
    public function getQueueName(): string
    {
        return static::CALL_MP3_CONVERTATION_QUEUE;
    }

    /**
     *
     * @return string
     */
    public function getErrorQueueName(): string
    {
        return static::CALL_MP3_CONVERTATION_ERROR_QUEUE;
    }

    /**
     *
     * @return bool
     */
    public function hasNextStep(): bool
    {
        return false;
    }

    /**
     *
     * @return string
     */
    public function getNextStepQueue(): string
    {
        throw new \RuntimeException('Next step is unavailable for precalculation step');
    }

    /**
     *
     * @return string
     */
    protected function createTemporaryFile(): string
    {
        $file = tmpfile();
        return stream_get_meta_data($file)['uri'];
    }
}
