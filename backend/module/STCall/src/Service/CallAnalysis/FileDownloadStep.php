<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis;

use Exception;
use GuzzleHttp\Exception\GuzzleException;
use RuntimeException;
use ST<PERSON><PERSON>\Entity\Exception\CallUpload\FileIsTooBigException;
use ST<PERSON><PERSON>\Entity\Exception\NoAccessToFileApiException;
use ST<PERSON>all\Data\CallsTable;
use ST<PERSON>all\Entity\CallFactory;
use STCall\Service\AwsTrait;
use STCall\Service\CallAnalysis\Downloading\FileDownloader;
use STCall\Service\Import\CallSaving\Result\Result;
use STCall\Service\Import\UploadParams\UploadParams;
use STCall\Service\Import\UploadService;
use STCompany\Entity\Company;
use STCompany\Service\CompanyService;
use STLib\Mvc\Hydrator\Hydrator;
use STRoboTruck\Service\DataCollection\DataCollector;

class FileDownloadStep extends BaseStep implements StepWithWaitingRoomInterface
{
    use AwsTrait;
    use StepWithWaitingRoom;

    public const string CALL_FILE_DOWNLOAD_QUEUE = 'call-file-download-step';
    public const string CALL_FILE_DOWNLOAD_ERROR_QUEUE = 'call-file-download-step-error';

    public const string CALL_FILE_WAITING_QUEUE = 'call-file-download-step-waiting';

    public const string CALL_FILE_WAITING_ERROR_QUEUE = 'call-file-download-step-waiting-error';
    /**
     *
     * @var UploadService
     */
    protected UploadService $uploadService;

    /**
     *
     * @var CompanyService
     */
    protected CompanyService $companyService;


    /**
     *
     * @var array
     */
    protected array $callParams = [];

    /**
     *
     * @var string|null
     */
    protected ?string $requestId = null;

    /**
     *
     * @var bool
     */
    protected bool $hasNextStep;

    public function __construct(
        CallsTable $callsTable,
        CallFactory $callFactory,
        private FileDownloader $fileDownloader,
        private Hydrator $hydrator,
        private DataCollector $dataCollector,
        private CallUploadValidator $callUploadValidator,
        UploadService $uploadService,
        CompanyService $companyService,
    ) {
        parent::__construct($callsTable, $callFactory);
        $this->uploadService = $uploadService;
        $this->companyService = $companyService;
        $this->waitingQueueName = self::CALL_FILE_WAITING_QUEUE;
        $this->waitingErrorQueueName = self::CALL_FILE_WAITING_ERROR_QUEUE;
    }

    /**
     *
     * @param int $companyId
     * @return bool
     * @throws NoAccessToFileApiException
     * @throws Exception
     * @throws GuzzleException
     */
    public function run(int $companyId): bool
    {
        $company = $this->companyService->getCompany($companyId);

        if (isset($this->callParams['recording_file'])) {
            $driverName = 'api-upload';

            try {
                $content = $this->fileDownloader->getCallFileContent($this->callParams['recording_file']);
            } catch (NoAccessToFileApiException | FileIsTooBigException $e) {
                $this->uploadCall($driverName, $company);

                throw $e;
            }
        } else {
            throw new RuntimeException('Incorrect request. There is no "recording_file" param');
        }

        try {
            $uploadResult = $this->uploadCall($driverName, $company, $content);
            $call = $uploadResult->getCall();

            if (!$uploadResult->isUpdate()) {
                $this->callUploadValidator->validate($call, $content);
            }

            $this->hasNextStep = $call->isSentToTranscribing() && !$uploadResult->isUpdate();
        } catch (Exception $e) {
            $this->dataCollector->collect(
                DataCollector::EVENT_CALL_UPLOAD_FILE_DOWNLOAD_STEP_ERROR,
                json_encode(['code' => $e->getCode(), 'error' => $e->getMessage()]),
                [
                    'id' => $this->requestId,
                    'company_id' => $company->getId(),
                    'call_id' => null,
                ],
                'api-calls-logs'
            );

            throw $e;
        }

        $this->call = $call;

        $this->dataCollector->collect(
            DataCollector::EVENT_CALL_UPLOAD_FILE_DOWNLOAD_STEP_SUCCESS,
            json_encode($call->toArray()),
            [
                'id' => $this->requestId,
                'company_id' => $companyId,
                'call_id' => null,
            ],
            'api-calls-logs'
        );

        return true;
    }

    /**
     *
     * @param array $options
     * @return StepInterface
     */
    public function applyOptions(array $options = []): StepInterface
    {
        $this->callParams = (array) $options['params'] ?? [];
        $this->requestId = $options['request_id'];
        return $this;
    }

    /**
     *
     * @return string
     */
    public function getNextStepQueue(): string
    {
        return LanguageDetectionStep::CALL_LANGUAGE_DETECTION_QUEUE;
    }

    /**
     *
     * @return string
     */
    public function getQueueName(): string
    {
        return static::CALL_FILE_DOWNLOAD_QUEUE;
    }

    /**
     *
     * @return string
     */
    public function getErrorQueueName(): string
    {
        return static::CALL_FILE_DOWNLOAD_ERROR_QUEUE;
    }

    /**
     *
     * @return bool
     */
    public function hasNextStep(): bool
    {
        return $this->hasNextStep;
    }

    protected function uploadCall(string $driverName, Company $company, ?string $content = null): Result
    {
        /** @var UploadParams $uploadParams */
        $uploadParams = $this->hydrator->hydrateClass(
            [
                'driver_name' => $driverName,
                'content' => $content,
                'sourced_content' => $content,
                'company' => $company,
                'user' => null,
                'options' => $this->callParams,
            ],
            UploadParams::class
        );

        return $this->uploadService->uploadCall($uploadParams);
    }

    protected function init(string $callId, int $companyId): StepInterface
    {
        return $this;
    }
}
