<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis;

use GuzzleHttp\Exception\GuzzleException;
use STApi\Entity\Exception\ThirdPartyApiException;
use STCall\Data\CallsTable;
use ST<PERSON>all\Entity\AlgoEventCollection;
use ST<PERSON>all\Entity\CallFactory;
use STCall\Service\Interfaces\TranslatorInterface;

abstract class BaseEventsDetectStep extends BaseStep
{
    public function __construct(
        CallsTable $callsTable,
        CallFactory $callFactory,
        private readonly TranslatorInterface $translator,
    ) {
        parent::__construct($callsTable, $callFactory);
    }

    /**
     * @param AlgoEventCollection $algoEventCollection
     * @return AlgoEventCollection
     * @throws GuzzleException
     * @throws ThirdPartyApiException
     */
    protected function translateAlgoEvents(
        AlgoEventCollection $algoEventCollection,
    ): AlgoEventCollection {
        foreach ($algoEventCollection as $algoEvent) {
            $algoEvent->setEnMainPointPhrase(
                $this->translator->translate($algoEvent->getMainPointPhrase())
            );
        }
        return $algoEventCollection;
    }
}
