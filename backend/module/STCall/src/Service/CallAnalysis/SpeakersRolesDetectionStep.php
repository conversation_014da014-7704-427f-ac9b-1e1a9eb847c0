<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis;

use GuzzleHttp\Exception\GuzzleException;
use JsonException;
use ST<PERSON><PERSON>go\Service\AiSolutionsCommutatorService;
use ST<PERSON>all\Data\CallsParagraphsTable;
use ST<PERSON>all\Data\CallsTable;
use <PERSON><PERSON>all\Entity\Call;
use ST<PERSON>all\Entity\CallFactory;
use ST<PERSON>all\Entity\Paragraph;
use STCall\Entity\ParagraphCollection;
use STCall\Service\CallAnalysis\Exception\StepIsAlreadyFinishedException;
use STCompany\Data\CompaniesTable;
use STCompany\Entity\Company;
use STLib\Mvc\Hydrator\BaseHydratorTrait;
use STRabbit\Service\ProvidesRabbit;

class SpeakersRolesDetectionStep extends BaseStep
{
    use ProvidesRabbit;
    use BaseHydratorTrait;

    protected const AGENT_NAME = 'agent';
    protected const CLIENT_NAME = 'client';

    public const CALL_SPEAKER_DETECTION_QUEUE = 'call-speakers-roles-detection-step';
    public const CALL_SPEAKER_DETECTION_ERROR_QUEUE = 'call-speakers-roles-detection-step-error';

    /**
     * @var AiSolutionsCommutatorService
     */
    protected AiSolutionsCommutatorService $aiSolutionsCommutatorService;

    /**
     * @var CallsParagraphsTable
     */
    protected CallsParagraphsTable $callsParagraphsTable;

    /**
     * @var CompaniesTable
     */
    protected CompaniesTable $companiesTable;

    /**
     *
     * @param CallsTable $callsTable
     * @param CallsParagraphsTable $callsParagraphsTable
     * @param CompaniesTable $companiesTable
     * @param AiSolutionsCommutatorService $aiSolutionsCommutatorService
     */
    public function __construct(
        CallsTable $callsTable,
        CallFactory $callFactory,
        CallsParagraphsTable $callsParagraphsTable,
        CompaniesTable $companiesTable,
        AiSolutionsCommutatorService $aiSolutionsCommutatorService,
    ) {
        parent::__construct($callsTable, $callFactory);
        $this->aiSolutionsCommutatorService = $aiSolutionsCommutatorService;
        $this->callsParagraphsTable = $callsParagraphsTable;
        $this->companiesTable = $companiesTable;
    }

    /**
     *
     * @param int $companyId
     * @return bool
     * @throws StepIsAlreadyFinishedException
     * @throws GuzzleException
     * @throws JsonException
     * @throws \ReflectionException
     * @throws \STApi\Entity\Exception\NotFoundApiException
     */
    public function run(int $companyId): bool
    {
        $call = $this->getCall();

        if ($call->isSpeakersRolesDetected()) {
            throw new Exception\StepIsAlreadyFinishedException('Speakers roles already detected');
        }

        if ($call->getCallType() !== Call::CALL_TYPE) {
            $call->isSpeakersRolesDetected(true);
            $this->callsTable->saveCall($call);
            return true;
        }

        $companyData = $this->companiesTable->getCompany($call->getCompanyId());
        /** @var Company $company */
        $company = $this->hydrate((array) $companyData->current(), Company::class);

        $paragraphsData = $this->callsParagraphsTable->getParagraphs(
            $company->getId(),
            $call->getId(),
            $company->getEncryptionKey()
        );
        $paragraphCollection = new ParagraphCollection();

        foreach ($paragraphsData as $paragraphData) {
            $paragraph = $this->hydrate($paragraphData, Paragraph::class);
            $paragraphCollection->add($paragraph);
        }

        $callSpeakersRoles = $this->getCallSpeakersRoles($call, $paragraphCollection);

        foreach ($paragraphCollection as $paragraph) {
            $speakerRole = match (array_search($paragraph->getSpeakerNumber(), $callSpeakersRoles)) {
                static::CLIENT_NAME => Call::CALL_SPEAKER_ROLE_CLIENT,
                static::AGENT_NAME => Call::CALL_SPEAKER_ROLE_AGENT,
                default => Call::CALL_SPEAKER_ROLE_UNCLEAR,
            };
            $paragraph->setSpeakerRole($speakerRole);
        }

        $this->callsParagraphsTable->saveParagraphs($paragraphCollection, $company->getEncryptionKey());

        $call->isSpeakersRolesDetected(true);
        $this->callsTable->saveCall($call);

        return true;
    }

    /**
     * @param Call $call
     * @param ParagraphCollection $paragraphs
     * @return array
     * @throws JsonException
     * @throws GuzzleException
     */
    protected function getCallSpeakersRoles(Call $call, ParagraphCollection $paragraphs): array
    {
        $call->setParagraphs($paragraphs);

        $response = $this->aiSolutionsCommutatorService->getParagraphsSpeakersRoles($call, $paragraphs);

        if ($response->isSuccessful()) {
            return $response->results;
        }

        return $this->detectSpeakersByVolume($call);
    }

    /**
     * @param Call $call
     * @return array
     */
    protected function detectSpeakersByVolume(Call $call): array
    {
        $speakers = [];
        if (!($call->getParagraphs() instanceof ParagraphCollection) || $call->getParagraphs()->isEmpty()) {
            return $speakers;
        }

        $speakersSpeechLength = [];

        /** @var Paragraph $paragraph */
        foreach ($call->getParagraphs() as $paragraph) {
            if (!isset($speakersSpeechLength[$paragraph->getSpeakerNumber()])) {
                $speakersSpeechLength[$paragraph->getSpeakerNumber()] = 0;
            }

            $speakersSpeechLength[$paragraph->getSpeakerNumber()] += mb_strlen((string) $paragraph->getEnText());
        }

        arsort($speakersSpeechLength, SORT_NUMERIC);
        $sortedSpeakerByVolume = array_keys($speakersSpeechLength);

        $agentSpeakerNumber = array_shift($sortedSpeakerByVolume);
        $clientSpeakerNumber = array_shift($sortedSpeakerByVolume);
        if (is_int($agentSpeakerNumber)) {
            $speakers[static::AGENT_NAME] = $agentSpeakerNumber;
        }
        if (is_int($clientSpeakerNumber)) {
            $speakers[static::CLIENT_NAME] = $clientSpeakerNumber;
        }

        return $speakers;
    }

    /**
     *
     * @return string
     */
    public function getQueueName(): string
    {
        return static::CALL_SPEAKER_DETECTION_QUEUE;
    }

    /**
     *
     * @return string
     */
    public function getErrorQueueName(): string
    {
        return static::CALL_SPEAKER_DETECTION_ERROR_QUEUE;
    }

    /**
     *
     * @return string
     */
    public function getNextStepQueue(): string
    {
        return ChecklistStep::CALL_CHECKLIST_QUEUE;
    }
}
