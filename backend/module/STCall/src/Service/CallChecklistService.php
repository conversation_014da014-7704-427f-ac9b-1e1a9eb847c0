<?php

declare(strict_types=1);

namespace STCall\Service;

use Carbon\Carbon;
use STAlgo\DTO\AlgoApiChecklistResponse;
use STCall\Data\CallsChecklistsPointsTable;
use STCall\Entity\Call;
use STCall\Helper\ChecklistCallsStatisticsHelper;
use STCall\Service\Interfaces\UserTeamIdsSelectorInterface;
use STCompany\Entity\Checklist\ChecklistPointCollection;

class CallChecklistService
{
    public function __construct(
        private readonly UserTeamIdsSelectorInterface $userTeamIdsSelector,
        private readonly CallsChecklistsPointsTable $callsChecklistsPointsTable,
    ) {
    }

    public function getAgentsTotalStatistics(
        int $companyId,
        int $userId,
        ?int $checklistId = null,
        ?int $agentId = null,
        Carbon $startDate = null,
        Carbon $endDate = null,
    ): array {
        $teamIds = $this->userTeamIdsSelector->getUserTeamIds($companyId, $userId);

        $agentsCallsSummary = $this->callsChecklistsPointsTable->getAgentsCallsSummary(
            $companyId,
            $teamIds,
            $checklistId,
            $agentId,
            $startDate,
            $endDate
        );
        $totalStatistics = $this->callsChecklistsPointsTable->getAgentsTotalStatistics(
            $companyId,
            $teamIds,
            $checklistId,
            $agentId,
            $startDate,
            $endDate
        );

        return $this->processTotalStatistics(
            $totalStatistics,
            $agentsCallsSummary,
            'agent_id',
            ['agent_name', 'agent_id'],
            ['agent_name', 'agent_id']
        );
    }

    public function getAgentCallsStatistics(
        int $agentId,
        int $companyId,
        int $userId,
        ?int $checklistId = null,
        Carbon $startDate = null,
        Carbon $endDate = null,
    ): array {
        $teamIds = $this->userTeamIdsSelector->getUserTeamIds($companyId, $userId);

        $data = [];
        $data['total'] = $this->getAgentsTotalStatistics($companyId, $userId, $checklistId, $agentId, $startDate, $endDate);
        $callsStatistics = $this->callsChecklistsPointsTable->getAgentCallsStatistics(
            $companyId,
            $teamIds,
            $agentId,
            $checklistId,
            $startDate,
            $endDate
        );

        $data['calls'] = $this->processCallsStatistics($callsStatistics);

        return $data;
    }

    public function getClientsTotalStatistics(
        int $companyId,
        int $userId,
        ?int $checklistId = null,
        ?string $clientId = null,
        Carbon $startDate = null,
        Carbon $endDate = null,
    ): array {
        $callsSummary = $this->callsChecklistsPointsTable->getClientsCallsSummary(
            $companyId,
            $this->userTeamIdsSelector->getUserTeamIds($companyId, $userId),
            $checklistId,
            $clientId,
            $startDate,
            $endDate
        );
        $totalStatistics = $this->callsChecklistsPointsTable->getClientsTotalStatistics(
            $companyId,
            $this->userTeamIdsSelector->getUserTeamIds($companyId, $userId),
            $checklistId,
            $clientId,
            $startDate,
            $endDate
        );

        return $this->processTotalStatistics(
            $totalStatistics,
            $callsSummary,
            'client_id',
            ['client_name', 'client_id'],
            ['client_name', 'client_id']
        );
    }

    public function getClientCallsStatistics(
        string $clientId,
        int $companyId,
        int $userId,
        ?int $checklistId = null,
        Carbon $startDate = null,
        Carbon $endDate = null,
    ): array {
        $data = [];
        $data['total'] = $this->getClientsTotalStatistics($companyId, $userId, $checklistId, $clientId, $startDate, $endDate);
        $callsStatistics = $this->callsChecklistsPointsTable->getClientCallsStatistics(
            $companyId,
            $this->userTeamIdsSelector->getUserTeamIds($companyId, $userId),
            $clientId,
            $checklistId,
            $startDate,
            $endDate
        );

        $data['calls'] = $this->processCallsStatistics($callsStatistics);

        return $data;
    }

    public function getCampaignsTotalStatistics(
        int $companyId,
        int $userId,
        ?int $checklistId = null,
        ?string $campaignId = null,
        Carbon $startDate = null,
        Carbon $endDate = null,
    ): array {
        $callsSummary = $this->callsChecklistsPointsTable->getCampaignsCallsSummary(
            $companyId,
            $this->userTeamIdsSelector->getUserTeamIds($companyId, $userId),
            $checklistId,
            $campaignId,
            $startDate,
            $endDate
        );
        $totalStatistics = $this->callsChecklistsPointsTable->getCampaignsTotalStatistics(
            $companyId,
            $this->userTeamIdsSelector->getUserTeamIds($companyId, $userId),
            $checklistId,
            $campaignId,
            $startDate,
            $endDate
        );

        return $this->processTotalStatistics(
            $totalStatistics,
            $callsSummary,
            'campaign_id',
            ['campaign_id'],
            ['campaign_id']
        );
    }

    public function getCampaignCallsStatistics(
        string $campaignId,
        int $userId,
        int $companyId,
        ?int $checklistId = null,
        Carbon $startDate = null,
        Carbon $endDate = null,
    ): array {
        $data = [];
        $data['total'] = $this->getCampaignsTotalStatistics($companyId, $userId, $checklistId, $campaignId, $startDate, $endDate);
        $callsStatistics = $this->callsChecklistsPointsTable->getCampaignCallsStatistics(
            $companyId,
            $this->userTeamIdsSelector->getUserTeamIds($companyId, $userId),
            $campaignId,
            $checklistId,
            $startDate,
            $endDate
        );

        $data['calls'] = $this->processCallsStatistics($callsStatistics);

        return $data;
    }

    public function getSourcesTotalStatistics(
        int $companyId,
        int $userId,
        ?int $checklistId = null,
        ?string $source = null,
        Carbon $startDate = null,
        Carbon $endDate = null,
    ): array {
        $callsSummary = $this->callsChecklistsPointsTable->getSourcesCallsSummary(
            $companyId,
            $this->userTeamIdsSelector->getUserTeamIds($companyId, $userId),
            $checklistId,
            $source,
            $startDate,
            $endDate
        );
        $totalStatistics = $this->callsChecklistsPointsTable->getSourcesTotalStatistics(
            $companyId,
            $this->userTeamIdsSelector->getUserTeamIds($companyId, $userId),
            $checklistId,
            $source,
            $startDate,
            $endDate
        );

        return $this->processTotalStatistics(
            $totalStatistics,
            $callsSummary,
            'source',
            ['source'],
            ['source']
        );
    }

    public function getSourceCallsStatistics(
        string $source,
        int $companyId,
        int $userId,
        ?int $checklistId = null,
        Carbon $startDate = null,
        Carbon $endDate = null,
    ): array {
        $data = [];
        $data['total'] = $this->getSourcesTotalStatistics($companyId, $userId, $checklistId, $source, $startDate, $endDate);
        $callsStatistics = $this->callsChecklistsPointsTable->getSourceCallsStatistics(
            $companyId,
            $this->userTeamIdsSelector->getUserTeamIds($companyId, $userId),
            $source,
            $checklistId,
            $startDate,
            $endDate
        );

        $data['calls'] = $this->processCallsStatistics($callsStatistics);

        return $data;
    }

    public function getTeamsTotalStatistics(
        int $companyId,
        int $userId,
        ?int $checklistId = null,
        ?int $teamId = null,
        Carbon $startDate = null,
        Carbon $endDate = null,
    ): array {
        $callsSummary = $this->callsChecklistsPointsTable->getTeamsCallsSummary(
            $companyId,
            $this->userTeamIdsSelector->getUserTeamIds($companyId, $userId),
            $checklistId,
            $teamId,
            $startDate,
            $endDate
        );
        $totalStatistics = $this->callsChecklistsPointsTable->getTeamsTotalStatistics(
            $companyId,
            $this->userTeamIdsSelector->getUserTeamIds($companyId, $userId),
            $checklistId,
            $teamId,
            $startDate,
            $endDate
        );

        return $this->processTotalStatistics(
            $totalStatistics,
            $callsSummary,
            'team_id',
            ['team_id', 'team_name'],
            ['team_id', 'team_name']
        );
    }

    public function getTeamCallsStatistics(
        int $teamId,
        int $companyId,
        int $userId,
        ?int $checklistId = null,
        Carbon $startDate = null,
        Carbon $endDate = null,
    ): array {
        $data = [];
        $data['total'] = $this->getTeamsTotalStatistics($companyId, $userId, $checklistId, $teamId, $startDate, $endDate);
        $callsStatistics = $this->callsChecklistsPointsTable->getTeamCallsStatistics(
            $companyId,
            $this->userTeamIdsSelector->getUserTeamIds($companyId, $userId),
            $teamId,
            $checklistId,
            $startDate,
            $endDate
        );

        $data['calls'] = $this->processCallsStatistics($callsStatistics);

        return $data;
    }

    public function getCallsStatistics(
        int $companyId,
        int $userId,
        ?int $checklistId = null,
        Carbon $startDate = null,
        Carbon $endDate = null,
    ): array {
        $data = [];
        $data['summary'] = $this->callsChecklistsPointsTable->getCallsSummary(
            $companyId,
            $this->userTeamIdsSelector->getUserTeamIds($companyId, $userId),
            $checklistId,
            $startDate,
            $endDate
        );

        $callsStatistics = $this->callsChecklistsPointsTable->getCallsStatistics(
            $companyId,
            $this->userTeamIdsSelector->getUserTeamIds($companyId, $userId),
            $checklistId,
            $startDate,
            $endDate
        );

        $data['calls'] = $this->processCallsStatistics($callsStatistics);

        return $data;
    }

    public function saveCallChecklistPointsResults(
        Call $call,
        ChecklistPointCollection $checklistPoints,
        AlgoApiChecklistResponse $checklistResultResponse,
    ): void {
        $callChecklistPoints = [];

        foreach ($checklistPoints as $checklistPoint) {
            $checklistPointResult = $checklistResultResponse->getChecklistPointResult($checklistPoint->getTitle());

            if ($checklistPointResult === null) {
                continue;
            }

            $assessmentType = (int) $checklistPointResult['assessment_type'];

            if ($assessmentType === 2) {
                $isPassed = true;
                $status = CallsChecklistsPointsTable::STATUS_PASSED;
            } elseif ($assessmentType === 0) {
                $isPassed = false;
                $status = CallsChecklistsPointsTable::STATUS_NOT_PASSED;
            } else { // $assessmentType === 1
                $isPassed = false;
                $status = CallsChecklistsPointsTable::STATUS_NOT_ENCOUNTERED;
            }
            $callChecklistPoints[] = [
                'call_id' => $call->getId(),
                'call_duration' => $call->getDuration(),
                'call_time' => $call->getTime(),
                'call_type' => $call->getCallType(),
                'company_id' => $call->getCompanyId(),
                'agent_id' => $call->getAgentId(),
                'client_id' => $call->getClientId(),
                'checklist_point_id' => $checklistPoint->getId(),
                'is_passed' => $isPassed,
                'status' => $status,
                'explanation' => $checklistPointResult['cot'],
            ];
            $callChecklistPointsIds[] = $checklistPoint->getId();
        }

        if (empty($callChecklistPoints)) {
            return;
        }

        $this->deleteByChecklistPointIds($call->getId(), $call->getCompanyId(), $callChecklistPointsIds);
        $this->callsChecklistsPointsTable->saveCallChecklistPoints($callChecklistPoints);
    }

    public function deleteCallChecklistPoints(Call $call): void
    {
        $this->callsChecklistsPointsTable->deleteByCall($call);
    }

    public function deleteByChecklistPointIds(string $callId, int $companyId, array $checklistPointIds): void
    {
        $this->callsChecklistsPointsTable->deleteByChecklistPointIds($callId, $companyId, $checklistPointIds);
    }

    /**
     * Process total statistics for different entities (agents, clients, campaigns, etc.)
     *
     * @param array $totalStatistics
     * @param array $callsSummary
     * @param string $idKey
     * @param array $itemKeys
     * @param array $checklistPointsKeysToUnset
     * @return array
     */
    private function processTotalStatistics(
        array $totalStatistics,
        array $callsSummary,
        string $idKey,
        array $itemKeys,
        array $checklistPointsKeysToUnset
    ): array {
        $data = [];

        foreach ($totalStatistics as $checklistsPoints) {
            if (empty($checklistsPoints)) {
                continue;
            }

            $item = [];
            $firstChecklistPoint = current($checklistsPoints);
            $entityId = $firstChecklistPoint[$idKey];

            // Copy all required keys from the first item
            foreach ($itemKeys as $key) {
                $item[$key] = $firstChecklistPoint[$key];
            }

            // Add summary
            $item['calls_duration'] = $callsSummary[$entityId]['calls_duration'];
            $item['total_conversations'] = $callsSummary[$entityId]['calls_count'];
            $item['clients_count'] = $callsSummary[$entityId]['clients_count'];
            $item['agents_count'] = $callsSummary[$entityId]['agents_count'];

            $item['score'] = ChecklistCallsStatisticsHelper::calculateScore($checklistsPoints, 'passed_percentage');

            $item['checklist_points'] = $this->processChecklistPoints($checklistsPoints, $checklistPointsKeysToUnset);

            $data[] = $item;
        }

        return $data;
    }

    /**
     * @param array $callsStatistics
     * @return array
     */
    private function processCallsStatistics(array $callsStatistics): array
    {
        $calls = [];

        foreach ($callsStatistics as $callId => $checklistsPoints) {
            if (empty($checklistsPoints)) {
                continue;
            }

            $firstChecklistPoint = current($checklistsPoints);

            $callItem = [
                'call_id' => $callId,
                'call_duration' => $firstChecklistPoint['call_duration'],
                'call_type' => $firstChecklistPoint['call_type'],
                'call_date' => $firstChecklistPoint['call_date'],
            ];

            $callItem['score'] = ChecklistCallsStatisticsHelper::calculateScore($checklistsPoints, 'is_passed', true);

            $callItem['checklist_points'] = $this->processChecklistPoints(
                $checklistsPoints,
                ['call_date', 'call_duration', 'call_id']
            );

            $calls[] = $callItem;
        }

        return $calls;
    }

    /**
     * @param array $checklistsPointsValues
     * @param array $keysToUnset
     * @return array
     */
    private function processChecklistPoints(array $checklistsPointsValues, array $keysToUnset): array
    {
        return array_map(static function ($item) use ($keysToUnset) {
            return array_diff_key($item, array_flip($keysToUnset));
        }, $checklistsPointsValues);
    }
}
