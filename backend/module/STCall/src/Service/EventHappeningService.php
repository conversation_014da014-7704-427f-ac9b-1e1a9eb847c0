<?php

declare(strict_types=1);

namespace STCall\Service;

use STCall\Service\Precalculation\CallPrecalculationManagerServiceTrait;

class EventHappeningService
{
    use CallPrecalculationManagerServiceTrait;

    /**
     *
     * @var \STCall\Data\EventHappeningsChangesTable
     */
    protected \STCall\Data\EventHappeningsChangesTable $eventHappeningsChangesTable;

    /**
     *
     * @param \STCall\Data\EventHappeningsChangesTable $eventHappeningsChangesTable
     */
    public function __construct(
        \STCall\Data\EventHappeningsChangesTable $eventHappeningsChangesTable,
    ) {
        $this->eventHappeningsChangesTable = $eventHappeningsChangesTable;
    }

    /**
     *
     * @param \STCall\Entity\EventHappening\EventHappeningChanging $eventHappeningChanging
     * @return int
     */
    public function changeEventHappening(\STCall\Entity\EventHappening\EventHappeningChanging $eventHappeningChanging): int
    {
        return $this->changeEventHappenings([$eventHappeningChanging]);
    }

    /**
     *
     * @param \STCall\Entity\EventHappening\EventHappeningChanging[] $eventHappeningChangings
     * @return int
     */
    public function changeEventHappenings(array $eventHappeningChangings): int
    {
        $affectedCount = $this->eventHappeningsChangesTable->saveEventHappeningChange($eventHappeningChangings);

        // mark as required to precalculations
        foreach ($eventHappeningChangings as $eventHappeningChanging) {
            $this->getCallPrecalculationManager()->markCallAsRequiredToPrecalculations(
                $eventHappeningChanging->getCompany()->getId(),
                $eventHappeningChanging->getRole()->getId(),
                $eventHappeningChanging->getCallId(),
            );
        }

        return $affectedCount;
    }
}
