<?php

declare(strict_types=1);

namespace STCall\Controller\Plugin;

use BadMethodCallException;
use <PERSON>inas\Mvc\Controller\Plugin\AbstractPlugin;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use STCall\Service\Interfaces\TranslatorInterface;
use STLib\Mvc\Controller\AbstractController;

/**
 * @method AbstractController getController()
 */
class Translator extends AbstractPlugin
{
    /**
     * @param string $name
     * @param array $arguments
     * @return mixed
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function __call(string $name, array $arguments): mixed
    {
        $translatorService = $this->getController()->getServiceManager()->get(TranslatorInterface::class);
        if (!method_exists($translatorService, $name)) {
            throw new BadMethodCallException('Invalid ' . TranslatorInterface::class . ' method: ' . $name);
        }
        return call_user_func_array([
            $translatorService,
            $name
        ], $arguments);
    }
}
