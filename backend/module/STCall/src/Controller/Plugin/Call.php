<?php

declare(strict_types=1);

namespace STCall\Controller\Plugin;

use Laminas\Mvc\Controller\Plugin\AbstractPlugin;
use LogicException;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use ST<PERSON>all\Service\CallAnalysis\StepInterface;
use STCall\Service\CallAnalysisService;
use STCall\Service\CallChecklistService;
use STCall\Service\CallErrorService;
use STCall\Service\CallReviewService;
use STCall\Service\CallSummarizationRemoverService;
use STCall\Service\CallSummarizationSelectorService;
use STCall\Service\ClientSummaryService;
use STCall\Service\EventHappeningService;
use STCall\Service\EventService;
use STCall\Service\Import\UploadService;
use STCall\Service\LanguageDriverService;
use STCall\Service\Precalculation\CallPrecalculationManagerService;
use STCall\Service\Precalculation\CallPrecalculationService;
use STLib\Mvc\Controller\AbstractController;
use STCall\Service\CallAnalysis\AlgoEventsStep;
use STCall\Service\CallAnalysis\ChecklistStep;
use STCall\Service\CallAnalysis\FileDownloadStep;
use STCall\Service\CallAnalysis\LanguageDetectionStep;
use STCall\Service\CallAnalysis\LlmEventsDetectionStep;
use STCall\Service\CallAnalysis\Mp3FileConvertationStep;
use STCall\Service\CallAnalysis\SpeakersRolesDetectionStep;
use STCall\Service\CallAnalysis\SummarizationStep;
use STCall\Service\CallAnalysis\TranscribingDistributionStep;
use STCall\Service\CallAnalysis\TranscribingJobCollectionStep;
use STCall\Service\CallAnalysis\TranscribingJobCreationStep;
use STCall\Service\CallAnalysis\TranscribingStep;
use STCall\Service\CallAnalysis\TranscribingWhisperStep;
use STCall\Service\CallAnalysis\TranslationStep;

/**
 * @method AbstractController getController()
 */
class Call extends AbstractPlugin
{
    public const STEPS = [
        AlgoEventsStep::class,
        ChecklistStep::class,
        FileDownloadStep::class,
        LanguageDetectionStep::class,
        LlmEventsDetectionStep::class,
        Mp3FileConvertationStep::class,
        SpeakersRolesDetectionStep::class,
        SummarizationStep::class,
        TranscribingDistributionStep::class,
        TranscribingJobCollectionStep::class,
        TranscribingJobCreationStep::class,
        TranscribingStep::class,
        TranscribingWhisperStep::class,
        TranslationStep::class,
    ];
    /**
     * @param string $name
     * @param array $arguments
     * @return mixed
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function __call(string $name, array $arguments): mixed
    {
        $callService = $this->getController()->getServiceManager()->get(\STCall\Service\CallService::class);
        if (!method_exists($callService, $name)) {
            throw new \BadMethodCallException('Invalid \STCall\Service\CallService method: ' . $name);
        }
        return call_user_func_array([
            $callService,
            $name
        ], $arguments);
    }

    /**
     * @return UploadService
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function upload(): UploadService
    {
        return $this->getController()->getServiceManager()->get(UploadService::class);
    }

    /**
     * @return CallAnalysisService
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function analysis(): CallAnalysisService
    {
        return $this->getController()->getServiceManager()->get(CallAnalysisService::class);
    }

    /**
     * @param string $step
     * @return StepInterface
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws LogicException
     */
    public function analysisStep(string $step): StepInterface
    {
        $dashToCamelCase = new \Laminas\Filter\Word\DashToCamelCase();
        $namespacesOptions = [
            'STCall\Service\CallAnalysis\\' . $dashToCamelCase->filter($step) . 'Step',
            'STCall\Service\CallAnalysis\\' . $dashToCamelCase->filter($step),
        ];
        foreach ($namespacesOptions as $namespacesOption) {
            if ($this->getController()->getServiceManager()->has($namespacesOption)) {
                return $this->getController()->getServiceManager()->get($namespacesOption);
            }
        }
        throw new LogicException('Can\'t find step for "' . $step . '"');
    }

    protected function getAnalysisStepsFactory($step)
    {
        $factoryClass = sprintf("STCall\\Service\\CallAnalysis\\%sStepFactory", $step);

        if (!class_exists($factoryClass)) {
            throw new \InvalidArgumentException(sprintf("Missing factory %s", $factoryClass));
        }

        return $this->getController()->getServiceManager()->get($factoryClass);
    }

    public function getAnalysisStep(string $step)
    {
        if (!in_array(sprintf("STCall\\Service\\CallAnalysis\\%sStep", $step), self::STEPS)) {
            throw new \InvalidArgumentException("Invalid step name");
        }

        $factory = $this->getAnalysisStepsFactory($step);

        return $factory($this->getController()->getServiceManager(), 'RateLimiterFactory.CallDownload');
    }
    /**
     * @return array
     */
    public function getAvailableAnalysisSteps(): array
    {
        return  array_map(
            fn($class) => str_replace('Step', '', basename(str_replace('\\', '/', $class))),
            self::STEPS
        );
    }


    /**
     * @return EventHappeningService
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function eventHappening(): EventHappeningService
    {
        return $this->getController()->getServiceManager()->get(EventHappeningService::class);
    }

    /**
     * @return CallPrecalculationService
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function precalculation(): CallPrecalculationService
    {
        return $this->getController()->getServiceManager()->get(CallPrecalculationService::class);
    }

    /**
     * @return CallPrecalculationManagerService
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function precalculationManager(): CallPrecalculationManagerService
    {
        return $this->getController()->getServiceManager()->get(CallPrecalculationManagerService::class);
    }

    /**
     * @return CallReviewService
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function review(): CallReviewService
    {
        return $this->getController()->getServiceManager()->get(CallReviewService::class);
    }

    /**
     * @return EventService
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function event(): EventService
    {
        return $this->getController()->getServiceManager()->get(EventService::class);
    }

    /**
     * @return CallChecklistService
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function checklist(): CallChecklistService
    {
        return $this->getController()->getServiceManager()->get(CallChecklistService::class);
    }

    public function callError(): CallErrorService
    {
        return $this->getController()->getServiceManager()->get(CallErrorService::class);
    }

    /**
     * @return CallSummarizationRemoverService
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function callSummarizationRemover(): CallSummarizationRemoverService
    {
        return $this->getController()->getServiceManager()->get(CallSummarizationRemoverService::class);
    }

    /**
     * @return CallSummarizationSelectorService
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function callSummarizationSelector(): CallSummarizationSelectorService
    {
        return $this->getController()->getServiceManager()->get(CallSummarizationSelectorService::class);
    }

    /**
     * @return LanguageDriverService
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function languageDriver(): LanguageDriverService
    {
        return $this->getController()->getServiceManager()->get(LanguageDriverService::class);
    }

    public function clientSummary(): ClientSummaryService
    {
        return $this->getController()->getServiceManager()->get(ClientSummaryService::class);
    }
}
