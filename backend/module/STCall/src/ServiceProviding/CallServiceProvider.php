<?php

declare(strict_types=1);

namespace STCall\ServiceProviding;

use Carbon\CarbonPeriod;
use STCall\Data\BillingRepository;
use STCompany\Service\Billing\BillingPeriod;
use STCompany\Service\Interfaces\BillingRepositoryInterface as CompanyBillingRepositoryInterface;

final readonly class CallServiceProvider implements CompanyBillingRepositoryInterface
{
    public function __construct(private BillingRepository $billingRepository)
    {
    }

    public function getCallsSecondsDurationByPeriod(int $companyId, BillingPeriod $billingPeriod): int
    {
        return $this->billingRepository->getCallsSecondsDurationByPeriod($companyId, $billingPeriod);
    }

    public function getChatsByPeriod(int $companyId, BillingPeriod $billingPeriod): int
    {
        return $this->billingRepository->getChatsByPeriod($companyId, $billingPeriod);
    }

    public function getSummarizationsByPeriod(int $companyId, BillingPeriod $billingPeriod): int
    {
        return $this->billingRepository->getSummarizationsByPeriod($companyId, $billingPeriod);
    }

    public function getClientSummarizationCallsByPeriod(int $companyId, BillingPeriod $billingPeriod): int
    {
        return $this->billingRepository->getClientSummarizationCallsByPeriod($companyId, $billingPeriod);
    }

    public function getChecklistCallsByPeriod(
        int $companyId,
        int $checklistId,
        BillingPeriod $trialPeriod
    ): int {
        return $this->billingRepository->getChecklistCallsByPeriod($companyId, $checklistId, $trialPeriod);
    }
}
