<?php

declare(strict_types=1);

return [
    'aws' => [
        'api' => [
            'version' => 'latest',
            'credentials' => [
                'key' => getenv('AWS_KEY'),
                'secret' => getenv('AWS_SECRET'),
            ],
        ],
        'env' => getenv('APP_ENV') ?? 'local',
    ],
    'deepgram' => [
        'api' => [
            'secret' => getenv('DEEPGRAM_SECRET'),
            'on-prem-secret' => getenv('DEEPGRAM_ON_PREM_SECRET'),
        ],
    ],
    'wordcab' => [
        'api' => [
            'token' => getenv('WORDCAB_TOKEN'),
        ],
    ],
    'speechmatics' => [
        'api' => [
            'token' => getenv('SPEECHMATICS_TOKEN'),
        ],
    ],
    'salad' => [
        'api' => [
            'token' => getenv('SALAD_TOKEN'),
            'url' => getenv('SALAD_API_URL'),
        ],
    ],
    'assembly' => [
        'api' => [
            'token' => getenv('ASSEMBLY_TOKEN'),
        ],
    ],
    'transcriptor' => [
        'api' => [
            'token' => getenv('TRANSCRIPTOR_TOKEN'),
        ],
    ],
    'whisper-sdk' => [
        'api-path' => getenv('WHISPER_API_PATH'),
    ],
];
