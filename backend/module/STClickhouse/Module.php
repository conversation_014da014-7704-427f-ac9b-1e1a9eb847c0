<?php

declare(strict_types=1);

namespace STClickhouse;

class Module extends \STLib\ModuleManager\MultiConfigModule
{
    /**
     *
     * @param \Laminas\ModuleManager\ModuleManager $moduleManager
     * @return void
     */
    public function init(\Laminas\ModuleManager\ModuleManager $moduleManager): void
    {
        $events = $moduleManager->getEventManager();
        $events->attach(\Laminas\ModuleManager\ModuleEvent::EVENT_LOAD_MODULES_POST, function (\Laminas\ModuleManager\ModuleEvent $moduleEvent) {
            $serviceManager = $moduleEvent->getParam('ServiceManager');
            $clickhouseClient = $serviceManager->get(Client\Client::class);
            Entity\Migration\BaseMigration::setClient($clickhouseClient);
            Service\Logger\Writer::setClient($clickhouseClient);
        });
    }
}
