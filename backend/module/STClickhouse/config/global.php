<?php

declare(strict_types=1);

return [
    'clickhouse' => [
        'connection' => [
            'host' => getenv('CLICKHOUSE_HOST'),
            'port' => getenv('CLICKHOUSE_PORT'),
            'username' => getenv('CLICKHOUSE_USER'),
            'password' => getenv('CLICKHOUSE_PASS'),
            'options' => [
                'database' => getenv('CLICKHOUSE_DB'),
                'timeout' => (int) getenv('CLICKHOUSE_TIMEOUT'),
                'connectTimeOut' => 5,
            ],
        ],
        'migrations' => [
            'dir' => getcwd() . '/data/migrations/clickhouse/',
            'table' => 'migrations',
        ],

    ],
];
