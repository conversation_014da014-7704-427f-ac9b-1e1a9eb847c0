<?php

declare(strict_types=1);

namespace STClickhouse;

return [
    'laminas-cli' => [
        'commands' => [
            'clickhouse:migration:create' => Command\CreateMigrationCommand::class,
            'clickhouse:migration:up' => Command\UpMigrationCommand::class,
            'clickhouse:migration:down' => Command\DownMigrationCommand::class,
        ],
    ],
    'controller_plugins' => [
        'invokables' => [
            'clickhouse' => Controller\Plugin\Clickhouse::class,
        ],
    ],
    'service_manager' => [
        'factories' => [
            Command\CreateMigrationCommand::class => \STLib\Mvc\Command\CommandFactory::class,
            Command\UpMigrationCommand::class => \STLib\Mvc\Command\CommandFactory::class,
            Command\DownMigrationCommand::class => \STLib\Mvc\Command\CommandFactory::class,
            Client\Client::class => Client\ClientFactory::class,
            Data\MigrationsTable::class => Entity\TableFactory::class,
            Service\MigrationService::class => Service\MigrationServiceFactory::class,
            Service\Logger\Writer::class => Service\Logger\WriterFactory::class,
        ],
    ],
];
