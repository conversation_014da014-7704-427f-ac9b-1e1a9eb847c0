<?php

declare(strict_types=1);

namespace STClickhouse\Command;

use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class DownMigrationCommand extends \STLib\Mvc\Command\AbstractCommand
{
    /**
     *
     * @var string
     */
    protected static $defaultName = 'clickhouse:migration:down';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'Down last clickhouse migration';

    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $this->clickhouse()->migration()->down();
        return \Symfony\Component\Console\Command\Command::SUCCESS;
    }
}
