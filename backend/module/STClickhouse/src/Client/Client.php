<?php

declare(strict_types=1);

namespace STClickhouse\Client;

class Client
{
    /**
     *
     * @var array
     */
    protected array $config = [];

    /**
     *
     * @var \ClickHouseDB\Client|null
     */
    protected ?\ClickHouseDB\Client $connection = null;

    /**
     *
     * @param array $config
     * @throws \RangeException
     */
    public function __construct(array $config)
    {
        foreach (
            [
                'host',
                'port',
                'username',
                'password',
            ] as $requiredParam
        ) {
            if (!isset($config[$requiredParam])) {
                throw new \RangeException(
                    'Param "' . $requiredParam . '" is required for STClickhouse module, check your config.'
                );
            }
        }
        $this->config = $config;
    }

    /**
     *
     * @return \ClickHouseDB\Client
     */
    public function getConnection(): \ClickHouseDB\Client
    {
        if (is_null($this->connection)) {
            $this->connection = new \ClickHouseDB\Client([
                'host' => $this->config['host'],
                'port' => $this->config['port'],
                'username' => $this->config['username'],
                'password' => $this->config['password'],
            ]);
            $this->connection->database($this->config['options']['database'] ?? 'default');
            $this->connection->setTimeout($this->config['options']['timeout'] ?? 10);
            $this->connection->setConnectTimeOut($this->config['options']['connectTimeOut'] ?? 5);
        }
        return $this->connection;
    }

    /**
     *
     * @param string $sql
     * @return array|null
     */
    public function selectAll(string $sql, array $bindings = []): ?array
    {
        return $this->getConnection()->select($sql, $bindings)->rows();
    }

    /**
     *
     * @param string $sql
     * @return \Iterator
     */
    public function selectIterator(string $sql): \Iterator
    {
        return $this->getConnection()->select($sql);
    }

    /**
     *
     * @param string $sql
     * @param string|null $column
     * @param array $bindings
     * @return mixed
     */
    public function selectOne(string $sql, ?string $column = null, array $bindings = []): mixed
    {
        return $this->getConnection()->select($sql, $bindings)->fetchOne($column);
    }

    /**
     *
     * @param string $sql
     * @param string|null $column
     * @return string|array
     */
    public function selectColumn(string $sql, ?string $column, array $bindings = [])
    {
        return array_column($this->getConnection()->select($sql, $bindings)->rows($column), $column);
    }

    /**
     *
     * @param string $sql
     * @param string|array $groupingByColumn
     * @param array $bindings
     * @return array
     */
    public function selectAsTree(string $sql, string|array $groupingByColumn, array $bindings = []): array
    {
        return $this->getConnection()->select($sql, $bindings)->rowsAsTree($groupingByColumn);
    }

    /**
     *
     * @param string $sql
     * @return string
     */
    public function selectRaw(string $sql): string
    {
        return $this->getConnection()->select($sql)->rawData();
    }

    /**
     *
     * @param string $sql
     * @return array
     */
    public function selectTotals(string $sql): array
    {
        return $this->getConnection()->select($sql)->totals();
    }

    public function selectValue(string $sql, array $bindings = []): mixed
    {
        $result = $this->selectOne($sql, null, $bindings);
        return is_array($result) ? current($result) : null;
    }

    /**
     *
     * @description Old implementation of pagination
     * @param string $sql
     * @param \STClickhouse\Entity\Pagination\Pagination $pagination
     * @param string|null $countSql
     * @return \STClickhouse\Entity\Pagination\Pagination
     * @see BaseTable::selectWithPagination()
     *
     */
    public function selectWithPagination(
        string $sql,
        \STClickhouse\Entity\Pagination\Pagination $pagination,
        ?string $countSql = null,
    ): \STClickhouse\Entity\Pagination\Pagination {
        $whereSql = $this->convertFilterToSql($pagination->getFilter());
        $sql .= $whereSql;
        if (is_string($countSql)) {
            $countSql .= $whereSql;
        }
        if (!empty($pagination->getGroupBy())) {
            $sql .= '
                GROUP BY
                    ' . implode(',', $pagination->getGroupBy()) . '
            ';
        }
        if (!empty($pagination->getHaving())) {
            $sql .= '
                HAVING
                    ' . implode(' ' . 'AND' . ' ', $pagination->getHaving()) . '
            ';
        }
        if (!$pagination->onlyOnePage()) {
            $count = is_string($countSql) ? (int) $this->selectValue($countSql) : $this->count($sql);
            $pagination->setCount($count);
        }
        if ($pagination->getParams()->hasSort()) {
            $sortParts = [];
            foreach ($pagination->getParams()->getSort() as $sort) {
                $sortParts[] = $pagination->getNormalizedColumnName($sort['column']) . ' ' . $sort['direction'];
            }
            $sql .= '
                ORDER BY ' . implode(',', $sortParts) . '
            ';
        }
        if (!$pagination->onlyOnePage()) {
            $sql .= '
                LIMIT ' . $pagination->getOffset() . ', ' . $pagination->getParams()->getItemsOnPage() . '
            ';
        }

        // get only selected columns
        $sql = '
            SELECT
                ' . $this->getSelectString($pagination->getParams()->getColumns(true)) . '
            FROM
            (
                ' . $sql . '
            )
        ';

        $result = $pagination->useIterator() ? $this->selectIterator($sql) : $this->selectAll($sql);
        $pagination->setResult($result);
        return $pagination;
    }

    /**
     *
     * @description Improved implementation of pagination
     * @param string $candidatesSql
     * @param string $candidatesIdColumnName
     * @param \STClickhouse\Entity\Pagination\CandidatePagination $pagination
     * @return \STClickhouse\Entity\Pagination\CandidatePagination
     * @see BaseTable::paginate()
     *
     */
    public function paginate(
        string $candidatesSql,
        string $candidatesIdColumnName,
        \STClickhouse\Entity\Pagination\CandidatePagination $pagination,
    ): \STClickhouse\Entity\Pagination\CandidatePagination {
        // apply filters to query
        $candidatesSql .= $this->convertFilterToSql($pagination->getFilter());

        // get count
        $countSql = '
            SELECT
                COUNT(*)
            FROM
                (
                    ' . $candidatesSql . '
                )
        ';
        $pagination->setCount((int) $this->selectValue($countSql));
        if ($pagination->getCount() === 0) {
            return $pagination;
        }

        // get searched records identificators
        $deepCopy = new \DeepCopy\DeepCopy(true);
        /** @var \STClickhouse\Entity\Pagination\Pagination $candidatesPagination */
        $candidatesPagination = $deepCopy->copy($pagination);
        $candidatesPagination->getParams()->setColumns([$candidatesIdColumnName]);

        $candidatesSql .= $this->getSortSql($pagination->getParams());

        if (!$candidatesPagination->onlyOnePage()) {
            $candidatesSql .= '
                LIMIT ' . $candidatesPagination->getOffset() . ', ' . $candidatesPagination->getParams(
            )->getItemsOnPage() . '
            ';
        }

        $candidateIds = array_column($this->selectAll($candidatesSql), $candidatesIdColumnName);
        $pagination->setCandidateIds($candidateIds);
        return $pagination;
    }

    /**
     *
     * @param \STClickhouse\Entity\Pagination\Params $params
     * @return string
     */
    public function getSortSql(\STClickhouse\Entity\Pagination\Params $params): string
    {
        if (!$params->hasSort()) {
            return '';
        }
        $sortParts = [];
        foreach ($params->getSort() as $sort) {
            $sortParts[] = $sort['column'] . ' ' . $sort['direction'];
        }
        return '
            ORDER BY ' . implode(',', $sortParts) . '
        ';
    }

    /**
     *
     * @param array $filter
     * @return string
     */
    public function convertFilterToSql(array $filter): string
    {
        $where = [];
        foreach ($filter as $column => $value) {
            if (is_array($value)) {
                switch ($value['type'] ?? null) {
                    case 'date':
                        $where[] = $value['column'] . ' ' . $value['compare'] . ' toDateTime(\'' . $value['value'] . '\')';
                        break;
                    case 'map_value_in_array':
                        $where[] = 'arrayExists(map_value -> map_value[\'' . $value['map_key'] . '\'] IN (\'' . implode(
                            '\',\'',
                            $value['value']
                        ) . '\') , ' . $value['column'] . ') = 1';
                        break;
                    case 'like':
                        $where[] = 'lowerUTF8(' . $value['column'] . ')' . ' LIKE \'%' . strtolower(
                            $value['value']
                        ) . '%\'';
                        break;
                    case 'compare':
                        $where[] = $value['column'] . ' ' . $value['compare'] . ' ' . $this->transformParamValue(
                            $value['value']
                        );
                        break;
                    case 'expression':
                        $where[] = '(' . $value['value'] . ')';
                        break;
                    case null:
                        if (!empty($value)) {
                            $transformedValues = [];
                            foreach ($value as $option) {
                                $transformedValues[] = $this->transformParamValue($option);
                            }
                            if (count($transformedValues) > 0) {
                                $where[] = $column . ' IN (' . implode(',', $transformedValues) . ')';
                            }
                        }
                        break;
                }
            } else {
                $where[] = $column . ' = ' . $this->transformParamValue($value);
            }
        }
        if (!empty($where)) {
            return '
                WHERE
            ' . implode(' AND ', $where);
        }
        return '';
    }

    /**
     *
     * @param string $sql
     * @return int
     */
    public function count(string $sql): int
    {
        return $this->getConnection()->select($sql)->count();
    }

    /**
     *
     * @param string $table
     * @param array $values
     * @param array $columns
     * @return \ClickHouseDB\Statement
     */
    public function insert(string $table, array $values, array $columns = []): \ClickHouseDB\Statement
    {
        return $this->getConnection()->insert($table, $values, $columns);
    }

    /**
     *
     * @param string $table
     * @param array $conditions
     * @return \ClickHouseDB\Statement
     * @throws \RuntimeException
     */
    public function delete(string $table, array $conditions = []): \ClickHouseDB\Statement
    {
        $whereArray = [];
        foreach ($conditions as $condition) {
            if (!is_array($condition) || count($condition) !== 3) {
                throw new \RuntimeException('Invalid where condition');
            }
            $sqlCondition = $this->transformCondition($condition[1], $condition[2]);
            $this->transformParamValue($condition[2]);
            $whereArray[] = $condition[0] . $sqlCondition . $condition[2];
        }
        return $this->write(
            '
            ALTER TABLE
                ' . $table . '
            DELETE
            ' . $this->onClusterSql() . '
            WHERE
                ' . implode(' ' . 'AND' . ' ', $whereArray) . '
        '
        );
    }

    /**
     *
     * @param string $table
     * @param array $conditions
     * @return \ClickHouseDB\Statement
     * @throws \RuntimeException
     */
    public function softDelete(string $table, array $conditions = []): \ClickHouseDB\Statement
    {
        $whereArray = [];
        foreach ($conditions as $condition) {
            if (!is_array($condition) || count($condition) !== 3) {
                throw new \RuntimeException('Invalid where condition');
            }
            $sqlCondition = $this->transformCondition($condition[1], $condition[2]);
            $this->transformParamValue($condition[2]);
            $whereArray[] = $condition[0] . $sqlCondition . $condition[2];
        }
        return $this->write(
            '
            DELETE
            FROM
                ' . $table . '
            ' . $this->onClusterSql() . '
            WHERE
                ' . implode(' ' . 'AND' . ' ', $whereArray) . '
        '
        );
    }

    /**
     *
     * @param string $string
     * @param array $chars
     * @return string
     */
    public function escapeChars(string $string, array $chars = ["'"]): string
    {
        $replace = array_map(function ($char) {
            return '\\' . $char;
        }, $chars);
        return str_replace($chars, $replace, $string);
    }

    /**
     *
     * @return string
     */
    public function onClusterSql(): string
    {
        return !$this->isFakeCluster() ? ('ON CLUSTER ' . $this->getCluster()) : '';
    }

    /**
     *
     * @return string
     */
    protected function getCluster(): string
    {
        return getenv('CLICKHOUSE_CLUSTER') !== false ? getenv('CLICKHOUSE_CLUSTER') : 'robonote';
    }

    /**
     *
     * @return bool
     */
    protected function isFakeCluster(): bool
    {
        return empty(getenv('CLICKHOUSE_CLUSTER'));
    }

    /**
     *
     * @param mixed $value
     * @return mixed
     */
    protected function transformParamValue(mixed &$value): mixed
    {
        $result = null;
        switch (gettype($value)) {
            case 'string':
                $result = ' \'' . $value . '\' ';
                break;
            case 'array':
                foreach ($value as &$element) {
                    $this->transformParamValue($element);
                }
                $result = ' (' . implode(',', $value) . ') ';
                break;
            case 'NULL':
                $result = 'NULL';
                break;
            default:
                $result = $value;
        }
        $value = $result;
        return $result;
    }

    /**
     *
     * @return string
     * @throws \RuntimeException
     */
    public function getDatabase(): string
    {
        if (!isset($this->config['options']['database'])) {
            throw new \RuntimeException('Unknown database');
        }
        return $this->config['options']['database'];
    }

    /**
     *
     * @param string $compare
     * @param mixed $conditionValue
     * @return string
     */
    protected function transformCondition(string $compare, &$conditionValue): string
    {
        $result = null;
        $conditionValueType = gettype($conditionValue);
        switch ($compare) {
            case '<>':
            case '!=':
                switch ($conditionValueType) {
                    case 'array':
                        $result = ' NOT IN ';
                        break;
                    case 'NULL':
                        $result = ' IS NOT ';
                        break;
                    default:
                        $result = ' != ';
                }
                break;
            case '=':
                switch ($conditionValueType) {
                    case 'array':
                        $result = ' IN ';
                        break;
                    case 'NULL':
                        $result = ' IS ';
                        break;
                    default:
                        $result = ' = ';
                }
                break;
            case '<':
            case '<=':
            case '>':
            case '>=':
                settype($conditionValue, 'float');
                break;
            default:
                throw new \RuntimeException('Invalid compare value');
        }
        return $result;
    }

    /**
     *
     * @param array $columns
     * @return string
     */
    public function getSelectString(array $columns): string
    {
        if (count($columns) === 0) {
            return '*';
        }
        $selectColumns = [];
        foreach ($columns as $alias => $column) {
            $columnSql = $column;
            if (is_string($alias)) {
                $columnSql .= ' ' . $alias;
            }
            $selectColumns[] = $columnSql;
        }
        return implode(',', $selectColumns);
    }

    /**
     *
     * @param string $name
     * @param array $arguments
     * @return mixed
     */
    public function __call(string $name, array $arguments): mixed
    {
        if (!method_exists($this->getConnection(), $name)) {
            throw new \BadMethodCallException('\ClickHouseDB\Client: unknown method "' . $name . '"');
        }
        return call_user_func_array([
            $this->getConnection(),
            $name
        ], $arguments);
    }

    /**
     *
     * @param string $tableName
     * @return \ClickHouseDB\Statement
     */
    public function truncate(string $tableName): \ClickHouseDB\Statement
    {
        return $this->getConnection()->write('TRUNCATE TABLE ' . $tableName);
    }

    /**
     *
     * @param string $sql
     * @param array $bindings
     * @return string
     */
    public function selectCsv(string $sql, array $bindings = [], bool $withNames = true): string
    {
        $sql .= $withNames ? '
            FORMAT CSVWithNames
        ' : '
            FORMAT CSV
        ';

        return $this->getConnection()->select($sql, $bindings)->rawData();
    }

    public function selectJSONEachRow(string $sql, array $bindings = [])
    {
        $sql .= '
            FORMAT JSONEachRow
        ';

        return $this->getConnection()->select($sql, $bindings)->rawData();
    }
}
