<?php

declare(strict_types=1);

namespace STClickhouse\Client;

class ClientFactory implements \Laminas\ServiceManager\Factory\FactoryInterface
{
    /**
     *
     * @param \Interop\Container\ContainerInterface $container
     * @param string $requestedName
     * @param array $options
     * @return \STClickhouse\Client\Client
     */
    public function createService(\Interop\Container\ContainerInterface $container, $requestedName, array $options = null): Client
    {
        return $this($container, $requestedName ?: PatternPluginManager::class, $this->creationOptions);
    }

    /**
     *
     * @param \Interop\Container\ContainerInterface $container
     * @param string $requestedName
     * @param array $options
     * @return \STClickhouse\Client\Client
     */
    public function __invoke(\Interop\Container\ContainerInterface $container, $requestedName, array $options = null): Client
    {
        $config = $container->get('config');
        $clickhouseConfig = $config['clickhouse']['connection'] ?? [];
        return new Client($clickhouseConfig);
    }
}
