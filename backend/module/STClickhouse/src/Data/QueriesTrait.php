<?php

declare(strict_types=1);

namespace STClickhouse\Data;

trait QueriesTrait
{
    /**
     *
     * @param array $array
     * @return \ClickHouseDB\Query\Expression\Expression
     */
    protected function convertToMap(array $array): \ClickHouseDB\Query\Expression\Expression
    {
        $expression = '{';
        $elements = [];
        foreach ($array as $key => $value) {
            $elements[] =
                    (is_string($key) ? $this->stringsInsertPrepare($key) : $key)
                    . ':'
                    . (is_string($value) ? $this->stringsInsertPrepare($value) : $value);
        }
        $expression .= implode(',', $elements) . '}';
        return new \ClickHouseDB\Query\Expression\Raw($expression);
    }

    /**
     *
     * @param string $table
     * @param array|string $orderColumn
     * @param string $versionColumn
     * @return string
     */
    protected function getFinalTableSql(string $table, array|string $orderColumn = 'id', string $versionColumn = 'created'): string
    {
        return $this->getFinalTableSqlUsingFinal($table);
    }

    /**
     *
     * @param string $table
     * @param array|string $orderColumn
     * @param string $versionColumn
     * @return string
     */
    protected function getFinalTableSqlUsingRank(string $table, array|string $orderColumn = 'id', string $versionColumn = 'created'): string
    {
        $orderColumns = is_array($orderColumn) ? $orderColumn : [$orderColumn];
        return '
            SELECT
                *
            FROM ( 
                SELECT
                    *, 
                    RANK() OVER (PARTITION BY ' . implode(',', $orderColumns) . ' ORDER BY ' .  $versionColumn . ' DESC) created_rank
                FROM
                    ' . $table . '
            )
            WHERE
                created_rank = 1
        ';
    }

    /**
     *
     * @param string $table
     * @param array|string $orderColumn
     * @param string $versionColumn
     * @return string
     */
    protected function getFinalTableSqlUsingJoin(string $table, array|string $orderColumn = 'id', string $versionColumn = 'created'): string
    {
        $tableName1 = $this->getTableUniqueIdentificator($table);
        $tableName2 = $this->getTableUniqueIdentificator($table);
        $orderColumns = is_array($orderColumn) ? $orderColumn : [$orderColumn];
        $sql = '
            SELECT
        ';
        foreach ($orderColumns as $orderColumn) {
            $sql .= '
                ' . $tableName1 . '.' . $orderColumn . ' ' . $orderColumn . ',
            ';
        }
        $sql .= '
                ' . $tableName1 . '.' . $versionColumn . ' ' . $versionColumn . ',
                ' . $tableName1 . '.*
            FROM
            (
                SELECT
                    ' . implode(',', $orderColumns) . ',
                    max(' . $versionColumn . ') as ' . $versionColumn . '
                FROM
                    ' . $table . '
                GROUP BY
                    ' . implode(',', $orderColumns) . '
            ) ' . $tableName2 . '
            INNER JOIN
                ' . $table . ' ' . $tableName1 . '
                ON ' . $tableName1 . '.' . $versionColumn . ' = ' . $tableName2 . '. ' . $versionColumn . '
        ';
        foreach ($orderColumns as $orderColumn) {
            $sql .= '
                AND ' . $tableName1 . '.' . $orderColumn . ' = ' . $tableName2 . ' .' . $orderColumn . ' 
            ';
        }
        return $sql;
    }

    /**
     *
     * @param string $table
     * @param array|string $orderColumn
     * @param string $versionColumn
     * @return string
     */
    protected function getFinalTableSqlUsingFinal(string $table, array|string $orderColumn = 'id', string $versionColumn = 'created'): string
    {
        return 'SELECT * FROM ' . $table . ' FINAL';
    }

    /**
     *
     * @param string $table
     * @param array|string $orderColumn
     * @param string $versionColumn
     * @param array $additionalColumns
     * @param array $where
     * @return string
     */
    protected function getFinalTableSqlUsingGroupBy(
        string $table,
        array|string $orderColumn = 'id',
        string $versionColumn = 'created',
        array $additionalColumns = [],
        array $where = []
    ): string {
        $orderColumns = is_array($orderColumn) ? $orderColumn : [$orderColumn];
        $lastValueAdditionalColumns = array_map(function ($additionalColumn) {
            return 'last_value_respect_nulls(' . $additionalColumn . ') `' . $additionalColumn . '`';
        }, $additionalColumns);
        $sql = '
            SELECT
                ' . implode(',', $orderColumns) . '
                ' . (count($additionalColumns) > 0 ? ',' : '') . '
                ' . implode(',', $lastValueAdditionalColumns) . '
            FROM
            (
                SELECT
                    *
                FROM
                    ' . $table . '
        ';
        $sql .= $this->convertFilterToSql($where);
        $sql .= '
                ORDER BY
                    ' . $versionColumn . '
            )
            GROUP BY
                ' . implode(',', $orderColumns) . '
        ';
        return $sql;
    }

    /**
     *
     * Copy of \STClickhouse\Client\Client::convertFilterToSql()
     * Sorry, it should be refactored ASAP
     * Fast changes, because prod is on fire
     *
     * @todo Combine with \STClickhouse\Client\Client
     *
     * @param array $filter
     * @return string
     */
    protected function convertFilterToSql(array $filter): string
    {
        $where = [];
        foreach ($filter as $column => $value) {
            if (is_array($value)) {
                switch ($value['type'] ?? null) {
                    case 'date':
                        $where[] =  $value['column'] . ' ' . $value['compare'] . ' toDateTime(\'' . $value['value'] . '\')';
                        break;
                    case 'like':
                        $where[] = 'lowerUTF8(' . $value['column'] . ')' . ' LIKE \'%' . strtolower($value['value']) . '%\'';
                        break;
                    case 'compare':
                        $where[] =  $value['column'] . ' ' . $value['compare'] . ' ' . $this->transformParamValue($value['value']);
                        break;
                    case 'expression':
                        $where[] = '(' . $value['value'] . ')';
                        break;
                    case null:
                        if (!empty($value)) {
                            $transformedValues = [];
                            foreach ($value as $option) {
                                $transformedValues[] = $this->transformParamValue($option);
                            }
                            if (count($transformedValues) > 0) {
                                $where[] = $column . ' IN (' . implode(',', $transformedValues) . ')' ;
                            }
                        }
                        break;
                }
            } else {
                $where[] = $column . ' = ' . $this->transformParamValue($value);
            }
        }
        if (!empty($where)) {
            return '
                WHERE
            ' . implode(' AND ', $where);
        }
        return '';
    }

    /**
     *
     * @param string $value
     * @return string
     */
    private function stringsInsertPrepare(string $value): string
    {
        return '\'' . str_replace("'", "\'", $value) . '\'';
    }

    /**
     *
     * @param string $table
     * @return string
     */
    private function getTableUniqueIdentificator(string $table): string
    {
        return $table . '_' . rand(1000000, 9999999);
    }

    /**
     *
     * Copy of \STClickhouse\Client\Client::convertFilterToSql()
     * Sorry, it should be refactored ASAP
     * Fast changes, because prod is on fire
     *
     * @todo Combine with \STClickhouse\Client\Client
     *
     * @param mixed $value
     * @return mixed
     */
    private function transformParamValue(mixed &$value): mixed
    {
        $result = null;
        switch (gettype($value)) {
            case 'string':
                $result = ' \'' . $value . '\' ';
                break;
            case 'array':
                foreach ($value as &$element) {
                    $this->transformParamValue($element);
                }
                $result = ' (' . implode(',', $value) . ') ';
                break;
            case 'NULL':
                $result = 'NULL';
                break;
            case 'boolean':
                $result = $value ? 'true' : 'false';
                break;
            default:
                $result = $value;
        }
        $value = $result;
        return $result;
    }
}
