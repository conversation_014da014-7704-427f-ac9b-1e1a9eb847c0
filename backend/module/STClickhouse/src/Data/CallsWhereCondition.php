<?php

declare(strict_types=1);

namespace STClickhouse\Data;

trait CallsWhereCondition
{
    /**
     *
     * @param int $companyId
     * @param array $filters
     * @param null|int|array $roleId
     * @return array
     */
    protected function getCallsWhereConditionFromArray(
        int $companyId,
        array $filters,
        null|int|array $roleId = null
    ): array {
        $callWhereCondition = array_filter($filters, function ($filter, $key) {
            if (
                in_array($key, [
                    'client_id',
                    'agent_id',
                    'call_origin',
                    'call_id',
                ])
            ) {
                return true;
            }
            return isset($filter['column']) && $filter['column'] === 'call_time';
        }, ARRAY_FILTER_USE_BOTH);
        $callWhereCondition['company_id'] = $companyId;
        if (!is_null($roleId)) {
            $callWhereCondition['role_id'] = $roleId;
        }
        return $callWhereCondition;
    }

    /**
     *
     * @param int $companyId
     * @param \STClickhouse\Entity\Pagination\Pagination $pagination
     * @param int|null $roleId
     * @return array
     */
    protected function getCallsWhereConditionFromPagination(
        int $companyId,
        \STClickhouse\Entity\Pagination\Pagination $pagination,
        ?int $roleId = null
    ): array {
        $filters = $pagination->getFilter();
        // rename filter origin to call_origin, like column in db
        if (isset($filters['origin'])) {
            $filters['call_origin'] = $filters['origin'];
        }
        return $this->getCallsWhereConditionFromArray($companyId, $filters, $roleId);
    }

    /**
     *
     * @param int $companyId
     * @param \Carbon\Carbon|null $startDate
     * @param \Carbon\Carbon|null $endDate
     * @param int|array|null $roleId
     * @param string|array|null $clientId
     * @param int|null|array $agentIds
     * @param array|null $callIds
     * @return array
     */
    protected function getCallsWhereConditionsFromMainParams(
        int $companyId,
        ?\Carbon\Carbon $startDate = null,
        ?\Carbon\Carbon $endDate = null,
        null|int|array $roleId = null,
        null|string|array $clientId = null,
        null|int|array $agentIds = null,
        ?array $callIds = null,
    ): array {
        $filters = [];
        if ($startDate instanceof \Carbon\Carbon) {
            $filters[] = [
                'column' => 'call_time',
                'value' => $startDate,
                'type' => 'date',
                'compare' => '>=',
            ];
        }
        if ($endDate instanceof \Carbon\Carbon) {
            $filters[] = [
                'column' => 'call_time',
                'value' => $endDate,
                'type' => 'date',
                'compare' => '<=',
            ];
        }
        if (!empty($clientId)) {
            $filters['client_id'] = $clientId;
        }
        if (!empty($agentIds)) {
            $filters['agent_id'] = $agentIds;
        }
        if (!empty($callIds)) {
            $filters['call_id'] = $callIds;
        }
        return $this->getCallsWhereConditionFromArray($companyId, $filters, $roleId);
    }
}
