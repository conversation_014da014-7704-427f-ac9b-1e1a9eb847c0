<?php

declare(strict_types=1);

namespace STClickhouse\Data;

class MigrationsTable extends \STClickhouse\Entity\BaseTable
{
    /**
     *
     * @var string|null
     */
    protected string $tableName = 'migrations';

    /**
     *
     * @var string|null
     */
    protected string $dataTableName = 'migrations';

    /**
     *
     * @param string|null $tableName
     */
    public function __construct(\STClickhouse\Client\Client $client, string $tableName = null)
    {
        if (!is_null($tableName)) {
            $this->tableName = $tableName;
        }
        return parent::__construct($client);
    }

    /**
     *
     * Create migration table if not exists
     *
     * @return void
     */
    public function createMigrationTableIfNotExists(): void
    {
        $this->getClient()->write('
            CREATE TABLE IF NOT EXISTS ' . $this->dataTableName . '
            ' . $this->getClient()->onClusterSql() . '
            (
                version String,
                apply_time DateTime DEFAULT NOW()
            ) ENGINE = ReplacingMergeTree()
                ORDER BY
                    (version)
        ');
        $this->getClient()->write('
            CREATE TABLE IF NOT EXISTS
                ' . $this->tableName . ' AS ' . $this->dataTableName . '
            ' . $this->getClient()->onClusterSql() . '
            ENGINE = Distributed(robonote, ' . $this->getClient()->getDatabase() . ', ' . $this->dataTableName . ', toInt32(version))
        ');
    }

    /**
     *
     * @return mixed
     */
    public function getLastAppliedMigration(): mixed
    {
        return $this->getClient()->selectOne('
            SELECT
                version
            FROM
                ' . $this->tableName . ' m
            ORDER BY
                apply_time DESC,
                version DESC
            LIMIT
                1
        ', 'version');
    }

    /**
     *
     * @return array
     */
    public function getAppliedMigrations(): array
    {
        $result = $this->getClient()->selectColumn('
            SELECT
                m.version
            FROM
                ' . $this->tableName . ' m
        ', 'version');
        if (is_null($result)) {
            return [];
        }
        return $result;
    }

    /**
     *
     * @param string $version
     * @return \ClickHouseDB\Statement
     */
    public function addMigration(string $version): \ClickHouseDB\Statement
    {
        return $this->getClient()->insert($this->tableName, [
            [
                $version,
            ],
        ], [
            'version',
        ]);
    }

    /**
     *
     * @param string $version
     * @return \ClickHouseDB\Statement
     */
    public function removeMigration(string $version): \ClickHouseDB\Statement
    {
        return $this->getClient()->softDelete($this->dataTableName, [
            [
                'version',
                '=',
                $version,
            ],
        ]);
    }
}
