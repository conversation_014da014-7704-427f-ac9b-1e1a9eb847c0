<?php

declare(strict_types=1);

namespace STClickhouse\Service\Logger;

class WriterFactory implements \Laminas\ServiceManager\Factory\FactoryInterface
{
    /**
     *
     * @param \Interop\Container\ContainerInterface $container
     * @param string $requestedName
     * @param array $options
     * @return \STClickhouse\Service\MigrationService
     */
    public function createService(\Interop\Container\ContainerInterface $container, $requestedName, array $options = null): Writer
    {
        return $this($container, $requestedName ?: PatternPluginManager::class, $this->creationOptions);
    }

    /**
     *
     * @param \Interop\Container\ContainerInterface $container
     * @param string $requestedName
     * @param array $options
     * @return \STClickhouse\Service\MigrationService
     */
    public function __invoke(\Interop\Container\ContainerInterface $container, $requestedName, array $options = null): Writer
    {
        $client = $container->get(\STClickhouse\Client\Client::class);
        $writer = new Writer();
        $writer->setClient($client);
        return $writer;
    }
}
