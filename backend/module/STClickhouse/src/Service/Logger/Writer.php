<?php

declare(strict_types=1);

namespace STClickhouse\Service\Logger;

class Writer extends \Laminas\Log\Writer\AbstractWriter
{
    /**
     *
     * @var \STClickhouse\Client\Client
     */
    protected static \STClickhouse\Client\Client $client;

    /**
     *
     * @var string
     */
    protected string $table;

    /**
     *
     * @var array
     */
    protected array $additinalColumns = [];

    /**
     *
     * @var bool
     */
    protected bool $withMessage = true;

    /**
     *
     * @return \STClickhouse\Client\Client
     */
    public static function getClient(): \STClickhouse\Client\Client
    {
        return static::$client;
    }

    /**
     *
     * @param \STClickhouse\Client\Client $client
     * @return void
     */
    public static function setClient(\STClickhouse\Client\Client $client): void
    {
        static::$client = $client;
    }

    /**
     *
     * @param array $settings
     * @return Writer
     */
    public function setSettings(array $settings): Writer
    {
        $this->table = $settings['table'] ?? null;
        $this->additinalColumns = $settings['additinal_columns'] ?? [];
        $this->withMessage = $settings['with_message'] ?? true;
        return $this;
    }

    /**
     *
     * @param array $event
     * @return void
     * @throws \RuntimeException
     */
    protected function doWrite(array $event): void
    {
        if (empty($this->table)) {
            throw new \RuntimeException('Invalid log table name');
        }
        $record = [];
        foreach ($this->additinalColumns as $additionalValue) {
            $record[] = $event['extra'][$additionalValue];
        }
        $columns = $this->additinalColumns;
        if ($this->withMessage) {
            $record[] = $event['message'] ?? '';
            $columns[] = 'message';
        }
        static::$client->insert($this->table, [
            $record
        ], $columns);
    }
}
