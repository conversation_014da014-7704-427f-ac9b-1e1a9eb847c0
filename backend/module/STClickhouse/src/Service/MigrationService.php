<?php

declare(strict_types=1);

namespace STClickhouse\Service;

class MigrationService
{
    use \STLog\Service\ProviderLogger;

    /**
     *
     * @var string|null
     */
    protected ?string $dir = null;

    /**
     *
     * @var \STClickhouse\Data\MigrationsTable
     */
    protected \STClickhouse\Data\MigrationsTable $migrationsTable;

    /**
     *
     * @param array $config
     * @throws \RangeException
     */
    public function __construct(\STClickhouse\Data\MigrationsTable $migrationTable, array $config)
    {
        if (!isset($config['dir'])) {
            throw new \RangeException('Param "dir" is required for clickhouse migrations, check your config.');
        }
        $this->dir = rtrim($config['dir'], '/');
        $this->migrationsTable = $migrationTable;

        spl_autoload_register(function ($class) {
            if (preg_match('/^Clickhouse\\\\Migrations\\\\([\d\w_]+)$/', $class, $matches)) {
                require_once $this->dir . '/' . $matches[1] . '.php';
            }
        });
    }

    /**
     *
     * @return array|null
     */
    public function up(): ?array
    {
        $this->createMigrationTableIfNotExists();
        $unappliedMigrations = $this->getUnappliedMigrations();
        if (count($unappliedMigrations) === 0) {
            $this->logger('clickhouse-migrations')->info('There are no migrations to execute');
            return null;
        }
        foreach ($unappliedMigrations as $migrationName) {
            $migration = $this->getMigrationObject($migrationName);
            $migration->up();
            $this->migrationsTable->addMigration($migrationName);
            $this->logger('clickhouse-migrations')->info('Migration ' . $migrationName . ' executed');
        }
        return $unappliedMigrations;
    }

    /**
     *
     * @return string|null
     */
    public function down(): ?string
    {
        $this->createMigrationTableIfNotExists();
        $migrationName = $this->migrationsTable->getLastAppliedMigration();
        if (is_null($migrationName)) {
            $this->logger('clickhouse-migrations')->info('There are no migrations to revert');
            return null;
        }
        $migration = $this->getMigrationObject($migrationName);
        $migration->down();
        $this->migrationsTable->removeMigration($migrationName);
        $this->logger('clickhouse-migrations')->info('Migration ' . $migrationName . ' reverted');
        return $migrationName;
    }

    /**
     *
     * @return string
     */
    public function create(): string
    {
        $template = file_get_contents(getcwd() . '/config/migration/clickhouse/Migration.php.template');
        $version = 'Version' . date('YmdHis');
        $migrationContent = str_replace([
            '{{version}}'
        ], [
            $version,
        ], $template);
        file_put_contents($this->dir . '/' . $version . '.php', $migrationContent);
        $this->logger('clickhouse-migrations')->info('Migration ' . $version . ' created');
        return $version;
    }

    /**
     *
     * @return array
     */
    protected function getAppliedMigrations(): array
    {
        return $this->migrationsTable->getAppliedMigrations();
    }

    /**
     *
     * @return array
     */
    protected function getMigrations(): array
    {
        $migrations = [];
        foreach (new \DirectoryIterator($this->dir) as $fileInfo) {
            if ($fileInfo->isDot()) {
                continue;
            }
            $migrations[] = preg_replace('/\.php$/', '', $fileInfo->getFilename());
        }
        return $migrations;
    }

    /**
     *
     * @return array
     */
    protected function getUnappliedMigrations(): array
    {
        $migrations = $this->getMigrations();
        sort($migrations);
        $appliedMigrations = $this->getAppliedMigrations();
        return array_diff($migrations, $appliedMigrations);
    }

    /**
     *
     * @return void
     */
    protected function createMigrationTableIfNotExists(): void
    {
        $this->migrationsTable->createMigrationTableIfNotExists();
    }

    /**
     *
     * @param string $migrationName
     * @return \STClickhouse\Entity\MigrationInterface
     */
    protected function getMigrationObject(string $migrationName): \STClickhouse\Entity\Migration\MigrationInterface
    {
        $migrationNamespace = '\Clickhouse\Migrations\\' . $migrationName;
        return new $migrationNamespace();
    }
}
