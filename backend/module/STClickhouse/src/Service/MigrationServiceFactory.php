<?php

declare(strict_types=1);

namespace STClickhouse\Service;

class MigrationServiceFactory implements \Laminas\ServiceManager\Factory\FactoryInterface
{
    /**
     *
     * @param \Interop\Container\ContainerInterface $container
     * @param string $requestedName
     * @param array $options
     * @return \STClickhouse\Service\MigrationService
     */
    public function createService(\Interop\Container\ContainerInterface $container, $requestedName, array $options = null): MigrationService
    {
        return $this($container, $requestedName ?: PatternPluginManager::class, $this->creationOptions);
    }

    /**
     *
     * @param \Interop\Container\ContainerInterface $container
     * @param string $requestedName
     * @param array $options
     * @return \STClickhouse\Service\MigrationService
     */
    public function __invoke(\Interop\Container\ContainerInterface $container, $requestedName, array $options = null): MigrationService
    {
        $migrationsTable = $container->get(\STClickhouse\Data\MigrationsTable::class);
        $config = $container->get('config');
        $migrationsConfig = $config['clickhouse']['migrations'] ?? [];
        return new MigrationService($migrationsTable, $migrationsConfig);
    }
}
