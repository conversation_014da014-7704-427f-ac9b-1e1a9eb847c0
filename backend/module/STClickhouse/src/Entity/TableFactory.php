<?php

declare(strict_types=1);

namespace STClickhouse\Entity;

use Interop\Container\ContainerInterface;
use Laminas\ServiceManager\Factory\FactoryInterface;

class TableFactory implements FactoryInterface
{
    /**
     *
     * @param ContainerInterface $container
     * @param string $requestedName
     * @param array $options
     * @return BaseTable
     */
    public function __invoke(ContainerInterface $container, $requestedName, array $options = null): BaseTable
    {
        $client = $container->get(\STClickhouse\Client\Client::class);
        $table = new $requestedName($client);
        return $table;
    }
}
