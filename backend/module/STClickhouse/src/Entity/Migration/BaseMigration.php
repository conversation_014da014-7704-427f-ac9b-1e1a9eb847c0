<?php

declare(strict_types=1);

namespace STClickhouse\Entity\Migration;

abstract class BaseMigration implements MigrationInterface
{
    /**
     *
     * @var \STClickhouse\Client\Client
     */
    protected static ?\STClickhouse\Client\Client $client = null;

    /**
     *
     * @param \STClickhouse\Client\Client $client
     * @return void
     */
    public static function setClient(\STClickhouse\Client\Client $client): void
    {
        static::$client = $client;
    }

    /**
     *
     * @return \STClickhouse\Client\Client
     * @throws \RuntimeException
     */
    public function getClient(): \STClickhouse\Client\Client
    {
        if (!(static::$client instanceof \STClickhouse\Client\Client)) {
            throw new \RuntimeException('Clickhouse client is not injected in migration object');
        }
        return static::$client;
    }

    /**
     *
     * @return string
     */
    protected function getCluster(): string
    {
        return getenv('CLICKHOUSE_CLUSTER') ?? 'robonote';
    }

    /**
     *
     * @return bool
     */
    protected function isFakeCluster(): bool
    {
        return !empty(getenv('CLICKHOUSE_CLUSTER'));
    }
}
