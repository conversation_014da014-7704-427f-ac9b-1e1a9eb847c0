<?php

namespace STClickhouse\Entity\Pagination;

class Params
{
    public const DEFAULT_ITEMS_ON_PAGE = 10;
    public const DEFAULT_SORT_DIRECTION = 'asc';
    protected const SORT_DIRECTIONS = [
        'asc',
        'desc',
    ];

    /**
     *
     * @var array
     */
    protected array $columns = [];

    /**
     *
     * @var int
     */
    protected int $pageNumber = 1;

    /**
     *
     * @var int
     */
    protected int $itemsOnPage = 10;

    /**
     *
     * @var array
     */
    protected array $sort = [];

    /**
     *
     * @var array
     */
    protected array $availableColumns = [];

    /**
     *
     * @param bool $filter
     * @return array
     */
    public function getColumns(bool $filter = false): array
    {
        if ($filter) {
            return array_intersect($this->columns, $this->getAvailableColumns());
        }
        return $this->columns;
    }

    /**
     *
     * @return int
     */
    public function getPageNumber(): int
    {
        return $this->pageNumber;
    }

    /**
     *
     * @return int
     */
    public function getItemsOnPage(): int
    {
        return $this->itemsOnPage;
    }

    /**
     *
     * @return array
     */
    public function getSort(): array
    {
        $this->checkSortColumns();
        return $this->sort;
    }

    /**
     *
     * @return array
     */
    public function getAvailableColumns(): array
    {
        return $this->availableColumns;
    }

    /**
     *
     * @param array $columns
     * @return Params
     */
    public function setColumns(array $columns): Params
    {
        $this->columns = $columns;
        return $this;
    }

    /**
     *
     * @param int|null $pageNumber
     * @return Params
     */
    public function setPageNumber(?int $pageNumber): Params
    {
        if ($pageNumber <= 0) {
            $pageNumber = 1;
        }
        $this->pageNumber = $pageNumber;
        return $this;
    }

    /**
     *
     * @param int|null $itemsOnPage
     * @return Params
     */
    public function setItemsOnPage(?int $itemsOnPage): Params
    {
        if ($itemsOnPage <= 0) {
            $itemsOnPage = static::DEFAULT_ITEMS_ON_PAGE;
        }
        $this->itemsOnPage = $itemsOnPage;
        return $this;
    }

    /**
     *
     * @param array $sort
     * @return Params
     */
    public function setSort(array $sort): Params
    {
        foreach ($sort as &$sortItem) {
            $sortItem['direction'] = isset($sortItem['direction']) && in_array($sortItem['direction'], static::SORT_DIRECTIONS) ? $sortItem['direction'] : static::DEFAULT_SORT_DIRECTION;
        }
        unset($sortItem);
        $this->sort = $sort;
        return $this;
    }

    /**
     *
     * @param array $availableColumns
     * @return Params
     */
    public function setAvailableColumns(array $availableColumns): Params
    {
        $this->availableColumns = $availableColumns;
        return $this;
    }

    /**
     *
     * @param array $availableColumns
     * @return Params
     */
    public function addAvailableColumns(array $availableColumns): Params
    {
        $this->availableColumns = array_merge($availableColumns, $this->availableColumns);
        return $this;
    }

    /**
     *
     * @return bool
     */
    public function hasSort(): bool
    {
        $this->checkSortColumns();
        return count($this->sort) > 0;
    }

    /**
     *
     * @return Params
     * @throws \RuntimeException
     */
    protected function checkSortColumns(): Params
    {
        foreach ($this->sort as $sort) {
            if (!in_array($sort['column'], $this->getAvailableColumns())) {
                throw new \RuntimeException('Invalid sort column "' . $sort['column'] . '"');
            }
        }
        return $this;
    }
}
