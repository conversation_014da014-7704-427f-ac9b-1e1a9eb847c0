<?php

namespace STClickhouse\Entity\Pagination;

class Pagination
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    /**
     *
     * @var int|null
     */
    protected ?int $count = 0;

    /**
     *
     * @var Params
     */
    protected Params $params;

    /**
     *
     * @var array
     */
    protected array $filter = [];

    /**
     *
     * @var array
     */
    protected array $additionalFilters = [];

    /**
     *
     * @var array
     */
    protected array $having = [];

    /**
     *
     * @var array
     */
    protected array $groupBy = [];

    /**
     *
     * @var array
     */
    protected array $columnMapper = [];

    /**
     *
     * @var array|\STLib\Expand\Collection|\Iterator
     */
    protected array|\STLib\Expand\Collection|\Iterator $result = [];

    /**
     *
     * @var string|null
     */
    protected ?string $resultClass = null;

    /**
     *
     * @var string|null
     */
    protected ?string $resultItemClass = null;

    /**
     *
     * @var callable|null
     */
    protected $resultCallback = null;

    /**
     *
     * @var bool
     */
    protected bool $onlyOnePage = false;

    /**
     *
     * @var bool
     */
    protected bool $useIterator = false;

    /**
     *
     * Class constructor
     */
    public function __construct()
    {
        $this->params = new Params();
    }

    /**
     *
     * @return int
     */
    public function getCount(): int
    {
        return $this->count;
    }

    /**
     *
     * @return Params
     */
    public function getParams(): Params
    {
        return $this->params;
    }

    /**
     *
     * @return array
     */
    public function getFilter(): array
    {
        return $this->filter;
    }

    /**
     *
     * @return array
     */
    public function getHaving(): array
    {
        return $this->having;
    }

    /**
     *
     * @return array
     */
    public function getGroupBy(): array
    {
        return $this->groupBy;
    }

    /**
     *
     * @return array
     */
    public function getColumnMapper(): array
    {
        return $this->columnMapper;
    }

    /**
     *
     * @return array|\STLib\Expand\Collection
     */
    public function getResult(): array|\STLib\Expand\Collection
    {
        return $this->result;
    }

    /**
     *
     * @return string|null
     */
    public function getResultClass(): ?string
    {
        return $this->resultClass;
    }

    /**
     *
     * @return string|null
     */
    public function getResultItemClass(): ?string
    {
        return $this->resultItemClass;
    }

    /**
     *
     * @return callable|null
     */
    public function getResultCallback(): ?callable
    {
        return $this->resultCallback;
    }

    /**
     *
     * @param int $count
     * @return Pagination
     */
    public function setCount(int $count): Pagination
    {
        $this->count = $count;
        return $this;
    }

    /**
     *
     * @param Params $params
     * @return Pagination
     */
    public function setParams(Params $params): Pagination
    {
        $this->params = $params;
        return $this;
    }

    /**
     *
     * @param array $filter
     * @return Pagination
     */
    public function setFilter(array $filter): Pagination
    {
        $this->filter = $filter;
        return $this;
    }

    /**
     *
     * @param array $having
     * @return Pagination
     */
    public function setHaving(array $having): Pagination
    {
        $this->having = $having;
        return $this;
    }

    /**
     *
     * @param array $groupBy
     * @return Pagination
     */
    public function setGroupBy(array $groupBy): Pagination
    {
        $this->groupBy = $groupBy;
        return $this;
    }

    /**
     *
     * @param array $columnMapper
     * @return Pagination
     */
    public function setColumnMapper(array $columnMapper): Pagination
    {
        $this->columnMapper = $columnMapper;
        return $this;
    }

    /**
     *
     * @param array|\STLib\Expand\Collection|\Iterator $result
     * @param bool $force
     * @return $this
     */
    public function setResult(array|\STLib\Expand\Collection|\Iterator $result, bool $force = false): Pagination
    {
        if ($force) {
            $this->result = $result;
            return $this;
        }
        // create result instance
        if (!is_null($this->getResultClass())) {
            if (!class_exists($this->getResultClass())) {
                throw new \RuntimeException('Cannot find class "' . $this->getResultClass() . '"');
            }
            $this->result = new ($this->getResultClass());
        }

        // insert items to result
        foreach ($result as $item) {
            if (is_string($this->getResultItemClass())) {
                if (!class_exists($this->getResultItemClass())) {
                    throw new \RuntimeException('Cannot find class "' . $this->getResultItemClass() . '"');
                }
                $item = $this->hydrate($item, $this->getResultItemClass(), withConstructor: true);
            }
            if (is_array($this->result)) {
                $this->result[] = $item;
            } elseif ($this->result instanceof \STLib\Expand\Collection) {
                $this->result->add($item);
            } else {
                throw new \RuntimeException('Invalid collection class "' . $this->getResultItemClass() . '"');
            }
        }

        if (is_array($this->result) && !is_null($this->getResultCallback())) {
            $this->result = array_map($this->getResultCallback(), $this->result);
        }

        return $this;
    }

    /**
     *
     * @param string|null $resultClass
     * @return Pagination
     */
    public function setResultClass(?string $resultClass): Pagination
    {
        $this->resultClass = $resultClass;
        return $this;
    }

    /**
     *
     * @param string|null $resultItemClass
     * @return Pagination
     */
    public function setResultItemClass(?string $resultItemClass): Pagination
    {
        $this->resultItemClass = $resultItemClass;
        return $this;
    }

    /**
     *
     * @param callable|null $resultCallback
     * @return Pagination
     */
    public function setResultCallback(?callable $resultCallback): Pagination
    {
        $this->resultCallback = $resultCallback;
        return $this;
    }

    /**
     *
     * @param string $column
     * @param mixed $value
     * @return Pagination
     */
    public function addFilter(string $column, mixed $value): Pagination
    {
        $this->filter[$column] = $value;
        return $this;
    }

    /**
     * @return array
     */
    public function getAdditionalFilters(): array
    {
        return $this->additionalFilters;
    }

    /**
     * @param array $additionalFilters
     * @return self
     */
    public function setAdditionalFilters(array $additionalFilters): self
    {
        $this->additionalFilters = $additionalFilters;

        return $this;
    }

    /**
     *
     * @param string $having
     * @return Pagination
     */
    public function addHaving(string $having): Pagination
    {
        $this->having[] = $having;
        return $this;
    }

    /**
     *
     * @return Pagination
     */
    public function clearFilter(): Pagination
    {
        $this->filter = [];
        return $this;
    }

    /**
     *
     * @return Pagination
     */
    public function clearResult(): Pagination
    {
        $this->result = [];
        return $this;
    }

    /**
     *
     * @return Pagination
     */
    public function normalizeFilter(): Pagination
    {
        $normalizedFilter = [];
        foreach ($this->getFilter() as $column => $value) {
            $column = $this->getNormalizedColumnName($column);
            if (isset($value['column'])) {
                $value['column'] = $this->getNormalizedColumnName($value['column']);
            }
            $normalizedFilter[$column] = $value;
        }
        $this->setFilter($normalizedFilter);
        return $this;
    }

    /**
     *
     * @param string $column
     * @return string
     */
    public function getNormalizedColumnName(string $column): string
    {
        if (array_key_exists($column, $this->columnMapper)) {
            return $this->columnMapper[$column];
        }
        return $column;
    }

    /**
     *
     * @return int
     */
    public function getLastPage(): int
    {
        if (is_null($this->count)) {
            throw new \RuntimeException('Count value is not set');
        }
        return ceil($this->count / $this->getParams()->getItemsOnPage());
    }

    /**
     *
     * @return int
     */
    public function getPage(): int
    {
        return min($this->getLastPage(), $this->params->getPageNumber());
    }

    /**
     *
     * @return int
     */
    public function getOffset(): int
    {
        return max(($this->getPage() - 1), 0) * ($this->getParams()->getItemsOnPage());
    }

    /**
     *
     * @param bool|null $onlyOnePage
     * @return Pagination|bool
     */
    public function onlyOnePage(?bool $onlyOnePage = null): Pagination|bool
    {
        if (is_null($onlyOnePage)) {
            return $this->onlyOnePage;
        }
        $this->onlyOnePage = $onlyOnePage;
        return $this;
    }

    /**
     *
     * @param bool|null $useIterator
     * @return Pagination|bool
     */
    public function useIterator(?bool $useIterator = null): Pagination|bool
    {
        if (is_null($useIterator)) {
            return $this->useIterator;
        }
        $this->useIterator = $useIterator;
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        $result = $this->extract($this);
        if ($this->result instanceof \STLib\Expand\Collection) {
            $result['result'] = $this->result->toArray();
        }
        unset($result['params']);
        unset($result['offset']);
        unset($result['filter']);
        unset($result['additional_filters']);
        unset($result['having']);
        unset($result['column_mapper']);
        unset($result['result_class']);
        unset($result['result_item_class']);
        unset($result['group_by']);
        unset($result['result_callback']);
        return $result;
    }
}
