<?php

namespace STClickhouse\Entity\Pagination;

class CandidatePagination
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    /**
     *
     * @var int|null
     */
    protected ?int $count = 0;

    /**
     *
     * @var Params
     */
    protected Params $params;

    /**
     *
     * @var array
     */
    protected array $filter = [];

    /**
     *
     * @var array|\Iterator
     */
    protected array|\Iterator $result = [];

    /**
     *
     * @var bool
     */
    protected bool $onlyOnePage = false;

    /**
     *
     * @var array|null
     */
    protected ?array $candidateIds = null;

    /**
     *
     * @var callable|null
     */
    protected $resultCallback = null;

    /**
     *
     * Class constructor
     */
    public function __construct()
    {
        $this->params = new Params();
    }

    /**
     *
     * @return int
     */
    public function getCount(): int
    {
        return $this->count;
    }

    /**
     *
     * @return Params
     */
    public function getParams(): Params
    {
        return $this->params;
    }

    /**
     *
     * @return array
     */
    public function getFilter(): array
    {
        return $this->filter;
    }

    /**
     *
     * @return array|\STLib\Expand\Collection
     */
    public function getResult(): array|\STLib\Expand\Collection
    {
        return $this->result;
    }

    /**
     * @return array|null
     */
    public function getCandidateIds(): ?array
    {
        return $this->candidateIds;
    }

    /**
     * @return callable|null
     */
    public function getResultCallback(): ?callable
    {
        return $this->resultCallback;
    }

    /**
     *
     * @param int $count
     * @return CandidatePagination
     */
    public function setCount(int $count): CandidatePagination
    {
        $this->count = $count;
        return $this;
    }

    /**
     *
     * @param Params $params
     * @return CandidatePagination
     */
    public function setParams(Params $params): CandidatePagination
    {
        $this->params = $params;
        return $this;
    }

    /**
     *
     * @param array $filter
     * @return CandidatePagination
     */
    public function setFilter(array $filter): CandidatePagination
    {
        $this->filter = $filter;
        return $this;
    }

    /**
     *
     * @param array|\Iterator $result
     * @return CandidatePagination
     */
    public function setResult(array|\Iterator $result): CandidatePagination
    {
        $this->result = is_array($result) && !is_null($this->getResultCallback()) ? array_map($this->getResultCallback(), $result) : $result;
        return $this;
    }

    /**
     * @param array|null $candidateIds
     * @return CandidatePagination
     */
    public function setCandidateIds(?array $candidateIds): CandidatePagination
    {
        $this->candidateIds = $candidateIds;
        return $this;
    }

    /**
     * @param callable|null $resultCallback
     * @return CandidatePagination
     */
    public function setResultCallback(?callable $resultCallback): CandidatePagination
    {
        $this->resultCallback = $resultCallback;
        return $this;
    }

    /**
     *
     * @param string $column
     * @param mixed $value
     * @return CandidatePagination
     */
    public function addFilter(string $column, mixed $value): CandidatePagination
    {
        $this->filter[$column] = $value;
        return $this;
    }

    /**
     *
     * @return int
     */
    public function getLastPage(): int
    {
        if (is_null($this->count)) {
            throw new \RuntimeException('Count value is not set');
        }
        return ceil($this->count / $this->getParams()->getItemsOnPage());
    }

    /**
     *
     * @return int
     */
    public function getPage(): int
    {
        return min($this->getLastPage(), $this->params->getPageNumber());
    }

    /**
     *
     * @return int
     */
    public function getOffset(): int
    {
        return max(($this->getPage() - 1), 0) * ($this->getParams()->getItemsOnPage());
    }

    /**
     *
     * @param bool|null $onlyOnePage
     * @return CandidatePagination|bool
     */
    public function onlyOnePage(?bool $onlyOnePage = null): CandidatePagination|bool
    {
        if (is_null($onlyOnePage)) {
            return $this->onlyOnePage;
        }
        $this->onlyOnePage = $onlyOnePage;
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        $result = $this->extract($this);
        if ($this->result instanceof \STLib\Expand\Collection) {
            $result['result'] = $this->result->toArray();
        }
        unset($result['params']);
        unset($result['offset']);
        unset($result['filter']);
        unset($result['result_callback']);
        unset($result['candidate_ids']);
        return $result;
    }
}
