<?php

declare(strict_types=1);

namespace STClickhouse\Controller\Plugin;

use Laminas\Mvc\Controller\Plugin\AbstractPlugin;

class Clickhouse extends AbstractPlugin
{
    /**
     *
     * @return \STClickhouse\Service\MigrationService
     */
    public function migration(): \STClickhouse\Service\MigrationService
    {
        return $this->getController()->getServiceManager()->get(\STClickhouse\Service\MigrationService::class);
    }
}
