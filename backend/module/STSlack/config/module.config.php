<?php

declare(strict_types=1);

namespace STSlack;

use STOnboarding\Service\Notification\Interfaces\SenderInterface as OnboardingSenderInterface;
use STSlack\ServiceProviding\SlackServiceProvider;

return [
    'service_manager' => [
        'factories' => [
            Service\SlackService::class => Service\SlackServiceFactory::class,
        ],
        'aliases' => [
            OnboardingSenderInterface::class => SlackServiceProvider::class,
        ],
    ],
];
