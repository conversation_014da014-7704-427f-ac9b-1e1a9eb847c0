<?php

declare(strict_types=1);

namespace STSlack\Service;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use RuntimeException;
use STCompany\Entity\Company;
use STCompany\Entity\Event\Event;

class SlackService
{
    /**
     *
     * @var array
     */
    protected array $config = [];

    /**
     *
     * @param array $config
     * @throws RuntimeException
     */
    public function __construct(array $config = [])
    {
        $this->config = $config;
    }

    /**
     * @param string $linkToFile
     * @param Company|null $company
     * @param Event|null $event
     * @return void
     * @throws GuzzleException
     */
    public function sendEmsDataSetConfirmedNotification(string $linkToFile, ?Company $company, ?Event $event): void
    {
        $blocks = [];
        // title
        $blocks[] = [
            'type' => 'rich_text',
            'elements' => [
                [
                    'type' => 'rich_text_section',
                    'elements' => [
                        [
                            'type' => 'text',
                            'text' => 'Data set is exported',
                        ],
                    ],
                ],
            ],
        ];
        // environment
        $blocks[] = [
            'type' => 'rich_text',
            'elements' => [
                [
                    'type' => 'rich_text_section',
                    'elements' => [
                        [
                            'type' => 'text',
                            'text' => 'Environment: ',
                        ],
                        [
                            'type' => 'text',
                            'text' => getenv('APP_ENV') ?? 'local',
                            'style' => [
                                'bold' => true,
                            ],
                        ],
                    ],
                ],
            ],
        ];
        // company info if exists
        if ($company instanceof Company) {
            $blocks[] = [
                'type' => 'rich_text',
                'elements' => [
                    [
                        'type' => 'rich_text_section',
                        'elements' => [
                            [
                                'type' => 'text',
                                'text' => 'Company: ',
                            ],
                            [
                                'type' => 'text',
                                'text' => $company->getName() . ' (' . $company->getId() . ')',
                                'style' => [
                                    'bold' => true,
                                ],
                            ],
                        ],
                    ],
                ],
            ];
        }
        // event info if exists
        if ($event instanceof Event) {
            $blocks[] = [
                'type' => 'rich_text',
                'elements' => [
                    [
                        'type' => 'rich_text_section',
                        'elements' => [
                            [
                                'type' => 'text',
                                'text' => 'Event: ',
                            ],
                            [
                                'type' => 'text',
                                'text' => $event->getName(),
                                'style' => [
                                    'bold' => true,
                                ],
                            ],
                        ],
                    ],
                ],
            ];
        }
        // link to file
        $blocks[] = [
            'type' => 'rich_text',
            'elements' => [
                [
                    'type' => 'rich_text_section',
                    'elements' => [
                        [
                            'type' => 'link',
                            'url' => $linkToFile,
                            'text' => 'Link to data set file',
                        ],
                    ],
                ],
            ],
        ];
        $payload = [
            'channel' => $this->config['channels']['ems'],
            'blocks' => $blocks,
        ];

        $this->postMessage($payload);
    }

    /**
     * @param array $payload
     * @return void
     * @throws GuzzleException
     */
    private function postMessage(array $payload): void
    {
        (new Client())->request('POST', 'https://slack.com/api/chat.postMessage', [
            'headers' => [
                'Content-type' => 'application/json',
                'Authorization' => 'Bearer ' . $this->config['bot']['token'],
            ],
            'json' => $payload,
        ]);
    }
}
