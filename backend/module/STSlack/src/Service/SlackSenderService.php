<?php

declare(strict_types=1);

namespace STSlack\Service;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use STSlack\Service\Interfaces\ConfigurationInterface;

class SlackSenderService
{
    public function __construct(private readonly ConfigurationInterface $configuration)
    {
    }

    /**
     * @throws GuzzleException
     */
    public function send(string $header, array $params, string $channel): void
    {
        $client = $this->createClient();

        $config = $this->configuration->get('slack');

        $blocks = [];

        // title
        $blocks[] = [
            'type' => 'section',
            'text' => [
                'type' => 'mrkdwn',
                'text' => $header,
            ]
        ];

        // environment
        $blocks[] = [
            'type' => 'rich_text',
            'elements' => [
                [
                    'type' => 'rich_text_section',
                    'elements' => [
                        [
                            'type' => 'text',
                            'text' => 'Environment: ',
                        ],
                        [
                            'type' => 'text',
                            'text' => $this->configuration->get('api')['env'],
                            'style' => [
                                'bold' => true,
                            ],
                        ],
                    ],
                ],
            ],
        ];

        $blocksText = '';
        foreach ($params as $name => $value) {
            if (is_array($value)) {
                $value = implode(', ', $value);
            }

            if (filter_var($value, FILTER_VALIDATE_URL)) {
                $blocksText .= $name . ': ' . $value;
            } else {
                $blocksText .= $name . ': ' . '*' . $value . '*';
            }

            $blocksText .= "\n";
        }

        $blocks[] = [
            'type' => 'section',
            'text' => [
                'type' => 'mrkdwn',
                'text' => $blocksText,
            ]
        ];

        $payload = [
            'channel' => $channel,
            'blocks' => $blocks,
        ];

        $client->request('POST', 'https://slack.com/api/chat.postMessage', [
            'headers' => [
                'Content-type' => 'application/json',
                'Authorization' => 'Bearer ' . $config['bot']['token']
            ],
            'json' => $payload,
        ]);
    }

    protected function createClient(): Client
    {
        return new Client();
    }
}
