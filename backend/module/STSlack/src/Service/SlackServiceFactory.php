<?php

declare(strict_types=1);

namespace STSlack\Service;

class SlackServiceFactory implements \Laminas\ServiceManager\Factory\FactoryInterface
{
    /**
     *
     * @param \Interop\Container\ContainerInterface $container
     * @param string $requestedName
     * @param array $options
     * @return \STSlack\Service\SlackService
     */
    public function createService(\Interop\Container\ContainerInterface $container, $requestedName, array $options = null): SlackService
    {
        return $this($container, $requestedName ?: PatternPluginManager::class, $this->creationOptions);
    }

    /**
     *
     * @param \Interop\Container\ContainerInterface $container
     * @param string $requestedName
     * @param array $options
     * @return \STSlack\Service\SlackService
     */
    public function __invoke(\Interop\Container\ContainerInterface $container, $requestedName, array $options = null): SlackService
    {
        $config = $container->get('config');
        $slackConfig = $config['slack'] ?? [];
        return new SlackService($slackConfig);
    }
}
