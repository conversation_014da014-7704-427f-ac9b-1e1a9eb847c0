<?php

declare(strict_types=1);

namespace STSlack\ServiceProviding;

use STOnboarding\Service\Notification\Interfaces\SenderInterface as OnboardingSenderInterface;
use STSlack\Service\SlackSenderService;

final readonly class SlackServiceProvider implements OnboardingSenderInterface
{
    public function __construct(private SlackSenderService $slackSenderService)
    {
    }

    public function send(string $header, array $params, string $channel): void
    {
        $this->slackSenderService->send($header, $params, $channel);
    }
}
