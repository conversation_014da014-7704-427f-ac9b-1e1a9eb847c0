<?php

declare(strict_types=1);

namespace STDashboard;

return [
    'controller_plugins' => [
        'invokables' => [
            'dashboard' => Controller\Plugin\Dashboard::class,
        ],
    ],
    'service_manager' => [
        'factories' => [
            Data\DashboardStatisticsCallsTable::class => \STClickhouse\Entity\TableFactory::class,
            Data\DashboardStatisticsCallsLanguagesTable::class => \STClickhouse\Entity\TableFactory::class,
            Data\DashboardStatisticsManagersKPITable::class => \STClickhouse\Entity\TableFactory::class,
            Data\DashboardStatisticsReviewedEventsTable::class => \STClickhouse\Entity\TableFactory::class,
            Data\DashboardStatisticsReviewedClientsTable::class => \STClickhouse\Entity\TableFactory::class,
            Service\UpdateDashboardStatisticsService::class => \STLib\Mvc\DependencyInjection\DefaultFactory::class,
            Service\DashboardStatisticsService::class => \STLib\Mvc\DependencyInjection\DefaultFactory::class,
        ],
    ],
];
