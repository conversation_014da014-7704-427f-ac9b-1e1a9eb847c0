<?php

declare(strict_types=1);

namespace STDashboard\Controller\Plugin;

use Laminas\Mvc\Controller\Plugin\AbstractPlugin;

class Dashboard extends AbstractPlugin
{
    /**
     *
     * @return \STDashboard\Service\UpdateDashboardStatisticsService
     */
    public function updateDashboardStatisticsService(): \STDashboard\Service\UpdateDashboardStatisticsService
    {
        return $this->getController()->getServiceManager()->get(\STDashboard\Service\UpdateDashboardStatisticsService::class);
    }

    /**
     *
     * @return \STDashboard\Service\DashboardStatisticsService
     */
    public function dashboardStatisticsService(): \STDashboard\Service\DashboardStatisticsService
    {
        return $this->getController()->getServiceManager()->get(\STDashboard\Service\DashboardStatisticsService::class);
    }
}
