<?php

declare(strict_types=1);

namespace STDashboard\Service;

use STDashboard\Data\DashboardStatisticsCallsLanguagesTable;
use STDashboard\Data\DashboardStatisticsCallsTable;
use STDashboard\Data\DashboardStatisticsEventsCategoriesTable;
use STDashboard\Data\DashboardStatisticsReviewedEventsTable;
use STDashboard\Data\DashboardStatisticsManagersKPITable;
use STDashboard\Data\DashboardStatisticsReviewedClientsTable;

class DashboardStatisticsService
{
    /**
     *
     * @param DashboardStatisticsCallsLanguagesTable $dashboardStatisticsCallsLanguagesTable
     * @param DashboardStatisticsCallsTable $dashboardStatisticsCallsTable
     * @param DashboardStatisticsManagersKPITable $dashboardStatisticsManagersKPITable
     * @param DashboardStatisticsReviewedClientsTable $dashboardStatisticsReviewedClientsTable
     * @param DashboardStatisticsReviewedEventsTable $dashboardStatisticsReviewedEventsTable
     * @param DashboardStatisticsEventsCategoriesTable $dashboardStatisticsEventsCategoriesTable
     */
    public function __construct(
        protected DashboardStatisticsCallsLanguagesTable $dashboardStatisticsCallsLanguagesTable,
        protected DashboardStatisticsCallsTable $dashboardStatisticsCallsTable,
        protected DashboardStatisticsManagersKPITable $dashboardStatisticsManagersKPITable,
        protected DashboardStatisticsReviewedClientsTable $dashboardStatisticsReviewedClientsTable,
        protected DashboardStatisticsReviewedEventsTable $dashboardStatisticsReviewedEventsTable,
        protected DashboardStatisticsEventsCategoriesTable $dashboardStatisticsEventsCategoriesTable,
    ) {
    }

    /**
     * @param int $companyId
     * @param ?int $roleId
     * @param string|null $startDate
     * @param string|null $endDate
     * @param array $teamsIds
     * @return array
     */
    public function getCallsStatistics(
        int $companyId,
        ?int $roleId,
        string $startDate = null,
        string $endDate = null,
        array $teamsIds = [],
    ): array {
        return $this->dashboardStatisticsCallsTable->getStatistics(
            $companyId,
            $roleId,
            $startDate,
            $endDate,
            $teamsIds,
        );
    }

    /**
     * @param int $companyId
     * @param ?int $roleId
     * @param string|null $startDate
     * @param string|null $endDate
     * @param array $teamsIds
     * @return array
     */
    public function getMangersKPIStatistics(
        int $companyId,
        ?int $roleId,
        string $startDate = null,
        string $endDate = null,
        array $teamsIds = [],
    ): array {
        return $this->dashboardStatisticsManagersKPITable->getStatistics(
            $companyId,
            $roleId,
            $startDate,
            $endDate,
            $teamsIds,
        );
    }

    /***
     * @param int $companyId
     * @param int|null $roleId
     * @param string|null $startDate
     * @param string|null $endDate
     * @param array $teamsIds
     * @return array
     */
    public function getMangersKPIStatisticsForCsvReport(
        int $companyId,
        ?int $roleId,
        string $startDate = null,
        string $endDate = null,
        array $teamsIds = [],
    ): array {
        $result = [];
        $statistics = $this->getMangersKPIStatistics(
            $companyId,
            $roleId,
            $startDate,
            $endDate,
            $teamsIds,
        );
        $detailedStatistics = $this->getDetailedManagersKPIStatistics(
            $companyId,
            $roleId,
            $startDate,
            $endDate,
            $teamsIds,
        );

        $summary = sprintf(
            'Reviewed Calls: %s; Reviewed Hours: %s; Comments Left: %s; Commented Calls: %s; Fully Reviewed: %s; Date Range: %s',
            $statistics['total_count'],
            $statistics['calls_duration'],
            $statistics['comments_left_count'],
            $statistics['commented_calls_count'],
            $statistics['fully_reviewed_count'] . '(' . $statistics['fully_reviewed_count_percentage'] . '%)',
            $startDate . ' - ' . $endDate,
        );

        foreach ($detailedStatistics as $i => $detailedStatisticsItem) {
            // If it's a first row we add value for only summary column
            $result[$i]['manager_name'] = $i === 0 ? '' : $detailedStatisticsItem['user_name'];
            $result[$i]['total_call_count'] = $i === 0 ? '' : $detailedStatisticsItem['total_calls_count'];
            $result[$i]['unique_clients'] = $i === 0 ? '' : $detailedStatisticsItem['unique_clients_count'];
            $result[$i]['calls_duration'] = $i === 0 ? '' : $detailedStatisticsItem['calls_duration'];
            $result[$i]['comments_left'] = $i === 0 ? '' : $detailedStatisticsItem['comments_left'];
            $result[$i]['summary'] = $i === 0 ? $summary : '';
        }

        return $result;
    }

    /**
     * @param int $companyId
     * @param ?int $roleId
     * @param string|null $startDate
     * @param string|null $endDate
     * @param array $teamsIds
     * @return array
     */
    public function getDetailedManagersKPIStatistics(
        int $companyId,
        ?int $roleId,
        string $startDate = null,
        string $endDate = null,
        array $teamsIds = [],
    ): array {
        $statisticsData = $this->dashboardStatisticsManagersKPITable->getDetailedStatistics(
            $companyId,
            $roleId,
            $startDate,
            $endDate,
            $teamsIds
        );
        $result = [];

        if (empty($statisticsData)) {
            return $result;
        }

        return $statisticsData;
    }

    /**
     * @param int $companyId
     * @param ?int $roleId
     * @param string|null $startDate
     * @param string|null $endDate
     * @param array $teamsIds
     * @return array
     */
    public function getReviewedStatistics(
        int $companyId,
        ?int $roleId,
        string $startDate = null,
        string $endDate = null,
        array $teamsIds = [],
    ): array {
        return $this->dashboardStatisticsReviewedEventsTable->getReviewedStatistics(
            $companyId,
            $roleId,
            $startDate,
            $endDate,
            $teamsIds
        );
    }

    /**
     * @param int $companyId
     * @param ?int $roleId
     * @param string|null $startDate
     * @param string|null $endDate
     * @param array $teamsIds
     * @return array
     */
    public function getDetailedReviewedStatistics(
        int $companyId,
        ?int $roleId,
        string $startDate = null,
        string $endDate = null,
        array $teamsIds = [],
    ): array {
        return $this->dashboardStatisticsReviewedEventsTable->getDetailedReviewedStatistics(
            $companyId,
            $roleId,
            $startDate,
            $endDate,
            $teamsIds
        );
    }

    /**
     *
     * @param int $categoryId
     * @param int $companyId
     * @param int|null $roleId
     * @param string $startDate
     * @param string $endDate
     * @param array $teamsIds
     * @return array
     */
    public function getReviewedEventsStatistics(
        int $categoryId,
        int $companyId,
        ?int $roleId,
        string $startDate = null,
        string $endDate = null,
        array $teamsIds = [],
    ): array {
        return $this->dashboardStatisticsReviewedEventsTable->getReviewedEventsStatistics(
            $categoryId,
            $companyId,
            $roleId,
            $startDate,
            $endDate,
            $teamsIds
        );
    }

    /**
     * @param int $companyId
     * @param ?int $roleId
     * @param string|null $startDate
     * @param string|null $endDate
     * @param array $teamsIds
     * @return array
     */
    public function getEventsCategoriesStatistics(
        int $companyId,
        ?int $roleId,
        string $startDate = null,
        string $endDate = null,
        array $teamsIds = [],
    ): array {
        $statisticsData = $this->dashboardStatisticsEventsCategoriesTable->getEventsCategoriesStatistics(
            $companyId,
            $roleId,
            $startDate,
            $endDate,
            $teamsIds
        );
        $callsStatistics = $this->dashboardStatisticsCallsTable->getStatistics(
            $companyId,
            $roleId,
            $startDate,
            $endDate,
            $teamsIds
        );

        return $this->getEventsCategoriesData($statisticsData, (int) $callsStatistics['analyzed_count']);
    }

    /**
     *
     * @param int $categoryId
     * @param int $companyId
     * @param int|null $roleId
     * @param string|null $startDate
     * @param string|null $endDate
     * @param array $teamsIds
     * @return array
     */
    public function getDetailedEventsCategoriesStatistics(
        int $categoryId,
        int $companyId,
        ?int $roleId,
        string $startDate = null,
        string $endDate = null,
        array $teamsIds = [],
    ): array {
        $statisticsData = $this->dashboardStatisticsEventsCategoriesTable->getDetailedEventsCategoriesStatistics(
            $categoryId,
            $companyId,
            $roleId,
            $startDate,
            $endDate,
            $teamsIds,
        );
        $callsStatistics = $this->dashboardStatisticsCallsTable->getStatistics(
            $companyId,
            $roleId,
            $startDate,
            $endDate,
            $teamsIds
        );

        return $this->getEventsCategoriesData($statisticsData, (int) $callsStatistics['analyzed_count']);
    }

    /**
     * @param int $companyId
     * @param ?int $roleId
     * @param string|null $startDate
     * @param string|null $endDate
     * @param array $teamsIds
     * @return array
     */
    public function getReviewedClientsStatistics(
        int $companyId,
        ?int $roleId,
        string $startDate = null,
        string $endDate = null,
        array $teamsIds = [],
    ): array {
        return $this->dashboardStatisticsReviewedClientsTable->getStatistics(
            $companyId,
            $roleId,
            $startDate,
            $endDate,
            $teamsIds,
        );
    }

    /**
     * @param int $companyId
     * @param ?int $roleId
     * @param string|null $startDate
     * @param string|null $endDate
     * @param array $teamsIds
     * @return array
     */
    public function callsLanguagesStatistics(
        int $companyId,
        ?int $roleId,
        string $startDate = null,
        string $endDate = null,
        array $teamsIds = [],
    ): array {
        $dashboardCallsLanguagesStatistics = $this->dashboardStatisticsCallsLanguagesTable->getStatistics(
            $companyId,
            $roleId,
            $startDate,
            $endDate,
            $teamsIds,
        );

        $result = [];
        $totalCallsCount = array_sum(
            array_column($dashboardCallsLanguagesStatistics, 'calls_count')
        );

        foreach ($dashboardCallsLanguagesStatistics as $languageData) {
            $result[] = [
                'language' => $languageData['language'],
                'calls_count' => $languageData['calls_count'],
                'percentage' => round(($languageData['calls_count'] / $totalCallsCount) * 100),
            ];
        }

        return $result;
    }

    /**
     * @param array $dashboardEventsCategoriesStatistics
     * @param int $totalCallsCount
     * @return array
     */
    private function getEventsCategoriesData(array $dashboardEventsCategoriesStatistics, int $totalCallsCount): array
    {
        $result = [];

        if (empty($dashboardEventsCategoriesStatistics)) {
            return [
                'total_calls_count' => 0,
                'items' => [],
            ];
        }

        $result['total_calls_count'] = $totalCallsCount;

        $result['items'] = array_map(static function ($eventCategoryStatistics) use ($totalCallsCount) {
            $eventCategoryStatistics['percentage'] = round(($eventCategoryStatistics['calls_count'] / $totalCallsCount) * 100);

            return $eventCategoryStatistics;
        }, $dashboardEventsCategoriesStatistics);

        return $result;
    }
}
