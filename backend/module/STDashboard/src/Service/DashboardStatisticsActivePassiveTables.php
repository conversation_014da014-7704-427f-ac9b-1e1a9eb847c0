<?php

declare(strict_types=1);

namespace STDashboard\Service;

trait DashboardStatisticsActivePassiveTables
{
    use \STRedis\Service\ProvidesRedis;

    /**
     *
     * @param string $tableName
     * @return string
     */
    protected function getPassiveTableName(string $tableName): string
    {
        $passiveTableName = $this->redis()->get($this->getTableNameFormat($tableName, 'passive'));

        return $passiveTableName ?? ($tableName . '_1');
    }

    /**
     *
     * @param string $tableName
     * @return string
     */
    protected function getActiveTableName(string $tableName): string
    {
        $activeTableName = $this->redis()->get($this->getTableNameFormat($tableName, 'active'));

        return $activeTableName ?? ($tableName . '_2');
    }

    /**
     *
     * @param string $tableName
     * @return void
     */
    protected function executeSwapActiveAndPassiveTables(string $tableName): void
    {
        $activeTableName = $this->getActiveTableName($tableName);
        $passiveTableName = $this->getPassiveTableName($tableName);

        $this->redis()->mset([
            $this->getTableNameFormat($tableName, 'active') => $passiveTableName,
            $this->getTableNameFormat($tableName, 'passive') => $activeTableName,
        ]);
    }

    /**
     *
     * @param string $tableName
     * @param string $type
     * @return string
     */
    private function getTableNameFormat(string $tableName, string $type): string
    {
        return sprintf('dashboard_tables:%s:%s', $tableName, $type);
    }
}
