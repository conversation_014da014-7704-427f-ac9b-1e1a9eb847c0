<?php

declare(strict_types=1);

namespace STDashboard\Service;

use STDashboard\Data\DashboardStatisticsCallsLanguagesTable;
use STDashboard\Data\DashboardStatisticsCallsTable;
use STDashboard\Data\DashboardStatisticsEventsCategoriesTable;
use STDashboard\Data\DashboardStatisticsReviewedEventsTable;
use STDashboard\Data\DashboardStatisticsManagersKPITable;
use STDashboard\Data\DashboardStatisticsReviewedClientsTable;

class UpdateDashboardStatisticsService
{
    /**
     *
     * @param DashboardStatisticsCallsLanguagesTable $dashboardStatisticsCallsLanguagesTable
     * @param DashboardStatisticsCallsTable $dashboardStatisticsCallsTable
     * @param DashboardStatisticsManagersKPITable $dashboardStatisticsManagersKPITable
     * @param DashboardStatisticsReviewedClientsTable $dashboardStatisticsReviewedClientsTable
     * @param DashboardStatisticsReviewedEventsTable $dashboardStatisticsReviewedEventsTable
     * @param DashboardStatisticsEventsCategoriesTable $dashboardStatisticsEventsCategoriesTable
     */
    public function __construct(
        protected DashboardStatisticsCallsLanguagesTable $dashboardStatisticsCallsLanguagesTable,
        protected DashboardStatisticsCallsTable $dashboardStatisticsCallsTable,
        protected DashboardStatisticsManagersKPITable $dashboardStatisticsManagersKPITable,
        protected DashboardStatisticsReviewedClientsTable $dashboardStatisticsReviewedClientsTable,
        protected DashboardStatisticsReviewedEventsTable $dashboardStatisticsReviewedEventsTable,
        protected DashboardStatisticsEventsCategoriesTable $dashboardStatisticsEventsCategoriesTable,
    ) {
    }

    /**
     *
     * @param int $companyId
     * @param \Carbon\Carbon $startDate
     * @param \Carbon\Carbon $endDate
     * @return void
     */
    public function updateCallsStatistics(int $companyId, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): void
    {
        $this->dashboardStatisticsCallsTable->updateStatistics($companyId, $startDate, $endDate);
    }

    /**
     *
     * @param int $companyId
     * @param \Carbon\Carbon $startDate
     * @param \Carbon\Carbon $endDate
     * @return void
     */
    public function updateCallsLanguagesStatistics(int $companyId, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): void
    {
        $this->dashboardStatisticsCallsLanguagesTable->updateStatistics($companyId, $startDate, $endDate);
    }

    /**
     *
     * @param int $companyId
     * @param \Carbon\Carbon $startDate
     * @param \Carbon\Carbon $endDate
     * @return void
     */
    public function updateEventsStatistics(int $companyId, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): void
    {
        $this->dashboardStatisticsReviewedEventsTable->updateStatistics($companyId, $startDate, $endDate);
    }

    /**
     *
     * @param int $companyId
     * @param \Carbon\Carbon $startDate
     * @param \Carbon\Carbon $endDate
     * @return void
     */
    public function updateEventsCategoriesStatistics(int $companyId, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): void
    {
        $this->dashboardStatisticsEventsCategoriesTable->updateStatistics($companyId, $startDate, $endDate);
    }

    /**
     *
     * @param int $companyId
     * @param \Carbon\Carbon $startDate
     * @param \Carbon\Carbon $endDate
     * @return void
     */
    public function updateReviewedClientsStatistics(int $companyId, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): void
    {
        $this->dashboardStatisticsReviewedClientsTable->updateStatistics($companyId, $startDate, $endDate);
    }

    /**
     *
     * @param int $companyId
     * @param \Carbon\Carbon $startDate
     * @param \Carbon\Carbon $endDate
     * @return void
     */
    public function updateManagersKPIStatistics(int $companyId, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): void
    {
        $this->dashboardStatisticsManagersKPITable->updateStatistics($companyId, $startDate, $endDate);
    }

    /**
     *
     * @return void
     */
    public function truncateCallStatistics(): void
    {
        $this->dashboardStatisticsCallsTable->truncateTable();
    }

    /**
     *
     * @return void
     */
    public function truncateCallsLanguagesStatistics(): void
    {
        $this->dashboardStatisticsCallsLanguagesTable->truncateTable();
    }

    /**
     *
     * @return void
     */
    public function truncateEventsStatistics(): void
    {
        $this->dashboardStatisticsReviewedEventsTable->truncateTable();
    }

    /**
     *
     * @return void
     */
    public function truncateEventsCategoriesStatistics(): void
    {
        $this->dashboardStatisticsEventsCategoriesTable->truncateTable();
    }

    /**
     *
     * @return void
     */
    public function swapEventsCategoriesStatistics(): void
    {
        $this->dashboardStatisticsEventsCategoriesTable->swapActiveAndPassiveTables();
    }

    /**
     *
     * @return void
     */
    public function truncateReviewedClientsStatistics(): void
    {
        $this->dashboardStatisticsReviewedClientsTable->truncateTable();
    }

    /**
     *
     * @return void
     */
    public function truncateManagersKPIStatistics(): void
    {
        $this->dashboardStatisticsManagersKPITable->truncateTable();
    }

    /**
     *
     * @return void
     */
    public function swapCallStatistics(): void
    {
        $this->dashboardStatisticsCallsTable->swapActiveAndPassiveTables();
    }

    /**
     *
     * @return void
     */
    public function swapCallsLanguagesStatistics(): void
    {
        $this->dashboardStatisticsCallsLanguagesTable->swapActiveAndPassiveTables();
    }

    /**
     *
     * @return void
     */
    public function swapEventsStatistics(): void
    {
        $this->dashboardStatisticsReviewedEventsTable->swapActiveAndPassiveTables();
    }

    /**
     *
     * @return void
     */
    public function swapReviewedClientsStatistics(): void
    {
        $this->dashboardStatisticsReviewedClientsTable->swapActiveAndPassiveTables();
    }

    /**
     *
     * @return void
     */
    public function swapManagersKPIStatistics(): void
    {
        $this->dashboardStatisticsManagersKPITable->swapActiveAndPassiveTables();
    }
}
