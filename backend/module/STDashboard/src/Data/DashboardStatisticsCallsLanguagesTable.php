<?php

declare(strict_types=1);

namespace STDashboard\Data;

class DashboardStatisticsCallsLanguagesTable extends DashboardStatisticsTable
{
    private const TABLE_NAME = 'dashboard_statistics_calls_languages';

    /**
     *
     * @param int $companyId
     * @param \Carbon\Carbon $startDate
     * @param \Carbon\Carbon $endDate
     * @return void
     */
    public function updateStatistics(int $companyId, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): void
    {
        $tableName = $this->getPassiveTableName(self::TABLE_NAME);
        $this->getClient()->write(
            <<<SQL
                INSERT INTO {$tableName}
                SELECT
                    calls.company_id company_id,
                    toDate(calls.call_time) call_date,
                    teams.teams_ids team_ids,
                    calls.call_language language,
                    count(calls.call_id) calls_count,
                    now() created_at
                FROM ({$this->getFinalTableSqlUsingGroupBy(
                    'calls',
                    [
                        'company_id',
                        'call_id',
                    ],
                    'created',
                    [
                        'agent_id',
                        'call_language',
                        'call_time',
                    ],
                    $this->getCallsWhereConditionsFromMainParams($companyId, $startDate, $endDate),
                )}) calls
                LEFT JOIN
                    (
                        SELECT
                            user_id,
                            groupArray(team_id) teams_ids
                        FROM
                            dictionary(users_teams)
                        GROUP BY
                            user_id
                    ) teams
                    ON teams.user_id = calls.agent_id
                WHERE 
                    language IS NOT NULL
                GROUP BY
                    company_id,
                    call_date,
                    team_ids,
                    language
            SQL
        );
    }

    /**
     * @param int $companyId
     * @param ?int $roleId
     * @param string|null $startDate
     * @param string|null $endDate
     * @param array|null $teamsIds
     * @return array
     */
    public function getStatistics(
        int $companyId,
        ?int $roleId,
        ?string $startDate = null,
        ?string $endDate = null,
        ?array $teamsIds = [],
    ): array {
        $sql = sprintf(
            'SELECT
                sum(calls_count) calls_count,
                language
            FROM %s 
            WHERE %s
            GROUP BY language
            ORDER BY calls_count DESC',
            $this->getActiveTableName(self::TABLE_NAME),
            $this->getWhereClause($companyId, $roleId, $startDate, $endDate, $teamsIds),
        );

        return $this->getClient()->selectAll($sql);
    }

    /**
     * @return string
     */
    protected function getDashboardTableName(): string
    {
        return self::TABLE_NAME;
    }

    /**
     * @param string $where
     * @param int|null $roleId
     * @return string
     */
    protected function getRoleFilterClause(?int $roleId): string
    {
        return '';
    }
}
