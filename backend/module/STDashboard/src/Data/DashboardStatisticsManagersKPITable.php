<?php

declare(strict_types=1);

namespace STDashboard\Data;

class DashboardStatisticsManagersKPITable extends DashboardStatisticsTable
{
    private const TABLE_NAME = 'dashboard_statistics_managers_kpi';

    /**
     *
     * @param int $companyId
     * @param \Carbon\Carbon $startDate
     * @param \Carbon\Carbon $endDate
     * @return void
     */
    public function updateStatistics(int $companyId, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): void
    {
        $this->getClient()->write(
            <<<SQL
                INSERT INTO {$this->getPassiveTableName(self::TABLE_NAME)}
                SELECT
                    calls.company_id company_id,
                    toDate(pc.reviewed_time) reviewed_date,
                    pc.role_id role_id,
                    teams.teams_ids team_ids,
                    pc.reviewer_user_id,
                    uniq(calls.client_id) unique_clients_count,
                    SUM(cc.comments_left) comments_left,
                    count(calls.call_id) calls_count,
                    countIf(cc.comments_left > 0) commented_calls_count,
                    countIf(pc.is_partly_reviewed) partly_reviewed_count,
                    countIf(pc.is_reviewed) fully_reviewed_count,
                    SUM(calls.call_duration) calls_duration,
                    now() created_at
                FROM ({$this->getFinalTableSqlUsingGroupBy(
                    'precalculated_calls',
                    [
                        'company_id',
                        'call_id',
                        'role_id',
                    ],
                    'created',
                    [
                        'reviewed_time',
                        'reviewer_user_id',
                        'is_partly_reviewed',
                        'is_reviewed',
                    ],
                    $this->getCallsWhereConditionsFromMainParams($companyId, $startDate, $endDate),
                )}) pc
                INNER JOIN ({$this->getFinalTableSqlUsingGroupBy(
                    'calls',
                    [
                        'company_id',
                        'call_id',
                    ],
                    'created',
                    [
                        'client_id',
                        'agent_id',
                        'call_duration',
                    ],
                    $this->getCallsWhereConditionsFromMainParams($companyId, $startDate, $endDate),
                )}) calls ON calls.call_id = pc.call_id
                LEFT JOIN (
                    SELECT
                        user_id,
                        groupArray(team_id) teams_ids
                    FROM
                        dictionary(users_teams)
                    GROUP BY 
                        user_id
                ) teams ON teams.user_id = calls.agent_id
                LEFT JOIN (
                    SELECT
                        call_id,
                        user_id,
                        count(comment_id) comments_left
                    FROM
                        calls_comments
                    GROUP BY 
                        call_id, 
                        user_id
                ) cc ON pc.call_id = cc.call_id AND pc.reviewer_user_id = cc.user_id
                WHERE
                    (pc.is_reviewed OR pc.is_partly_reviewed)
                    AND pc.reviewer_user_id > 0
                GROUP BY
                    company_id,
                    reviewed_date,
                    role_id,
                    team_ids,
                    reviewer_user_id
            SQL
        );
    }

    /**
     * @return string
     */
    protected function getDateColumnName(): string
    {
        return 'reviewed_date';
    }

    /**
     * @return string
     */
    protected function getDashboardTableName(): string
    {
        return self::TABLE_NAME;
    }

    /**
     * @param int $companyId
     * @param ?int $roleId
     * @param string|null $startDate
     * @param string|null $endDate
     * @param array|null $teamsIds
     * @return array
     */
    public function getStatistics(
        int $companyId,
        ?int $roleId,
        ?string $startDate = null,
        ?string $endDate = null,
        ?array $teamsIds = [],
    ): array {
        $sql = sprintf(
            'SELECT
                sum(partly_reviewed_count) partly_reviewed_count,
                sum(fully_reviewed_count) fully_reviewed_count,
                sum(commented_calls_count) commented_calls_count,
                sum(comments_left) comments_left_count,
                sum(calls_duration) calls_duration,
                sum(calls_count) total_calls_count,
                partly_reviewed_count + fully_reviewed_count total_count,
                round((partly_reviewed_count / total_count) * 100) partly_reviewed_count_percentage,
                round((fully_reviewed_count / total_count) * 100) fully_reviewed_count_percentage
            FROM %s 
            WHERE %s',
            $this->getActiveTableName(self::TABLE_NAME),
            $this->getWhereClause($companyId, $roleId, $startDate, $endDate, $teamsIds),
        );

        return $this->getClient()->selectOne($sql);
    }

    /**
     * @param int $companyId
     * @param ?int $roleId
     * @param string|null $startDate
     * @param string|null $endDate
     * @param array|null $teamsIds
     * @return array
     */
    public function getDetailedStatistics(
        int $companyId,
        ?int $roleId,
        ?string $startDate = null,
        ?string $endDate = null,
        ?array $teamsIds = [],
    ): array {
        $sql = sprintf(
            'SELECT
                dsmk.*,
                u.user_name
            FROM (
                SELECT
                    reviewer_user_id,
                    sum(partly_reviewed_count) partly_reviewed_count,
                    sum(fully_reviewed_count) fully_reviewed_count,
                    sum(unique_clients_count) unique_clients_count,
                    sum(comments_left) comments_left,
                    sum(calls_duration) calls_duration,
                    sum(calls_count) total_calls_count
                FROM %s
                WHERE %s
                GROUP BY 
                    reviewer_user_id
            ) dsmk
            INNER JOIN
                dictionary(users) u
                ON u.user_id = dsmk.reviewer_user_id',
            $this->getActiveTableName(self::TABLE_NAME),
            $this->getWhereClause($companyId, $roleId, $startDate, $endDate, $teamsIds),
        );

        return $this->getClient()->selectAll($sql);
    }
}
