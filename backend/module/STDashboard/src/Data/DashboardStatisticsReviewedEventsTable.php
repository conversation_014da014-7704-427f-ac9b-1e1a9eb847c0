<?php

declare(strict_types=1);

namespace STDashboard\Data;

class DashboardStatisticsReviewedEventsTable extends DashboardStatisticsTable
{
    private const TABLE_NAME = 'dashboard_statistics_reviewed_events';

    /**
     *
     * @param int $companyId
     * @param \Carbon\Carbon $startDate
     * @param \Carbon\Carbon $endDate
     * @return void
     */
    public function updateStatistics(int $companyId, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): void
    {
        $tableName = $this->getPassiveTableName(self::TABLE_NAME);

        $this->getClient()->getConnection()->setTimeOut(60);
        $this->getClient()->write(
            <<<SQL
                INSERT INTO {$tableName}
                SELECT
                    company_id,
                    call_date,
                    role_id,
                    team_ids,
                    category_id,
                    event_id,
                    countIf(isMovedFromNeutral) moved_from_neutral_count,
                    countIf(isMovedToNeutral) moved_to_neutral_count,
                    countIf(isChangedFromOther) changed_from_other_count,
                    countIf(isChangedToOther) changed_to_other_count,
                    countIf(isConfirmed) confirmed_count,
                    now() created_at
                FROM (
                    SELECT
                        pce.company_id company_id,
                        toDate(calls.call_time) call_date,
                        pce.role_id role_id,
                        teams.teams_ids team_ids,
                        events.category_id category_id,
                        pce.event_id event_id,
                        pce.event_is_deleted isMovedToNeutral,
                        if(pce.event_changed_from_event_id is NULL AND NOT pce.event_is_deleted, true, false) isMovedFromNeutral,
                        if(NOT pce.event_is_deleted AND pce.event_changed_from_event_id IS NOT NULL AND pce.event_changed_from_event_id != pce.event_id, true, false) isChangedFromOther,
                        false isChangedToOther,
                        if(pce.event_changed_from_event_id = pce.event_id AND NOT pce.event_is_deleted, true, false) isConfirmed
                    FROM ({$this->getFinalTableSqlUsingGroupBy(
                        'precalculated_calls_events',
                        [
                            'company_id',
                            'role_id',
                            'call_id',
                            'paragraph',
                            'event_id',
                        ],
                        'created',
                        [
                            'event_category_id',
                            'event_changed_from_event_id',
                            'event_is_deleted',
                        ],
                        $this->getCallsWhereConditionsFromMainParams($companyId, $startDate, $endDate),
                    )}) pce
                    INNER JOIN (
                        SELECT
                            event_id,
                            last_value_respect_nulls(category_id) category_id
                        FROM dictionary(events)
                        GROUP BY event_id
                    ) events ON events.event_id = pce.event_id
                    INNER JOIN ({$this->getFinalTableSqlUsingGroupBy(
                        'calls',
                        [
                            'company_id',
                            'call_id',
                        ],
                        'created',
                        [
                            'agent_id',
                            'call_time',
                        ],
                        $this->getCallsWhereConditionsFromMainParams($companyId, $startDate, $endDate),
                    )}) calls ON calls.call_id = pce.call_id
                    INNER JOIN ({$this->getFinalTableSqlUsingGroupBy(
                        'precalculated_calls',
                        [
                            'company_id',
                            'call_id',
                            'role_id',                            
                        ],
                        'created',
                        [
                            'is_reviewed',
                        ],
                        array_merge
                        (
                            [
                                'is_reviewed' => 1,
                            ],
                            $this->getCallsWhereConditionsFromMainParams($companyId, $startDate, $endDate)
                        )
                    )}) pc ON pc.call_id = calls.call_id AND pc.role_id = pce.role_id
                    LEFT JOIN (
                        SELECT
                            user_id,
                            groupArray(team_id) teams_ids
                        FROM dictionary(users_teams)
                        GROUP BY 
                            user_id
                    ) teams ON teams.user_id = calls.agent_id
                    UNION ALL
                    SELECT
                        pce.company_id company_id,
                        toDate(calls.call_time) call_date,
                        pce.role_id role_id,
                        teams.teams_ids team_ids,
                        events.category_id category_id,
                        pce.event_changed_from_event_id event_id,
                        false isMovedToNeutral,
                        false isMovedFromNeutral,
                        false isChangedFromOther,
                        true isChangedToOther,
                        false isConfirmed
                    FROM ({$this->getFinalTableSqlUsingGroupBy(
                        'precalculated_calls_events',
                        [
                            'company_id',
                            'role_id',
                            'call_id',
                            'paragraph',
                            'event_id',
                        ],
                        'created',
                        [
                            'event_changed_from_event_id',
                            'event_is_deleted',
                        ],
                        $this->getCallsWhereConditionsFromMainParams($companyId, $startDate, $endDate),
                    )}) pce
                    INNER JOIN (
                        SELECT
                            event_id,
                            last_value_respect_nulls(category_id) category_id
                        FROM dictionary(events)
                        GROUP BY event_id
                    ) events ON events.event_id = pce.event_changed_from_event_id
                    INNER JOIN ({$this->getFinalTableSqlUsingGroupBy(
                        'calls',
                        [
                            'company_id',
                            'call_id',
                        ],
                        'created',
                        [
                            'agent_id',
                            'call_time',
                        ],
                        $this->getCallsWhereConditionsFromMainParams($companyId, $startDate, $endDate),
                    )}) calls ON calls.call_id = pce.call_id
                    INNER JOIN ({$this->getFinalTableSqlUsingGroupBy(
                        'precalculated_calls',
                        [
                            'company_id',
                            'call_id',
                            'role_id',                     
                        ],
                        'created',
                        [
                            'is_reviewed',
                        ],
                        array_merge
                        (
                            [
                                'is_reviewed' => 1,
                            ],
                            $this->getCallsWhereConditionsFromMainParams($companyId, $startDate, $endDate)
                        )
                    )}) pc ON pc.call_id = calls.call_id AND pc.role_id = pce.role_id
                    LEFT JOIN (
                        SELECT
                            user_id,
                            groupArray(team_id) teams_ids
                        FROM dictionary(users_teams)
                        GROUP BY 
                            user_id
                    ) teams ON teams.user_id = calls.agent_id
                    WHERE
                        NOT pce.event_is_deleted
                        AND pce.event_changed_from_event_id IS NOT NULL
                        AND pce.event_changed_from_event_id != pce.event_id
                    )
                    GROUP BY
                        company_id,
                        call_date,
                        role_id,
                        team_ids,
                        category_id,
                        event_id
                    ORDER BY call_date DESC;
            SQL
        );
    }

    /**
     * @return string
     */
    protected function getDashboardTableName(): string
    {
        return self::TABLE_NAME;
    }

    /**
     * @param int $companyId
     * @param ?int $roleId
     * @param string|null $startDate
     * @param string|null $endDate
     * @param array|null $teamsIds
     * @return array
     */
    public function getReviewedStatistics(
        int $companyId,
        ?int $roleId,
        ?string $startDate = null,
        ?string $endDate = null,
        ?array $teamsIds = [],
    ): array {
        $sql = sprintf(
            'SELECT
                SUM(confirmed_count) confirmed_count,
                SUM(changed_from_other_count) changed_from_other_count,
                SUM(changed_to_other_count) changed_to_other_count,
                SUM(moved_from_neutral_count) moved_from_neutral_count,
                SUM(moved_to_neutral_count) moved_to_neutral_count,
                confirmed_count + changed_from_other_count + changed_to_other_count + moved_from_neutral_count + moved_to_neutral_count total_count,
                round((confirmed_count / total_count) * 100) confirmed_percentage,
                round((changed_from_other_count / total_count) * 100) changed_from_other_percentage,
                round((changed_to_other_count / total_count) * 100) changed_to_other_percentage,
                round((moved_from_neutral_count / total_count) * 100) moved_from_neutral_percentage,
                round((moved_to_neutral_count / total_count) * 100) moved_to_neutral_percentage
            FROM %s 
            WHERE %s',
            $this->getActiveTableName(self::TABLE_NAME),
            $this->getWhereClause($companyId, $roleId, $startDate, $endDate, $teamsIds),
        );

        return $this->getClient()->selectOne($sql);
    }

    /**
     * @param int $companyId
     * @param ?int $roleId
     * @param string|null $startDate
     * @param string|null $endDate
     * @param array|null $teamsIds
     * @return array
     */
    public function getDetailedReviewedStatistics(
        int $companyId,
        ?int $roleId,
        ?string $startDate = null,
        ?string $endDate = null,
        ?array $teamsIds = [],
    ): array {
        $sql = sprintf(
            'SELECT
                category_id id,
                any(events.category_name) name,
                any(events.fill_color_hex) fill_color_hex,
                any(events.outline_color_hex) outline_color_hex,
                sum(confirmed_count) confirmed_count,
                sum(changed_from_other_count) changed_from_other_count,
                sum(changed_to_other_count) changed_to_other_count,
                sum(moved_from_neutral_count) moved_from_neutral_count,
                sum(moved_to_neutral_count) moved_to_neutral_count,
                confirmed_count + changed_from_other_count + changed_to_other_count + moved_from_neutral_count + moved_to_neutral_count total_count,
                round((confirmed_count / total_count) * 100) confirmed_percentage,
                round((changed_from_other_count / total_count) * 100) changed_from_other_percentage,
                round((changed_to_other_count / total_count) * 100) changed_to_other_percentage,
                round((moved_from_neutral_count / total_count) * 100) moved_from_neutral_percentage,
                round((moved_to_neutral_count / total_count) * 100) moved_to_neutral_percentage
            FROM %s dse
            INNER JOIN (
                SELECT
                    category_id,
                    last_value_respect_nulls(category_name) category_name,
                    last_value_respect_nulls(fill_color_hex) fill_color_hex,
                    last_value_respect_nulls(outline_color_hex) outline_color_hex
                FROM dictionary(events)
                GROUP BY category_id
            ) events ON events.category_id = dse.category_id            
            WHERE %s
            GROUP BY 
                category_id',
            $this->getActiveTableName(self::TABLE_NAME),
            $this->getWhereClause($companyId, $roleId, $startDate, $endDate, $teamsIds),
        );

        return $this->getClient()->selectAll($sql);
    }

    /**
     *
     * @param int $categoryId
     * @param int $companyId
     * @param int|null $roleId
     * @param string|null $startDate
     * @param string|null $endDate
     * @param array|null $teamsIds
     * @return array
     */
    public function getReviewedEventsStatistics(
        int $categoryId,
        int $companyId,
        ?int $roleId,
        ?string $startDate = null,
        ?string $endDate = null,
        ?array $teamsIds = [],
    ): array {
        $sql = sprintf(
            'SELECT
                event_id id,
                any(events.event_name) name,
                any(events.icon) event_icon,
                any(events.fill_color_hex) fill_color_hex,
                any(events.outline_color_hex) outline_color_hex,
                SUM(confirmed_count) confirmed_count,
                SUM(changed_from_other_count) changed_from_other_count,
                SUM(changed_to_other_count) changed_to_other_count,
                SUM(moved_from_neutral_count) moved_from_neutral_count,
                SUM(moved_to_neutral_count) moved_to_neutral_count,
                confirmed_count + changed_from_other_count + changed_to_other_count + moved_from_neutral_count + moved_to_neutral_count total_count,
                round((confirmed_count / total_count) * 100) confirmed_percentage,
                round((changed_from_other_count / total_count) * 100) changed_from_other_percentage,
                round((changed_to_other_count / total_count) * 100) changed_to_other_percentage,
                round((moved_from_neutral_count / total_count) * 100) moved_from_neutral_percentage,
                round((moved_to_neutral_count / total_count) * 100) moved_to_neutral_percentage
            FROM %s dse
            INNER JOIN (
                SELECT
                    event_id,
                    last_value_respect_nulls(event_name) event_name,
                    last_value_respect_nulls(fill_color_hex) fill_color_hex,
                    last_value_respect_nulls(outline_color_hex) outline_color_hex,
                    last_value_respect_nulls(icon) icon
                FROM dictionary(events)
                GROUP BY event_id
            ) events ON events.event_id = dse.event_id  
            WHERE %s
            GROUP BY
                event_id',
            $this->getActiveTableName(self::TABLE_NAME),
            $this->getWhereClause($companyId, $roleId, $startDate, $endDate, $teamsIds)
                . ' AND category_id = \'' . $categoryId . '\'',
        );

        return $this->getClient()->selectAll($sql);
    }
}
