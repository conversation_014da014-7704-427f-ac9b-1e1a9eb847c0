<?php

declare(strict_types=1);

namespace STDashboard\Data;

class DashboardStatisticsReviewedClientsTable extends DashboardStatisticsTable
{
    private const TABLE_NAME = 'dashboard_statistics_reviewed_clients';

    /**
     *
     * @param int $companyId
     * @param \Carbon\Carbon $startDate
     * @param \Carbon\Carbon $endDate
     * @return void
     */
    public function updateStatistics(int $companyId, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): void
    {
        $this->getClient()->write(
            <<<SQL
                INSERT INTO {$this->getPassiveTableName(self::TABLE_NAME)}
                    SELECT
                        c.company_id company_id,
                        toDate(c.call_time) call_date,
                        if(pc.role_id > 0, pc.role_id, NULL) role_id,
                        teams.teams_ids team_ids,
                        c.client_id client_id,
                        countIf(c.is_analyzed) is_analyzed_count,
                        countIf(pc.is_reviewed) is_reviewed_count,
                        count() count
                    FROM ({$this->getFinalTableSqlUsingGroupBy(
                        'calls',
                        [
                            'company_id',
                            'call_id',
                        ],
                        'created',
                        [
                            'client_id',
                            'agent_id',
                            'call_time',
                            'is_analyzed',
                            'is_deleted',
                        ],
                        $this->getCallsWhereConditionsFromMainParams($companyId, $startDate, $endDate),
                    )}) c
                    LEFT JOIN ({$this->getFinalTableSqlUsingGroupBy(
                        'precalculated_calls',
                        [
                            'company_id',
                            'call_id',
                            'role_id',
                        ],
                        'created',
                        [
                            'is_reviewed',
                        ],
                        $this->getCallsWhereConditionsFromMainParams($companyId, $startDate, $endDate),
                    )}) pc ON pc.call_id = c.call_id
                    LEFT JOIN (
                        SELECT
                            user_id,
                            groupArray(team_id) teams_ids
                        FROM dictionary(users_teams)
                        GROUP BY 
                            user_id
                    ) teams ON teams.user_id = c.agent_id
                    WHERE 
                        NOT c.is_deleted 
                    GROUP BY
                        company_id,
                        call_date,
                        role_id,
                        team_ids,
                        client_id
            SQL
        );
    }

    /**
     * @return string
     */
    protected function getDashboardTableName(): string
    {
        return self::TABLE_NAME;
    }

    /**
     * @param int $companyId
     * @param ?int $roleId
     * @param string|null $startDate
     * @param string|null $endDate
     * @param array|null $teamsIds
     * @return array
     */
    public function getStatistics(
        int $companyId,
        ?int $roleId,
        ?string $startDate = null,
        ?string $endDate = null,
        ?array $teamsIds = [],
    ): array {
        $sql = sprintf(
            'SELECT
                sumIf(is_reviewed, true) reviewed_count,
                sumIf(is_partly_reviewed, true) partly_reviewed_count,
                sumIf(is_not_reviewed, true) not_reviewed_count,
                sumIf(is_not_analyzed, true) not_analyzed_count,
                reviewed_count + partly_reviewed_count + not_analyzed_count + not_reviewed_count total_clients_count,
                round((reviewed_count / total_clients_count) * 100) reviewed_percentage,
                round((partly_reviewed_count / total_clients_count) * 100) partly_reviewed_percentage,
                round((not_reviewed_count / total_clients_count) * 100) not_reviewed_percentage,
                round((not_analyzed_count / total_clients_count) * 100) not_analyzed_percentage
            FROM (
                SELECT 
                    if(sum(count) = sum(is_reviewed_count), true, false) is_reviewed,
                    if(sum(is_reviewed_count) > 0 AND sum(is_reviewed_count) != sum(count), true, false) is_partly_reviewed,
                    if(sum(is_reviewed_count) = 0, true, false) is_not_reviewed,
                    if(sum(is_analyzed_count) = 0, true, false) is_not_analyzed
                FROM %s
                WHERE %s
                GROUP BY client_id
            )',
            $this->getActiveTableName(self::TABLE_NAME),
            $this->getWhereClause($companyId, $roleId, $startDate, $endDate, $teamsIds),
        );

        return $this->getClient()->selectOne($sql);
    }
}
