<?php

declare(strict_types=1);

namespace STDashboard\Data;

class DashboardStatisticsEventsCategoriesTable extends DashboardStatisticsTable
{
    public const TABLE_NAME = 'dashboard_statistics_events_categories';

    /**
     *
     * @param int $companyId
     * @param \Carbon\Carbon $startDate
     * @param \Carbon\Carbon $endDate
     * @return void
     */
    public function updateStatistics(int $companyId, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): void
    {
        $tableName = $this->getPassiveTableName(self::TABLE_NAME);

        $this->getClient()->getConnection()->setTimeOut(60);
        $this->getClient()->write(
            <<<SQL
                INSERT INTO {$tableName}
                SELECT
                    company_id,
                    call_date,
                    role_id,
                    team_ids,
                    category_id,
                    event_id,
                    count() events_count,
                    groupUniqArray(call_id) calls_ids,
                    now() created_at
                FROM (
                    SELECT
                        pce.call_id call_id,
                        pce.company_id company_id,
                        toDate(pce.call_time) call_date,
                        pce.role_id role_id,
                        teams.teams_ids team_ids,
                        events.category_id category_id,
                        pce.event_id event_id
                    FROM ({$this->getFinalTableSqlUsingGroupBy(
                        'precalculated_calls_events',
                        [
                            'company_id',
                            'role_id',
                            'call_id',
                            'paragraph',
                            'event_id',
                        ],
                        'created',
                        [
                            'event_category_id',
                            'event_changed_from_event_id',
                            'event_is_deleted',
                            'agent_id',
                            'call_time'
                            
                        ],
                        array_merge
                        (
                            [
                                'event_is_deleted' => 0,
                            ],
                            $this->getCallsWhereConditionsFromMainParams($companyId, $startDate, $endDate)
                        )
                    )}) pce
                    INNER JOIN (
                        SELECT
                            event_id,
                            last_value_respect_nulls(category_id) category_id
                        FROM dictionary(events)
                        GROUP BY event_id
                    ) events ON events.event_id = pce.event_id
                    LEFT JOIN (
                        SELECT
                            user_id,
                            groupArray(team_id) teams_ids
                        FROM dictionary(users_teams)
                        GROUP BY 
                            user_id
                    ) teams ON teams.user_id = pce.agent_id
                )
                GROUP BY
                    company_id,
                    call_date,
                    role_id,
                    team_ids,
                    category_id,
                    event_id
                ORDER BY call_date DESC;
            SQL
        );
    }

    /**
     * @param int $companyId
     * @param ?int $roleId
     * @param string|null $startDate
     * @param string|null $endDate
     * @param array|null $teamsIds
     * @return array
     */
    public function getEventsCategoriesStatistics(
        int $companyId,
        ?int $roleId,
        ?string $startDate = null,
        ?string $endDate = null,
        ?array $teamsIds = [],
    ): array {
        $sql = sprintf(
            'SELECT
                category_id id,
                any(events.category_name) name,
                any(events.fill_color_hex) fill_color_hex,
                any(events.outline_color_hex) outline_color_hex,
                arrayUniq(flatten(groupArray(calls_ids))) calls_count,
                SUM(events_count) total_count
            FROM %s dsec
            INNER JOIN (
                SELECT
                    category_id,
                    last_value_respect_nulls(category_name) category_name,
                    last_value_respect_nulls(fill_color_hex) fill_color_hex,
                    last_value_respect_nulls(outline_color_hex) outline_color_hex
                FROM dictionary(events)
                GROUP BY category_id
            ) events ON events.category_id = dsec.category_id            
            WHERE %s
            GROUP BY 
                category_id',
            $this->getActiveTableName(self::TABLE_NAME),
            $this->getWhereClause($companyId, $roleId, $startDate, $endDate, $teamsIds),
        );

        return $this->getClient()->selectAll($sql);
    }

    /**
     *
     * @param int $categoryId
     * @param int $companyId
     * @param int|null $roleId
     * @param string|null $startDate
     * @param string|null $endDate
     * @param array|null $teamsIds
     * @return array
     */
    public function getDetailedEventsCategoriesStatistics(
        int $categoryId,
        int $companyId,
        ?int $roleId,
        ?string $startDate = null,
        ?string $endDate = null,
        ?array $teamsIds = [],
    ): array {
        $sql = sprintf(
            'SELECT
                event_id id,
                any(events.event_name) name,
                any(events.icon) event_icon,
                any(events.fill_color_hex) fill_color_hex,
                any(events.outline_color_hex) outline_color_hex,
                arrayUniq(flatten(groupArray(calls_ids))) calls_count,
                SUM(events_count) total_count
            FROM %s dsec
            INNER JOIN (
                SELECT
                    event_id,
                    last_value_respect_nulls(event_name) event_name,
                    last_value_respect_nulls(fill_color_hex) fill_color_hex,
                    last_value_respect_nulls(outline_color_hex) outline_color_hex,
                    last_value_respect_nulls(icon) icon
                FROM dictionary(events)
                GROUP BY event_id
            ) events ON events.event_id = dsec.event_id           
            WHERE %s
            GROUP BY 
                event_id',
            $this->getActiveTableName(self::TABLE_NAME),
            $this->getWhereClause($companyId, $roleId, $startDate, $endDate, $teamsIds)
            . ' AND category_id = \'' . $categoryId . '\'',
        );

        return $this->getClient()->selectAll($sql);
    }

    /**
     * @param int $companyId
     * @param ?int $roleId
     * @param string|null $startDate
     * @param string|null $endDate
     * @param array|null $teamsIds
     * @return int
     */
    public function getEventsCallsCount(
        int $companyId,
        ?int $roleId,
        ?string $startDate = null,
        ?string $endDate = null,
        ?array $teamsIds = [],
    ): int {
        $sql = sprintf(
            'SELECT
                arrayUniq(flatten(groupArray(calls_ids))) calls_count
            FROM %s   
            WHERE %s',
            $this->getActiveTableName(self::TABLE_NAME),
            $this->getWhereClause($companyId, $roleId, $startDate, $endDate, $teamsIds),
        );

        return (int) $this->getClient()->selectValue($sql);
    }

    /**
     * @param int $categoryId
     * @param int $companyId
     * @param ?int $roleId
     * @param string|null $startDate
     * @param string|null $endDate
     * @param array|null $teamsIds
     * @return int
     */
    public function getEventCategoryCallsCount(
        int $categoryId,
        int $companyId,
        ?int $roleId,
        ?string $startDate = null,
        ?string $endDate = null,
        ?array $teamsIds = [],
    ): int {
        $sql = sprintf(
            'SELECT
                arrayUniq(flatten(groupArray(calls_ids))) calls_count
            FROM %s   
            WHERE %s',
            $this->getActiveTableName(self::TABLE_NAME),
            $this->getWhereClause($companyId, $roleId, $startDate, $endDate, $teamsIds)
            . ' AND category_id = \'' . $categoryId . '\'',
        );

        return (int) $this->getClient()->selectValue($sql);
    }

    /**
     * @return string
     */
    protected function getDashboardTableName(): string
    {
        return self::TABLE_NAME;
    }
}
