<?php

declare(strict_types=1);

namespace STDashboard\Data;

use STDashboard\Service\DashboardStatisticsActivePassiveTables;

abstract class DashboardStatisticsTable extends \STClickhouse\Entity\BaseTable
{
    use \STClickhouse\Data\QueriesTrait;
    use DashboardStatisticsActivePassiveTables;
    use \STClickhouse\Data\CallsWhereCondition;

    /**
     *
     * @return void
     */
    public function truncateTable(): void
    {
        $this->getClient()->truncate($this->getPassiveTableName($this->getDashboardTableName()));
    }

    /**
     *
     * @return void
     */
    public function swapActiveAndPassiveTables(): void
    {
        $this->executeSwapActiveAndPassiveTables($this->getDashboardTableName());
    }

    /**
     * @param int $companyId
     * @param ?int $roleId
     * @param string|null $startDate
     * @param string|null $endDate
     * @param array $teamsIds
     * @return string
     */
    protected function getWhereClause(
        int $companyId,
        ?int $roleId,
        string $startDate = null,
        string $endDate = null,
        array $teamsIds = []
    ): string {
        $where = 'company_id = ' . $companyId;
        $where .= $this->getRoleFilterClause($roleId);

        if ($startDate !== null) {
            $where .= sprintf(' AND %s >= \'%s\'', $this->getDateColumnName(), $startDate);
        }

        if ($endDate !== null) {
            $where .= sprintf(' AND %s <= \'%s\'', $this->getDateColumnName(), $endDate);
        }

        if (!empty($teamsIds)) {
            $where .= ' AND hasAny(team_ids, [' . implode(',', $teamsIds) . '])';
        }

        return $where;
    }

    /**
     * @return string
     */
    protected function getDateColumnName(): string
    {
        return 'call_date';
    }

    /**
     * @param string $where
     * @param int|null $roleId
     * @return string
     */
    protected function getRoleFilterClause(?int $roleId): string
    {
        return $roleId !== null ? ' AND (role_id IS NULL OR role_id = ' . $roleId . ')' : ' AND role_id IS NULL';
    }

    /**
     *
     * @param int $companyId
     * @param \Carbon\Carbon $startDate
     * @param \Carbon\Carbon $endDate
     * @return void
     */
    abstract public function updateStatistics(int $companyId, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): void;

    /**
     *
     * @return string
     */
    abstract protected function getDashboardTableName(): string;
}
