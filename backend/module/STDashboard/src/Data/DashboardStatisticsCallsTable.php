<?php

declare(strict_types=1);

namespace STDashboard\Data;

class DashboardStatisticsCallsTable extends DashboardStatisticsTable
{
    private const TABLE_NAME = 'dashboard_statistics_calls';

    /**
     *
     * @param int $companyId
     * @param \Carbon\Carbon $startDate
     * @param \Carbon\Carbon $endDate
     * @return void
     */
    public function updateStatistics(int $companyId, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): void
    {
        $this->getClient()->write(
            <<<SQL
                INSERT INTO {$this->getPassiveTableName(self::TABLE_NAME)}
                SELECT
                    calls.company_id company_id,
                    toDate(calls.call_time) call_date,
                    pc.role_id role_id,
                    teams.teams_ids team_ids,
                    0 calls_count,
                    0 calls_duration,
                    0 comments_count,
                    countIf(pc.is_reviewed) reviewed_count,
                    0 analyzed_count,
                    countIf(calls.call_duration >= ectb.effective_call_threshold_bar) effective_count,
                    sumIf(calls.call_duration, pc.is_reviewed) reviewed_duration,
                    0 analyzed_duration,
                    sumIf(calls.call_duration, calls.call_duration >= ectb.effective_call_threshold_bar) effective_duration,
                    now() created_at
                FROM ({$this->getFinalTableSqlUsingGroupBy(
                    'calls',
                    [
                        'company_id',
                        'call_id',
                    ],
                    'created',
                    [
                        'agent_id',
                        'call_time',
                        'call_duration',
                        'is_analyzed',
                    ],
                    array_merge(
                        ['is_deleted' => 0],
                        $this->getCallsWhereConditionsFromMainParams($companyId, $startDate, $endDate),
                    ),
                )}) calls
                INNER JOIN ({$this->getFinalTableSqlUsingGroupBy(
                    'precalculated_calls',
                    [
                        'company_id',
                        'call_id',
                        'role_id',
                    ],
                    'created',
                    [
                        'is_reviewed',
                    ],
                    $this->getCallsWhereConditionsFromMainParams($companyId, $startDate, $endDate),
                )}) pc ON calls.call_id = pc.call_id
                INNER JOIN (
                    SELECT
                        role_id,
                        effective_call_threshold_bar
                    FROM
                        dictionary(roles)
                ) ectb ON ectb.role_id = pc.role_id
                LEFT JOIN (
                    SELECT
                        user_id,
                        groupArray(team_id) teams_ids
                    FROM
                        dictionary(users_teams)
                    GROUP BY
                        user_id
                ) teams ON teams.user_id = calls.agent_id
                GROUP BY
                    company_id,
                    call_date,
                    role_id,
                    teams_ids
            SQL
        );

        /* Statistics not related to roles (calls count, duration etc.) is calculated separately, to avoid issues
           with duplicate numbers in grouped data, when role filter is not used (https://robonote.atlassian.net/browse/ROB-969)
        */
        $this->getClient()->write(
            <<<SQL
                INSERT INTO {$this->getPassiveTableName(self::TABLE_NAME)}
                SELECT
                    calls.company_id company_id,
                    toDate(calls.call_time) call_date,
                    NULL role_id,
                    teams.teams_ids team_ids,
                    count(calls.call_id) calls_count,
                    sum(calls.call_duration) calls_duration,
                    sum(length(pc.comments)) comments_count,
                    0 reviewed_count,
                    countIf(calls.is_analyzed) analyzed_count,
                    0 effective_count,
                    0 reviewed_duration,
                    sumIf(calls.call_duration, calls.is_analyzed) analyzed_duration,
                    0 effective_duration,
                    now() created_at
                FROM ({$this->getFinalTableSqlUsingGroupBy(
                    'calls',
                    [
                        'company_id',
                        'call_id',
                    ],
                    'created',
                    [
                        'agent_id',
                        'call_time',
                        'call_duration',
                        'is_analyzed',
                    ],
                    array_merge(
                        ['is_deleted' => 0],
                        $this->getCallsWhereConditionsFromMainParams($companyId, $startDate, $endDate),
                    ),
                )}) calls
                LEFT JOIN ({$this->getFinalTableSqlUsingGroupBy(
                    'precalculated_calls',
                    [
                        'company_id',
                        'call_id',
                    ],
                    'created',
                    [
                        'comments',
                    ],
                    $this->getCallsWhereConditionsFromMainParams($companyId, $startDate, $endDate),
                )}) pc ON calls.call_id = pc.call_id
                LEFT JOIN (
                    SELECT
                        user_id,
                        groupArray(team_id) teams_ids
                    FROM
                        dictionary(users_teams)
                    GROUP BY
                        user_id
                ) teams ON teams.user_id = calls.agent_id
                GROUP BY
                    company_id,
                    call_date,
                    teams_ids
            SQL
        );
    }

    /**
     * @param int $companyId
     * @param ?int $roleId
     * @param string|null $startDate
     * @param string|null $endDate
     * @param array|null $teamsIds
     * @return array
     */
    public function getStatistics(
        int $companyId,
        ?int $roleId,
        ?string $startDate = null,
        ?string $endDate = null,
        ?array $teamsIds = [],
    ): array {
        $sql = sprintf(
            'SELECT
                sum(calls_count) total_calls_count,
                sum(calls_duration) calls_duration,
                sum(comments_count) comments_count,
                sum(effective_count) effective_count,
                sum(reviewed_count) reviewed_count,
                sum(analyzed_count) analyzed_count,
                total_calls_count - reviewed_count not_reviewed_count,
                total_calls_count - effective_count not_effective_count,
                total_calls_count - analyzed_count not_analyzed_count,
                sum(analyzed_duration) analyzed_duration,
                sum(effective_duration) effective_duration,
                sum(reviewed_duration) reviewed_duration,
                calls_duration - analyzed_duration not_analyzed_duration,
                calls_duration - effective_duration not_effective_duration,
                calls_duration - reviewed_duration not_reviewed_duration,
                round((effective_count / total_calls_count) * 100) effective_percentage,
                round((reviewed_count / total_calls_count) * 100) reviewed_percentage,
                round((analyzed_count / total_calls_count) * 100) analyzed_percentage,
                round((not_analyzed_count / total_calls_count) * 100) not_analyzed_percentage,
                round((not_effective_count / total_calls_count) * 100) not_effective_percentage,
                round((not_reviewed_count / total_calls_count) * 100) not_reviewed_percentage
            FROM %s WHERE %s',
            $this->getActiveTableName(self::TABLE_NAME),
            $this->getWhereClause($companyId, $roleId, $startDate, $endDate, $teamsIds),
        );

        return $this->getClient()->selectOne($sql);
    }

    /**
     * @return string
     */
    protected function getDashboardTableName(): string
    {
        return self::TABLE_NAME;
    }
}
