<?php

declare(strict_types=1);

namespace STRoboMetrics;

use STRoboMetrics\Controller\Plugin\RoboMetrics;

return [
    'controller_plugins' => [
        'invokables' => [
            'roboMetrics' => RoboMetrics::class,
        ],
    ],
    'service_manager' => [
        'factories' => [
            Data\TranscribeDriversLanguagesTable::class => \STLib\Mvc\Data\TableFactory::class,
            Data\TranslationDriversLanguagesTable::class => \STLib\Mvc\Data\TableFactory::class,
            Data\CompaniesTable::class => \STLib\Mvc\Data\TableFactory::class,
            Data\AlgoApisTable::class => \STLib\Mvc\Data\TableFactory::class,
            Data\CallsTable::class => \STClickhouse\Entity\TableFactory::class,
            Service\ClickhouseService::class => \STLib\Mvc\DependencyInjection\DefaultFactory::class,
            Service\AlgoApiService::class => \STLib\Mvc\DependencyInjection\DefaultFactory::class,
            Service\CompanyService::class => \STLib\Mvc\DependencyInjection\DefaultFactory::class,
            Service\DictionaryService::class => \STLib\Mvc\DependencyInjection\DefaultFactory::class,
            Service\CallService::class => \STLib\Mvc\DependencyInjection\DefaultFactory::class,
            Service\LanguageDriverService::class => \STLib\Mvc\DependencyInjection\DefaultFactory::class,
        ]
    ],
];
