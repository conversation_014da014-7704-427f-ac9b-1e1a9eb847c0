<?php

declare(strict_types=1);

namespace STRoboMetrics\Service;

class ClickhouseService
{
    /**
     *
     * @param \STRoboMetrics\Data\ClickhouseRepository $clickhouseRepository
     */
    public function __construct(
        protected \STRoboMetrics\Data\ClickhouseRepository $clickhouseRepository,
    ) {
    }

    /**
     *
     * @param \Carbon\Carbon $afterTime
     * @return array
     */
    public function getPerformance(\Carbon\Carbon $afterTime): array
    {
        return $this->clickhouseRepository->getPerformance($afterTime);
    }

    /**
     *
     * @param int $minQueryDurationMs
     * @param \Carbon\Carbon $afterTime
     * @return array
     */
    public function getSlowQueries(int $minQueryDurationMs, \Carbon\Carbon $afterTime): array
    {
        return $this->clickhouseRepository->getSlowQueries($minQueryDurationMs, $afterTime);
    }

    /**
     *
     * @param \Carbon\Carbon $afterTime
     * @return array
     */
    public function getErrorQueries(\Carbon\Carbon $afterTime): array
    {
        return $this->clickhouseRepository->getErrorQueries($afterTime);
    }
}
