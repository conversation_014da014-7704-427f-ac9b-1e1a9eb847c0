<?php

declare(strict_types=1);

namespace STRoboMetrics\Service;

use STRoboMetrics\Data\TranscribeDriversLanguagesTable;
use STRoboMetrics\Data\TranslationDriversLanguagesTable;

class LanguageDriverService
{
    /**
     *
     * @param TranscribeDriversLanguagesTable $transcribeDriversLanguagesTable
     * @param TranslationDriversLanguagesTable $translationDriversLanguagesTable
     */
    public function __construct(
        protected TranscribeDriversLanguagesTable $transcribeDriversLanguagesTable,
        protected TranslationDriversLanguagesTable $translationDriversLanguagesTable,
    ) {
    }

    /**
     * @return array
     */
    public function getTranscribeDriversLanguages(): array
    {
        return $this->transcribeDriversLanguagesTable->getDriversLanguages()->toArray();
    }

    /**
     * @return array
     */
    public function getTranslationDriversLanguages(): array
    {
        return $this->translationDriversLanguagesTable->getDriversLanguages()->toArray();
    }
}
