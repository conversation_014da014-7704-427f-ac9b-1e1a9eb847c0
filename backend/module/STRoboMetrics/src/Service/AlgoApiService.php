<?php

declare(strict_types=1);

namespace STRoboMetrics\Service;

use STRoboMetrics\Data\AlgoApisTable;
use STRoboMetrics\Data\CompaniesTable;

class AlgoApiService
{
    /**
     *
     * @param CompaniesTable $companiesTable
     * @param AlgoApisTable $algoApisTable
     */
    public function __construct(
        protected CompaniesTable $companiesTable,
        protected AlgoApisTable $algoApisTable,
    ) {
    }

    /**
     *
     * @return array
     */
    public function getCompaniesWithAlgoApis(): array
    {
        return $this->companiesTable->getCompaniesWithAlgoApis()->toArray();
    }

    /**
     *
     * @return array
     */
    public function getAlgoApisWithCompanies(): array
    {
        return $this->algoApisTable->getAlgoApisWithCompanies()->toArray();
    }
}
