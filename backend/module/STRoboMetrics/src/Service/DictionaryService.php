<?php

declare(strict_types=1);

namespace STRoboMetrics\Service;

use STAlgo\Data\AlgoEventsTable;
use STRoboMetrics\Data\CompaniesTable;

class DictionaryService
{
    /**
     *
     * @param CompaniesTable $companiesTable
     * @param AlgoEventsTable $algoEventsTable
     */
    public function __construct(
        protected CompaniesTable $companiesTable,
        protected AlgoEventsTable $algoEventsTable,
    ) {
    }

    /**
     *
     * @return array
     */
    public function getCompaniesNames(): array
    {
        return $this->companiesTable->getCompaniesNames()->toArray();
    }

    /**
     *
     * @return array
     */
    public function getAlgoEventsNames(): array
    {
        return $this->algoEventsTable->getAlgoEventsNames()->toArray();
    }
}
