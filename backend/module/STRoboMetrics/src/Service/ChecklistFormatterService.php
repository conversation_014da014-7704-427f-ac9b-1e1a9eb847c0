<?php

declare(strict_types=1);

namespace STRoboMetrics\Service;

class ChecklistFormatterService
{
    public function formatChecklistsStatistics(array $checklistsStats): array
    {
        $formattedStats = [];

        foreach ($checklistsStats as $i => $stat) {
            $formattedStats[$i]['company_name'] = $stat['company_name'];
            $formattedStats[$i]['billing_units'] = 0;
            $formattedStats[$i]['checklists'] = '';

            foreach ($stat['checklists'] as $checklist) {
                $formattedStats[$i]['checklists'] .= sprintf(
                    '%s: %d' . PHP_EOL,
                    $checklist['checklist_name'],
                    $checklist['total_checklist_conversations']
                );
                $formattedStats[$i]['billing_units'] += $checklist['billing_units'];
            }
        }

        return $formattedStats;
    }

    public function formatChecklistsBillingStatistics(array $checklistsStats): array
    {
        $totalChecklistCalls = [];
        $checklistsSplitted = [];

        foreach ($checklistsStats as $i => $stat) {
            $totalChecklistCalls[$i]['company_name'] = $stat['company_name'];
            $totalChecklistCalls[$i]['total_checklist_calls'] = 0;

            $checklistsSplitted[$i]['company_name'] = $stat['company_name'];
            $checklistsSplitted[$i]['checklists_splitted'] = '';

            foreach ($stat['checklists'] as $checklist) {
                $totalChecklistCalls[$i]['total_checklist_calls'] += $checklist['total_checklist_conversations'];
                $checklistsSplitted[$i]['checklists_splitted'] .= sprintf(
                    '%s: %d' . PHP_EOL,
                    $checklist['checklist_name'],
                    $checklist['total_checklist_conversations']
                );
            }
        }

        $formattedStats = [
            'total_checklist_calls' => $totalChecklistCalls,
            'checklists_splitted' => $checklistsSplitted
        ];

        return $formattedStats;
    }

    public function formatUsageStatistics(
        array $callsStats,
        array $chatStats,
        array $checklistsStats,
        array $summarizationStats,
        array $clientSummariesStats
    ): array {
        // Index all statistics by company_name for easier lookup
        $callsStatsMap = $this->mapStatsByCompanyName($callsStats);
        $chatStatsMap = $this->mapStatsByCompanyName($chatStats);
        $checklistsStatsMap = $this->mapStatsByCompanyName($checklistsStats);
        $summarizationStatsMap = $this->mapStatsByCompanyName($summarizationStats);
        $clientSummariesStatsMap = $this->mapStatsByCompanyName($clientSummariesStats);

        $allCompanyNames = array_unique(
            array_merge(
                array_keys($callsStatsMap),
                array_keys($chatStatsMap),
                array_keys($checklistsStatsMap),
                array_keys($summarizationStatsMap),
                array_keys($clientSummariesStatsMap)
            )
        );

        // Create combined statistics array
        $combinedStats = [];
        foreach ($allCompanyNames as $companyName) {
            $combinedStats[] = $this->createCombinedStatForCompany(
                $companyName,
                $callsStatsMap,
                $chatStatsMap,
                $checklistsStatsMap,
                $summarizationStatsMap,
                $clientSummariesStatsMap,
            );
        }

        return $combinedStats;
    }

    public function formatUsageBillingStatistics(
        array $uploadedCallsStats,
        array $transcribedCallsStats,
        array $chatStats,
        array $checklistsSplittedStats,
        array $checklistsStats,
        array $summarizationStats,
        array $clientSummariesStats
    ): array {
        // Index all statistics by company_name for easier lookup
        $uploadedCallsStatsMap = $this->mapStatsByCompanyName($uploadedCallsStats);
        $transcribedCallsStatsMap = $this->mapStatsByCompanyName($transcribedCallsStats);
        $chatStatsMap = $this->mapStatsByCompanyName($chatStats);
        $checklistsSplittedStatsMap = $this->mapStatsByCompanyName($checklistsSplittedStats);
        $checklistsStatsMap = $this->mapStatsByCompanyName($checklistsStats);
        $summarizationStatsMap = $this->mapStatsByCompanyName($summarizationStats);
        $clientSummariesStatsMap = $this->mapStatsByCompanyName($clientSummariesStats);

        $allCompanyNames = array_unique(
            array_merge(
                array_keys($uploadedCallsStatsMap),
                array_keys($transcribedCallsStatsMap),
                array_keys($chatStatsMap),
                array_keys($checklistsSplittedStatsMap),
                array_keys($checklistsStatsMap),
                array_keys($summarizationStatsMap),
                array_keys($clientSummariesStatsMap)
            )
        );

        // Create combined statistics array
        $combinedStats = [];
        foreach ($allCompanyNames as $companyName) {
            $combinedStats[] = $this->createCombinedBillingStatForCompany(
                $companyName,
                $uploadedCallsStatsMap,
                $transcribedCallsStatsMap,
                $chatStatsMap,
                $checklistsSplittedStatsMap,
                $checklistsStatsMap,
                $summarizationStatsMap,
                $clientSummariesStatsMap,
            );
        }

        return $combinedStats;
    }

    private function mapStatsByCompanyName(array $stats): array
    {
        return $stats
            ? array_combine(array_column($stats, 'company_name'), $stats)
            : [];
    }

    /**
     * Create a combined statistic entry for a single company
     */
    private function createCombinedStatForCompany(
        string $companyName,
        array $callsStatsMap,
        array $chatStatsMap,
        array $checklistsStatsMap,
        array $summarizationStatsMap,
        array $clientSummariesStatsMap
    ): array {
        $combinedStat = [
            'company_name' => $companyName,
            'total_call_hours' => $callsStatsMap[$companyName]['total_call_hours'] ?? 0,
            'total_analyzed_hours' => $callsStatsMap[$companyName]['total_analyzed_hours'] ?? 0,
            'total_chats_count' => $chatStatsMap[$companyName]['total_chats_count'] ?? 0,
            'total_summarization_conversations' => $summarizationStatsMap[$companyName]['total_summarization_conversations'] ?? 0,
            'total_client_summaries' => $clientSummariesStatsMap[$companyName]['total_client_summaries'] ?? 0,
            'checklists' => '',
            'billing_units' => 0
        ];

        if (isset($checklistsStatsMap[$companyName])) {
            $combinedStat['checklists'] = $checklistsStatsMap[$companyName]['checklists'];
            $combinedStat['billing_units'] = $checklistsStatsMap[$companyName]['billing_units'];
        }

        return $combinedStat;
    }

    /**
     * Create a combined statistic entry for a single company
     */
    private function createCombinedBillingStatForCompany(
        string $companyName,
        array $uploadedCallsStatsMap,
        array $transcribedCallsStats,
        array $chatStatsMap,
        array $checklistsSplittedStatsMap,
        array $checklistsStatsMap,
        array $summarizationStatsMap,
        array $clientSummariesStatsMap
    ): array {
        $combinedStat = [
            'company_name' => $companyName,
            'total_uploaded_call_hours' => $uploadedCallsStatsMap[$companyName]['total_uploaded_call_hours'] ?? 0,
            'total_transcribed_call_hours' => $transcribedCallsStats[$companyName]['total_transcribed_call_hours'] ?? 0,
            'total_chats_count' => $chatStatsMap[$companyName]['total_chats_count'] ?? 0,
            'total_summarization_conversations' => $summarizationStatsMap[$companyName]['total_summarization_conversations'] ?? 0,
            'total_client_summaries' => $clientSummariesStatsMap[$companyName]['total_client_summaries'] ?? 0,
            'total_checklist_calls' => $checklistsStatsMap[$companyName]['total_checklist_calls'] ?? 0,
            'checklists_splitted' => ''
        ];

        if (isset($checklistsSplittedStatsMap[$companyName])) {
            $combinedStat['checklists_splitted'] = $checklistsSplittedStatsMap[$companyName]['checklists_splitted'];
        }

        return $combinedStat;
    }
}
