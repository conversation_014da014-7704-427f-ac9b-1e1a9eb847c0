<?php

declare(strict_types=1);

namespace STRoboMetrics\Service;

use STRoboMetrics\Data\CallsTable;
use STRoboMetrics\Data\CompaniesTable;

class CompanyService
{
    /**
     *
     * @param CallsTable $callsTable
     * @param CompaniesTable $companiesTable
     */
    public function __construct(
        protected CallsTable $callsTable,
        protected CompaniesTable $companiesTable,
    ) {
    }

    /**
     *
     * @param \STRoboMetrics\Request\Company\CompaniesCallsVolumeRequest $request
     * @return array
     */
    public function getCompaniesCallsVolume(\STRoboMetrics\Request\Company\CompaniesCallsVolumeRequest $request): array
    {
        return $this->callsTable->getCompaniesCallsVolume($request);
    }

    /**
     *
     * @return array
     */
    public function getCompaniesForRoboMetrics(): array
    {
        return $this->companiesTable->getCompaniesForRoboMetrics()->toArray();
    }
}
