<?php

declare(strict_types=1);

namespace STRoboMetrics\Service;

use Carbon\Carbon;
use STRoboMetrics\Data\CallsAlgoEventsTable;
use STRoboMetrics\Data\CallsParagraphsTable;
use STRoboMetrics\Data\PrecalculatedCallsEventsTable;

class AlgoStatisticsService
{
    /**
     *
     * @param CallsAlgoEventsTable $callsAlgoEventsTable
     * @param CallsParagraphsTable $callsParagraphsTable
     * @param PrecalculatedCallsEventsTable $precalculatedCallsEventsTable
     */
    public function __construct(
        protected CallsAlgoEventsTable $callsAlgoEventsTable,
        protected CallsParagraphsTable $callsParagraphsTable,
        protected PrecalculatedCallsEventsTable $precalculatedCallsEventsTable,
    ) {
    }

    /**
     *
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @param float|null $score
     * @return array
     */
    public function getAlgoEventsCountPerParagraphByDays(Carbon $startDate, Carbon $endDate, ?float $score = null): array
    {
        $algoEventsCountByDays = $this->callsAlgoEventsTable->getAlgoEventsCountByDays($startDate, $endDate, $score);
        $paragraphsCountByDays = $this->callsParagraphsTable->getParagraphsCountByDays($startDate, $endDate);

        $algoEventsCountByDaysPerParagraph = [];

        foreach ($algoEventsCountByDays as $date => $algoEventsCountItem) {
            $algoEventsCountByDaysPerParagraph[] = [
                'date' => $date,
                'count' => round($algoEventsCountItem['events_count'] / $paragraphsCountByDays[$date]['paragraphs_count'], 1),
            ];
        }

        return $algoEventsCountByDaysPerParagraph;
    }

    /**
     *
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @param float|null $score
     * @return array
     */
    public function getAlgoEventsCountPerCallByDays(Carbon $startDate, Carbon $endDate, ?float $score = null): array
    {
        return $this->callsAlgoEventsTable->getAlgoEventsCountPerCallByDays($startDate, $endDate, $score);
    }

    /**
     *
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @param string|null $algoEventName
     * @return array
     */
    public function getAverageScorePerEventByDays(Carbon $startDate, Carbon $endDate, string $algoEventName = null): array
    {
        return $this->callsAlgoEventsTable->getAverageScorePerEventByDays($startDate, $endDate, $algoEventName);
    }

    /**
     *
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @param int|null $companyId
     * @return array
     */
    public function getParagraphsCountPerCallByDays(Carbon $startDate, Carbon $endDate, ?int $companyId = null): array
    {
        return $this->callsParagraphsTable->getParagraphsCountPerCallByDays($startDate, $endDate, $companyId);
    }

    /**
     *
     * @param Carbon $startDay
     * @param Carbon $endDate
     * @param int|null $companyId
     * @return array
     */
    public function getParagraphsLengthByDays(Carbon $startDate, Carbon $endDate, ?int $companyId = null): array
    {
        return $this->callsParagraphsTable->getParagraphsLengthByDays($startDate, $endDate, $companyId);
    }

    /**
     *
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @param string|null $algoEventName
     * @return array
     */
    public function getAlgoEventsStatistics(Carbon $startDate, Carbon $endDate, string $algoEventName = null): array
    {
        return $this->precalculatedCallsEventsTable->getAlgoEventsStatistics($startDate, $endDate, $algoEventName);
    }
}
