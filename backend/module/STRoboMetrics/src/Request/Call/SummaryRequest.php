<?php

declare(strict_types=1);

namespace STRoboMetrics\Request\Call;

use ST<PERSON>all\Entity\Call;
use ST<PERSON>all\Entity\ChatCall;
use STRoboMetrics\Request\BaseRequest;

class SummaryRequest extends BaseRequest
{
    public const TIME_DIMENSIONS_ALL = 'all';

    protected const CALL_DATETIME_PARAM_DIMENSION = [
        'uploaded_time',
        'transcribed_at',
        'call_time',
        'analyzed_at'
    ];

    protected const TIME_DIMENSIONS = [
        self::TIME_DIMENSIONS_ALL,
        'year',
        'month',
        'day',
        'hour',
        'minute',
    ];

    /**
     *
     * @var bool
     */
    protected bool $isUsingCompanyDimension = false;

    /**
     *
     * @var string
     */
    protected string $callDatetimeParamDimension = self::CALL_DATETIME_PARAM_DIMENSION[0];

    /**
     *
     * @var string
     */
    protected string $timeDimension = self::TIME_DIMENSIONS[0];

    protected string $callType = Call::CALL_TYPE;

    /**
     *
     * @return bool
     */
    public function getIsUsingCompanyDimension(): bool
    {
        return $this->isUsingCompanyDimension;
    }

    /**
     *
     * @return string
     */
    public function getCallDatetimeParamDimension(): string
    {
        return $this->callDatetimeParamDimension;
    }

    /**
     *
     * @return string
     */
    public function getTimeDimension(): string
    {
        return $this->timeDimension;
    }

    /**
     *
     * @param bool $isUsingCompanyDimension
     * @return SummaryRequest
     */
    public function setIsUsingCompanyDimension(bool $isUsingCompanyDimension): SummaryRequest
    {
        $this->isUsingCompanyDimension = $isUsingCompanyDimension;
        return $this;
    }

    /**
     *
     * @param string|null $callDatetimeParamDimension
     * @return SummaryRequest
     * @throws \InvalidArgumentException
     */
    public function setCallDatetimeParamDimension(?string $callDatetimeParamDimension): SummaryRequest
    {
        if (is_null($callDatetimeParamDimension)) {
            return $this;
        }
        if (!in_array($callDatetimeParamDimension, static::CALL_DATETIME_PARAM_DIMENSION)) {
            throw new \InvalidArgumentException('Incorrect value "' . $callDatetimeParamDimension . '" for "call datetime param dimension". Possible values are "' . implode(', ', static::CALL_DATETIME_PARAM_DIMENSION) . '"');
        }
        $this->callDatetimeParamDimension = $callDatetimeParamDimension;
        return $this;
    }

    /**
     *
     * @param string|null $timeDimension
     * @return SummaryRequest
     * @throws \InvalidArgumentException
     */
    public function setTimeDimension(?string $timeDimension): SummaryRequest
    {
        if (is_null($timeDimension)) {
            return $this;
        }
        if (!in_array($timeDimension, static::TIME_DIMENSIONS)) {
            throw new \InvalidArgumentException('Incorrect value "' . $timeDimension . '" for "time dimension". Possible values are "' . implode(', ', static::TIME_DIMENSIONS) . '"');
        }
        $this->timeDimension = $timeDimension;
        return $this;
    }

    /**
     *
     * @param bool|null $isUsingCompanyDimension
     * @return SummaryRequest|bool
     */
    public function isUsingCompanyDimension(?bool $isUsingCompanyDimension = null): SummaryRequest|bool
    {
        if (is_null($isUsingCompanyDimension)) {
            return $this->isUsingCompanyDimension;
        }
        $this->isUsingCompanyDimension = $isUsingCompanyDimension;
        return $this;
    }

    /**
     *
     * @return string
     */
    public function getTimeDimensionFormat(): string
    {
        return match ($this->timeDimension) {
            'year' => '%Y',
            'month' => '%Y-%m',
            'day' => '%Y-%m-%d',
            'hour' => '%Y-%m-%d %H:00',
            'minute' => '%Y-%m-%d %H:%i',
        };
    }

    public function getCallType(): string
    {
        return $this->callType;
    }

    public function setCallType(string $callType): SummaryRequest
    {
        $this->callType = $callType;
        return $this;
    }
}
