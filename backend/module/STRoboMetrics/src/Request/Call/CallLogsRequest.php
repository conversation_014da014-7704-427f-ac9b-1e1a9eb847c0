<?php

declare(strict_types=1);

namespace STRoboMetrics\Request\Call;

class CallLogsRequest
{
    /**
     * @var int|null
     */
    protected ?int $companyId = null;

    /**
     * @var string
     */
    protected string $callId;

    /**
     * @return int|null
     */
    public function getCompanyId(): ?int
    {
        return $this->companyId;
    }

    /**
     * @return string
     */
    public function getCallId(): string
    {
        return $this->callId;
    }

    /**
     * @param string $callId
     * @return CallLogsRequest
     */
    public function setCallId(string $callId): CallLogsRequest
    {
        $this->callId = $callId;
        return $this;
    }

    /**
     *
     * @param int|null $companyId
     * @return SummaryRequest
     */
    public function setCompanyId(?int $companyId): CallLogsRequest
    {
        $this->companyId = $companyId;
        return $this;
    }
}
