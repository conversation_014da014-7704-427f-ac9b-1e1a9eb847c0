<?php

declare(strict_types=1);

namespace STRoboMetrics\Request;

abstract class BaseRequest
{
    /**
     *
     * @var int|null
     */
    protected ?int $companyId = null;

    /**
     *
     * @var \Carbon\Carbon
     */
    protected \Carbon\Carbon $startDate;

    /**
     *
     * @var \Carbon\Carbon
     */
    protected \Carbon\Carbon $endDate;

    /**
     *
     * @return int|null
     */
    public function getCompanyId(): ?int
    {
        return $this->companyId;
    }

    /**
     *
     * @return \Carbon\Carbon
     */
    public function getStartDate(): \Carbon\Carbon
    {
        return $this->startDate;
    }

    /**
     *
     * @return \Carbon\Carbon
     */
    public function getEndDate(): \Carbon\Carbon
    {
        return $this->endDate;
    }

    /**
     *
     * @param int|null $companyId
     * @return BaseRequest
     */
    public function setCompanyId(?int $companyId): BaseRequest
    {
        $this->companyId = $companyId;
        return $this;
    }

    /**
     *
     * @param \Carbon\Carbon $startDate
     * @return BaseRequest
     */
    public function setStartDate(\Carbon\Carbon $startDate): BaseRequest
    {
        $this->startDate = $startDate;
        return $this;
    }

    /**
     *
     * @param \Carbon\Carbon|string $endDate
     * @return BaseRequest
     */
    public function setEndDate(\Carbon\Carbon|string $endDate): BaseRequest
    {
        $this->endDate = $endDate;
        return $this;
    }
}
