<?php

declare(strict_types=1);

namespace STRoboMetrics\Request\Company;

class CompaniesCallsVolumeRequest
{
    /**
     *
     * @var \Carbon\Carbon
     */
    protected \Carbon\Carbon $startDate;

    /**
     *
     * @var \Carbon\Carbon
     */
    protected \Carbon\Carbon $endDate;

    /**
     *
     * @return \Carbon\Carbon
     */
    public function getStartDate(): \Carbon\Carbon
    {
        return $this->startDate;
    }

    /**
     *
     * @return \Carbon\Carbon
     */
    public function getEndDate(): \Carbon\Carbon
    {
        return $this->endDate;
    }

    /**
     *
     * @param \Carbon\Carbon $startDate
     * @return CompaniesCallsVolumeRequest
     */
    public function setStartDate(\Carbon\Carbon $startDate): CompaniesCallsVolumeRequest
    {
        $this->startDate = $startDate;
        return $this;
    }

    /**
     *
     * @param \Carbon\Carbon|string $endDate
     * @return CompaniesCallsVolumeRequest
     */
    public function setEndDate(\Carbon\Carbon|string $endDate): CompaniesCallsVolumeRequest
    {
        $this->endDate = $endDate;
        return $this;
    }
}
