<?php

declare(strict_types=1);

namespace STRoboMetrics\Data;

use Laminas\Db\ResultSet\ResultSetInterface;
use Laminas\Db\Sql\Expression;

class CompaniesTable extends \STLib\Db\AbstractTable
{
    /**
     *
     * @return \Laminas\Db\ResultSet\ResultSet
     */
    public function getCompaniesNames(): ResultSetInterface
    {
        $select = $this->tableGateway->getSql()->select();
        $select->columns([
            'company_id',
            'company_name',
        ]);

        return $this->tableGateway->selectWith($select);
    }

    /**
     *
     * @return ResultSetInterface
     */
    public function getCompaniesForRoboMetrics(): ResultSetInterface
    {
        $select = $this->tableGateway->getSql()->select();
        $select
                ->columns(
                    [
                        'companies.company_id as company_id',
                        'company_name',
                        'balance' => new Expression('round((paid_transcribing_time / 3600))'),
                        'threshold_bar',
                        'min_call_duration_for_auto_analyze',
                    ],
                    false
                )
                ->where([
                    'companies.deleted' => false,
                ]);


        return $this->tableGateway->selectWith($select);
    }

    /**
     *
     * @return ResultSetInterface
     */
    public function getCompaniesWithAlgoApis(): ResultSetInterface
    {
        $select = $this->tableGateway->getSql()->select();
        $select
            ->columns(
                [
                    'companies.company_id as company_id',
                    'company_name',
                    'algo_api_id' => new Expression('GROUP_CONCAT(aa.algo_api_id SEPARATOR \', \')'),
                    'path' => new Expression('GROUP_CONCAT(aa.path SEPARATOR \' | \')'),
                ],
                false
            )
            ->join(
                [
                    'caa' => 'companies_algo_apis',
                ],
                'caa.company_id = companies.company_id',
                []
            )
            ->join(
                [
                    'aa' => 'algo_apis',
                ],
                'aa.algo_api_id = caa.algo_api_id',
                []
            )
            ->where([
                'companies.deleted' => false,
                'aa.is_deleted' => 0
            ])
            ->group('companies.company_id');

        return $this->tableGateway->selectWith($select);
    }
}
