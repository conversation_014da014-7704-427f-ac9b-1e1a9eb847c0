<?php

declare(strict_types=1);

namespace STRoboMetrics\Data;

use Carbon\Carbon;

class CallsParagraphsTable extends \STClickhouse\Entity\BaseTable
{
    use \STClickhouse\Data\QueriesTrait;

    /**
     *
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return array
     */
    public function getParagraphsCountByDays(Carbon $startDate, Carbon $endDate): array
    {
        $sql = '
            SELECT
                count(*) paragraphs_count,
                toDate(call_time) date
            FROM 
            (
                ' . $this->getFinalTableSqlUsingGroupBy(
            'calls_paragraphs',
            [
                    'company_id',
                    'call_id',
                    'paragraph_number',
                ],
            'created',
            [
                    'call_time',
                ],
            [
                    [
                        'column' => 'call_time',
                        'value' => $startDate,
                        'type' => 'date',
                        'compare' => '>=',
                    ],
                    [
                        'column' => 'call_time',
                        'value' => $endDate,
                        'type' => 'date',
                        'compare' => '<',
                    ],
                ]
        ) . '
            ) 
            GROUP BY date
        ';

        return $this->getClient()->selectAsTree($sql, 'date');
    }

    /**
     *
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @param int|null $companyId
     * @return array
     */
    public function getParagraphsCountPerCallByDays(Carbon $startDate, Carbon $endDate, ?int $companyId = null): array
    {
        $where = [
            [
                'column' => 'call_time',
                'value' => $startDate,
                'type' => 'date',
                'compare' => '>=',
            ],
            [
                'column' => 'call_time',
                'value' => $endDate,
                'type' => 'date',
                'compare' => '<',
            ],
        ];

        if ($companyId !== null) {
            $where['company_id'] = $companyId;
        }

        $sql = '
            SELECT
                count(*) paragraphs_count,
                uniq(call_id) calls_count,
                round(paragraphs_count / calls_count) paragraphs_count_per_call,
                toDate(call_time) date
            FROM 
            (
                ' . $this->getFinalTableSqlUsingGroupBy(
            'calls_paragraphs',
            [
                    'company_id',
                    'call_id',
                    'paragraph_number',
                ],
            'created',
            [
                    'call_time',
                ],
            $where
        ) . '
            ) 
            GROUP BY date
        ';

        return $this->getClient()->selectAll($sql);
    }

    /**
     *
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @param int|null $companyId
     * @return array
     */
    public function getParagraphsLengthByDays(Carbon $startDate, Carbon $endDate, ?int $companyId = null): array
    {
        $where = [
            [
                'column' => 'call_time',
                'value' => $startDate,
                'type' => 'date',
                'compare' => '>=',
            ],
            [
                'column' => 'call_time',
                'value' => $endDate,
                'type' => 'date',
                'compare' => '<',
            ],
        ];

        if ($companyId !== null) {
            $where['company_id'] = $companyId;
        }

        $sql = '
            SELECT
                count(*) paragraphs_count,
                uniq(call_id) calls_count,
                round(avg(length(text)), 1) avg_paragraph_length,
                toDate(call_time) date
            FROM 
            (
                ' . $this->getFinalTableSqlUsingGroupBy(
            'calls_paragraphs',
            [
                    'company_id',
                    'call_id',
                    'paragraph_number',
                ],
            'created',
            [
                    'call_time',
                    'text',
                    'start_time',
                    'end_time',
                ],
            $where
        ) . '
            ) 
            GROUP BY date
        ';

        return $this->getClient()->selectAll($sql);
    }
}
