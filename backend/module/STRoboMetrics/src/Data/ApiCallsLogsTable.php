<?php

declare(strict_types=1);

namespace STRoboMetrics\Data;

use STRoboMetrics\Request\Call\CallLogsRequest;

class ApiCallsLogsTable extends \STClickhouse\Entity\BaseTable
{
    /**
     *
     * @param CallLogsRequest $request
     * @return array
     */
    public function getCallLogs(CallLogsRequest $request): array
    {
        $sql = <<<SQL
            SELECT 
                *
            FROM 
                api_calls_logs
            WHERE 
                company_id = {$request->getCompanyId()}
                AND call_id = '{$request->getCallId()}'
            ORDER BY
                created DESC
        SQL;

        return $this->getClient()->selectAll($sql);
    }

    /**
     *
     * @param CallLogsRequest $request
     * @return array
     */
    public function getCallLogsByMessage(CallLogsRequest $request): array
    {
        $sql = <<<SQL
            SELECT 
                *
            FROM 
                api_calls_logs 
            WHERE 
                company_id = {$request->getCompanyId()}
                AND 
                (
                    message LIKE '%{$request->getCallId()}%'
                    OR id = '{$request->getCallId()}'
                )
            ORDER BY
                created DESC
            LIMIT 100
        SQL;

        return $this->getClient()->selectAll($sql);
    }
}
