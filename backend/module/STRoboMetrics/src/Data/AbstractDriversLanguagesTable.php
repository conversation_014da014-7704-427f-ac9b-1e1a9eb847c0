<?php

declare(strict_types=1);

namespace STRoboMetrics\Data;

use STLib\Db\AbstractTable;

class AbstractDriversLanguagesTable extends AbstractTable
{
    /**
     *
     * @return \Laminas\Db\ResultSet\ResultSetInterface
     */
    public function getDriversLanguages(): \Laminas\Db\ResultSet\ResultSetInterface
    {
        return $this->tableGateway->selectWith(
            $this->tableGateway->getSql()->select()
        );
    }
}
