<?php

declare(strict_types=1);

namespace STRoboMetrics\Data;

use Carbon\Carbon;

class CallsAlgoEventsTable extends \STClickhouse\Entity\BaseTable
{
    use \STClickhouse\Data\QueriesTrait;

    /**
     *
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @param float|null $score
     * @return array
     */
    public function getAlgoEventsCountByDays(Carbon $startDate, Carbon $endDate, ?float $score = null): array
    {
        $where = [
            [
                'column' => 'call_time',
                'value' => $startDate,
                'type' => 'date',
                'compare' => '>=',
            ],
            [
                'column' => 'call_time',
                'value' => $endDate,
                'type' => 'date',
                'compare' => '<',
            ],
        ];

        if ($score !== null) {
            $where[] = [
                'column' => 'score',
                'value' => $score,
                'type' => 'compare',
                'compare' => '>=',
            ];
        }

        $sql = '
            SELECT
                count(*) events_count,
                toDate(call_time) date
            FROM 
            (
                ' . $this->getFinalTableSqlUsingGroupBy(
            'calls_algo_events',
            [
                'company_id',
                'call_id',
                'paragraph_number',
                'algo_api_id',
                'event',
            ],
            'created',
            ['call_time'],
            $where
        ) . '
            ) 
            GROUP BY date
        ';

        return $this->getClient()->selectAsTree($sql, 'date');
    }

    /**
     *
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @param float|null $score
     * @return array
     */
    public function getAlgoEventsCountPerCallByDays(Carbon $startDate, Carbon $endDate, ?float $score = null): array
    {
        $where = [
            [
                'column' => 'call_time',
                'value' => $startDate,
                'type' => 'date',
                'compare' => '>=',
            ],
            [
                'column' => 'call_time',
                'value' => $endDate,
                'type' => 'date',
                'compare' => '<',
            ],
        ];

        if ($score !== null) {
            $where[] = [
                'column' => 'score',
                'value' => $score,
                'type' => 'compare',
                'compare' => '>=',
            ];
        }

        $sql = '
            SELECT
                count(*) events_count,
                uniq(call_id) calls_count,
                round(events_count / calls_count) events_count_per_call,
                toDate(call_time) date
            FROM 
            (
                ' . $this->getFinalTableSqlUsingGroupBy(
            'calls_algo_events',
            [
                'company_id',
                'call_id',
                'paragraph_number',
                'algo_api_id',
                'event',
            ],
            'created',
            ['call_time'],
            $where
        ) . '
            ) 
            GROUP BY date
        ';

        return $this->getClient()->selectAll($sql);
    }

    /**
     *
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @param string|null $algoEventName
     * @return array
     */
    public function getAverageScorePerEventByDays(Carbon $startDate, Carbon $endDate, string $algoEventName = null): array
    {
        $where = [
            [
                'column' => 'call_time',
                'value' => $startDate,
                'type' => 'date',
                'compare' => '>=',
            ],
            [
                'column' => 'call_time',
                'value' => $endDate,
                'type' => 'date',
                'compare' => '<',
            ],
        ];

        if ($algoEventName !== null) {
            $where['event'] = $algoEventName;
        }


        $sql = '
            SELECT
                round(avg(score), 2) avg_score,
                count(event) count_of_detection,
                toDate(call_time) date
            FROM 
            (
                ' . $this->getFinalTableSqlUsingGroupBy(
            'calls_algo_events',
            [
                'company_id',
                'call_id',
                'paragraph_number',
                'algo_api_id',
                'event',
            ],
            'created',
            [
                'call_time',
                'score',
            ],
            $where
        ) . '
            ) 
            GROUP BY date
        ';

        return $this->getClient()->selectAll($sql);
    }
}
