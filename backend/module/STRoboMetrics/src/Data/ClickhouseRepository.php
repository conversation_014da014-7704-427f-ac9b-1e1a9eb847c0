<?php

declare(strict_types=1);

namespace STRoboMetrics\Data;

class ClickhouseRepository extends \STClickhouse\Entity\BaseTable
{
    /**
     *
     * @param \Carbon\Carbon $afterTime
     * @return array
     */
    public function getPerformance(\Carbon\Carbon $afterTime): array
    {
        $sql = '
            SELECT
                formatDateTime(query_start_time, \'%Y-%m-%d %H:%i\') time,
                COUNT(*) queries_count,
                AVG(memory_usage) avg_memory_usage,
                AVG(query_duration_ms) avg_duration_ms
            FROM
                system.query_log
            WHERE 
                type = \'QueryFinish\'
                AND query_kind = \'Select\'
                AND query_start_time >= toDateTime(\'' . $afterTime . '\')
            GROUP BY 
                time
            ORDER BY
                time DESC
            LIMIT 1000
        ';
        return $this->getClient()->selectAll($sql);
    }

    /**
     *
     * @param int $minQueryDurationMs
     * @param \Carbon\Carbon $afterTime
     * @return array
     */
    public function getSlowQueries(int $minQueryDurationMs, \Carbon\Carbon $afterTime): array
    {
        $sql = '
            SELECT
                event_time,
                query_duration_ms,
                read_rows,
                read_bytes,
                memory_usage,
                query,
                query_id,
                exception_code,
                stack_trace
            FROM
                system.query_log
            WHERE 
                event_time >= toDateTime(\'' . $afterTime . '\')
                AND query_duration_ms >= ' . $minQueryDurationMs . '
            ORDER BY 
                event_time DESC
            LIMIT 200
        ';
        return $this->getClient()->selectAll($sql);
    }

    /**
     *
     * @param \Carbon\Carbon $afterTime
     * @return array
     */
    public function getErrorQueries(\Carbon\Carbon $afterTime): array
    {
        $sql = '
            SELECT
                event_time,
                query_duration_ms,
                read_rows,
                read_bytes,
                memory_usage,
                query,
                query_id,
                exception_code,
                stack_trace
            FROM
                system.query_log
            WHERE 
                type = \'ExceptionWhileProcessing\'
                AND event_time >= toDateTime(\'' . $afterTime . '\')
            ORDER BY 
                event_time DESC
            LIMIT 200
        ';
        return $this->getClient()->selectAll($sql);
    }
}
