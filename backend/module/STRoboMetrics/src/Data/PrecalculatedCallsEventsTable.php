<?php

declare(strict_types=1);

namespace STRoboMetrics\Data;

use Carbon\Carbon;

class PrecalculatedCallsEventsTable extends \STClickhouse\Entity\BaseTable
{
    use \STClickhouse\Data\QueriesTrait;

    /**
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @param string|null $algoEventName
     * @return array
     */
    public function getAlgoEventsStatistics(Carbon $startDate, Carbon $endDate, string $algoEventName = null): array
    {
        $sql = <<<SQL
                SELECT
                    call_date,
                    algo_event,
                    countIf(isMovedFromNeutral) false_negative,
                    countIf(isMovedToNeutral) moved_to_neutral_count,
                    countIf(isChangedToOther) changed_to_other_count,
                    (moved_to_neutral_count + changed_to_other_count) false_positive,
                    countIf(isConfirmed) true_positives,
                    round(true_positives / nullif(true_positives + false_positive + false_negative, 0), 2) as total_accuracy,
                    round(true_positives / nullif(true_positives + false_positive, 0), 2) as detected_accuracy,
                    round(true_positives / nullif(true_positives + false_negative, 0), 2) as not_detected_accuracy
                FROM (
                    SELECT
                        toDate(pc.call_time) call_date,
                        events.event algo_event,
                        pce.event_is_deleted isMovedToNeutral,
                        if(pce.event_changed_from_event_id is NULL AND NOT pce.event_is_deleted, true, false) isMovedFromNeutral,
                        if(NOT pce.event_is_deleted AND pce.event_changed_from_event_id IS NOT NULL AND pce.event_changed_from_event_id != pce.event_id, true, false) isChangedFromOther,
                        false isChangedToOther,
                        if(pce.event_changed_from_event_id = pce.event_id AND NOT pce.event_is_deleted, true, false) isConfirmed
                    FROM ({$this->getFinalTableSqlUsingGroupBy(
                        'precalculated_calls_events',
                        [
                            'company_id',
                            'role_id',
                            'call_id',
                            'paragraph',
                            'event_id',
                        ],
                        'created',
                        [
                            'event_category_id',
                            'event_changed_from_event_id',
                            'event_is_deleted',
                        ],
                        [
                            [
                                'column' => 'call_time',
                                'value' => $startDate,
                                'type' => 'date',
                                'compare' => '>=',
                            ],
                            [
                                'column' => 'call_time',
                                'value' => $endDate,
                                'type' => 'date',
                                'compare' => '<',
                            ],
                        ],
                    )}) pce
                    INNER JOIN (
                        SELECT
                            last_value_respect_nulls(algo_event) event,
                            event_id
                        FROM dictionary(events)
                        GROUP BY event_id
                        HAVING
                            uniq(algo_event) = 1 AND uniq(search_word_name) = 0
                    ) events ON events.event_id = pce.event_id
                    INNER JOIN ({$this->getFinalTableSqlUsingGroupBy(
                        'precalculated_calls',
                        [
                            'company_id',
                            'call_id',
                            'role_id',
                        ],
                        'created',
                        [
                            'is_reviewed',
                            'call_time',
                        ],
                        [
                            [
                                'column' => 'call_time',
                                'value' => $startDate,
                                'type' => 'date',
                                'compare' => '>=',
                            ],
                            [
                                'column' => 'call_time',
                                'value' => $endDate,
                                'type' => 'date',
                                'compare' => '<',
                            ],
                            'is_reviewed' => 1,
                        ],
                    )}) pc ON pc.call_id = pce.call_id
                    UNION ALL
                    SELECT
                        toDate(pc.call_time) call_date,
                        events.event algo_event,
                        false isMovedToNeutral,
                        false isMovedFromNeutral,
                        false isChangedFromOther,
                        true isChangedToOther,
                        false isConfirmed
                    FROM ({$this->getFinalTableSqlUsingGroupBy(
                        'precalculated_calls_events',
                        [
                            'company_id',
                            'role_id',
                            'call_id',
                            'paragraph',
                            'event_id',
                        ],
                        'created',
                        [
                            'event_changed_from_event_id',
                            'event_is_deleted',
                        ],
                        [
                            [
                                'column' => 'call_time',
                                'value' => $startDate,
                                'type' => 'date',
                                'compare' => '>=',
                            ],
                            [
                                'column' => 'call_time',
                                'value' => $endDate,
                                'type' => 'date',
                                'compare' => '<',
                            ],
                        ],
                    )}) pce
                    INNER JOIN (
                        SELECT
                            last_value_respect_nulls(algo_event) event,
                            event_id
                        FROM dictionary(events)
                        GROUP BY event_id
                        HAVING
                            uniq(algo_event) = 1 AND uniq(search_word_name) = 0
                    ) events ON events.event_id = pce.event_changed_from_event_id
                    INNER JOIN ({$this->getFinalTableSqlUsingGroupBy(
                        'precalculated_calls',
                        [
                            'company_id',
                            'call_id',
                            'role_id',
                        ],
                        'created',
                        [
                            'is_reviewed',
                            'call_time',
                        ],
                        [
                            [
                                'column' => 'call_time',
                                'value' => $startDate,
                                'type' => 'date',
                                'compare' => '>=',
                            ],
                            [
                                'column' => 'call_time',
                                'value' => $endDate,
                                'type' => 'date',
                                'compare' => '<',
                            ],
                            'is_reviewed' => 1,
                        ],
                    )}) pc ON pc.call_id = pce.call_id
                    WHERE
                        NOT pce.event_is_deleted
                        AND pce.event_changed_from_event_id IS NOT NULL
                        AND pce.event_changed_from_event_id != pce.event_id
                    )
            SQL;

        if ($algoEventName !== null) {
            $sql .= ' WHERE algo_event = \'' . $algoEventName . '\'';
        }

        $sql .= ' GROUP BY call_date, algo_event';

        return $this->getClient()->selectAll($sql);
    }
}
