<?php

declare(strict_types=1);

namespace STRoboMetrics\Data;

class AlgoApisTable extends \STLib\Db\AbstractTable
{
    /**
     *
     * @return \Laminas\Db\ResultSet\ResultSetInterface
     */
    public function getAlgoApisWithCompanies(): \Laminas\Db\ResultSet\ResultSetInterface
    {
        $select = $this->tableGateway->getSql()->select();
        $select
            ->columns(
                [
                    'algo_apis.path as path',
                    'companies_id' => new \Laminas\Db\Sql\Expression('GROUP_CONCAT(DISTINCT caa.company_id SEPARATOR \' | \')'),
                    'companies_names' => new \Laminas\Db\Sql\Expression('GROUP_CONCAT(DISTINCT c.company_name SEPARATOR \' | \')'),
                    'algo_events' => new \Laminas\Db\Sql\Expression('GROUP_CONCAT(DISTINCT ae.algo_event SEPARATOR \' | \')'),
                ],
                false
            )
            ->join(
                [
                    'caa' => 'companies_algo_apis',
                ],
                'caa.algo_api_id = algo_apis.algo_api_id',
                [],
                \Laminas\Db\Sql\Select::JOIN_LEFT
            )
            ->join(
                [
                    'c' => 'companies',
                ],
                'caa.company_id = c.company_id',
                [],
                \Laminas\Db\Sql\Select::JOIN_LEFT
            )
            ->join(
                [
                    'ae' => 'algo_events',
                ],
                'ae.algo_api_id = caa.algo_api_id',
                [],
                \Laminas\Db\Sql\Select::JOIN_LEFT
            )
            ->group('algo_apis.algo_api_id')
        ;

        return $this->tableGateway->selectWith($select);
    }
}
