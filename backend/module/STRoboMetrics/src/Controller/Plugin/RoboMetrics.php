<?php

declare(strict_types=1);

namespace STRoboMetrics\Controller\Plugin;

use Laminas\Mvc\Controller\Plugin\AbstractPlugin;

class RoboMetrics extends AbstractPlugin
{
    /**
     *
     * @return \STRoboMetrics\Service\DictionaryService
     */
    public function dictionary(): \STRoboMetrics\Service\DictionaryService
    {
        return $this->getController()->getServiceManager()->get(\STRoboMetrics\Service\DictionaryService::class);
    }

    /**
     *
     * @return \STRoboMetrics\Service\AlgoApiService
     */
    public function algoApi(): \STRoboMetrics\Service\AlgoApiService
    {
        return $this->getController()->getServiceManager()->get(\STRoboMetrics\Service\AlgoApiService::class);
    }

    /**
     *
     * @return \STRoboMetrics\Service\AlgoStatisticsService
     */
    public function algoStatistics(): \STRoboMetrics\Service\AlgoStatisticsService
    {
        return $this->getController()->getServiceManager()->get(\STRoboMetrics\Service\AlgoStatisticsService::class);
    }

    /**
     *
     * @return \STRoboMetrics\Service\CallService
     */
    public function call(): \STRoboMetrics\Service\CallService
    {
        return $this->getController()->getServiceManager()->get(\STRoboMetrics\Service\CallService::class);
    }

    /**
     *
     * @return \STRoboMetrics\Service\CompanyService
     */
    public function company(): \STRoboMetrics\Service\CompanyService
    {
        return $this->getController()->getServiceManager()->get(\STRoboMetrics\Service\CompanyService::class);
    }

    /**
     *
     * @return \STRoboMetrics\Service\ClickhouseService
     */
    public function clickhouse(): \STRoboMetrics\Service\ClickhouseService
    {
        return $this->getController()->getServiceManager()->get(\STRoboMetrics\Service\ClickhouseService::class);
    }

    /**
     *
     * @return \STRoboMetrics\Service\LanguageDriverService
     */
    public function languageDriver(): \STRoboMetrics\Service\LanguageDriverService
    {
        return $this->getController()->getServiceManager()->get(\STRoboMetrics\Service\LanguageDriverService::class);
    }
}
