<?php

declare(strict_types=1);

namespace STCompany\Data;

class CompanyDataRemoveRepository extends \STClickhouse\Entity\BaseTable
{
    /**
     * @param int $companyId
     * @return void
     */
    public function removeCallsLogsData(int $companyId): void
    {
        $sql = '
            DELETE FROM
                api_calls_logs
            WHERE
                company_id = ' . $companyId . '
        ';
        $this->getClient()->write($sql);
    }

    /**
     * @param int $companyId
     * @param \Carbon\Carbon $startDate
     * @param \Carbon\Carbon $endDate
     * @return void
     */
    public function removeCallsData(int $companyId, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): void
    {
        $sql = '
            DELETE FROM
                calls
            WHERE
                company_id = ' . $companyId . '
                AND call_time >= toDateTime(\'' . $startDate . '\')
                AND call_time <= toDateTime(\'' . $endDate . '\')
        ';
        $this->getClient()->write($sql);
    }

    /**
     * @param int $companyId
     * @param \Carbon\Carbon $startDate
     * @param \Carbon\Carbon $endDate
     * @return void
     */
    public function removeAlgoEventsData(int $companyId, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): void
    {
        $sql = '
            DELETE FROM
                calls_algo_events
            WHERE
                company_id = ' . $companyId . '
                AND call_time >= toDateTime(\'' . $startDate . '\')
                AND call_time <= toDateTime(\'' . $endDate . '\')
        ';
        $this->getClient()->write($sql);
    }

    /**
     * @param int $companyId
     * @return void
     */
    public function removeNotConsistentCallsCommentsData(int $companyId): void
    {
        $sql = '
            DELETE FROM
                calls_comments
            WHERE
                company_id = ' . $companyId . '
                AND call_id NOT IN (
                    SELECT DISTINCT
                        call_id
                    FROM
                        calls
                    WHERE
                        company_id = ' . $companyId . '
                )
        ';
        $this->getClient()->write($sql);
    }

    /**
     * @param int $companyId
     * @return void
     */
    public function removeNotConsistentCallsCommentsNotificationsData(int $companyId): void
    {
        $sql = '
            DELETE FROM
                calls_comments_notifications
            WHERE
                company_id = ' . $companyId . '
                AND comment_id NOT IN (
                    SELECT DISTINCT
                        comment_id
                    FROM
                        calls_comments
                    WHERE
                        company_id = ' . $companyId . '
                )
        ';
        $this->getClient()->write($sql);
    }

    /**
     * @param int $companyId
     * @param \Carbon\Carbon $startDate
     * @param \Carbon\Carbon $endDate
     * @return void
     */
    public function removeCallsParagraphsData(int $companyId, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): void
    {
        $sql = '
            DELETE FROM
                calls_paragraphs
            WHERE
                company_id = ' . $companyId . '
                AND call_time >= toDateTime(\'' . $startDate . '\')
                AND call_time <= toDateTime(\'' . $endDate . '\')
        ';
        $this->getClient()->write($sql);
    }

    /**
     * @param int $companyId
     * @param \Carbon\Carbon $startDate
     * @param \Carbon\Carbon $endDate
     * @return void
     */
    public function removeCallsReviewsData(int $companyId, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): void
    {
        $sql = '
            DELETE FROM
                calls_reviews
            WHERE
                company_id = ' . $companyId . '
                AND call_time >= toDateTime(\'' . $startDate . '\')
                AND call_time <= toDateTime(\'' . $endDate . '\')
        ';
        $this->getClient()->write($sql);
    }

    /**
     * @param int $companyId
     * @return void
     */
    public function removeNotConsistentClientsData(int $companyId): void
    {
        $sql = '
            DELETE FROM
                clients
            WHERE
                company_id = ' . $companyId . '
                AND client_id NOT IN (
                    SELECT DISTINCT
                        client_id
                    FROM
                        calls
                    WHERE
                        company_id = ' . $companyId . '
                )
        ';
        $this->getClient()->write($sql);
    }

    /**
     * @param int $companyId
     * @param \Carbon\Carbon $startDate
     * @param \Carbon\Carbon $endDate
     * @return void
     */
    public function removePrecalculatedAgentsData(int $companyId, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): void
    {
        $sql = '
            DELETE FROM
                precalculated_agents
            WHERE
                company_id = ' . $companyId . '
                AND date >= toDateTime(\'' . $startDate . '\')
                AND date <= toDateTime(\'' . $endDate . '\')
        ';
        $this->getClient()->write($sql);
    }

    /**
     * @param int $companyId
     * @param \Carbon\Carbon $startDate
     * @param \Carbon\Carbon $endDate
     * @return void
     */
    public function removePrecalculatedCallsData(int $companyId, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): void
    {
        $sql = '
            DELETE FROM
                precalculated_calls
            WHERE
                company_id = ' . $companyId . '
                AND call_time >= toDateTime(\'' . $startDate . '\')
                AND call_time <= toDateTime(\'' . $endDate . '\')
        ';
        $this->getClient()->write($sql);
    }

    /**
     * @param int $companyId
     * @param \Carbon\Carbon $startDate
     * @param \Carbon\Carbon $endDate
     * @return void
     */
    public function removePrecalculatedCallsEventsData(int $companyId, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): void
    {
        $sql = '
            DELETE FROM
                precalculated_calls_events
            WHERE
                company_id = ' . $companyId . '
                AND call_time >= toDateTime(\'' . $startDate . '\')
                AND call_time <= toDateTime(\'' . $endDate . '\')
        ';
        $this->getClient()->write($sql);
    }

    /**
     * @param int $companyId
     * @param \Carbon\Carbon $startDate
     * @param \Carbon\Carbon $endDate
     * @return void
     */
    public function removePrecalculatedClientsData(int $companyId, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): void
    {
        $sql = '
            DELETE FROM
                precalculated_clients
            WHERE
                company_id = ' . $companyId . '
                AND date >= toDateTime(\'' . $startDate . '\')
                AND date <= toDateTime(\'' . $endDate . '\')
        ';
        $this->getClient()->write($sql);
    }
}
