<?php

namespace STCompany\Data;

use <PERSON><PERSON>\Db\Sql\Select;
use <PERSON><PERSON>\Db\Sql\Where;
use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Entity\LlmEvent\LlmEvent;
use STCompany\Entity\LlmEvent\LlmEventCollection;
use STLib\Db\HydratedAbstractTable;

class CompanyLlmEventsTable extends HydratedAbstractTable
{
    public static string $table = 'companies_llm_events';
    public static string $entityName = LlmEvent::class;
    public static string $collectionName = LlmEventCollection::class;

    public static function getTableName(): string
    {
        return self::$table;
    }

    public static function getEntityName(): string
    {
        return self::$entityName;
    }

    public static function getCollectionName(): string
    {
        return self::$collectionName;
    }

    /**
     * @param int $companyId
     * @return LlmEventCollection
     */
    public function getLlmEvents(int $companyId): LlmEventCollection
    {
        $select = $this->tableGateway->getSql()
            ->select()
            ->columns(['company_id']);
        $this->joinLlmEvents($select);
        $select
            ->where([
                self::$table . '.company_id' => $companyId,
            ]);

        return $this->tableGateway->selectWith($select)->getCollection();
    }

    public function getLlmEventsCount(int $companyId): int
    {
        $select = $this->tableGateway->getSql()
            ->select()
            ->columns(['company_id']);
        $this->joinLlmEvents($select);
        $select
            ->where([
                self::$table . '.company_id' => $companyId,
            ]);

        return $this->tableGateway->selectWith($select)->count();
    }

    /**
     * @throws NotFoundApiException
     */
    public function getLlmEvent(int $llmEventId, int $companyId): LlmEvent
    {
        $select = $this->tableGateway->getSql()->select()
            ->columns(['company_id']);
        $this->joinLlmEvents($select);
        $select
            ->where([
                self::$table . '.company_id' => $companyId,
                'le.llm_event_id' => $llmEventId
            ]);

        $result = $this->tableGateway->selectWith($select);
        if ($result->count() === 0) {
            throw new NotFoundApiException('Company llm event not found');
        }

        return $result->current();
    }

    private function joinLlmEvents(Select $select): void
    {
        $select->join(
            [
                'le' => 'llm_events',
            ],
            'le.llm_event_id = ' . self::$table . '.llm_event_id'
        );
    }

    public function saveEvent(LlmEvent $llmEvent): void
    {
        $data = [
            'llm_event_id' => $llmEvent->getId(),
            'company_id' => $llmEvent->getCompanyId(),
        ];

        $this->tableGateway->insert($data);
    }

    public function saveEvents(LlmEventCollection $llmEvents): void
    {
        $data = [];
        foreach ($llmEvents as $llmEvent) {
            $data[] = [
                'company_id' => $llmEvent->getCompanyId(),
                'llm_event_id' => $llmEvent->getId(),
            ];
        }

        if (empty($data)) {
            return;
        }

        $this->multiInsert($data, useTransaction: false);
    }

    public function deleteEvent(int $llmEventId, int $companyId): void
    {
        $this->tableGateway->delete([
            'llm_event_id' => $llmEventId,
            'company_id' => $companyId,
        ]);
    }

    public function isLlmEventConnectedToAnotherCompany(int $llmEventId, int $companyId): bool
    {
        $select = $this->tableGateway->getSql()->select();
        $select
            ->where(function (Where $where) use ($llmEventId, $companyId) {
                $where->equalTo(self::$table . '.llm_event_id', $llmEventId);
                $where->notEqualTo(self::$table . '.company_id', $companyId);
            });

        $result = $this->tableGateway->selectWith($select);

        return $result->count() !== 0;
    }
}
