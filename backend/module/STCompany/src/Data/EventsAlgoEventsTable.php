<?php

declare(strict_types=1);

namespace STCompany\Data;

use Laminas\Db\ResultSet\ResultSet;
use <PERSON>inas\Db\Sql\Expression;
use STCompany\Entity\Event\Event;
use STLib\Db\AbstractTable;

class EventsAlgoEventsTable extends AbstractTable
{
    /**
     * @param int|array $eventIds
     * @return ResultSet
     */
    public function getAlgoEvents(int|array $eventIds): ResultSet
    {
        $select = $this->tableGateway->getSql()->select();
        $select->where([
            'event_id' => $eventIds,
        ]);
        return $this->tableGateway->selectWith($select);
    }

    /**
     * @param Event $event
     * @return int
     */
    public function saveAlgoEvents(Event $event): int
    {
        $data = [];
        foreach ($event->getAlgoEvents() as $algoEvent) {
            $data[] = [
                'event_id' => $event->getId(),
                'algo_event' => $algoEvent,
            ];
        }
        return $this->multiInsert($data, false);
    }

    /**
     * @param int $eventId
     * @param array|string|null $algoEvents
     * @return int
     */
    public function deleteAlgoEvents(int $eventId, array|string $algoEvents = null): int
    {
        if (!is_null($algoEvents)) {
            return $this->tableGateway->delete([
                'event_id' => $eventId,
                'algo_event' => $algoEvents,
            ]);
        } else {
            return $this->tableGateway->delete([
                'event_id' => $eventId,
            ]);
        }
    }

    public function orderAlgoEventsByCount(array $algoEventNames, int $limit): ResultSet
    {
        $select = $this->tableGateway->getSql()->select();
        $select
            ->columns([
                'algo_event',
                'event_count' => new Expression('COUNT(*)'),
            ])
            ->where([
                'algo_event' => $algoEventNames,
            ])
            ->group('algo_event')
            ->order('event_count DESC')
            ->limit($limit);

        return $this->tableGateway->selectWith($select);
    }
}
