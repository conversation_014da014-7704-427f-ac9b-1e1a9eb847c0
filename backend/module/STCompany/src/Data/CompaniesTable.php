<?php

declare(strict_types=1);

namespace STCompany\Data;

use Carbon\Carbon;
use Laminas\Db\ResultSet\ResultSetInterface;
use Laminas\Db\Sql\Predicate\Expression;
use Laminas\Db\Sql\Select;
use ReflectionException;
use ST<PERSON>pi\Entity\Exception\NotFoundApiException;
use STCompany\Entity\Company;
use STCompany\Entity\CompanyCollection;
use STLib\Db\AbstractTable;
use STLib\Mvc\Hydrator\BaseHydratorTrait;

class CompaniesTable extends AbstractTable
{
    use BaseHydratorTrait;

    /**
     *
     * @return ResultSetInterface
     */
    public function getCompanyIds(): ResultSetInterface
    {
        $select = $this->tableGateway->getSql()->select();
        $select
            ->columns([
                'company_id'
            ])
            ->where([
                'deleted' => false,
            ]);
        return $this->tableGateway->selectWith($select);
    }

    /**
     *
     * @return ResultSetInterface
     */
    public function getCompanyIdsWithS3Integration(): ResultSetInterface
    {
        $select = $this->tableGateway->getSql()->select();
        $select->columns([
            'company_id'
        ]);
        $select->where([
            'companies.deleted' => false,
            'companies.is_s3_integration_enabled' => true,
        ]);

        return $this->tableGateway->selectWith($select);
    }

    public function getCompanyIdsWithClientSummaryEnabled(): array
    {
        $select = $this->tableGateway->getSql()->select();
        $select->columns([
            'company_id'
        ]);
        $select->where([
            'companies.deleted' => false,
            'companies.is_client_summary_enabled' => true,
        ]);

        return array_map(function ($companyId) {
            return (int) $companyId;
        }, array_column($this->tableGateway->selectWith($select)->toArray(), 'company_id'));
    }

    /**
     *
     * @return \Laminas\Db\ResultSet\ResultSetInterface
     */
    public function getCompanyIdsWithCallsRemovingEnabled(): \Laminas\Db\ResultSet\ResultSetInterface
    {
        $select = $this->tableGateway->getSql()->select();
        $select->columns([
            'company_id'
        ]);
        $select->where([
            'companies.deleted' => false,
        ])
            ->where
            ->isNotNull('companies.days_to_remove_calls');

        return $this->tableGateway->selectWith($select);
    }

    /**
     *
     * @return ResultSetInterface
     */
    public function getCompanyIdsWithS3ReportIntegration(): ResultSetInterface
    {
        $select = $this->tableGateway->getSql()->select();
        $select->columns([
            'company_id'
        ]);
        $select
            ->where([
                'deleted' => false,
            ])
            ->where
            ->isNotNull('aws_s3_report_bucket_region')
            ->and
            ->isNotNull('aws_s3_report_bucket_name');

        return $this->tableGateway->selectWith($select);
    }

    /**
     *
     * @param int $companyId
     * @return ResultSetInterface
     * @throws NotFoundApiException
     */
    public function getCompany(int $companyId, ?int $userId = null): ResultSetInterface
    {
        $select = $this->tableGateway->getSql()->select();
        $select->where([
            'companies.company_id' => $companyId,
            'companies.deleted' => false,
        ]);
        if (!is_null($userId)) {
            $select
                ->join(
                    [
                        'ucr' => 'users_companies_roles',
                    ],
                    'ucr.company_id = companies.company_id',
                    [],
                    Select::JOIN_INNER
                )
                ->where([
                    'ucr.user_id' => $userId,
                ]);
        }
        $result = $this->tableGateway->selectWith($select);
        if ($result->count() === 0) {
            throw new NotFoundApiException('Company not found');
        }
        return $result;
    }

    /**
     *
     * @param string $companyToken
     * @return ResultSetInterface
     * @throws NotFoundApiException
     */
    public function getCompanyByApiToken(string $companyToken): ResultSetInterface
    {
        $select = $this->tableGateway->getSql()->select();
        $select
            ->join(
                [
                    'aa' => 'api_applications',
                ],
                'aa.company_id = companies.company_id',
                [],
                Select::JOIN_INNER
            )
            ->where([
                'aa.application_token' => $companyToken,
                'companies.deleted' => false,
            ]);
        $result = $this->tableGateway->selectWith($select);
        if ($result->count() === 0) {
            throw new NotFoundApiException('Company not found');
        }
        return $result;
    }

    /**
     *
     * @param int $userId
     * @return ResultSetInterface
     */
    public function getCompaniesByUserId(int $userId): ResultSetInterface
    {
        $select = $this->tableGateway->getSql()->select();
        $select
            ->columns([
                'company_id',
                'company_name',
                'aws_s3_bucket_region',
                'aws_s3_bucket_name',
                'aws_s3_bucket_dir',
                'deleted',
                'threshold_bar',
                'paid_transcribing_time',
                'min_score',
                'max_score',
                'base_score',
                'min_call_duration_for_auto_analyze',
                'min_call_duration_for_call_summary',
                'is_checklists_enabled',
                'is_summarization_enabled',
                'is_client_summary_enabled',
                'is_export_enabled',
                'llm_events_limit',
                'is_manage_llm_events_by_users_enabled',
                'front_id',
                'status',
                'created_at',
            ])
            ->join(
                [
                    'ucr' => 'users_companies_roles',
                ],
                'ucr.company_id = companies.company_id',
                []
            )
            ->join(
                [
                    'bs' => 'billing_settings',
                ],
                'bs.company_id = companies.company_id',
                [
                    'trial_duration' => 'duration',
                ],
                Select::JOIN_LEFT
            )
            ->where([
                'ucr.user_id' => $userId,
                'companies.deleted' => false,
            ]);

        return $this->tableGateway->selectWith($select);
    }

    /**
     *
     * @param Company $company
     * @return int
     */
    public function saveCompany(Company $company): int
    {
        $data = [
            'company_name' => $company->getName(),
            'threshold_bar' => $company->getThresholdBar(),
            'company_avatar' => $company->getAvatar(),
            'aws_s3_bucket_region' => $company->getAwsS3BucketRegion(),
            'aws_s3_bucket_name' => $company->getAwsS3BucketName(),
            'aws_s3_bucket_dir' => $company->getAwsS3BucketDir(),
            'paid_transcribing_time' => $company->getPaidTranscribingTime(),
            'min_call_duration_for_auto_analyze' => $company->getMinCallDurationForAutoAnalyze(),
            'min_call_duration_for_call_summary' => $company->getMinCallDurationForCallSummary(),
            'front_id' => $company->getFrontId(),
            'last_export_date' => $company->getLastExportDate(),
            'is_checklists_enabled' => $company->isChecklistsEnabled(),
            'is_summarization_enabled' => $company->isSummarizationEnabled(),
            'status' => $company->getStatus(),
            'created_at' => $company->getCreatedAt(),
        ];

        if (!is_null($company->isDeleted())) {
            $data['deleted'] = $company->isDeleted();
        }

        if ($company->getId() > 0) {
            $this->tableGateway->update($data, [
                'company_id' => $company->getId(),
            ]);
        } else {
            $this->tableGateway->insert($data);
            $company->setId((int) $this->tableGateway->lastInsertValue);
        }

        return $company->getId();
    }

    /**
     *
     * @param int $companyId
     * @return int
     */
    public function deleteCompany(int $companyId): int
    {
        return $this->tableGateway->update([
            'deleted' => true,
        ], [
            'company_id' => $companyId,
        ]);
    }

    public function getCompaniesIdsToExport(): array
    {
        $select = $this->tableGateway->getSql()->select();
        $select
            ->columns([
                'company_id',
            ])
            ->where([
                'deleted' => 0,
                'is_export_enabled' => 1,
            ]);

        return array_column($this->tableGateway->selectWith($select)->toArray(), 'company_id');
    }

    public function partialUpdateCompany(int $companyId, array $data): void
    {
        $this->tableGateway->update($data, [
            'company_id' => $companyId,
        ]);
    }

    /**
     * @throws ReflectionException
     */
    public function getTrialCompaniesThatExpired(): CompanyCollection
    {
        $today = new Carbon();

        $select = $this->getTrialCompaniesWithBillingSettingsQuery();
        $select
            ->where(
                new Expression(
                    'DATE(DATE_ADD(companies.created_at, INTERVAL bs.duration DAY)) <= DATE(?)',
                    $today->format('Y-m-d')
                )
            );

        $result = $this->tableGateway->selectWith($select);

        $companyCollection = new CompanyCollection();

        if ($result->count() === 0) {
            return $companyCollection;
        }

        foreach ($result as $companyData) {
            $companyCollection->add($this->hydrate((array) $companyData, Company::class));
        }

        return $companyCollection;
    }

    /**
     * @throws ReflectionException
     */
    public function getTrialCompaniesThatExpiresIn3Days(): CompanyCollection
    {
        $threeDaysAhead = (new Carbon())->addDays(3);

        $select = $this->getTrialCompaniesWithBillingSettingsQuery();
        $select
            ->where(
                new Expression(
                    'DATE(DATE_ADD(companies.created_at, INTERVAL bs.duration DAY)) = DATE(?)',
                    $threeDaysAhead->format('Y-m-d')
                )
            );

        $result = $this->tableGateway->selectWith($select);

        $companyCollection = new CompanyCollection();

        if ($result->count() === 0) {
            return $companyCollection;
        }

        foreach ($result as $companyData) {
            $companyCollection->add($this->hydrate((array) $companyData, Company::class));
        }

        return $companyCollection;
    }

    private function getTrialCompaniesWithBillingSettingsQuery(): Select
    {
        return $this->tableGateway->getSql()
            ->select()
            ->join(
                [
                    'bs' => 'billing_settings',
                ],
                'bs.company_id = companies.company_id'
            )
            ->where([
                'companies.status' => Company::STATUS_TRIAL,
            ]);
    }

    public function setTrialExpiredStatus(array $companyIds): void
    {
        if (empty($companyIds)) {
            return;
        }

        $data = [
            'status' => Company::STATUS_TRIAL_EXPIRED,
        ];
        $this->tableGateway->update($data, [
            'company_id' => $companyIds,
        ]);
    }

    public function getCallDownloadRateLimiterSettings()
    {
        $select = $this->tableGateway->getSql()->select();
        $select
            ->columns([
                'company_id',
                'call_file_download_rate_limiter',
            ])
            ->where([
                'deleted' => 0,
            ])
            ->where
            ->isNotNull('call_file_download_rate_limiter');
        ;
        $rateLimiterSettingsData = array_column(
            $this->tableGateway->selectWith($select)->toArray(),
            'call_file_download_rate_limiter',
            'company_id',
        );


        $settingsMap = [];
        foreach ($rateLimiterSettingsData as $key => $value) {
            $settings = explode('|', $value);
            $settingsMap[$key] = [
                'id' => 'call-download-' . $key,
                'policy' => $settings[0],
                'limit' => (int) $settings[1],
                'interval' => $settings[2],
            ];
        }

        return $settingsMap;
    }

    /**
     * @throws ReflectionException
     */
    public function getCompaniesWithTrialInPeriod(Carbon $startDate, Carbon $endDate): CompanyCollection
    {
        $select = $this->tableGateway->getSql()
            ->select()
            ->join(
                [
                    'bs' => 'billing_settings',
                ],
                'bs.company_id = companies.company_id'
            );
        $select
            ->where([
                // Trial ends after checking period starts
                new Expression(
                    'DATE(DATE_ADD(companies.created_at, INTERVAL bs.duration DAY)) >= DATE(?)',
                    $startDate->format('Y-m-d')
                ),
                // And company was created before checking period ends
                new Expression('companies.created_at <= DATE(?)', $endDate->format('Y-m-d')),
            ]);

        $result = $this->tableGateway->selectWith($select);

        $companyCollection = new CompanyCollection();

        if ($result->count() === 0) {
            return $companyCollection;
        }

        foreach ($result as $companyData) {
            $companyCollection->add($this->hydrate((array) $companyData, Company::class, true));
        }

        return $companyCollection;
    }
}
