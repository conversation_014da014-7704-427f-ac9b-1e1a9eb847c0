<?php

namespace STCompany\Data;

use Laminas\Db\Sql\Select;
use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Entity\Industry\Industry;
use STCompany\Entity\Industry\IndustryCollection;
use STLib\Db\HydratedAbstractTable;
use STLib\Mvc\Data\CollectionResultSet;

class CompanyIndustriesTable extends HydratedAbstractTable
{
    public static string $table = 'companies_industries';
    public static string $entityName = Industry::class;
    public static string $collectionName = IndustryCollection::class;

    public static function getTableName(): string
    {
        return self::$table;
    }

    public static function getEntityName(): string
    {
        return self::$entityName;
    }

    public static function getCollectionName(): string
    {
        return self::$collectionName;
    }

    public function getIndustries(int $companyId): IndustryCollection
    {
        $select = $this->tableGateway->getSql()
            ->select()
            ->columns(['company_id']);
        $this->joinIndustries($select);
        $select
            ->where([
                self::$table . '.company_id' => $companyId,
            ]);

        /**
         * @var CollectionResultSet $result
         */
        $result = $this->tableGateway->selectWith($select);

        return $result->getCollection();
    }

    /**
     * @throws NotFoundApiException
     */
    public function getIndustry(int $industryId, int $companyId): Industry
    {
        $select = $this->tableGateway->getSql()
            ->select()
            ->columns(['company_id']);
        $this->joinIndustries($select);
        $select
            ->where([
                self::$table . '.company_id' => $companyId,
                'i.industry_id' => $industryId
            ]);

        $result = $this->tableGateway->selectWith($select);
        if ($result->count() === 0) {
            throw new NotFoundApiException('Company industry not found');
        }

        return $result->current();
    }

    public function saveIndustry(Industry $industry): void
    {
        $data = [
            'industry_id' => $industry->getId(),
            'company_id' => $industry->getCompanyId(),
        ];

        $this->tableGateway->insert($data);
    }

    public function deleteIndustry(int $industryId, int $companyId): void
    {
        $this->tableGateway->delete([
            'industry_id' => $industryId,
            'company_id' => $companyId,
        ]);
    }

    private function joinIndustries(Select $select): void
    {
        $select->join(
            [
                'i' => 'industries',
            ],
            'i.industry_id = ' . self::$table . '.industry_id'
        );
    }
}
