<?php

declare(strict_types=1);

namespace STCompany\Data;

use Lam<PERSON>\Db\Sql\Select;
use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Entity\Event\Event;
use STCompany\Entity\Event\EventCollection;
use STLib\Db\HydratedAbstractTable;
use STLib\Expand\Collection;
use STLib\Mvc\Data\CollectionResultSet;

class EventsTable extends HydratedAbstractTable
{
    public static string $table = 'events';
    public static string $entityName = Event::class;
    public static string $collectionName = EventCollection::class;

    public static function getTableName(): string
    {
        return self::$table;
    }

    public static function getEntityName(): string
    {
        return self::$entityName;
    }

    public static function getCollectionName(): string
    {
        return self::$collectionName;
    }

    /**
     * @param int $companyId
     * @param array|int|null $categoryId
     * @param int|null $roleId
     * @return EventCollection
     */
    public function getEvents(int $companyId, null|array|int $categoryId = null, ?int $roleId = null): Collection
    {
        $categoryIds = is_array($categoryId) ? $categoryId : array_filter([$categoryId]);
        $select = $this->tableGateway->getSql()->select();
        $select
                ->join(
                    [
                        'ec' => 'events_categories',
                    ],
                    'ec.category_id = events.category_id',
                    [
                        'color_id',
                    ],
                    Select::JOIN_LEFT,
                )
                ->join(
                    [
                        'r' => 'roles',
                    ],
                    'r.role_id = events.role_id',
                    [],
                    Select::JOIN_INNER,
                );
        $where = [
            'r.company_id' => $companyId,
        ];
        if (!empty($categoryIds)) {
            $where['events.category_id'] = $categoryIds;
        }
        if (is_int($roleId)) {
            $where['events.role_id'] = $roleId;
        }
        $select->where($where);

        /**
         * @var CollectionResultSet $result
         */
        $result = $this->tableGateway->selectWith($select);

        return $result->getCollection();
    }

    /**
     * @param int $companyId
     * @param int $eventId
     * @return Event
     * @throws NotFoundApiException
     */
    public function getEvent(int $companyId, int $eventId): Event
    {
        $select = $this->tableGateway->getSql()->select();
        $select
                ->join(
                    [
                        'r' => 'roles',
                    ],
                    'r.role_id = events.role_id',
                    [],
                    Select::JOIN_INNER
                )
                ->where([
                    'r.company_id' => $companyId,
                    'events.event_id' => $eventId,
                ]);
        $result = $this->tableGateway->selectWith($select);
        if ($result->count() === 0) {
            throw new NotFoundApiException('Event not found');
        }

        return $result->current();
    }

    /**
     *
     * @param Event $event
     * @return int
     */
    public function saveEvent(Event $event): int
    {
        $data = [
            'event_name' => $event->getName(),
            'icon' => $event->getIcon(),
            'role_id' => $event->getRole()->getId(),
            'category_id' => $event->getCategoryId(),
            'is_confirm_needed' => $event->isConfirmNeeded(),
            'is_pinned' => $event->isPinned(),
            'score' => $event->getScore(),
        ];

        if ($event->getId() > 0) {
            $this->tableGateway->update($data, [
                'event_id' => $event->getId(),
            ]);
        } else {
            $this->tableGateway->insert($data);
            $event->setId((int)$this->tableGateway->lastInsertValue);
        }

        return $event->getId();
    }

    /**
     *
     * @param int $eventId
     * @return int
     */
    public function deleteEvent(int $eventId): int
    {
        return $this->tableGateway->delete([
            'event_id' => $eventId,
        ]);
    }
}
