<?php

declare(strict_types=1);

namespace STCompany\Data;

use <PERSON>inas\Db\ResultSet\ResultSetInterface;
use STLib\Db\AbstractTable;

class UsersNotificationsTable extends AbstractTable
{
    /**
     *
     * @param int $companyId
     * @param int $userId
     * @return ResultSetInterface
     */
    public function getNotifications(int $companyId, int $userId): ResultSetInterface
    {
        return $this->tableGateway->select([
            'company_id' => $companyId,
            'user_id' => $userId,
        ]);
    }

    /**
     *
     * @param int $companyId
     * @param string $notificationType
     * @return ResultSetInterface
     */
    public function getNotificationsByNotificationType(int $companyId, string $notificationType): ResultSetInterface
    {
        $select = $this->tableGateway->getSql()->select();
        $select
                ->join(
                    [
                            'u' => 'users',
                        ],
                    'u.user_id = users_notifications.user_id',
                    [
                            'user_name',
                            'user_email',
                        ],
                    \Laminas\Db\Sql\Select::JOIN_INNER,
                )
                ->join(
                    [
                            'ucr' => 'users_companies_roles',
                        ],
                    'u.user_id = ucr.user_id',
                    [],
                    \Laminas\Db\Sql\Select::JOIN_INNER,
                )
                ->where([
                    'notification_type' => $notificationType,
                    'ucr.company_id' => $companyId,
                    'users_notifications.company_id' => $companyId,
                ]);
        return $this->tableGateway->selectWith($select);
    }

    /**
     *
     * @param int $companyId
     * @param int $userId
     * @param \STCompany\Entity\Notification\UserNotificationCollection $notificationCollection
     * @return int
     */
    public function insertNotifications(int $companyId, int $userId, \STCompany\Entity\Notification\UserNotificationCollection $notificationCollection): int
    {
        $data = [];
        foreach ($notificationCollection as $notification) {
            $data[] = [
                'user_id' => $userId,
                'company_id' => $companyId,
                'notification_type' => $notification->getNotificationType(),
                'delivery_ways' => json_encode($notification->getDeliveryWays()),
            ];
        }
        return $this->multiInsert($data);
    }

    /**
     *
     * @param int $companyId
     * @param int $userId
     * @return int
     */
    public function deleteNotifications(int $companyId, int $userId): int
    {
        return $this->tableGateway->delete([
            'user_id' => $userId,
            'company_id' => $companyId,
        ]);
    }
}
