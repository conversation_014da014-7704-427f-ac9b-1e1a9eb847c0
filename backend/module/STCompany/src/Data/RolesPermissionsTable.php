<?php

declare(strict_types=1);

namespace STCompany\Data;

class RolesPermissionsTable extends \STLib\Db\AbstractTable
{
    /**
     *
     * @param int|array $rolesIds
     * @return \Laminas\Db\ResultSet\ResultSet
     */
    public function getPermissionsByRoleIds(int|array $rolesIds): \Laminas\Db\ResultSet\ResultSet
    {
        $select = $this->tableGateway->getSql()->select();
        $select->join(
            [
                'p' => 'permissions',
            ],
            'roles_permissions.permission_id = p.permission_id',
            [
                'permission_id',
                'parent_permission_id',
                'permission_name',
                'system_name',
                'is_read_permission_only',
                'is_write_permission_only',
            ],
            \Laminas\Db\Sql\Select::JOIN_INNER
        )
        ->where([
            'roles_permissions.role_id' => $rolesIds,
        ]);

        $result = $this->tableGateway->selectWith($select);
        return $result;
    }

    /**
     *
     * @param \STCompany\Entity\Role $role
     * @return int
     */
    public function savePermissions(\STCompany\Entity\Role $role): int
    {
        $data = [];
        foreach ($role->getPermissions() as $permission) {
            $data[] = [
                'role_id' => $role->getId(),
                'permission_id' => $permission->getId(),
                'access_level' => $permission->getAccessLevel(),
            ];
        }
        return $this->multiInsert($data, false);
    }

    /**
     *
     * @param int $roleId
     * @return int
     */
    public function deletePermissions(int $roleId): int
    {
        return $this->tableGateway->delete([
            'role_id' => $roleId,
        ]);
    }
}
