<?php

declare(strict_types=1);

namespace STCompany\Data;

class PrecalculatedAgentsTable extends \STClickhouse\Entity\BaseTable
{
    use \STClickhouse\Data\QueriesTrait;
    use \STClickhouse\Data\CallsWhereCondition;

    /**
     * @param int $companyId
     * @param array|int $agentIds
     * @param \Carbon\Carbon|null $startDate
     * @param \Carbon\Carbon|null $endDate
     * @param int|array|null $roleId
     * @return void
     */
    public function precalculateAgents(
        int $companyId,
        array|int $agentIds,
        \Carbon\Carbon $startDate = null,
        \Carbon\Carbon $endDate = null,
        int|array $roleId = null
    ): void {
        $sql = '
            INSERT INTO
                precalculated_agents
                (
                    date,
                    company_id,
                    role_id,
                    agent_id,
                    event_ids,
                    event_category_ids,
                    total_calls_duration,
                    total_calls_count,
                    effective_calls_duration,
                    not_effective_calls_duration,
                    effective_calls_count,
                    not_effective_calls_count,
                    reviewed_calls_count,
                    total_chats_count,
                    reviewed_chats_count,
                    score,
                    unique_clients
                )
            SELECT
                ab.date,
                ab.company_id,
                ab.role_id,
                ab.agent_id,
                ae.event_ids,
                ae.event_category_ids,
                ab.total_calls_duration,
                ab.total_calls_count,
                ab.effective_calls_duration,
                ab.not_effective_calls_duration,
                ab.effective_calls_count,
                ab.not_effective_calls_count,
                ar.reviewed_calls_count,
                ab.total_chats_count,
                ar.reviewed_chats_count,
                ar.score,
                ab.unique_clients
            FROM
                (
                    ' . $this->getAgentBasicStatisticsSql($companyId, $agentIds, $startDate, $endDate, $roleId) . '
                ) ab
            LEFT JOIN
                (
                    ' . $this->getAgentReviewStatisticsSql($companyId, $agentIds, $startDate, $endDate, $roleId) . '
                ) ar
                ON ar.date = ab.date
                AND ar.company_id = ab.company_id
                AND ar.agent_id = ab.agent_id
                AND ar.role_id = ab.role_id
            LEFT JOIN
                (
                    ' . $this->getAgentEventsStatisticsSql($companyId, $agentIds, $startDate, $endDate, $roleId) . '
                ) ae
                ON ae.date = ab.date
                AND ae.company_id = ab.company_id
                AND ae.agent_id = ab.agent_id
                AND ae.role_id = ab.role_id
        ';
        $this->getClient()->getConnection()->write($sql);
    }

    /**
     * @param int $companyId
     * @param array|int $agentIds
     * @param \Carbon\Carbon|null $startDate
     * @param \Carbon\Carbon|null $endDate
     * @param int|array|null $roleId
     * @return string
     */
    protected function getAgentBasicStatisticsSql(
        int $companyId,
        array|int $agentIds,
        \Carbon\Carbon $startDate = null,
        \Carbon\Carbon $endDate = null,
        int|array $roleId = null
    ): string {
        $sql = '
            SELECT
                formatDateTime(c.call_time, \'%Y-%m-%d\') date,
                c.company_id company_id,
                c.agent_id agent_id,
                r.role_id role_id,
                sum(c.call_duration) total_calls_duration,
                countIf(c.call_type = \'call\') total_calls_count,
                countIf(c.call_type = \'chat-call\') total_chats_count,
                sumIf(c.call_duration, c.call_duration >= r.effective_call_threshold_bar) effective_calls_duration,
                sumIf(c.call_duration, c.call_duration < r.effective_call_threshold_bar) not_effective_calls_duration,
                countIf(c.call_duration >= r.effective_call_threshold_bar) effective_calls_count,
                countIf(c.call_duration < r.effective_call_threshold_bar) not_effective_calls_count,
                arrayReduce(\'groupUniqArray\', groupArray(c.client_id)) unique_clients
            FROM
                (
                    ' .
                    $this->getFinalTableSqlUsingGroupBy(
                        'calls',
                        [
                            'company_id',
                            'call_id',
                        ],
                        'created',
                        [
                            'agent_id',
                            'client_id',
                            'call_duration',
                            'call_time',
                            'call_type',
                            'is_deleted',
                        ],
                        $this->getCallsWhereConditionsFromMainParams($companyId, $startDate, $endDate, agentIds: $agentIds)
                    ) . '
                ) c
            INNER JOIN
                (
                    SELECT
                        *
                    FROM
                        dictionary(roles)';
        if (!is_null($roleId)) {
            $roleIds = is_int($roleId) ? [$roleId] : $roleId;
            $sql .= '
                    WHERE
                        role_id IN (' . implode(',', $roleIds) . ')
            ';
        }
        $sql .= '
                ) r
                ON r.company_id = c.company_id
            WHERE
                c.is_deleted = 0
            GROUP BY
                date,
                c.company_id,
                c.agent_id,
                r.role_id
        ';
        return $sql;
    }

    /**
     * @param int $companyId
     * @param array|int $agentIds
     * @param \Carbon\Carbon|null $startDate
     * @param \Carbon\Carbon|null $endDate
     * @param int|array|null $roleId
     * @return string
     */
    protected function getAgentReviewStatisticsSql(
        int $companyId,
        array|int $agentIds,
        \Carbon\Carbon $startDate = null,
        \Carbon\Carbon $endDate = null,
        int|array $roleId = null
    ): string {
        return '
            SELECT
                formatDateTime(pc.call_time, \'%Y-%m-%d\') date,
                pc.company_id company_id,
                pc.role_id role_id,
                pc.agent_id agent_id,
                countIf(
                    pc.is_reviewed = 1 AND c.call_type = \'' . \STCall\Entity\Call::CALL_TYPE . '\'
                ) reviewed_calls_count,
                countIf(
                    pc.is_reviewed = 1 AND c.call_type = \'' . \STCall\Entity\ChatCall::CHAT_CALL_TYPE . '\'
                ) reviewed_chats_count,
                sum(pc.score) score,
                avg(pc.risk_rank) risk_rank
            FROM
                (
                    ' .
                        $this->getFinalTableSqlUsingGroupBy(
                            'calls',
                            [
                                'company_id',
                                'call_id',
                            ],
                            'created',
                            [
                                'agent_id',
                                'call_type',
                                'is_deleted',
                            ],
                            $this->getCallsWhereConditionsFromMainParams($companyId, $startDate, $endDate, agentIds: $agentIds)
                        ) . '
                ) c
                INNER JOIN
                (
                    ' .
                        $this->getFinalTableSqlUsingGroupBy(
                            'precalculated_calls',
                            [
                                'company_id',
                                'call_id',
                                'role_id',
                            ],
                            'created',
                            [
                                'agent_id',
                                'call_time',
                                'is_reviewed',
                                'score',
                                'risk_rank',
                            ],
                            $this->getCallsWhereConditionsFromMainParams($companyId, $startDate, $endDate, $roleId, agentIds: $agentIds)
                        ) . '
                ) pc
                ON c.call_id = pc.call_id
                AND c.agent_id = pc.agent_id
                AND c.company_id = pc.company_id
            WHERE
                c.is_deleted = 0
            GROUP BY
                date,
                pc.company_id,
                pc.agent_id,
                pc.role_id
        ';
    }

    /**
     * @param int $companyId
     * @param array|int $agentIds
     * @param \Carbon\Carbon|null $startDate
     * @param \Carbon\Carbon|null $endDate
     * @param int|array|null $roleId
     * @return string
     */
    protected function getAgentEventsStatisticsSql(
        int $companyId,
        array|int $agentIds,
        \Carbon\Carbon $startDate = null,
        \Carbon\Carbon $endDate = null,
        int|array $roleId = null
    ): string {
        return '
            SELECT
                formatDateTime(pce.call_time, \'%Y-%m-%d\') date,
                pce.company_id company_id,
                pce.role_id role_id,
                pce.agent_id agent_id,
                arrayReduce(\'groupUniqArray\', groupArray(pce.event_id)) event_ids,
                arrayReduce(\'groupUniqArray\', groupArray(pce.event_category_id)) event_category_ids
            FROM
                (
                    SELECT
                        call_time,
                        company_id,
                        role_id,
                        agent_id,
                        event_id,
                        event_category_id
                    FROM
                        (
                            ' .
                            $this->getFinalTableSqlUsingGroupBy(
                                'precalculated_calls_events',
                                [
                                    'company_id',
                                    'role_id',
                                    'call_id',
                                    'paragraph',
                                    'event_id',
                                ],
                                'created',
                                [
                                    'call_time',
                                    'agent_id',
                                    'event_is_deleted',
                                    'event_category_id',
                                ],
                                $this->getCallsWhereConditionsFromMainParams($companyId, $startDate, $endDate, $roleId, agentIds: $agentIds),
                            ) . '
                        )
                    WHERE
                        event_is_deleted = 0
                        AND call_id IN (
                            SELECT
                                call_id
                            FROM
                            (
                                ' .
                                    $this->getFinalTableSqlUsingGroupBy(
                                        'calls',
                                        [
                                            'company_id',
                                            'call_id',
                                        ],
                                        'created',
                                        [
                                            'is_deleted',
                                        ],
                                        $this->getCallsWhereConditionsFromMainParams($companyId, $startDate, $endDate, agentIds: $agentIds)
                                    )
                                . '
                            )
                            WHERE
                                is_deleted = 0
                        )
                ) pce
            GROUP BY
                date,
                pce.company_id,
                pce.agent_id,
                pce.role_id
        ';
    }
}
