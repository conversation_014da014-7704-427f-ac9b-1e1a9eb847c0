<?php

declare(strict_types=1);

namespace STCompany\Data;

class EventsCategoriesTable extends \STLib\Db\AbstractTable
{
    /**
     *
     * @param int $companyId
     * @param int|null $roleId
     * @return \Laminas\Db\ResultSet\ResultSet
     */
    public function getCategories(int $companyId, ?int $roleId = null): \Laminas\Db\ResultSet\ResultSet
    {
        $select = $this->tableGateway->getSql()->select();
        $select
                ->join(
                    [
                        'r' => 'roles',
                    ],
                    'r.role_id = events_categories.role_id',
                    [],
                    \Laminas\Db\Sql\Select::JOIN_INNER
                );
        $where = [
            'r.company_id' => $companyId,
        ];
        if (is_int($roleId)) {
            $where['r.role_id'] = $roleId;
        }
        $select->where($where);
        return $this->tableGateway->selectWith($select);
    }

    /**
     * @param int $companyId
     * @param int $categoryId
     * @return \Laminas\Db\ResultSet\ResultSet
     */
    public function getCategory(int $companyId, int $categoryId): \Laminas\Db\ResultSet\ResultSet
    {
        $select = $this->tableGateway->getSql()->select();
        $select
                ->join(
                    [
                        'r' => 'roles',
                    ],
                    'r.role_id = events_categories.role_id',
                    [],
                    \Laminas\Db\Sql\Select::JOIN_INNER
                )
                ->where([
                    'r.company_id' => $companyId,
                    'events_categories.category_id' => $categoryId,
                ]);
        $result = $this->tableGateway->selectWith($select);
        if ($result->count() === 0) {
            throw new \STApi\Entity\Exception\NotFoundApiException('Category not found');
        }
        return $this->tableGateway->selectWith($select);
    }

    /**
     *
     * @param \STCompany\Entity\Event\Category $category
     * @return int
     */
    public function saveCategory(\STCompany\Entity\Event\Category $category): int
    {
        $data = [
            'color_id' => $category->getColor()->getId(),
            'role_id' => $category->getRole()->getId(),
            'category_name' => $category->getName(),
        ];

        if ($category->getId() > 0) {
            $this->tableGateway->update($data, [
                'category_id' => $category->getId(),
            ]);
        } else {
            $this->tableGateway->insert($data);
            $category->setId((int)$this->tableGateway->lastInsertValue);
        }

        return $category->getId();
    }

    /**
     *
     * @param array|int $categoryId
     * @return int
     */
    public function deleteCategory(array|int $categoryId): int
    {
        return $this->tableGateway->delete([
            'category_id' => $categoryId,
        ]);
    }
}
