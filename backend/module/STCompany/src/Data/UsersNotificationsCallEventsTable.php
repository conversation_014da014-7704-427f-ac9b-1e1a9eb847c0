<?php

declare(strict_types=1);

namespace STCompany\Data;

use STC<PERSON>pany\Entity\Role;
use STLib\Db\AbstractTable;

class UsersNotificationsCallEventsTable extends AbstractTable
{
    /**
     *
     * @param int $userId
     * @return \Laminas\Db\ResultSet\ResultSetInterface
     */
    public function getNotificationCallEvents(int $userId): \Laminas\Db\ResultSet\ResultSetInterface
    {
        return $this->tableGateway->select([
            'user_id' => $userId,
        ]);
    }

    /**
     *
     * @param int $companyId
     * @param array $eventIds
     * @return \Laminas\Db\ResultSet\ResultSetInterface
     */
    public function getNotificationCallEventsByEventIds(int $companyId, array $eventIds): \Laminas\Db\ResultSet\ResultSetInterface
    {
        $select = $this->tableGateway->getSql()->select();
        $select
                ->join(
                    [
                        'u' => 'users',
                    ],
                    'u.user_id = users_notifications_call_events.user_id',
                    [],
                    \Laminas\Db\Sql\Select::JOIN_INNER
                )
                ->join(
                    [
                        'e' => 'events',
                    ],
                    'e.event_id = users_notifications_call_events.event_id',
                    [],
                    \Laminas\Db\Sql\Select::JOIN_INNER
                )
                ->join(
                    [
                        'ucr' => 'users_companies_roles',
                    ],
                    'ucr.user_id = u.user_id',
                    [],
                    \Laminas\Db\Sql\Select::JOIN_INNER
                )
                ->join(
                    [
                        'user_r' => 'roles',
                    ],
                    'user_r.role_id = ucr.role_id',
                    [],
                    \Laminas\Db\Sql\Select::JOIN_INNER
                )
                ->join(
                    [
                        'event_r' => 'roles',
                    ],
                    'event_r.role_id = e.role_id',
                    [],
                    \Laminas\Db\Sql\Select::JOIN_INNER
                )
                ->join(
                    [
                        'rp' => 'roles_permissions',
                    ],
                    new \Laminas\Db\Sql\Expression('rp.role_id = user_r.role_id AND rp.permission_id = ' . PermissionsTable::COMPANY_VIEW_LIKE_ANOTHER_ROLE),
                    [],
                    \Laminas\Db\Sql\Select::JOIN_LEFT
                )
                ->where
                ->equalTo('ucr.company_id', $companyId)
                ->AND
                ->in('users_notifications_call_events.event_id', $eventIds)
                ->AND
                ->equalTo('event_r.company_id', $companyId)
                ->AND
                ->nest()
                ->equalTo('e.role_id', 'user_r.role_id')
                ->OR
                ->equalTo('rp.permission_id', PermissionsTable::COMPANY_VIEW_LIKE_ANOTHER_ROLE)
                ->OR
                ->in('user_r.role_type', [
                    Role::ADMIN_ROLE_TYPE,
                    Role::COMPANY_ADMIN_ROLE_TYPE,
                ])
                ->unnest();
        return $this->tableGateway->selectWith($select);
    }

    /**
     *
     * @param int $userId
     * @param array $eventIds
     * @return void
     */
    public function insertNotificationCallEvents(int $userId, array $eventIds): void
    {
        $data = [];
        foreach ($eventIds as $eventId) {
            $data[] = [
                'user_id' => $userId,
                'event_id' => $eventId,
            ];
        }
        $this->multiInsert($data);
    }

    /**
     *
     * @param int $userId
     * @return void
     */
    public function deleteNotificationCallEvents(int $userId): void
    {
        $this->tableGateway->delete([
            'user_id' => $userId,
        ]);
    }
}
