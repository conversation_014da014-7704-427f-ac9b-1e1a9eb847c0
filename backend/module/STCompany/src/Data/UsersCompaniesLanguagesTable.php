<?php

declare(strict_types=1);

namespace STCompany\Data;

class UsersCompaniesLanguagesTable extends \STLib\Db\AbstractTable
{
    /**
     *
     * @param int $companyId
     * @param array $teamIds
     * @return \Laminas\Db\ResultSet\ResultSet
     */
    public function getUsersLanguages(int $companyId, array $teamIds = []): \Laminas\Db\ResultSet\ResultSet
    {
        $select = $this->tableGateway->getSql()->select();
        $select
                ->where([
                    'users_companies_languages.company_id' => $companyId,
                ])
                ->order('users_companies_languages.is_default DESC');
        if (count($teamIds) > 0) {
            $select
                ->quantifier('DISTINCT')
                ->join(
                    [
                        'ut' => 'users_teams',
                    ],
                    'ut.user_id = users_companies_languages.user_id',
                    [],
                    \Laminas\Db\Sql\Select::JOIN_INNER
                )
                ->where([
                    'ut.team_id' => $teamIds,
                ]);
        }
        $result = $this->tableGateway->selectWith($select);
        return $result;
    }

    /**
     *
     * @param int $companyId
     * @param int $userId
     * @return \Laminas\Db\ResultSet\ResultSet
     */
    public function getUserLanguages(int $companyId, int $userId): \Laminas\Db\ResultSet\ResultSet
    {
        $select = $this->tableGateway->getSql()->select();
        $select
                ->where([
                    'company_id' => $companyId,
                    'user_id' => $userId,
                ])
                ->order('is_default DESC');
        return $this->tableGateway->selectWith($select);
    }

    /**
     *
     * @param int $companyId
     * @param \STCompany\Entity\User $user
     * @return int
     */
    public function saveUserLanguages(int $companyId, \STCompany\Entity\User $user): int
    {
        $data = [];
        // first language is_default is equal 1, others are equal 0
        $isDefaultLanguage = true;
        foreach ($user->getLanguages() as $language) {
            $data[] = [
                'user_id' => $user->getId(),
                'company_id' => $companyId,
                'language' => $language,
                'is_default' => $isDefaultLanguage,
            ];
            $isDefaultLanguage = false;
        }
        return $this->multiInsert($data, useTransaction: false);
    }

    /**
     *
     * @param int $companyId
     * @param int $userId
     * @return int
     */
    public function deleteUserLanguages(int $companyId, int $userId): int
    {
        return $this->tableGateway->delete([
            'user_id' => $userId,
            'company_id' => $companyId
        ]);
    }
}
