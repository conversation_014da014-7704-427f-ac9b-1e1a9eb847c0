<?php

declare(strict_types=1);

namespace STCompany\Data;

use Laminas\Db\ResultSet\ResultSet;
use STApi\Entity\Exception\NotFoundApiException;

class TeamsTable extends \STLib\Db\AbstractTable
{
    /**
     *
     * @param int $companyId
     * @param int|null $userId
     * @return ResultSet
     */
    public function getTeams(int $companyId, ?int $userId = null): ResultSet
    {
        $select = $this->tableGateway->getSql()->select();
        $select
                ->where([
                    'company_id' => $companyId,
                ]);
        if (!is_null($userId)) {
            $select
                    ->join(
                        [
                            'ut' => 'users_teams',
                        ],
                        'ut.team_id = teams.team_id',
                        [],
                        \Laminas\Db\Sql\Select::JOIN_INNER
                    )
                    ->where([
                        'ut.user_id' => $userId,
                    ]);
        }
        $result = $this->tableGateway->selectWith($select);
        return $result;
    }


    /**
     *
     * @param int $companyId
     * @param array $teamIds
     * @return ResultSet
     */
    public function getTeamsByIds(int $companyId, array $teamIds): ResultSet
    {
        return $this->tableGateway->select([
            'team_id' => $teamIds,
            'company_id' => $companyId,
        ]);
    }

    /**
     * @param int $companyId
     * @param int $teamId
     * @return ResultSet
     * @throws NotFoundApiException
     */
    public function getTeam(int $companyId, int $teamId): ResultSet
    {
        $result = $this->getTeamsByIds($companyId, [
            $teamId,
        ]);
        if ($result->count() === 0) {
            throw new NotFoundApiException('Team not found');
        }
        return $result;
    }

    /**
     *
     * @param int $companyId
     * @param string $teamName
     * @return ResultSet
     * @throws NotFoundApiException
     */
    public function getTeamByName(int $companyId, string $teamName): ResultSet
    {
        $result = $this->tableGateway->select([
            'team_name' => $teamName,
            'company_id' => $companyId,
        ]);
        if ($result->count() === 0) {
            throw new NotFoundApiException('Team not found');
        }
        return $result;
    }

    /**
     *
     * @param int $companyId
     * @param \STCall\Entity\Import\Result\Team $team
     * @return ResultSet
     */
    public function getTeamIdByCompanyTeam(int $companyId, \STCall\Entity\Import\Result\Team $team): ResultSet
    {
        return $this->tableGateway->select([
            'company_id' => $companyId,
            'company_team_id' => $team->getId(),
        ]);
    }

    /**
     *
     * @param int $companyId
     * @param array $teamIds
     * @return Resultset
     */
    public function getTeamsHierarchy(int $companyId, array $teamIds = []): Resultset
    {
        $select = $this->tableGateway->getSql()->select();

        $select
            ->join(
                [
                    'ut' => 'users_teams',
                ],
                'teams.team_id = ut.team_id',
                [],
                \Laminas\Db\Sql\Select::JOIN_LEFT
            )
            ->join(
                [
                    'u' => 'users',
                ],
                'ut.user_id = u.user_id',
                [
                    '*'
                ],
                \Laminas\Db\Sql\Select::JOIN_LEFT
            )
            ->join(
                [
                    'ucr' => 'users_companies_roles',
                ],
                'u.user_id = ucr.user_id',
                [
                    'role_id',
                    'is_active',
                    'company_user_id',
                    'is_auto_analyze_calls',
                ],
                \Laminas\Db\Sql\Select::JOIN_INNER
            )
            ->join(
                [
                    'r' => 'roles',
                ],
                'ucr.role_id = r.role_id',
                [
                    'role_name',
                    'role_type',
                ],
                \Laminas\Db\Sql\Select::JOIN_INNER
            )
            ->order('ucr.is_active DESC')
            ->order('teams.team_id DESC')
            ->where([
                'teams.company_id' => $companyId,
            ])
            ->where
            ->nest()
            ->isNull('r.company_id')
            ->OR
            ->equalTo('r.company_id', $companyId)
            ->unnest();
        if (count($teamIds) > 0) {
            $select->where([
                'teams.team_id' => $teamIds,
            ]);
        }

        $result = $this->tableGateway->selectWith($select);
        return $result;
    }

    /**
     *
     * @param int $companyId
     * @param int $user1Id
     * @param int $user2Id
     * @return ResultSet
     */
    public function getUsersEqualTeams(int $companyId, int $user1Id, int $user2Id): ResultSet
    {
        $select = $this->tableGateway->getSql()->select();
        $select
                ->columns([
                    'team_id',
                ])
                ->join(
                    [
                        'ut' => 'users_teams',
                    ],
                    'teams.team_id = ut.team_id',
                    [],
                    \Laminas\Db\Sql\Select::JOIN_INNER
                )
                ->join(
                    [
                        'u' => 'users',
                    ],
                    'ut.user_id = u.user_id',
                    [],
                    \Laminas\Db\Sql\Select::JOIN_INNER
                )
                ->where([
                    'teams.company_id' => $companyId,
                ]);
        $subSelect = clone $select;
        $subSelect->where([
            'u.user_id' => $user2Id,
        ]);
        $select
            ->where([
                'u.user_id' => $user1Id,
            ])
            ->where
            ->in('teams.team_id', $subSelect);
        $result = $this->tableGateway->selectWith($select);
        return $result;
    }

    /**
     *
     * @param \STCompany\Entity\Team $team
     * @param \STCall\Entity\Import\Result\Team|null $resultTeam
     * @return int
     */
    public function saveTeam(\STCompany\Entity\Team $team, ?\STCall\Entity\Import\Result\Team $resultTeam = null): int
    {
        $data = [
            'team_name' => $team->getName(),
            'company_id' => $team->getCompanyId(),
        ];
        if ($resultTeam instanceof \STCall\Entity\Import\Result\Team && !empty($resultTeam->getId())) {
            $data['company_team_id'] = $team->getCompanyId();
        }

        if ($team->getId() > 0) {
            $this->tableGateway->update($data, [
                'team_id' => $team->getId(),
            ]);
        } else {
            $this->tableGateway->insert($data);
            $team->setId((int)$this->tableGateway->lastInsertValue);
        }

        return $team->getId();
    }

    /**
     *
     * @param int $companyId
     * @param int $teamId
     * @return int
     */
    public function deleteTeam(int $companyId, int $teamId): int
    {
        return $this->tableGateway->delete([
            'team_id' => $teamId,
            'company_id' => $companyId,
        ]);
    }

    /**
     *
     * @param int $companyId
     * @param array $userIds
     * @param type $teamIds
     * @return ResultSet
     */
    public function getUsersTeams(int $companyId, array $userIds = null, $teamIds = []): ResultSet
    {
        $select = $this->tableGateway->getSql()->select();
        $select
                ->join(
                    [
                        'ut' => 'users_teams',
                    ],
                    'ut.team_id = teams.team_id',
                    [
                        'user_id'
                    ],
                    \Laminas\Db\Sql\Select::JOIN_INNER
                )
                ->join(
                    [
                        'ucr' => 'users_companies_roles',
                    ],
                    new \Laminas\Db\Sql\Expression('ucr.user_id = ut.user_id AND ucr.company_id = ' . $companyId),
                    [],
                    \Laminas\Db\Sql\Select::JOIN_INNER
                );

        if (is_array($userIds)) {
            $select
                    ->where([
                        'ut.user_id' => $userIds,
                    ]);
        }
        if (count($teamIds) > 0) {
            $select
                    ->where([
                        'ut.team_id' => $teamIds,
                    ]);
        }
        $select
                ->where([
                    'teams.company_id' => $companyId,
                ]);

        $result = $this->tableGateway->selectWith($select);
        return $result;
    }

    /**
     *
     * @param int $companyId
     * @param int $userId
     * @return ResultSet
     */
    public function getUserTeams(int $companyId, int $userId): ResultSet
    {
        $result = $this->getUsersTeams($companyId, [
            $userId,
        ]);

        return $result;
    }
}
