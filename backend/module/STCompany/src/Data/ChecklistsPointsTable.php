<?php

namespace STCompany\Data;

use Carbon\Carbon;
use Exception;
use Laminas\Db\Sql\Expression;
use Laminas\Db\Sql\Select;
use <PERSON>inas\Db\Sql\Sql;
use STApi\Entity\Exception\NotFoundApiException;
use STLib\Db\HydratedAbstractTable;
use STC<PERSON>pany\Entity\Checklist\ChecklistPoint;
use STCompany\Entity\Checklist\ChecklistPointCollection;

class ChecklistsPointsTable extends HydratedAbstractTable
{
    public static string $table = 'checklists_points';
    public static string $entityName = ChecklistPoint::class;
    public static string $collectionName = ChecklistPointCollection::class;

    public static function getTableName(): string
    {
        return self::$table;
    }

    public static function getEntityName(): string
    {
        return self::$entityName;
    }

    public static function getCollectionName(): string
    {
        return self::$collectionName;
    }

    /**
     * @throws NotFoundApiException
     */
    public function getChecklistPoint(int $checklistPointId, int $companyId): ChecklistPoint
    {
        $select = $this->tableGateway->getSql()->select();
        $select
            ->join(
                [
                    'cl' => 'checklists',
                ],
                'cl.checklist_id = checklists_points.checklist_id',
                [],
                Select::JOIN_INNER
            )
            ->where([
                'cl.company_id' => $companyId,
                'checklists_points.checklist_point_id' => $checklistPointId,
            ])
        ;

        $result = $this->tableGateway->selectWith($select);
        if ($result->count() === 0) {
            throw new NotFoundApiException('Checklist point not found');
        }

        return $result->current();
    }

    public function getChecklistPointByTitleAndChecklistId(string $title, int $checklistId): ?ChecklistPoint
    {
        $select = $this->tableGateway->getSql()->select();
        $select->where(['checklist_id' => $checklistId, 'title' => $title]);

        return $this->tableGateway->selectWith($select)->current();
    }

    /**
     * @param int $checklistId
     * @param int $companyId
     * @return ChecklistPointCollection
     */
    public function getChecklistPointsByChecklistId(int $checklistId, int $companyId): ChecklistPointCollection
    {
        $select = $this->tableGateway->getSql()->select();
        $select
            ->join(
                [
                    'cl' => 'checklists',
                ],
                'cl.checklist_id = checklists_points.checklist_id',
                [],
                Select::JOIN_INNER
            )
            ->where([
                'cl.company_id' => $companyId,
                'checklists_points.checklist_id' => $checklistId,
            ])
        ;

        return $this->tableGateway->selectWith($select)->getCollection();
    }

    /**
     * @param int $companyId
     * @param int|null $checklistId
     * @return Carbon|null
     */
    public function getChecklistPointLastUpdate(int $companyId, ?int $checklistId = null): ?Carbon
    {
        $where = [
            'cl.company_id' => $companyId,
        ];

        if ($checklistId !== null) {
            $where['cl.checklist_id'] = $checklistId;
        }

        $adapter = $this->tableGateway->getAdapter();
        $sql = new Sql($adapter);

        $select = $sql->select();
        $select->from('checklists_points');
        $select->columns([
            'maxUpdatedAt' => new Expression('MAX(updated_at)')
        ]);
        $select
            ->join(
                [
                    'cl' => 'checklists',
                ],
                'cl.checklist_id = checklists_points.checklist_id',
                [],
                Select::JOIN_INNER
            )
            ->where($where)
        ;

        $statement = $sql->prepareStatementForSqlObject($select);
        $result = $statement->execute()->current();

        return $result['maxUpdatedAt'] ? new Carbon($result['maxUpdatedAt']) : null;
    }

    public function saveChecklistPoint(ChecklistPoint $checklistPoint): void
    {
        $data = [
            'checklist_id' => $checklistPoint->getChecklistId(),
            'title' => $checklistPoint->getTitle(),
            'description' => $checklistPoint->getDescription(),
            'expected_actions' => $checklistPoint->getExpectedActions(),
            'good_performance_description' => $checklistPoint->getGoodPerformanceDescription(),
            'good_performance_example' => $checklistPoint->getGoodPerformanceExample(),
            'bad_performance_description' => $checklistPoint->getBadPerformanceDescription(),
            'bad_performance_example' => $checklistPoint->getBadPerformanceExample(),
            'is_optional' => $checklistPoint->isOptional(),
            'trigger_condition' => $checklistPoint->getTriggerCondition(),
            'updated_at' => $checklistPoint->getUpdatedAt()
        ];

        if (!is_null($checklistPoint->getId())) {
            $this->tableGateway->update($data, [
                'checklist_point_id' => $checklistPoint->getId(),
            ]);
        } else {
            $this->tableGateway->insert($data);
            $checklistPoint->setId($this->tableGateway->lastInsertValue);
        }
    }

    public function deleteChecklistPoint(int $checklistPointId): void
    {
        $this->tableGateway->delete(
            [
                'checklist_point_id' => $checklistPointId,
            ]
        );
    }

    /**
     * @throws Exception
     */
    public function updateOrder(array $orderedIds): void
    {
        $caseParts = [];
        $bindParams = [];
        foreach ($orderedIds as $index => $id) {
            $caseParts[] = 'WHEN checklist_point_id = ? THEN ?';
            $bindParams[] = $id;
            $bindParams[] = $index + 1;
        }

        $caseExpr = new Expression('CASE ' . implode(' ', $caseParts) . ' END', $bindParams);

        $this->tableGateway->update(['order' => $caseExpr], [
            'checklist_point_id' => $orderedIds,
        ]);
    }
}
