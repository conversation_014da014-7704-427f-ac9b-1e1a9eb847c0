<?php

declare(strict_types=1);

namespace STCompany\Data;

class CompaniesVocabularyWordsTable extends \STLib\Db\AbstractTable
{
    /**
     *
     * @param int $companyId
     * @return \Laminas\Db\ResultSet\ResultSet
     */
    public function getCompanyVocabulary(int $companyId): \Laminas\Db\ResultSet\ResultSet
    {
        $select = $this->tableGateway->getSql()->select();
        $select->where([
            'company_id' => $companyId,
        ]);

        return $this->tableGateway->selectWith($select);
    }

    /**
     *
     * @param int $companyId
     * @param int $vocabularyWordId
     * @return \Laminas\Db\ResultSet\ResultSet
     * @throws \STApi\Entity\Exception\NotFoundApiException
     */
    public function getCompanyVocabularyWord(int $companyId, int $vocabularyWordId): \Laminas\Db\ResultSet\ResultSet
    {
        $select = $this->tableGateway->getSql()->select();
        $select->where([
            'company_id' => $companyId,
            'word_id' => $vocabularyWordId,
        ]);

        $result = $this->tableGateway->selectWith($select);

        if ($result->count() === 0) {
            throw new \STApi\Entity\Exception\NotFoundApiException('Vocabulary word is not found');
        }

        return $result;
    }

    /**
     *
     * @param \STCompany\Entity\CompanyVocabularyWord $vocabularyWord
     * @return int
     */
    public function saveCompanyVocabularyWord(\STCompany\Entity\CompanyVocabularyWord $vocabularyWord): int
    {
        $data = [
            'word' => $vocabularyWord->getWord(),
            'company_id' => $vocabularyWord->getCompanyId(),
        ];

        if ($vocabularyWord->getId() > 0) {
            $this->tableGateway->update($data, [
                'word_id' => $vocabularyWord->getId(),
            ]);
        } else {
            $this->tableGateway->insert($data);
            $vocabularyWord->setId((int) $this->tableGateway->lastInsertValue);
        }

        return $vocabularyWord->getId();
    }

    /**
     *
     * @param int $companyId
     * @param int $vocabularyWordId
     * @return void
     */
    public function deleteCompanyVocabularyWord(int $companyId, int $vocabularyWordId): void
    {
        $this->tableGateway->delete([
            'company_id' => $companyId,
            'word_id' => $vocabularyWordId,
        ]);
    }
}
