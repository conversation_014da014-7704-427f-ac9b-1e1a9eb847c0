<?php

declare(strict_types=1);

namespace STCompany\Data;

use Laminas\Db\Sql\Expression;
use Laminas\Db\Sql\Select;
use <PERSON><PERSON>\Db\Sql\Sql;
use STA<PERSON>\Entity\Exception\NotFoundApiException;
use ST<PERSON>ib\Db\HydratedAbstractTable;
use STC<PERSON>pany\Entity\Checklist\Checklist;
use STCompany\Entity\Checklist\ChecklistCollection;

class ChecklistsTable extends HydratedAbstractTable
{
    public static string $table = 'checklists';
    public static string $entityName = Checklist::class;
    public static string $collectionName = ChecklistCollection::class;

    public static function getTableName(): string
    {
        return self::$table;
    }

    public static function getEntityName(): string
    {
        return self::$entityName;
    }

    public static function getCollectionName(): string
    {
        return self::$collectionName;
    }

    /**
     * Save a checklist
     *
     * @param Checklist $checklist
     * @return Checklist
     */
    public function saveChecklist(Checklist $checklist): Checklist
    {
        $data = [
            'company_id' => $checklist->getCompanyId(),
            'name' => $checklist->getName(),
            'call_duration_threshold' => $checklist->getCallDurationThreshold(),
            'calls_teams' => json_encode($checklist->getCallsTeams()),
            'calls_scope' => $checklist->getCallsscope(),
            'calls_statuses' => json_encode($checklist->getCallsStatuses()),
            'description' => $checklist->getDescription(),
        ];

        $id = $checklist->getId();

        if ($id === null) {
            $this->tableGateway->insert($data);
            $checklist->setId((int) $this->tableGateway->getLastInsertValue());
        } else {
            $this->tableGateway->update($data, ['checklist_id' => $id]);
        }

        return $checklist;
    }

    public function deleteChecklist(int $checklistId, int $companyId): void
    {
        $this->tableGateway->delete(
            [
                'checklist_id' => $checklistId,
                'company_id' => $companyId,
            ]
        );
    }

    /**
     * @throws NotFoundApiException
     */
    public function getChecklist(int $checklistId, int $companyId): Checklist
    {
        $select = $this->tableGateway->getSql()->select();
        $select->where(['checklist_id' => $checklistId, 'company_id' => $companyId]);

        $result = $this->tableGateway->selectWith($select);

        if ($result->count() === 0) {
            throw new NotFoundApiException('Checklist not found');
        }

        return $result->current();
    }

    /**
     * @param int $companyId
     * @return ChecklistCollection
     */
    public function getCompanyChecklists(int $companyId): ChecklistCollection
    {
        $select = $this->tableGateway->getSql()->select();
        $select->where(['company_id' => $companyId]);

        return $this->tableGateway->selectWith($select)->getCollection();
    }

    public function getCompanyChecklistsSummary(int $companyId): array
    {
        $adapter = $this->tableGateway->getAdapter();
        $sql = new Sql($adapter);

        $select = $sql->select();
        $select->from('checklists');
        $select->columns([
            'checklist_id' => 'checklist_id',
            'name' => 'name',
            'checklists_points_count' => new Expression('count(DISTINCT cp.checklist_point_id)'),
        ]);
        $select->where(['checklists.company_id' => $companyId]);
        $select->join(
            [
                'cp' => 'checklists_points',
            ],
            'cp.checklist_id = checklists.checklist_id',
            [],
            Select::JOIN_LEFT
        );
        $select->group('checklists.checklist_id');

        $statement = $sql->prepareStatementForSqlObject($select);
        $resultSet = $statement->execute();

        return iterator_to_array($resultSet);
    }
}
