<?php

declare(strict_types=1);

namespace STCompany\Data;

class CompaniesLanguagesTable extends \STLib\Db\AbstractTable
{
    /**
     *
     * @param int|array $companyId
     * @return \Laminas\Db\ResultSet\ResultSet
     */
    public function getCompanyLanguages(int|array $companyId): \Laminas\Db\ResultSet\ResultSet
    {
        $select = $this->tableGateway->getSql()->select();
        $select
                ->where([
                    'company_id' => $companyId,
                ])
                ->order('is_default DESC');
        return $this->tableGateway->selectWith($select);
    }

    /**
     *
     * @param \STCompany\Entity\Company $company
     * @return int
     */
    public function saveCompanyLanguages(\STCompany\Entity\Company $company): int
    {
        $data = [];
        // first language is_default is equal 1, others are equal 0
        $isDefaultLanguage = true;
        foreach ($company->getLanguages() as $language) {
            $data[] = [
                'company_id' => $company->getId(),
                'language' => $language,
                'is_default' => $isDefaultLanguage,
            ];
            $isDefaultLanguage = false;
        }
        return $this->multiInsert($data, false);
    }

    /**
     *
     * @param \STCompany\Entity\Company $company
     * @return int
     */
    public function deleteCompanyLanguages(\STCompany\Entity\Company $company): int
    {
        return $this->tableGateway->delete([
            'company_id' => $company->getId(),
        ]);
    }
}
