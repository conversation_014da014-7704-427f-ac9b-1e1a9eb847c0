<?php

declare(strict_types=1);

namespace STCompany\Data;

class CompaniesRatesTable extends \STLib\Db\AbstractTable
{
    /**
     *
     * @param int $companyId
     * @param float $rate
     * @return void
     */
    public function saveCompanyRate(int $companyId, float $rate): void
    {
        $this->insertOrUpdate(
            ['company_id' => $companyId],
            ['rate' => $rate],
        );
    }
}
