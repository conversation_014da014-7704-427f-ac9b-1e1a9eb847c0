<?php

declare(strict_types=1);

namespace STCompany\Data;

use Carbon\Carbon;
use STClickhouse\Data\CallsWhereCondition;
use STClickhouse\Data\QueriesTrait;
use STClickhouse\Entity\BaseTable;

class PrecalculatedClientsTable extends BaseTable
{
    use QueriesTrait;
    use CallsWhereCondition;

    /**
     * @param int $companyId
     * @param array|string $clientIds
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @param int|array|null $roleId
     * @return void
     */
    public function precalculateClients(
        int $companyId,
        array|string $clientIds,
        Carbon $startDate = null,
        Carbon $endDate = null,
        int|array $roleId = null
    ): void {
        $sql = '
            INSERT INTO
                precalculated_clients
                (
                    date,
                    company_id,
                    role_id,
                    client_id,
                    agent_ids,
                    event_ids,
                    event_category_ids,
                    total_calls_duration,
                    effective_calls_duration,
                    not_effective_calls_duration,
                    effective_calls_count,
                    not_effective_calls_count,
                    reviewed_calls_count,
                    reviewed_chats_count,
                    score,
                    risk_rank,
                    last_call_time,
                    client_name,
                    country,
                    status,
                    source,
                    acquisition_date,
                    is_converted,
                    converted_date,
                    last_transaction_date,
                    campaign_id,
                    value
                )
            SELECT
                cb.date,
                cb.company_id,
                cb.role_id,
                cb.client_id,
                cb.agent_ids,
                ce.event_ids,
                ce.event_category_ids,
                cb.total_calls_duration,
                cb.effective_calls_duration,
                cb.not_effective_calls_duration,
                cb.effective_calls_count,
                cb.not_effective_calls_count,
                cr.reviewed_calls_count,
                cr.reviewed_chats_count,
                cr.score,
                cr.risk_rank,
                cb.last_call_time,
                cb.client_name,
                cb.country,
                cb.status,
                cb.source,
                cb.acquisition_date,
                cb.is_converted,
                cb.converted_date,
                cb.last_transaction_date,
                cb.campaign_id,
                cb.value
            FROM
                (
                    ' . $this->getClientBasicStatisticsSql($companyId, $clientIds, $startDate, $endDate, $roleId) . '
                ) cb
            LEFT JOIN
                (
                    ' . $this->getClientReviewStatisticsSql($companyId, $clientIds, $startDate, $endDate, $roleId) . '
                ) cr
                ON cr.date = cb.date
                AND cr.company_id = cb.company_id
                AND cr.client_id = cb.client_id
                AND cr.role_id = cb.role_id
            LEFT JOIN
                (
                    ' . $this->getClientEventsStatisticsSql($companyId, $clientIds, $startDate, $endDate, $roleId) . '
                ) ce
                ON ce.date = cb.date
                AND ce.company_id = cb.company_id
                AND ce.client_id = cb.client_id
                AND ce.role_id = cb.role_id
        ';
        $this->getClient()->getConnection()->write($sql);
    }

    /**
     * @param int $companyId
     * @param array|string $clientIds
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @param int|array|null $roleId
     * @return string
     */
    protected function getClientBasicStatisticsSql(
        int $companyId,
        array|string $clientIds,
        Carbon $startDate = null,
        Carbon $endDate = null,
        int|array $roleId = null
    ): string {
        $sql = '
            SELECT
                formatDateTime(c.call_time, \'%Y-%m-%d\') date,
                cl.company_id company_id,
                cl.client_id client_id,
                r.role_id role_id,
                arrayReduce(\'groupUniqArray\', groupArray(c.agent_id)) agent_ids,
                sum(c.call_duration) total_calls_duration,
                sumIf(c.call_duration, c.call_duration >= r.effective_call_threshold_bar) effective_calls_duration,
                sumIf(c.call_duration, c.call_duration < r.effective_call_threshold_bar) not_effective_calls_duration,
                countIf(c.call_duration >= r.effective_call_threshold_bar) effective_calls_count,
                countIf(c.call_duration < r.effective_call_threshold_bar) not_effective_calls_count,
                max(c.call_time) last_call_time,
                last_value_respect_nulls(cl.client_name) client_name,
                last_value_respect_nulls(cl.country) country,
                last_value_respect_nulls(cl.status) status,
                last_value_respect_nulls(cl.source) source,
                last_value_respect_nulls(cl.acquisition_date) acquisition_date,
                last_value_respect_nulls(cl.is_converted) is_converted,
                last_value_respect_nulls(cl.converted_date) converted_date,
                last_value_respect_nulls(cl.last_transaction_date) last_transaction_date,
                last_value_respect_nulls(cl.campaign_id) campaign_id,
                last_value_respect_nulls(cl.value) value
            FROM
                (
                    ' .
                    $this->getFinalTableSqlUsingGroupBy(
                        'clients',
                        [
                            'client_id',
                            'company_id',
                        ],
                        'created',
                        [
                            'client_name',
                            'country',
                            'status',
                            'source',
                            'acquisition_date',
                            'is_converted',
                            'converted_date',
                            'last_transaction_date',
                            'campaign_id',
                            'value',
                        ],
                        [
                            'company_id' => $companyId,
                            'client_id' => $clientIds,
                        ]
                    ) . '
                ) cl
            INNER JOIN
                (
                    ' .
                    $this->getFinalTableSqlUsingGroupBy(
                        'calls',
                        [
                            'company_id',
                            'call_id',
                        ],
                        'created',
                        [
                            'client_id',
                            'agent_id',
                            'call_duration',
                            'call_time',
                            'call_type',
                            'is_deleted',
                        ],
                        $this->getCallsWhereConditionsFromMainParams($companyId, $startDate, $endDate, clientId: $clientIds)
                    ) . '
                ) c
                ON cl.client_id = c.client_id
                AND cl.company_id = c.company_id
            INNER JOIN
                (
                    SELECT
                        *
                    FROM
                        dictionary(roles)';
        if (!is_null($roleId)) {
            $roleIds = is_int($roleId) ? [$roleId] : $roleId;
            $sql .= '
                    WHERE
                        role_id IN (' . implode(',', $roleIds) . ')
            ';
        }
        $sql .= '
                ) r
                ON r.company_id = c.company_id
            WHERE
                c.is_deleted = 0
            GROUP BY
                date,
                cl.company_id,
                cl.client_id,
                r.role_id
        ';
        return $sql;
    }

    /**
     * @param int $companyId
     * @param array|string $clientIds
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @param int|array|null $roleId
     * @return string
     */
    protected function getClientReviewStatisticsSql(
        int $companyId,
        array|string $clientIds,
        Carbon $startDate = null,
        Carbon $endDate = null,
        int|array $roleId = null
    ): string {
        return '
            SELECT
                formatDateTime(pc.call_time, \'%Y-%m-%d\') date,
                pc.company_id company_id,
                pc.role_id role_id,
                pc.client_id client_id,
                countIf(
                    pc.is_reviewed = 1 AND c.call_type = \'' . \STCall\Entity\Call::CALL_TYPE . '\'
                ) reviewed_calls_count,
                countIf(
                    pc.is_reviewed = 1 AND c.call_type = \'' . \STCall\Entity\ChatCall::CHAT_CALL_TYPE . '\'
                ) reviewed_chats_count,
                sum(pc.score) score,
                avg(pc.risk_rank) risk_rank
            FROM
                (
                    ' .
                        $this->getFinalTableSqlUsingGroupBy(
                            'calls',
                            [
                                'company_id',
                                'call_id',
                            ],
                            'created',
                            [
                                'client_id',
                                'call_type',
                                'is_deleted',
                            ],
                            $this->getCallsWhereConditionsFromMainParams($companyId, $startDate, $endDate, clientId: $clientIds)
                        ) . '
                ) c
                INNER JOIN
                (
                    ' .
                        $this->getFinalTableSqlUsingGroupBy(
                            'precalculated_calls',
                            [
                                'company_id',
                                'call_id',
                                'role_id',
                            ],
                            'created',
                            [
                                'client_id',
                                'call_time',
                                'is_reviewed',
                                'score',
                                'risk_rank',
                            ],
                            $this->getCallsWhereConditionsFromMainParams($companyId, $startDate, $endDate, $roleId, $clientIds)
                        ) . '
                ) pc
                ON c.call_id = pc.call_id
                AND c.client_id = pc.client_id
                AND c.company_id = pc.company_id
            WHERE
                c.is_deleted = 0
            GROUP BY
                date,
                pc.company_id,
                pc.client_id,
                pc.role_id
        ';
    }

    /**
     * @param int $companyId
     * @param array|string $clientIds
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @param int|array|null $roleId
     * @return string
     */
    protected function getClientEventsStatisticsSql(
        int $companyId,
        array|string $clientIds,
        Carbon $startDate = null,
        Carbon $endDate = null,
        int|array $roleId = null
    ): string {
        return '
            SELECT
                formatDateTime(pce.call_time, \'%Y-%m-%d\') date,
                pce.company_id company_id,
                pce.role_id role_id,
                pce.client_id client_id,
                arrayReduce(\'groupUniqArray\', groupArray(pce.event_id)) event_ids,
                arrayReduce(\'groupUniqArray\', groupArray(pce.event_category_id)) event_category_ids
            FROM
                (
                    SELECT
                        call_time,
                        company_id,
                        role_id,
                        client_id,
                        event_id,
                        event_category_id
                    FROM
                        (
                            ' .
                            $this->getFinalTableSqlUsingGroupBy(
                                'precalculated_calls_events',
                                [
                                    'company_id',
                                    'role_id',
                                    'call_id',
                                    'paragraph',
                                    'event_id',
                                ],
                                'created',
                                [
                                    'call_time',
                                    'client_id',
                                    'event_is_deleted',
                                    'event_category_id',
                                ],
                                $this->getCallsWhereConditionsFromMainParams($companyId, $startDate, $endDate, $roleId, $clientIds),
                            ) . '
                        )
                    WHERE
                        event_is_deleted = 0
                        AND call_id IN (
                            SELECT
                                call_id
                            FROM
                            (
                                ' .
                                $this->getFinalTableSqlUsingGroupBy(
                                    'calls',
                                    [
                                        'company_id',
                                        'call_id',
                                    ],
                                    'created',
                                    [
                                        'is_deleted',
                                    ],
                                    $this->getCallsWhereConditionsFromMainParams($companyId, $startDate, $endDate, clientId: $clientIds)
                                )
                                . '
                            )
                            WHERE
                                is_deleted = 0
                        )
                ) pce
            GROUP BY
                date,
                pce.company_id,
                pce.client_id,
                pce.role_id
        ';
    }
}
