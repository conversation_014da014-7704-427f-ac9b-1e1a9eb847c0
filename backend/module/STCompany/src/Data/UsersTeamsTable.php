<?php

declare(strict_types=1);

namespace STCompany\Data;

use Laminas\Db\ResultSet\ResultSetInterface;

class UsersTeamsTable extends \STLib\Db\AbstractTable
{
    /**
     *
     * @param int $companyId
     * @param int $teamId
     * @return \Laminas\Db\ResultSet\ResultSet
     */
    public function getUsersByTeamId(int $companyId, int $teamId): \Laminas\Db\ResultSet\ResultSetInterface
    {
        $select = $this->tableGateway->getSql()->select();
        $select->join(
            [
                'teams' => 'teams',
            ],
            'teams.team_id = users_teams.team_id',
            [],
            \Laminas\Db\Sql\Select::JOIN_INNER
        )
        ->where([
            'teams.company_id' => $companyId
        ]);
        $select->join(
            [
                'users' => 'users',
            ],
            'users.user_id = users_teams.user_id',
            [
                'user_name',
                'user_email',
                'user_avatar',
            ],
            \Laminas\Db\Sql\Select::JOIN_INNER
        )
        ->where([
            'users_teams.team_id' => $teamId,
            'teams.company_id' => $companyId
        ]);

        return $this->tableGateway->selectWith($select);
    }

    /**
     *
     * @param int $companyId
     * @param array $roleTypes
     * @param array $usersIds
     * @return ResultSetInterface
     */
    public function getUsersWithTeams(int $companyId, array $roleTypes, array $usersIds): ResultSetInterface
    {
        $select = $this->tableGateway->getSql()->select();

        $select
            ->columns(['user_id'])
            ->join(
                [
                    'ucr' => 'users_companies_roles',
                ],
                'users_teams.user_id = ucr.user_id',
                []
            )
            ->join(
                [
                    'r' => 'roles',
                ],
                'ucr.role_id = r.role_id',
                []
            )
            ->where([
                'users_teams.user_id' => $usersIds,
                'ucr.company_id' => $companyId,
                'r.role_type' => $roleTypes,
            ])
        ;

        return $this->tableGateway->selectWith($select);
    }

    /**
     *
     * @param int $userId
     * @param int $teamId
     * @return bool
     */
    public function save(int $userId, int $teamId): bool
    {
        return $this->insertOrUpdate([
            'user_id' => $userId,
            'team_id' => $teamId,
        ]);
    }

    /**
     *
     * @param array $userIds
     * @param int $teamId
     * @return void
     * @throws \Exception
     */
    public function saveUsers(array $userIds, int $teamId): void
    {
        $this->beginTransaction();

        $this->deleteUsersFromTeam($teamId);

        $data = [];
        foreach ($userIds as $userId) {
            $data[] = [
                'user_id' => $userId,
                'team_id' => $teamId,
            ];
        }
        $this->multiInsertOrUpdate($data, ['user_id', 'team_id'], useTransaction: false);

        $this->commit();
    }

    /**
     *
     * @param int $userId
     * @param \STCompany\Entity\TeamCollection $teamCollection
     * @return int
     */
    public function bulkInsert(int $userId, \STCompany\Entity\TeamCollection $teamCollection): int
    {
        if ($teamCollection->count() === 0) {
            return 0;
        }
        $data = [];
        foreach ($teamCollection as $team) {
            $data[] = [
                'team_id' => $team->getId(),
                'user_id' => $userId,
            ];
        }
        return $this->multiInsert($data, useTransaction: false);
    }

    /**
     *
     * @param int $teamId
     * @return int
     */
    public function deleteUsersFromTeam(int $teamId): int
    {
        return $this->tableGateway->delete([
            'team_id' => $teamId,
        ]);
    }

    /**
     *
     * @param array $usersIds
     * @return int
     */
    public function deleteUsersFromAllTeams(array $usersIds): int
    {
        return $this->tableGateway->delete([
            'user_id' => $usersIds,
        ]);
    }

    /**
     *
     * @param int $userId
     * @param int|array $teamIds
     * @return int
     */
    public function delete(int $userId, int|array $teamIds): int
    {
        return $this->tableGateway->delete([
            'user_id' => $userId,
            'team_id' => $teamIds,
        ]);
    }
}
