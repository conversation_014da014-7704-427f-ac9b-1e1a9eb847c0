<?php

declare(strict_types=1);

namespace STCompany\Data;

class CompaniesCallTemplatesTable extends \STLib\Db\AbstractTable
{
    /**
     *
     * @param int|array $companyId
     * @return \Laminas\Db\ResultSet\ResultSet
     */
    public function getCompanyCallTemplates(int|array $companyId): \Laminas\Db\ResultSet\ResultSet
    {
        $select = $this->tableGateway->getSql()->select();
        $select->where([
            'company_id' => $companyId,
        ]);
        return $this->tableGateway->selectWith($select);
    }
}
