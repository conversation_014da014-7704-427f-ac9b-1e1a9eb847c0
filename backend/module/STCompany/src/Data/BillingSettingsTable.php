<?php

declare(strict_types=1);

namespace STCompany\Data;

use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Entity\BillingSettings\BillingSettings;
use STCompany\Entity\BillingSettings\BillingSettingsCollection;
use STLib\Db\HydratedAbstractTable;

class BillingSettingsTable extends HydratedAbstractTable
{
    public static string $table = 'billing_settings';
    public static string $entityName = BillingSettings::class;
    public static string $collectionName = BillingSettingsCollection::class;

    public static function getTableName(): string
    {
        return self::$table;
    }

    public static function getEntityName(): string
    {
        return self::$entityName;
    }

    public static function getCollectionName(): string
    {
        return self::$collectionName;
    }

    /**
     * @throws NotFoundApiException
     */
    public function getBillingSettings(int $companyId): BillingSettings
    {
        $select = $this->tableGateway->getSql()->select();
        $select->where(['company_id' => $companyId]);

        $result = $this->tableGateway->selectWith($select);
        if ($result->count() === 0) {
            throw new NotFoundApiException('Billing settings not found.');
        }

        return $result->current();
    }

    public function createBillingSettings(BillingSettings $billingSettings): void
    {
        $data = $billingSettings->toArray();

        $this->tableGateway->insert($data);
    }

    public function updateBillingSettings(BillingSettings $billingSettings): void
    {
        $data = $billingSettings->toArray();

        $this->tableGateway->update($data, [
            'company_id' => $billingSettings->getCompanyId(),
        ]);
    }
}
