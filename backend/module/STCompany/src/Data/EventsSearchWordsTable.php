<?php

declare(strict_types=1);

namespace STCompany\Data;

class EventsSearchWordsTable extends \STLib\Db\AbstractTable
{
    /**
     *
     * @param int|array $eventIds
     * @return \Laminas\Db\ResultSet\ResultSet
     */
    public function getSearchWordsByEventIds(int|array $eventIds): \Laminas\Db\ResultSet\ResultSetInterface
    {
        $select = $this->tableGateway->getSql()->select();
        $select->where([
            'event_id' => $eventIds,
        ]);
        return $this->tableGateway->selectWith($select);
    }

    /**
     *
     * @param int $companyId
     * @param int $eventId
     * @param int $searchWordId
     * @return \Laminas\Db\ResultSet\ResultSet
     * @throws \STApi\Entity\Exception\NotFoundApiException
     */
    public function getSearchWord(int $companyId, int $eventId, int $searchWordId): \Laminas\Db\ResultSet\ResultSet
    {
        $select = $this->tableGateway->getSql()->select();
        $select
                ->join(
                    [
                        'e' => 'events',
                    ],
                    'e.event_id = events_search_words.event_id',
                    [],
                    \Laminas\Db\Sql\Select::JOIN_INNER
                )
                ->join(
                    [
                        'r' => 'roles',
                    ],
                    'r.role_id = e.role_id',
                    [],
                    \Laminas\Db\Sql\Select::JOIN_INNER
                )
                ->where([
                    'r.company_id' => $companyId,
                    'events_search_words.search_word_id' => $searchWordId,
                    'events_search_words.event_id' => $eventId,
                ]);
        $result = $this->tableGateway->selectWith($select);
        if ($result->count() === 0) {
            throw new \STApi\Entity\Exception\NotFoundApiException('Search word not found');
        }
        return $result;
    }

    /**
     *
     * @param \STCompany\Entity\Event\Event $event
     * @return int
     */
    public function saveSearchWords(\STCompany\Entity\Event\Event $event): int
    {
        $data = [];
        foreach ($event->getSearchWord() as $searchWord) {
            $data[] = [
                'event_id' => $event->getId(),
                'search_word_name' => $searchWord->getName(),
            ];
        }
        return $this->multiInsert($data, false);
    }

    /**
     *
     * @param \STCompany\Entity\Event\SearchWord $searchWord
     * @return int
     */
    public function addSearchWordByEventId(\STCompany\Entity\Event\SearchWord $searchWord): int
    {
        $data = [
            'event_id' => $searchWord->getEventId(),
            'search_word_name' => $searchWord->getName(),
        ];

        if ($searchWord->getId() > 0) {
            $this->tableGateway->update($data, [
                'search_word_id' => $searchWord->getId(),
            ]);
        } else {
            $this->tableGateway->insert($data);
            $searchWord->setId((int)$this->tableGateway->lastInsertValue);
        }

        return $searchWord->getId();
    }

    /**
     *
     * @param int $eventId
     * @param int $searchWordId
     * @return int
     */
    public function deleteSearchWords(int $eventId, int $searchWordId = null): int
    {
        $where = [
            'event_id' => $eventId,
        ];
        if (is_int($searchWordId)) {
            $where['search_word_id'] = $searchWordId;
        }
        return $this->tableGateway->delete($where);
    }

    /**
     *
     * @param int $eventId
     * @param int $searchWordId
     * @return int
     */
    public function deleteSearchWord(int $eventId, int $searchWordId): int
    {
        return $this->deleteSearchWords($eventId, $searchWordId);
    }
}
