<?php

declare(strict_types=1);

namespace STCompany\Data;

use Laminas\Db\ResultSet\ResultSet;

class EventsColorsTable extends \STLib\Db\AbstractTable
{
    public const RED_COLOR_ID = 1;
    public const GREY_COLOR_ID = 8;

    /**
     *
     * @param int $colorId
     * @return ResultSet
     * @throws \STApi\Entity\Exception\NotFoundApiException
     */
    public function getColor(int $colorId): ResultSet
    {
        $select = $this->tableGateway->getSql()->select();
        $select->where([
            'color_id' => $colorId,
        ]);
        $result = $this->tableGateway->selectWith($select);
        if ($result->count() === 0) {
            throw new \STApi\Entity\Exception\NotFoundApiException('Color not found');
        }
        return $this->tableGateway->selectWith($select);
    }

    public function getColorByName(string $colorName): ResultSet
    {
        $select = $this->tableGateway->getSql()->select();
        $select->where([
            'name' => $colorName,
        ]);

        return $this->tableGateway->selectWith($select);
    }

    /**
     *
     * @return ResultSet
     */
    public function getColors(): ResultSet
    {
        $select = $this->tableGateway->getSql()->select();
        $select->order('priority ASC');
        return $this->tableGateway->selectWith($select);
    }
}
