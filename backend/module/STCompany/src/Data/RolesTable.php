<?php

declare(strict_types=1);

namespace STCompany\Data;

use <PERSON>inas\Db\ResultSet\ResultSet;
use <PERSON><PERSON>\Db\ResultSet\ResultSetInterface;
use Laminas\Db\Sql\Expression;
use ST<PERSON>pi\Entity\Exception\NotFoundApiException;
use STCompany\Entity\Role;
use STLib\Db\AbstractTable;

class RolesTable extends AbstractTable
{
    /**
     *
     * @param int|array $companyId
     * @param array $roleTypes
     * @return ResultSet
     */
    public function getRoles(int|array $companyId, array $roleTypes = []): ResultSet
    {
        $select = $this->tableGateway->getSql()->select();
        $select->where([
            'company_id' => $companyId,
        ]);
        if (!empty($roleTypes)) {
            $select->where([
                'role_type' => $roleTypes,
            ]);
        }
        return $this->tableGateway->selectWith($select);
    }

    /**
     *
     * @param int $companyId
     * @param int $roleId
     * @return ResultSet
     */
    public function getRole(int $companyId, int $roleId): ResultSet
    {
        $select = $this->tableGateway->getSql()->select();
        $select->where([
            'company_id' => $companyId,
            'role_id' => $roleId,
        ]);
        $result = $this->tableGateway->selectWith($select);
        if ($result->count() === 0) {
            throw new NotFoundApiException('Role not found');
        }
        return $this->tableGateway->selectWith($select);
    }

    /**
     *
     * @param int $companyId
     * @return ResultSet
     * @throws NotFoundApiException
     */
    public function getAgentRole(int $companyId): ResultSet
    {
        $select = $this->tableGateway->getSql()->select();
        $select
                ->where([
                    'company_id' => $companyId,
                    'role_type' => Role::AGENT_ROLE_TYPE,
                ])
                ->limit(1);
        $result = $this->tableGateway->selectWith($select);
        if ($result->count() === 0) {
            throw new NotFoundApiException('Agent role not found');
        }
        return $this->tableGateway->selectWith($select);
    }

    /**
     * @throws NotFoundApiException
     */
    public function getRoleByType(int $roleType, int $companyId): ResultSet
    {
        $select = $this->tableGateway->getSql()->select();
        $select
            ->where([
                'company_id' => $companyId,
                'role_type' => $roleType,
            ])
            ->limit(1);
        $result = $this->tableGateway->selectWith($select);
        if ($result->count() === 0) {
            throw new NotFoundApiException('Role not found');
        }
        return $this->tableGateway->selectWith($select);
    }

    /**
     *
     * @param int $companyId
     * @param int userId
     * @return ResultSet
     */
    public function getRoleByUserId(int $companyId, int $userId): ResultSet
    {
        $select = $this->tableGateway->getSql()->select();
        $select
                ->join(
                    [
                        'ucr' => 'users_companies_roles',
                    ],
                    'ucr.role_id = roles.role_id',
                    [],
                    \Laminas\Db\Sql\Select::JOIN_INNER
                )
                ->where([
                    'ucr.user_id' => $userId,
                    'roles.company_id' => $companyId,
                ]);

        $result = $this->tableGateway->selectWith($select);

        return $result;
    }

    /**
     *
     * @param array $roleIds
     * @return ResultSet
     */
    public function getEventsCount(array $roleIds): ResultSet
    {
        $select = $this->tableGateway->getSql()->select();
        $select
                ->columns([
                    'role_id',
                    'count' => new Expression('COUNT(*)'),
                ])
                ->join(
                    [
                        'e' => 'events',
                    ],
                    'e.role_id = roles.role_id',
                    [],
                    \Laminas\Db\Sql\Select::JOIN_INNER
                )
                ->where([
                    'roles.role_id' => $roleIds,
                ])
                ->group('roles.role_id');
        return $this->tableGateway->selectWith($select);
    }

    /**
     *
     * @param int $companyId
     * @return ResultSetInterface
     */
    public function getRoleIds(int $companyId): ResultSetInterface
    {
        $select = $this->tableGateway->getSql()->select();
        $select
            ->columns([
                'role_id'
            ])
            ->where([
                'company_id' => $companyId,
            ]);
        return $this->tableGateway->selectWith($select);
    }

    /**
     *
     * @param Role $role
     * @return int
     */
    public function saveRole(Role $role): int
    {
        $data = [
            'role_name' => $role->getName(),
            'company_id' => $role->getCompanyId(),
            'role_type' => $role->getRoleType(),
            'effective_call_threshold_bar' => $role->getEffectiveCallThresholdBar(),
        ];

        if ($role->getId() > 0) {
            $this->tableGateway->update($data, [
                'role_id' => $role->getId(),
            ]);
        } else {
            $this->tableGateway->insert($data);
            $role->setId((int)$this->tableGateway->lastInsertValue);
        }

        return $role->getId();
    }

    /**
     *
     * @param int $companyId
     * @param int $roleId
     * @return int
     */
    public function deleteRole(int $companyId, int $roleId): int
    {
        return $this->tableGateway->delete([
            'role_id' => $roleId,
            'company_id' => $companyId,
        ]);
    }
}
