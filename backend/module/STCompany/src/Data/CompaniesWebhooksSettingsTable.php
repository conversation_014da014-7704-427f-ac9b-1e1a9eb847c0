<?php

declare(strict_types=1);

namespace STCompany\Data;

use STApi\Entity\Exception\NotFoundApiException;
use STLib\Db\AbstractTable;

class CompaniesWebhooksSettingsTable extends AbstractTable
{
    public function getWebhookSettingsDataByType(string $webhookType, int $companyId): ?array
    {
        $select = $this->tableGateway->getSql()->select();
        $select
            ->where([
                'type' => $webhookType,
                'company_id' => $companyId
            ]);

        $result = $this->tableGateway->selectWith($select);
        if ($result->count() === 0) {
            return null;
        }

        return current($result->toArray());
    }

    public function createWebhookSettings(
        string $webhookType,
        string $url,
        array $headers,
        bool $isEnabled,
        int $companyId
    ): int {
        $data = [
            'type' => $webhookType,
            'url' => $url,
            'headers' => json_encode($headers),
            'is_enabled' => $isEnabled,
            'company_id' => $companyId,
        ];

        $this->tableGateway->insert($data);

        return (int) $this->tableGateway->getLastInsertValue();
    }

    public function updateWebhookSettings(
        int $companyWebhookSettingId,
        string $url,
        array $headers,
        bool $isEnabled
    ): void {
        $data = [
            'url' => $url,
            'headers' => json_encode($headers),
            'is_enabled' => $isEnabled,
        ];

        $this->tableGateway->update($data, [
            'company_webhook_setting_id' => $companyWebhookSettingId,
        ]);
    }

    /**
     * @throws NotFoundApiException
     */
    public function getWebhookSettingsData(int $companyWebhookSettingId, int $companyId): ?array
    {
        $select = $this->tableGateway->getSql()->select();
        $select
            ->where([
                'company_webhook_setting_id' => $companyWebhookSettingId,
                'company_id' => $companyId
            ]);

        $result = $this->tableGateway->selectWith($select);
        if ($result->count() === 0) {
            throw new NotFoundApiException('Webhook settings not found');
        }

        return current($result->toArray());
    }

    public function getCompaniesWebhookSettingsDataList(int $companyId): array
    {
        $select = $this->tableGateway->getSql()->select();
        $select->where(['company_id' => $companyId]);

        $resultSet = $this->tableGateway->selectWith($select);

        $result = [];
        foreach ($resultSet as $row) {
            $result[] = (array) $row;
        }

        return $result;
    }

    public function deleteWebhookSettings(int $companyWebhookSettingId): void
    {
        $this->tableGateway->delete(['company_webhook_setting_id' => $companyWebhookSettingId]);
    }
}
