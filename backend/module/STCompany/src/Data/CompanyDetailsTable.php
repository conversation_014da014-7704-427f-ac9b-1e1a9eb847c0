<?php

namespace STCompany\Data;

use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Entity\CompanyDetailsCollection;
use STCompany\Entity\CompanyDetails;
use STLib\Db\HydratedAbstractTable;

class CompanyDetailsTable extends HydratedAbstractTable
{
    public static string $table = 'companies_details';
    public static string $entityName = CompanyDetails::class;
    public static string $collectionName = CompanyDetailsCollection::class;

    public static function getTableName(): string
    {
        return self::$table;
    }

    public static function getEntityName(): string
    {
        return self::$entityName;
    }

    public static function getCollectionName(): string
    {
        return self::$collectionName;
    }

    public function getCompanyDetails(int $companyId): CompanyDetails
    {
        $select = $this->tableGateway->getSql()->select();
        $select->where(['company_id' => $companyId]);

        $result = $this->tableGateway->selectWith($select);
        if ($result->count() === 0) {
            return new CompanyDetails();
        }

        return $result->current();
    }

    public function saveCompanyDetails(CompanyDetails $companyDetails): void
    {
        $this->tableGateway->insert($companyDetails->toArray());
    }

    public function updateCompanyDetails(CompanyDetails $companyDetails): void
    {
        $this->tableGateway->update($companyDetails->toArray(), ['company_id' => $companyDetails->getCompanyId()]);
    }
}
