<?php

declare(strict_types=1);

namespace STCompany\Validator;

use STLib\Validator\Validator;

class EventSearchWordValidator extends Validator
{
    protected const INCORRECT_SEARCH_WORDS_NAME = 'Search word name must be not empty and less than 255 symbols';

    /**
     *
     * @param \STCompany\Entity\Event\SearchWord $searchWord
     * @return Validator
     * @throws \InvalidArgumentException
     */
    public function setInstance($searchWord): Validator
    {
        if (!($searchWord instanceof \STCompany\Entity\Event\SearchWord)) {
            throw new \InvalidArgumentException('SearchWord must be instance of "\STCompany\Entity\Event\SearchWord"');
        }
        return parent::setInstance($searchWord);
    }

    /**
     *
     * @see \STLib\Validator\ValidatorInterface::run()
     */
    public function run()
    {
        if ($this->hasCheck('name')) {
            $searchWordValidator = new \Laminas\Validator\StringLength([
                'min' => 1,
                'max' => 255,
            ]);
            if (!$searchWordValidator->isValid($this->getInstance()->getName())) {
                $this->addError('search_words', static::INCORRECT_SEARCH_WORDS_NAME);
            }
        }
    }
}
