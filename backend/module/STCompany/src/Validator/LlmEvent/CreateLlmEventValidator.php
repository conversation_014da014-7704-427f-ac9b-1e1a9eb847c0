<?php

namespace STCompany\Validator\LlmEvent;

use STCompany\Data\CompanyLlmEventsTable;
use STCompany\Entity\Company;
use STConfiguration\Service\ConfigurationService;
use STLib\Validator\Validator;
use ST<PERSON>lmEvent\Validator\SaveLlmEventValidator;

class CreateLlmEventValidator extends Validator
{
    public function __construct(
        private readonly SaveLlmEventValidator $saveLlmEventValidator,
        private readonly CompanyLlmEventsTable $companyLlmEventsTable,
        private readonly ConfigurationService $configuration
    ) {
    }

    public function run(): void
    {
        /** @var array $input */
        $input = $this->getInstance();

        /** @var Company $company */
        [
            'company' => $company,
            'name' => $name,
            'description' => $description
        ] = $input;

        if ($this->companyLlmEventsTable->getLlmEventsCount($company->getId()) >= $company->getLlmEventsLimit()) {
            $this->addError('name', sprintf(
                'Your company has reached the limit of %d LLM events. Contact us at %s to extend this limit.',
                $company->getLlmEventsLimit(),
                $this->configuration->get('application')['support_manager_email']
            ));

            return;
        }

        $this->saveLlmEventValidator->setInstance(['id' => null, 'name' => $name, 'description' => $description]);
        $this->saveLlmEventValidator->validate();
        if ($this->saveLlmEventValidator->hasError('name')) {
            $this->addError('name', $this->saveLlmEventValidator->getErrors('name')[0]);
            return;
        }
        if ($this->saveLlmEventValidator->hasError('description')) {
            $this->addError('description', $this->saveLlmEventValidator->getErrors('description')[0]);
        }
    }
}
