<?php

declare(strict_types=1);

namespace STCompany\Validator;

use ST<PERSON>pi\Entity\Exception\NotFoundApiException;
use STCompany\Data\CompaniesTable;
use STCompany\Data\RolesTable;
use STLib\Validator\Validator;

class CloneRoleValidator extends Validator
{
    public function __construct(
        private readonly RolesTable $rolesTable,
        private readonly CompaniesTable $companiesTable,
    ) {
    }

    protected const string ERROR_SOURCE_ROLE_IS_NOT_EXIST = 'The source role is not exist for provided company';
    protected const string ERROR_DESTINATION_ROLE_IS_NOT_EXIST = 'The destination role is not exist for provided company';
    protected const string ERROR_DESTINATION_COMPANY_IS_NOT_EXIST = 'The destination company is not exist';

    public function run(): void
    {
        /** @var array $input */
        $input = $this->getInstance();

        try {
            $this->rolesTable->getRole($input['source_company_id'], $input['source_role_id']);
        } catch (NotFoundApiException) {
            $this->addError('source_role_id', self::ERROR_SOURCE_ROLE_IS_NOT_EXIST);
        }

        try {
            $this->companiesTable->getCompany($input['destination_company_id']);
        } catch (NotFoundApiException) {
            $this->addError('destination_company_id', self::ERROR_DESTINATION_COMPANY_IS_NOT_EXIST);
        }

        if (isset($input['destination_role_id'])) {
            try {
                $this->rolesTable->getRole($input['destination_company_id'], $input['destination_role_id']);
            } catch (NotFoundApiException) {
                $this->addError('destination_role_id', self::ERROR_DESTINATION_ROLE_IS_NOT_EXIST);
            }
        }
    }
}
