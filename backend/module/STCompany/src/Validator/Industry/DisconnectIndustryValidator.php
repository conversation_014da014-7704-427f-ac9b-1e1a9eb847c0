<?php

namespace STCompany\Validator\Industry;

use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Data\CompanyIndustriesTable;
use STCompany\Service\Interfaces\IndustrySelectorInterface;
use STLib\Validator\Validator;

class DisconnectIndustryValidator extends Validator
{
    public function __construct(
        private readonly IndustrySelectorInterface $industrySelector,
        private readonly CompanyIndustriesTable $companyIndustryTable,
    ) {
    }

    protected const string ERROR_INDUSTRY_DOES_NOT_EXIST = 'The industry does not exists.';
    protected const string ERROR_INDUSTRY_WAS_NOT_CONNECTED = 'The industry was not connected.';

    public function run(): void
    {
        /** @var array $input */
        $input = $this->getInstance();

        ['industry_id' => $industryId, 'company_id' => $companyId] = $input;

        try {
            $this->industrySelector->getIndustry($industryId);
        } catch (NotFoundApiException) {
            $this->addError('id', self::ERROR_INDUSTRY_DOES_NOT_EXIST);

            return;
        }

        try {
            $this->companyIndustryTable->getIndustry($industryId, $companyId);
        } catch (NotFoundApiException) {
            $this->addError('id', self::ERROR_INDUSTRY_WAS_NOT_CONNECTED);
        }
    }
}
