<?php

namespace STCompany\Validator;

use STApi\Entity\Exception\NotFoundApiException;
use STLib\Validator\Validator;
use STLlmEvent\Data\LlmEventsTable;

class ConnectLlmEventValidator extends Validator
{
    protected const string ERROR_EVENT_DOES_NOT_EXIST = 'The llm event does not exists.';

    public function __construct(private readonly LlmEventsTable $llmEventsTable)
    {
    }

    public function run(): void
    {
        /** @var array $input */
        $input = $this->getInstance();

        ['llm_event_id' => $llmEventId] = $input;

        try {
            $this->llmEventsTable->getLlmEvent($llmEventId);
        } catch (NotFoundApiException) {
            $this->addError('id', self::ERROR_EVENT_DOES_NOT_EXIST);
        }
    }
}
