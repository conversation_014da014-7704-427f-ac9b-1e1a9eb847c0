<?php

declare(strict_types=1);

namespace STCompany\Validator;

use STCompany\Entity\Company;
use STLib\Validator\Validator;

final class UpdateStatusValidator extends Validator
{
    private const string WRONG_CHANGE_ERROR_TEMPLATE = 'Status cannot be changed from %s to %s.';

    private static array $allowedStatusesTransitions = [
        Company::STATUS_TRIAL => [
            Company::STATUS_TRIAL_EXPIRED,
            Company::STATUS_ACTIVE,
            Company::STATUS_SUSPENDED
        ],
        Company::STATUS_TRIAL_EXPIRED => [
            Company::STATUS_ACTIVE,
            Company::STATUS_SUSPENDED
        ],
        Company::STATUS_ACTIVE => [
            Company::STATUS_SUSPENDED
        ],
        Company::STATUS_SUSPENDED => [
            Company::STATUS_ACTIVE
        ]
    ];

    public function run(): void
    {
        /** @var Company $company */
        ['company' => $company, 'status' => $status] = $this->getInstance();

        if (!in_array($status, Company::$statuses)) {
            $this->addError(
                'status',
                'Wrong status, available statuses are: ' . implode(', ', Company::$statuses)
            );

            return;
        }

        $allowedStatuses = self::$allowedStatusesTransitions[$company->getStatus()];

        if (!in_array($status, $allowedStatuses)) {
            $this->addError('status', sprintf(self::WRONG_CHANGE_ERROR_TEMPLATE, $company->getStatus(), $status));
        }
    }
}
