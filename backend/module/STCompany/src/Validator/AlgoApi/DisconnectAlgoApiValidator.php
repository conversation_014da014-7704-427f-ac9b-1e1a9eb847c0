<?php

namespace STCompany\Validator\AlgoApi;

use STAlgo\Data\CompaniesAlgoApisTable;
use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Service\Interfaces\AlgoApiSelectorInterface;
use STLib\Validator\Validator;

class DisconnectAlgoApiValidator extends Validator
{
    public function __construct(
        private readonly AlgoApiSelectorInterface $algoApiSelector,
        private readonly CompaniesAlgoApisTable $companiesAlgoApisTable,
    ) {
    }

    protected const string ERROR_ALGO_API_DOES_NOT_EXIST = 'The algo api does not exists.';
    protected const string ERROR_ALGO_API_WAS_NOT_CONNECTED = 'The algo api was not connected.';

    public function run(): void
    {
        /** @var array $input */
        $input = $this->getInstance();

        ['algo_api_id' => $algoApiId, 'company_id' => $companyId] = $input;

        try {
            $this->algoApiSelector->getAlgoApi($algoApiId);
        } catch (NotFoundApiException) {
            $this->addError('id', self::ERROR_ALGO_API_DOES_NOT_EXIST);

            return;
        }

        try {
            $this->companiesAlgoApisTable->getAlgoApi($algoApiId, $companyId);
        } catch (NotFoundApiException) {
            $this->addError('id', self::ERROR_ALGO_API_WAS_NOT_CONNECTED);
        }
    }
}
