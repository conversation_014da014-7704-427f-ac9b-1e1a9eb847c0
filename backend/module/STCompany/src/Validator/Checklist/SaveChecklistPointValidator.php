<?php

namespace STCompany\Validator\Checklist;

use <PERSON><PERSON>\Validator\NumberComparison;
use <PERSON><PERSON>\Validator\StringLength;
use <PERSON><PERSON><PERSON>\Entity\Exception\NotFoundApiException;
use STCompany\Data\ChecklistsPointsTable;
use <PERSON><PERSON>ompany\Data\ChecklistsTable;
use <PERSON><PERSON>ib\Validator\Check\AiInput;
use STLib\Validator\Validator;

class SaveChecklistPointValidator extends Validator
{
    private const int MAX_CHECKLIST_POINTS = 10;

    public function __construct(
        private readonly ChecklistsPointsTable $checklistsPointsTable,
        private readonly ChecklistsTable $checklistsTable,
    ) {
    }

    public function run(): void
    {
        /** @var array $input */
        $input = $this->getInstance();
        $checklistPointId = $input['checklist_point_id'] ?? null;
        $checklistId = $input['checklist_id'] ? (int) $input['checklist_id'] : null;
        $companyId = $input['company_id'];

        if ($checklistId === null) {
            $this->addError('checklist_id', 'Checklist ID is required');
        }

        $textLengthValidator = new StringLength([
            'min' => 1,
            'max' => 1024,
        ]);
        $requiredTextProperties = [
            'title',
            'expected_actions',
            'good_performance_description',
            'bad_performance_description',
        ];

        foreach ($requiredTextProperties as $propertyName) {
            if (isset($input[$propertyName])) {
                if (!$textLengthValidator->isValid($input[$propertyName])) {
                    $this->addError($propertyName, 'The ' . $propertyName . 'should not exceed 1024 characters');
                }
            } else {
                $this->addError($propertyName, 'The ' . $propertyName . ' is required');
            }
        }

        $aiInputTextProperties = [
            'trigger_condition',
            'description',
            'expected_actions',
            'good_performance_description',
            'bad_performance_description',
            'good_performance_example',
            'bad_performance_example',
        ];

        foreach ($aiInputTextProperties as $propertyName) {
            if (isset($input[$propertyName])) {
                $textContentValidator = new AiInput();

                if (!$textContentValidator->isValid($input[$propertyName])) {
                    $errors = $textContentValidator->getMessages();

                    foreach ($errors as $error) {
                        $this->addError($propertyName, $error);
                    }
                }
            }
        }

        if ($this->hasError()) {
            return;
        }

        $checklistPointWithTitle = $this->checklistsPointsTable->getChecklistPointByTitleAndChecklistId(
            $input['title'],
            $checklistId
        );

        if (
            $checklistPointWithTitle !== null
            && $checklistPointId !== $checklistPointWithTitle->getId()
        ) {
            $this->addError('title', 'Checklist point with the title already exists in the checklist');
        }

        try {
            $this->checklistsTable->getChecklist($checklistId, $companyId);
        } catch (NotFoundApiException $e) {
            $this->addError('checklist_id', 'Checklist not found');
        }

        if ($checklistPointId !== null) {
            try {
                $this->checklistsPointsTable->getChecklistPoint($checklistPointId, $companyId);
            } catch (NotFoundApiException $e) {
                $this->addError('checklist_point_id', 'Checklist point not found');
            }
        } else {
            $checklistPointsCount = $this->checklistsPointsTable->getChecklistPointsByChecklistId($checklistId, $companyId)->count();

            if ($checklistPointsCount === self::MAX_CHECKLIST_POINTS) {
                $this->addError('checklist_id', 'You can\'t create more than ' . self::MAX_CHECKLIST_POINTS . ' checklist points');
            }
        }
    }
}
