<?php

namespace STCompany\Validator\Checklist;

use STLib\Validator\Check\AiInput;
use STLib\Validator\Validator;

class ImproveChecklistPointValidator extends Validator
{
    public function run(): void
    {
        /** @var array $input */
        $input = $this->getInstance();

        $textProperties = [
            'title',
            'expected_actions',
            'good_performance_description',
            'bad_performance_description',
        ];

        foreach ($textProperties as $propertyName) {
            if (isset($input[$propertyName])) {
                $textContentValidator = new AiInput();

                if (!$textContentValidator->isValid($input[$propertyName])) {
                    $errors = $textContentValidator->getMessages();

                    foreach ($errors as $error) {
                        $this->addError($propertyName, $error);
                    }
                }
            }
        }
    }
}
