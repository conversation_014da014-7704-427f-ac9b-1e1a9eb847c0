<?php

declare(strict_types=1);

namespace STCompany\Validator\Checklist;

use STCompany\Data\ChecklistsPointsTable;
use STLib\Validator\Validator;

final class ChangeOrderValidator extends Validator
{
    private const string ERROR_WRONG_SET_OF_CHECKLIST_POINTS = 'Wrong set of checklist points';

    public function __construct(private readonly ChecklistsPointsTable $checklistsPointsTable)
    {
    }

    public function run(): void
    {
        /** @var array $input */
        $input = $this->getInstance();

        [
            'checklist_id' => $checklistId,
            'company_id' => $companyId,
            'ordered_ids' => $orderedIds,
        ] = $input;

        $checklistPointCollection = $this->checklistsPointsTable->getChecklistPointsByChecklistId(
            $checklistId,
            $companyId
        );

        $checklistPointsIds = array_column($checklistPointCollection->toArray(), 'id');
        sort($checklistPointsIds);

        $orderedIds = array_values($orderedIds);
        sort($orderedIds);

        if ($checklistPointsIds !== $orderedIds) {
            $this->addError('order', self::ERROR_WRONG_SET_OF_CHECKLIST_POINTS);
        }
    }
}
