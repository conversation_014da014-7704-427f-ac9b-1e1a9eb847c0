<?php

declare(strict_types=1);

namespace STCompany\Validator;

class EventValidatorFactory implements \Laminas\ServiceManager\Factory\FactoryInterface
{
    /**
     *
     * @param \Interop\Container\ContainerInterface $container
     * @param string $requestedName
     * @param array $options
     * @return EventValidator
     */
    public function createService(\Interop\Container\ContainerInterface $container, $requestedName, array $options = null): EventValidator
    {
        return $this($container, $requestedName ?: PatternPluginManager::class, $this->creationOptions);
    }

    /**
     *
     * @param \Interop\Container\ContainerInterface $container
     * @param string $requestedName
     * @param array $options
     * @return EventValidator
     */
    public function __invoke(\Interop\Container\ContainerInterface $container, $requestedName, array $options = null): EventValidator
    {
        $eventsTable = $container->get(\STCompany\Data\EventsTable::class);
        $rolesTable = $container->get(\STCompany\Data\RolesTable::class);
        $eventValidator = new EventValidator();
        $eventValidator->addTable('events', $eventsTable);
        $eventValidator->addTable('roles', $rolesTable);
        return $eventValidator;
    }
}
