<?php

declare(strict_types=1);

namespace STCompany\Validator;

use STLib\Validator\Validator;
use <PERSON><PERSON>\Validator\StringLength;
use STCompany\Entity\CompanyDetails;

class CompanyDetailsValidator extends Validator
{
    protected const TEXT_FIELD_TOO_LONG = 'Field cannot exceed 1024 characters';

    /**
     * @param CompanyDetails $instance
     * @return Validator
     * @throws \InvalidArgumentException
     */
    public function setInstance($instance): Validator
    {
        if (!($instance instanceof CompanyDetails)) {
            throw new \InvalidArgumentException('Request params must be instance of \STCompany\Entity\CompanyDetails');
        }
        return parent::setInstance($instance);
    }

    /**
     * @see \STLib\Validator\ValidatorInterface::run()
     */
    public function run()
    {
        $textFields = [
            'industry',
            'products_services',
            'sales_model',
            'key_goals',
            'call_types_categories',
            'biggest_risks',
            'regulations_standards',
            'unique_proposition',
            'additional_info'
        ];

        $textLengthValidator = new StringLength([
            'max' => 1024,
        ]);

        foreach ($textFields as $field) {
            $value = $this->getInstance()->{'get' . str_replace('_', '', ucwords($field, '_'))}();
            if ($value !== null && !$textLengthValidator->isValid($value)) {
                $this->addError($field, self::TEXT_FIELD_TOO_LONG);
            }
        }
    }
}
