<?php

namespace STCompany\Validator\Webhook;

use <PERSON><PERSON>\Validator\StringLength;
use STCall\Service\Webhooks\Interfaces\WebhookServiceInterface;
use STCompany\Service\Webhooks\WebhookSettingsSelector;
use <PERSON><PERSON><PERSON>\Validator\Validator;

class SaveWebhookValidator extends Validator
{
    protected const string ERROR_WRONG_TYPE = 'Wrong type.';
    protected const string ERROR_IS_ENABLED = 'Wrong is enabled.';
    protected const string ERROR_WRONG_HEADERS = 'Headers must be an array.';
    protected const string ERROR_WEBHOOK_ALREADY_EXISTS = 'Webhook with that type already exists.';

    public function __construct(
        private readonly WebhookExistsValidator $webhookExistsValidator,
        private readonly WebhookSettingsSelector $webhookSettingsSelector
    ) {
    }

    public function run(): void
    {
        /** @var array $input */
        $input = $this->getInstance();

        $id = null;
        if (array_key_exists('id', $input)) {
            $id = $input['id'];
        }

        if (is_null($id)) {
            [
                'type' => $type,
                'is_enabled' => $isEnabled,
                'url' => $url,
                'headers' => $headers,
                'company_id' => $companyId,
            ] = $input;
            if (!in_array($type, WebhookServiceInterface::TYPES)) {
                $this->addError('type', self::ERROR_WRONG_TYPE);
                return;
            }

            if (!is_null($this->webhookSettingsSelector->getCompaniesWebhooksSettingsDataByType($type, $companyId))) {
                $this->addError('type', self::ERROR_WEBHOOK_ALREADY_EXISTS);
            }
        } else {
            [
                'is_enabled' => $isEnabled,
                'url' => $url,
                'headers' => $headers,
                'company_id' => $companyId,
            ] = $input;
            $this->webhookExistsValidator->setInstance(['id' => $id, 'company_id' => $companyId]);
            $this->webhookExistsValidator->validate();
            if ($this->webhookExistsValidator->hasError()) {
                $this->addError('id', $this->webhookExistsValidator->getErrors()[0]);

                return;
            }
        }

        if (!is_array($headers)) {
            $this->addError('headers', self::ERROR_WRONG_HEADERS);
            return;
        }

        if (!is_bool($isEnabled)) {
            $this->addError('is_enabled', self::ERROR_IS_ENABLED);
            return;
        }

        $stringFiledErrorFormat = '%s must be not empty and less than %d symbols';

        $stringLengthValidator = new StringLength([
            'min' => 1,
            'max' => 1024,
        ]);

        if (!$stringLengthValidator->isValid($url)) {
            $this->addError('url', sprintf($stringFiledErrorFormat, 'Url', 1024));
        }
    }
}
