<?php

namespace STCompany\Validator\Webhook;

use ST<PERSON><PERSON>\Entity\Exception\NotFoundApiException;
use STCompany\Service\Webhooks\WebhookSettingsSelector;
use STLib\Validator\Validator;

class WebhookExistsValidator extends Validator
{
    protected const string ERROR_WEBHOOK_DOES_NOT_EXIST = 'The webhook does not exists.';

    public function __construct(private readonly WebhookSettingsSelector $webhookSettingsSelector)
    {
    }

    public function run(): void
    {
        /** @var array $input */
        $input = $this->getInstance();

        ['id' => $id, 'company_id' => $companyId] = $input;

        try {
            $this->webhookSettingsSelector->getCompaniesWebhooksSettingsData($id, $companyId);
        } catch (NotFoundApiException) {
            $this->addError('id', self::ERROR_WEBHOOK_DOES_NOT_EXIST);
        }
    }
}
