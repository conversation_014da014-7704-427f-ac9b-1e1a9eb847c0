<?php

declare(strict_types=1);

namespace STCompany\Validator;

use InvalidArgumentException;
use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Data\RolesTable;
use STCompany\Data\TeamsTable;
use STCompany\Data\UsersCompaniesRolesTable;
use STCompany\Entity\User;
use STLib\Validator\Validator;
use STUser\Data\UsersTable;
use STUser\Validator\UserValidator;

class CompanyUserValidator extends UserValidator
{
    public const string TEAM_NOT_EXISTS = 'Team is not exists';
    public const string ROLE_NOT_EXISTS = 'Role is not exists';
    public const string COMPANY_NOT_EXISTS = 'Company is not exists';
    public const string COMPANY_USER_EXISTS = 'User has already access to company';

    /**
     *
     * @var int
     */
    protected int $companyId;

    public function __construct(
        UsersTable $usersTable,
        private readonly RolesTable $rolesTable,
        private readonly TeamsTable $teamsTable,
        private readonly UsersCompaniesRolesTable $usersCompaniesRolesTable,
    ) {
        parent::__construct($usersTable);
    }


    /**
     *
     * @return int
     */
    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    /**
     *
     * @param int $companyId
     * @return CompanyUserValidator
     */
    public function setCompanyId(int $companyId): CompanyUserValidator
    {
        $this->companyId = $companyId;
        return $this;
    }

    /**
     *
     * @param User $user
     * @return Validator
     * @throws InvalidArgumentException
     */
    public function setInstance($user): Validator
    {
        if (!($user instanceof User)) {
            throw new InvalidArgumentException('User must be instance of "\STCompany\Entity\User"');
        }
        return parent::setInstance($user);
    }

    /**
     *
     * @see \STLib\Validator\ValidatorInterface::run()
     */
    public function run()
    {
        parent::run();

        if ($this->hasCheck('teams-exists')) {
            foreach ($this->getInstance()->getTeams() as $team) {
                try {
                    $this->teamsTable->getTeam($this->getCompanyId(), $team->getId());
                } catch (NotFoundApiException) {
                    $this->addError('team_id', static::TEAM_NOT_EXISTS);
                }
            }
        }

        if ($this->hasCheck('role-exists')) {
            $role = $this->getInstance()->getRole() ?? null;

            try {
                $this->rolesTable->getRole($this->getCompanyId(), $role->getId());
            } catch (NotFoundApiException) {
                $this->addError('role_id', static::ROLE_NOT_EXISTS);
            }
        }

        if ($this->hasCheck('company-user-not-exists')) {
            $userId = $this->getInstance()->getId() ?? null;

            if ($this->usersCompaniesRolesTable->isUserAlreadyExists($userId, $this->getCompanyId())) {
                $this->addError('user_id', static::COMPANY_USER_EXISTS);
            }
        }
    }
}
