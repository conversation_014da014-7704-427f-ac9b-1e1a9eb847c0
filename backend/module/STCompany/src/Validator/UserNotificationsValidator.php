<?php

declare(strict_types=1);

namespace STCompany\Validator;

use <PERSON><PERSON>\Validator\NotEmpty;
use <PERSON><PERSON>\Validator\StringLength;
use STLib\Validator\Validator;

class UserNotificationsValidator extends Validator
{
    protected const INCORRECT_TYPE = 'Type must be not empty and less than 255 symbols';
    protected const INCORRECT_DELIVERY_WAYS = 'Delivery types field is empty or content is not valid JSON';
    protected const INCORRECT_DELIVERY_WAY = 'Delivery way is not valid';
    protected const INCORRECT_TYPE_VALUE = 'Type is not valid';

    /**
     *
     * @see \STLib\Validator\ValidatorInterface::run()
     */
    public function run(): void
    {
        $notificationCollection = $this->getInstance();
        foreach ($notificationCollection as $notification) {
            $this->checkNotificationType($notification->getNotificationType());
            $this->checkDeliveryWays($notification->getDeliveryWays(), $notification->getNotificationType());
        }
    }

    /**
     *
     * @param string|null $type
     * @return void
     */
    private function checkNotificationType(?string $type): void
    {
        $lengthValidator = new StringLength([
            'min' => 1,
            'max' => 255,
        ]);
        if (!$lengthValidator->isValid($type)) {
            $this->addError($type, static::INCORRECT_TYPE);
        }

        if (!in_array($type, \STCompany\Entity\Notification\UserNotification::TYPES, true)) {
            $this->addError($type, static::INCORRECT_TYPE_VALUE);
        }
    }

    /**
     *
     * @param type $deliveryWays
     * @return void
     */
    private function checkDeliveryWays($deliveryWays, string $type): void
    {
        $validator = new NotEmpty();

        if (!$validator->isValid($deliveryWays)) {
            $this->addError($type, static::INCORRECT_DELIVERY_WAYS);
        }

        foreach ($deliveryWays as $deliveryType) {
            if (!in_array($deliveryType, \STCompany\Entity\Notification\UserNotification::DELIVERY_WAYS, true)) {
                $this->addError($type, static::INCORRECT_DELIVERY_WAY);
                break;
            }
        }
    }
}
