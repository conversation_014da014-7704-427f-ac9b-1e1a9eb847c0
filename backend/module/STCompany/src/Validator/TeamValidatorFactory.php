<?php

declare(strict_types=1);

namespace STCompany\Validator;

class TeamValidatorFactory implements \Laminas\ServiceManager\Factory\FactoryInterface
{
    /**
     *
     * @param \Interop\Container\ContainerInterface $container
     * @param string $requestedName
     * @param array $options
     * @return TeamValidator
     */
    public function createService(\Interop\Container\ContainerInterface $container, $requestedName, array $options = null): TeamValidator
    {
        return $this($container, $requestedName ?: PatternPluginManager::class, $this->creationOptions);
    }

    /**
     *
     * @param \Interop\Container\ContainerInterface $container
     * @param string $requestedName
     * @param array $options
     * @return TeamValidator
     */
    public function __invoke(\Interop\Container\ContainerInterface $container, $requestedName, array $options = null): TeamValidator
    {
        $companiesTable = $container->get(\STCompany\Data\CompaniesTable::class);
        $teamValidator = new TeamValidator();
        $teamValidator->addTable('companies', $companiesTable);
        return $teamValidator;
    }
}
