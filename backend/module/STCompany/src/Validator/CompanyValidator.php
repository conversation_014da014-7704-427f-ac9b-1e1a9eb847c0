<?php

declare(strict_types=1);

namespace STCompany\Validator;

use STLib\Validator\Validator;
use <PERSON><PERSON>\Validator\NotEmpty;
use <PERSON>inas\Validator\Between;

class CompanyValidator extends Validator
{
    public const EMPTY_NAME_ERROR = 'Empty name';
    public const TOO_LARGE_AVATAR_ERROR = 'Avatar must be less than 50 kb';
    public const THRESHOLD_RANGE_ERROR = 'Threshold must be between 0 and 1';

    /**
     *
     * @param \STCompany\Entity\Company $instance
     * @return Validator
     * @throws \InvalidArgumentException
     */
    public function setInstance($instance): Validator
    {
        if (!($instance instanceof \STCompany\Entity\Company)) {
            throw new \InvalidArgumentException('Request params must be instance of \STCompany\Entity\Company');
        }
        return parent::setInstance($instance);
    }

    /**
     *
     * @see \STLib\Validator\ValidatorInterface::run()
     */
    public function run()
    {
        if ($this->hasCheck('name')) {
            $name = $this->getInstance()->getName() ?? null;
            $nameValidator = new NotEmpty();
            if (!$nameValidator->isValid($name)) {
                $this->addError('name', self::EMPTY_NAME_ERROR);
            }
        }

        if ($this->hasCheck('threshold_bar')) {
            $thresholdBar = $this->getInstance()->getThresholdBar() ?? null;
            $thresholdValidator = new Between([
                'min' => 0,
                'max' => 1,
                'inclusive' => true,
            ]);
            if (!$thresholdValidator->isValid($thresholdBar)) {
                $this->addError('threshold_bar', self::THRESHOLD_RANGE_ERROR);
            }
        }

        if ($this->hasCheck('avatar') && !empty($this->getInstance()->getAvatar())) {
            $avatarValidator = new \Laminas\Validator\StringLength([
                'max' => 50 * 1024,
            ]);
            if (!$avatarValidator->isValid($this->getInstance()->getAvatar())) {
                $this->addError('avatar', self::TOO_LARGE_AVATAR_ERROR);
            }
        }
    }
}
