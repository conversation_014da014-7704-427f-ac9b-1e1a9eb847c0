<?php

declare(strict_types=1);

namespace STCompany\Validator;

class CompanyVocabularyWordValidatorFactory implements \Laminas\ServiceManager\Factory\FactoryInterface
{
    /**
     *
     * @param \Interop\Container\ContainerInterface $container
     * @param string $requestedName
     * @param array $options
     * @return CompanyVocabularyWordValidator
     */
    public function createService(\Interop\Container\ContainerInterface $container, $requestedName, array $options = null): CompanyVocabularyWordValidator
    {
        return $this($container, $requestedName ?: PatternPluginManager::class, $this->creationOptions);
    }

    /**
     *
     * @param \Interop\Container\ContainerInterface $container
     * @param string $requestedName
     * @param array $options
     * @return CompanyVocabularyWordValidator
     */
    public function __invoke(\Interop\Container\ContainerInterface $container, $requestedName, array $options = null): CompanyVocabularyWordValidator
    {
        $companiesVocabularyWordsTable = $container->get(\STCompany\Data\CompaniesVocabularyWordsTable::class);
        $companyVocabularyWordValidator = new CompanyVocabularyWordValidator();
        $companyVocabularyWordValidator->addTable('companies_vocabulary_words', $companiesVocabularyWordsTable);
        return $companyVocabularyWordValidator;
    }
}
