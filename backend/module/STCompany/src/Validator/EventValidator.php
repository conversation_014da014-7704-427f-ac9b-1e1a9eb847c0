<?php

declare(strict_types=1);

namespace STCompany\Validator;

use STLib\Validator\Validator;

class EventValidator extends Validator
{
    protected const INCORRECT_NAME = 'Event name must be not empty and less than 255 symbols';
    protected const NAME_IS_NOT_UNIQUE = 'Event name has already taken';
    protected const INVALID_COLOR = 'Invalid color value, hex format expected';
    protected const INCORRECT_ICON = 'Invalid icon value, string expected';
    protected const INCORRECT_SEARCH_WORDS_NAME = 'Search word name must be not empty';
    protected const INCORRECT_ALGO_EVENT_NAME = 'Algo event must be not empty';
    protected const ROLE_DB_RECORD_NOT_EXISTS_ERROR = 'Role is not exists';

    /**
     *
     * @param \STCompany\Entity\Event $event
     * @return Validator
     * @throws \InvalidArgumentException
     */
    public function setInstance($event): Validator
    {
        if (!($event instanceof \STCompany\Entity\Event\Event)) {
            throw new \InvalidArgumentException('Event must be instance of "\STCompany\Entity\Event"');
        }
        return parent::setInstance($event);
    }

    /**
     *
     * @see \STLib\Validator\ValidatorInterface::run()
     */
    public function run()
    {
        if ($this->hasCheck('name')) {
            $nameValidator = new \Laminas\Validator\StringLength([
                'min' => 1,
                'max' => 255,
            ]);
            if (!$nameValidator->isValid($this->getInstance()->getName())) {
                $this->addError('name', static::INCORRECT_NAME);
            }
        }

        if ($this->hasCheck('role-exists')) {
            $roleId = $this->getInstance()->getRole()->getId() ?? null;
            $roleExistsValidator = new \Laminas\Validator\Db\RecordExists([
                'table'   => $this->getTable('roles')->getTable(),
                'field'   => 'role_id',
                'adapter' => $this->getTable('roles')->getAdapter(),
            ]);
            if (!$roleExistsValidator->isValid($roleId)) {
                $this->addError('role_id', static::ROLE_DB_RECORD_NOT_EXISTS_ERROR);
            }
        }

        if ($this->hasCheck('name-unique')) {
            $nameIsNotExistsValidator = new \Laminas\Validator\Db\NoRecordExists([
                'table'   => $this->getTable('events')->getTable(),
                'field'   => 'event_name',
                'adapter' => $this->getTable('events')->getAdapter(),
            ]);
            $exclude = new \Laminas\Db\Sql\Where();
            $exclude
                    ->AND
                    ->equalTo('event_name', $this->getInstance()->getName())
                    ->AND
                    ->notEqualTo('event_id', $this->getInstance()->getId() ?? 0)
                    ->AND
                    ->equalTo('role_id', $this->getInstance()->getRole()->getId());
            $nameIsNotExistsValidator->setExclude($exclude);
            if (!$nameIsNotExistsValidator->isValid($this->getInstance()->getName())) {
                $this->addError('name', self::NAME_IS_NOT_UNIQUE);
            }
        }

        if ($this->hasCheck('icon')) {
            $iconValidator = new \Laminas\Validator\StringLength([
                'min' => 1,
                'max' => 255,
            ]);
            if (!$iconValidator->isValid($this->getInstance()->getIcon())) {
                $this->addError('icon', static::INCORRECT_ICON);
            }
        }

        if ($this->hasCheck('search-words')) {
            foreach ($this->getInstance()->getSearchWord() as $searchWord) {
                $searchWordValidator = new \Laminas\Validator\StringLength([
                    'min' => 1,
                    'max' => 255,
                ]);
                if (!$searchWordValidator->isValid($searchWord->getName())) {
                    $this->addError('search_words', static::INCORRECT_SEARCH_WORDS_NAME);
                }
            }
        }

        if ($this->hasCheck('algo-events')) {
            foreach ($this->getInstance()->getAlgoEvents() as $algoEvent) {
                $algoEventValidator = new \Laminas\Validator\StringLength([
                    'min' => 1,
                    'max' => 255,
                ]);
                if (!$algoEventValidator->isValid($algoEvent)) {
                    $this->addError('algo_events', static::INCORRECT_ALGO_EVENT_NAME);
                }
            }
        }
    }
}
