<?php

declare(strict_types=1);

namespace STCompany\Validator;

use Lam<PERSON>\Validator\Digits;
use STCompany\Entity\Company;
use STLib\Validator\Validator;

final class UpdateSettingsValidator extends Validator
{
    private const string ERROR_WRONG_SETTING_NAME = 'Wrong setting name.';
    private const string ERROR_WRONG_SETTING_VALUE = 'Wrong setting value.';

    private static array $availableSettings = [
        'is_checklists_enabled',
        'is_summarization_enabled',
        'is_s3_integration_enabled',
        'is_manual_import_enabled',
        'is_client_summary_enabled',
        'is_manual_import_enabled',
        'is_manage_llm_events_by_users_enabled',
        'llm_events_limit',
    ];

    private static array $boolSettings = [
        'is_checklists_enabled',
        'is_summarization_enabled',
        'is_s3_integration_enabled',
        'is_manual_import_enabled',
        'is_manage_llm_events_by_users_enabled'
    ];
    private static array $intSettings = [
        'llm_events_limit'
    ];

    public function run(): void
    {
        ['setting_name' => $settingName, 'value' => $value] = $this->getInstance();

        if (!in_array($settingName, self::$availableSettings)) {
            $this->addError('setting_name', self::ERROR_WRONG_SETTING_NAME);

            return;
        }

        if (in_array($settingName, self::$boolSettings)) {
            if (!is_bool($value)) {
                $this->addError('value', self::ERROR_WRONG_SETTING_VALUE);
                return;
            }
        }

        if (in_array($settingName, self::$intSettings)) {
            $intValidator = new Digits();
            if (!$intValidator->isValid($value)) {
                $this->addError('value', self::ERROR_WRONG_SETTING_VALUE);
            }
        }
    }
}
