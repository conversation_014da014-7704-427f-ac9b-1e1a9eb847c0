<?php

declare(strict_types=1);

namespace STCompany\Validator;

use InvalidArgumentException;
use <PERSON><PERSON>\Validator\Db\RecordExists;
use <PERSON><PERSON>\Validator\GreaterThan;
use <PERSON><PERSON>\Validator\InArray;
use <PERSON><PERSON>\Validator\StringLength;
use STCompany\Data\PermissionsTable;
use STCompany\Entity\Role;
use ST<PERSON>ib\Validator\Validator;

class RoleValidator extends Validator
{
    protected const INCORRECT_NAME = 'Role name must be not empty and less than 255 symbols';
    protected const INCORRECT_ROLE_TYPE = 'Incorrect role type';
    protected const COMPANY_DB_RECORD_NOT_EXISTS_ERROR = 'Company is not exists';
    protected const INVALID_PERMISSION_ID = 'Invalid permission id value';
    protected const INVALID_ACCESS_LEVEL = 'Invalid access level value';
    protected const INCORRECT_EFFECTIVE_TRESHOLD_BAR_VALUE = 'Effective treshold should be number';

    /**
     *
     * @var array
     */
    protected array $permissionIds = [];

    /**
     *
     * @param Role $role
     * @return Validator
     * @throws InvalidArgumentException
     */
    public function setInstance($role): Validator
    {
        if (!($role instanceof Role)) {
            throw new InvalidArgumentException('Role must be instance of "\STCompany\Entity\Role"');
        }
        return parent::setInstance($role);
    }

    /**
     *
     * @param array $permissionIds
     * @return Validator
     */
    public function setPermissionIds(array $permissionIds): Validator
    {
        $this->permissionIds = $permissionIds;
        return $this;
    }

    /**
     *
     * @see \STLib\Validator\ValidatorInterface::run()
     */
    public function run()
    {
        if ($this->hasCheck('name')) {
            $nameValidator = new StringLength([
                'min' => 1,
                'max' => 255,
            ]);
            if (!$nameValidator->isValid($this->getInstance()->getName())) {
                $this->addError('name', static::INCORRECT_NAME);
            }
        }

        if ($this->hasCheck('role_type')) {
            $availableRoleValidator = new InArray([
                'haystack' => [Role::COMPANY_SUPERVISOR_ROLE_TYPE, Role::MANAGER_ROLE_TYPE],
                'strict' => true,
            ]);
            if (!$availableRoleValidator->isValid($this->getInstance()->getRoleType())) {
                $this->addError('type', static::INCORRECT_ROLE_TYPE);
            }
        }

        if ($this->hasCheck('effective_call_threshold_bar')) {
            $effectiveCallThresholdBarValidator = new GreaterThan([
                'min' => 0,
                'inclusive' => true,
            ]);
            if (!$effectiveCallThresholdBarValidator->isValid($this->getInstance()->getEffectiveCallThresholdBar())) {
                $this->addError('effective_call_threshold_bar', static::INCORRECT_EFFECTIVE_TRESHOLD_BAR_VALUE);
            }
        }

        if ($this->hasCheck('company-exists')) {
            $companyId = $this->getInstance()->getCompanyId() ?? null;
            $companyExistsValidator = new RecordExists([
                'table' => $this->getTable('companies')->getTable(),
                'field' => 'company_id',
                'adapter' => $this->getTable('companies')->getAdapter(),
            ]);
            if (!$companyExistsValidator->isValid($companyId)) {
                $this->addError('company_id', static::COMPANY_DB_RECORD_NOT_EXISTS_ERROR);
            }
        }

        if ($this->hasCheck('permissions')) {
            foreach ($this->getInstance()->getPermissions() as $permission) {
                $permissionExistsValidator = new InArray([
                    'haystack' => $this->permissionIds,
                    'strict' => true,
                ]);
                if (!$permissionExistsValidator->isValid($permission->getId())) {
                    $this->addError('permissions', static::INVALID_PERMISSION_ID);
                }

                $accessLevelValidator = new InArray([
                    'haystack' => [
                        PermissionsTable::WRITE_PERMISSION,
                        PermissionsTable::READ_PERMISSION,
                    ],
                    'strict' => true,
                ]);
                if (!$accessLevelValidator->isValid($permission->getAccessLevel())) {
                    $this->addError('access_level', static::INVALID_ACCESS_LEVEL);
                }
            }
        }
    }
}
