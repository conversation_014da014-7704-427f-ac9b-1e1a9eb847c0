<?php

declare(strict_types=1);

namespace STCompany\Validator;

use STLib\Validator\Validator;

class EventCategoryValidator extends Validator
{
    protected const INCORRECT_NAME = 'Name must be not empty and less than 255 symbols';
    protected const DUPLICATE_NAME_ERROR = 'Name is already exists';
    protected const COLOR_DB_RECORD_NOT_EXISTS_ERROR = 'Color is not exists';
    protected const ROLE_DB_RECORD_NOT_EXISTS_ERROR = 'Role is not exists';

    /**
     *
     * @param \STCompany\Entity\Event\Category $category
     * @return Validator
     * @throws \InvalidArgumentException
     */
    public function setInstance($category): Validator
    {
        if (!($category instanceof \STCompany\Entity\Event\Category)) {
            throw new \InvalidArgumentException('Category must be instance of "\STCompany\Entity\Event\Category"');
        }
        return parent::setInstance($category);
    }

    /**
     *
     * @see \STLib\Validator\ValidatorInterface::run()
     */
    public function run()
    {
        if ($this->hasCheck('name')) {
            $nameValidator = new \Laminas\Validator\StringLength([
                'min' => 1,
                'max' => 255,
            ]);
            if (!$nameValidator->isValid($this->getInstance()->getName())) {
                $this->addError('name', static::INCORRECT_NAME);
            }
        }

        if ($this->hasCheck('name-not-exists')) {
            $nameNotExistsValidator = new \Laminas\Validator\Db\NoRecordExists([
                'table'   => $this->getTable('events_categories')->getTable(),
                'field'   => 'category_name',
                'adapter' => $this->getTable('events_categories')->getAdapter(),
            ]);
            $exclude = new \Laminas\Db\Sql\Where();
            $exclude
                    ->AND
                    ->equalTo('category_name', $this->getInstance()->getName())
                    ->AND
                    ->notEqualTo('category_id', $this->getInstance()->getId() ?? 0)
                    ->AND
                    ->equalTo('role_id', $this->getInstance()->getRole()->getId());
            $nameNotExistsValidator->setExclude($exclude);

            if (!$nameNotExistsValidator->isValid($this->getInstance()->getName())) {
                $this->addError('name', static::DUPLICATE_NAME_ERROR);
            }
        }

        if ($this->hasCheck('color-exists')) {
            $colorId = $this->getInstance()->getColor()->getId() ?? null;
            $colorExistsValidator = new \Laminas\Validator\Db\RecordExists([
                'table'   => $this->getTable('events_colors')->getTable(),
                'field'   => 'color_id',
                'adapter' => $this->getTable('events_colors')->getAdapter(),
            ]);
            if (!$colorExistsValidator->isValid($colorId)) {
                $this->addError('color_id', static::COLOR_DB_RECORD_NOT_EXISTS_ERROR);
            }
        }

        if ($this->hasCheck('role-exists')) {
            $roleId = $this->getInstance()->getRole()->getId() ?? null;
            $roleExistsValidator = new \Laminas\Validator\Db\RecordExists([
                'table'   => $this->getTable('roles')->getTable(),
                'field'   => 'role_id',
                'adapter' => $this->getTable('roles')->getAdapter(),
            ]);
            if (!$roleExistsValidator->isValid($roleId)) {
                $this->addError('role_id', static::ROLE_DB_RECORD_NOT_EXISTS_ERROR);
            }
        }
    }
}
