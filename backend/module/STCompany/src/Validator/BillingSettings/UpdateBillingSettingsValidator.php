<?php

declare(strict_types=1);

namespace STCompany\Validator\BillingSettings;

use Carbon\Carbon;
use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Entity\Company;
use STCompany\Service\Billing\BillingSettings\BillingSettingsSelector;

final class UpdateBillingSettingsValidator extends SaveBillingSettingsValidator
{
    private const string ERROR_SETTINGS_DOESNT_EXISTS = 'Billing settings doesn\'t exists.';
    private const string ERROR_WRONG_DURATION = 'End of trial period cannot be less than today.';

    public function __construct(private readonly BillingSettingsSelector $billingSettingsSelector)
    {
    }

    public function run(): void
    {
        $input = $this->getInstance();
        /** @var Company $company */
        ['company' => $company, 'duration' => $duration] = $input;

        if (!in_array($company->getStatus(), [Company::STATUS_TRIAL, Company::STATUS_TRIAL_EXPIRED], true)) {
            $this->addError(
                'company_id',
                'Billing settings changing available only for statuses: Trial, Trial Expired.'
            );

            return;
        }

        try {
            $this->billingSettingsSelector->getBillingSettings($company->getId());
        } catch (NotFoundApiException) {
            $this->addError(
                'company_id',
                self::ERROR_SETTINGS_DOESNT_EXISTS
            );

            return;
        }

        parent::run();

        // We do not allow changing the trial duration so that the new end of the trial period will be in the past.
        // The only thing you can do is extend the trial period from today.
        $createdAt = $company->getCreatedAt()->clone();
        $createdAt->startOfDay();
        $endDate = (Carbon::now())->startOfDay();

        // +1 to add current day
        $companyExistsDays = (int) $createdAt->diffInDays($endDate) + 1;

        if ($companyExistsDays > $duration) {
            $this->addError(
                'duration',
                self::ERROR_WRONG_DURATION
            );
        }
    }
}
