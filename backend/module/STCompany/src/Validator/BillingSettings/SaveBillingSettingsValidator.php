<?php

declare(strict_types=1);

namespace STCompany\Validator\BillingSettings;

use Laminas\Validator\Digits;
use STLib\Validator\Validator;

class SaveBillingSettingsValidator extends Validator
{
    private const string ERROR_WRONG_TYPE = ' must be an integer.';

    private array $settingsNames = [
        'duration',
        'calls_seconds',
        'chats_amount',
        'summarizations_amount',
        'checklists_amount',
        'each_checklists_calls_amount',
    ];

    public function run(): void
    {
        $input = $this->getInstance();

        $intValidator = new Digits();
        foreach ($this->settingsNames as $name) {
            if (!$intValidator->isValid($input[$name])) {
                $this->addError($name, $name . self::ERROR_WRONG_TYPE);

                return;
            }
        }
    }
}
