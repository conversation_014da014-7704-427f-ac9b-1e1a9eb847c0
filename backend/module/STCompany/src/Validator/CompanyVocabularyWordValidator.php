<?php

declare(strict_types=1);

namespace STCompany\Validator;

use STLib\Validator\Validator;

class CompanyVocabularyWordValidator extends Validator
{
    public const EMPTY_WORD_ERROR = 'Empty vocabulaty word';
    public const WORD_CONTAINS_SPACE_SYMBOL = 'Word contains space symbol';
    public const DUPLICATE_WORD = 'Vocabulary word has been already exists';

    /**
     *
     * @param \STCompany\Entity\CompanyVocabularyWord $request
     * @return Validator
     * @throws \InvalidArgumentException
     */
    public function setInstance($request): Validator
    {
        if (!($request instanceof \STCompany\Entity\CompanyVocabularyWord)) {
            throw new \InvalidArgumentException('Request should be object of class: ' . \STCompany\Entity\CompanyVocabularyWord::class);
        }
        return parent::setInstance($request);
    }

    /**
     *
     * @see \STLib\Validator\ValidatorInterface::run()
     */
    public function run()
    {
        if (!(new \Laminas\Validator\NotEmpty())->isValid($this->getInstance()->getWord())) {
            $this->addError('word', self::EMPTY_WORD_ERROR);
        }

        if (
            !(new \Laminas\Validator\Regex([
            'pattern' => '/^[^\s]+$/',
            ]))->isValid($this->getInstance()->getWord())
        ) {
            $this->addError('word', self::WORD_CONTAINS_SPACE_SYMBOL);
        }

        $exclude = new \Laminas\Db\Sql\Where();
        $exclude
                ->AND
                ->equalTo('word', $this->getInstance()->getWord())
                ->AND
                ->notEqualTo('word_id', $this->getInstance()->getId() ?? 0)
                ->AND
                ->equalTo('company_id', $this->getInstance()->getCompanyId());
        $duplicateWordValidator = new \Laminas\Validator\Db\NoRecordExists([
            'table' => $this->getTable('companies_vocabulary_words')->getTable(),
            'field' => 'word',
            'adapter' => $this->getTable('companies_vocabulary_words')->getAdapter(),
            'exclude' => $exclude,
        ]);
        if (!$duplicateWordValidator->isValid($this->getInstance()->getWord())) {
            $this->addError('word', static::DUPLICATE_WORD);
        }
    }
}
