<?php

namespace STCompany\Validator;

use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Data\CompanyLlmEventsTable;
use STLib\Validator\Validator;
use STLlmEvent\Data\LlmEventsTable;

class DisconnectLlmEventValidator extends Validator
{
    public function __construct(
        private readonly LlmEventsTable $llmEventsTable,
        private readonly CompanyLlmEventsTable $companyLlmEventsTable,
    ) {
    }

    protected const string ERROR_EVENT_DOES_NOT_EXIST = 'The event does not exists.';
    protected const string ERROR_EVENT_WAS_NOT_CONNECTED = 'The event was not connected.';

    public function run(): void
    {
        /** @var array $input */
        $input = $this->getInstance();

        ['llm_event_id' => $llmEventId, 'company_id' => $companyId] = $input;

        try {
            $this->llmEventsTable->getLlmEvent($llmEventId);
        } catch (NotFoundApiException) {
            $this->addError('id', self::ERROR_EVENT_DOES_NOT_EXIST);

            return;
        }

        try {
            $this->companyLlmEventsTable->getLlmEvent($llmEventId, $companyId);
        } catch (NotFoundApiException) {
            $this->addError('id', self::ERROR_EVENT_WAS_NOT_CONNECTED);
        }
    }
}
