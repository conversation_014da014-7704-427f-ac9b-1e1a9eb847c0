<?php

declare(strict_types=1);

namespace STCompany\Validator;

class RoleValidatorFactory implements \Laminas\ServiceManager\Factory\FactoryInterface
{
    /**
     *
     * @param \Interop\Container\ContainerInterface $container
     * @param type $requestedName
     * @param array $options
     * @return RoleValidator
     */
    public function createService(\Interop\Container\ContainerInterface $container, $requestedName, array $options = null): RoleValidator
    {
        return $this($container, $requestedName ?: PatternPluginManager::class, $this->creationOptions);
    }

    /**
     *
     * @param \Interop\Container\ContainerInterface $container
     * @param string $requestedName
     * @param array $options
     * @return RoleValidator
     */
    public function __invoke(\Interop\Container\ContainerInterface $container, $requestedName, array $options = null): RoleValidator
    {
        $companiesTable = $container->get(\STCompany\Data\CompaniesTable::class);
        $roleValidator = new RoleValidator();
        $roleValidator->addTable('companies', $companiesTable);
        $permissionsTable = $container->get(\STCompany\Data\PermissionsTable::class);
        $permissionIds = array_column($permissionsTable->getPermissions()->toArray(), 'permission_id');
        $roleValidator->setPermissionIds($permissionIds);
        return $roleValidator;
    }
}
