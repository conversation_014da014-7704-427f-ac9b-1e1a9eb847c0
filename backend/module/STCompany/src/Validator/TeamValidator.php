<?php

declare(strict_types=1);

namespace STCompany\Validator;

use STLib\Validator\Validator;

class TeamValidator extends Validator
{
    protected const INCORRECT_NAME = 'Team name must be not empty and less than 255 symbols';
    protected const COMPANY_DB_RECORD_NOT_EXISTS_ERROR = 'Company is not exists';

    /**
     *
     * @param \STCompany\Entity\Team $team
     * @return Validator
     * @throws \InvalidArgumentException
     */
    public function setInstance($team): Validator
    {
        if (!($team instanceof \STCompany\Entity\Team)) {
            throw new \InvalidArgumentException('Team must be instance of "\STCompany\Entity\Team"');
        }
        return parent::setInstance($team);
    }

    /**
     *
     * @see \STLib\Validator\ValidatorInterface::run()
     */
    public function run()
    {
        if ($this->hasCheck('name')) {
            $nameValidator = new \Laminas\Validator\StringLength([
                'min' => 1,
                'max' => 255,
            ]);
            if (!$nameValidator->isValid($this->getInstance()->getName())) {
                $this->addError('name', static::INCORRECT_NAME);
            }
        }

        if ($this->hasCheck('company-exists')) {
            $companyId = $this->getInstance()->getCompanyId() ?? null;
            $companyExistsValidator = new \Laminas\Validator\Db\RecordExists([
                'table'   => $this->getTable('companies')->getTable(),
                'field'   => 'company_id',
                'adapter' => $this->getTable('companies')->getAdapter(),
            ]);
            if (!$companyExistsValidator->isValid($companyId)) {
                $this->addError('company_id', static::COMPANY_DB_RECORD_NOT_EXISTS_ERROR);
            }
        }
    }
}
