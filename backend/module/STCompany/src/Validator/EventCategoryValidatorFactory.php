<?php

declare(strict_types=1);

namespace STCompany\Validator;

class EventCategoryValidatorFactory implements \Laminas\ServiceManager\Factory\FactoryInterface
{
    /**
     *
     * @param \Interop\Container\ContainerInterface $container
     * @param type $requestedName
     * @param array $options
     * @return RoleValidator
     */
    public function createService(\Interop\Container\ContainerInterface $container, $requestedName, array $options = null): EventCategoryValidator
    {
        return $this($container, $requestedName ?: PatternPluginManager::class, $this->creationOptions);
    }

    /**
     *
     * @param \Interop\Container\ContainerInterface $container
     * @param string $requestedName
     * @param array $options
     * @return RoleValidator
     */
    public function __invoke(\Interop\Container\ContainerInterface $container, $requestedName, array $options = null): EventCategoryValidator
    {
        $rolesTable = $container->get(\STCompany\Data\RolesTable::class);
        $eventsColorsTable = $container->get(\STCompany\Data\EventsColorsTable::class);
        $eventsCategoriesTable = $container->get(\STCompany\Data\EventsCategoriesTable::class);
        $eventCategoryValidator = new EventCategoryValidator();
        $eventCategoryValidator->addTable('roles', $rolesTable);
        $eventCategoryValidator->addTable('events_colors', $eventsColorsTable);
        $eventCategoryValidator->addTable('events_categories', $eventsCategoriesTable);
        return $eventCategoryValidator;
    }
}
