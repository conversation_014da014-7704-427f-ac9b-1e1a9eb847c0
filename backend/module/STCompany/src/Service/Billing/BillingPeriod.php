<?php

declare(strict_types=1);

namespace STCompany\Service\Billing;

use Carbon\CarbonPeriod;

class BillingPeriod
{
    public function __construct(
        private readonly CarbonPeriod $inclusionPeriod,
        private readonly ?CarbonPeriod $excludedPeriod = null,
    ) {
    }

    public function getInclusionPeriod(): CarbonPeriod
    {
        return $this->inclusionPeriod;
    }

    public function getExcludedPeriod(): ?CarbonPeriod
    {
        return $this->excludedPeriod;
    }
}
