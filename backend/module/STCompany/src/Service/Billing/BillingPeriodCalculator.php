<?php

declare(strict_types=1);

namespace STCompany\Service\Billing;

use Carbon\Carbon;
use Carbon\CarbonPeriod;
use RuntimeException;
use STCompany\Entity\BillingSettings\BillingSettings;
use STCompany\Entity\Company;

class BillingPeriodCalculator
{
    public function calculate(Company $company, ?BillingSettings $billingSettings = null): BillingPeriod
    {
        if (in_array($company->getStatus(), [Company::STATUS_TRIAL, Company::STATUS_TRIAL_EXPIRED], true)) {
            return $this->calculateTrialPeriod($company, $billingSettings);
        }

        if (in_array($company->getStatus(), [Company::STATUS_ACTIVE, Company::STATUS_SUSPENDED], true)) {
            return $this->calculateNonTrialPeriod($company, $billingSettings);
        }

        throw new RuntimeException('Unknown company status: Some unknown status.');
    }

    public function calculateTrialPeriod(Company $company, ?BillingSettings $billingSettings = null): BillingPeriod
    {
        if (is_null($billingSettings)) {
            throw new RuntimeException('Cannot calculate billing period without billing settings.');
        }

        $inclusionPeriod = $this->calculateTrialInclusionPeriod($company, $billingSettings);

        return new BillingPeriod($inclusionPeriod);
    }

    private function calculateNonTrialPeriod(Company $company, ?BillingSettings $billingSettings = null): BillingPeriod
    {
        if (is_null($billingSettings)) {
            return new BillingPeriod($this->calculateNonTrialInclusionPeriod($company));
        }

        $trialPeriod = $this->calculateTrialInclusionPeriod($company, $billingSettings);

        $inclusionPeriod = $this->calculateNonTrialInclusionPeriod($company);

        if ($inclusionPeriod->getStartDate() > $trialPeriod->getEndDate()) {
            return new BillingPeriod($inclusionPeriod);
        }

        $excludedPeriod = CarbonPeriod::create($trialPeriod->getStartDate(), $trialPeriod->getEndDate());

        return new BillingPeriod($inclusionPeriod, $excludedPeriod);
    }

    private function calculateTrialInclusionPeriod(Company $company, BillingSettings $billingSettings): CarbonPeriod
    {
        $startDate = $company->getCreatedAt();
        $endDate = (clone $company->getCreatedAt())->addDays($billingSettings->getDuration());

        return CarbonPeriod::create($startDate, $endDate);
    }

    private function calculateNonTrialInclusionPeriod(Company $company): CarbonPeriod
    {
        $startDate = Carbon::now()->copy()->startOfMonth();
        $endDate = Carbon::now()->copy()->endOfMonth();

        return CarbonPeriod::create($startDate, $endDate);
    }
}
