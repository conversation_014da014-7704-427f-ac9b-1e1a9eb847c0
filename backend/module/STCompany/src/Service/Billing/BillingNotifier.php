<?php

declare(strict_types=1);

namespace STCompany\Service\Billing;

use STCompany\Entity\CompanyCollection;
use STCompany\Entity\Role;
use STCompany\Service\Interfaces\ConfigurationInterface;
use STCompany\Service\UserService;
use STMail\Service\MailService;

class BillingNotifier
{
    public function __construct(
        private readonly UserService $userService,
        private readonly ConfigurationInterface $configuration,
        private readonly MailService $mailService,
    ) {
    }

    public function notifyTrialEndsIn3Days(CompanyCollection $companies): void
    {
        $this->notify($companies, 'trial-period-3-days-left');
    }

    public function notifyTrialEnded(CompanyCollection $companies): void
    {
        $this->notify($companies, 'trial-period-ended');
    }

    private function notify(CompanyCollection $companies, string $templateName): void
    {
        $supportManagerEmail = $this->configuration->get('application')['support_manager_email'];

        $mailDataTemplate = [
            'template_id' => $templateName,
            'substitutions' => [
                'dynamic_html' => [
                    'support_email_link' => 'mailto:' . $supportManagerEmail,
                ],
                'client_name' => null,
            ],
        ];

        foreach ($companies as $company) {
            $companyAdmins = $this->userService->getUsersByRoleType($company->getId(), [Role::COMPANY_ADMIN_ROLE_TYPE]);
            foreach ($companyAdmins as $admin) {
                $mailData = $mailDataTemplate;
                $mailData['substitutions']['client_name'] = $admin->getName();

                $this->mailService->addToQueue($company->getFrontId(), $admin, $mailData);
            }
        }
    }
}
