<?php

declare(strict_types=1);

namespace STCompany\Service\Billing\FeaturesCheckers;

use STCall\Entity\Call;
use STCompany\Entity\Company;

class ActiveStatusFeaturesAvailableChecker implements FeaturesAvailableCheckerInterface
{
    /**
     * @param Company $company
     * @return bool
     */
    public function isCallAnalysisAvailable(Company $company): bool
    {
        return true;
    }

    public function isChatAnalysisAvailable(Company $company): bool
    {
        return true;
    }

    /**
     * @param Call $call
     * @param Company $company
     * @return bool
     */
    public function isCallAnalysisAvailableForCall(Call $call, Company $company): bool
    {
        return $company->getPaidTranscribingTime() >= $call->getDuration();
    }

    public function isSummarizationAvailable(Company $company): bool
    {
        return $company->isSummarizationEnabled();
    }

    public function isClientSummarizationAvailable(Company $company): bool
    {
        return $company->isClientSummaryEnabled();
    }

    public function isChecklistAvailable(int $checklistId, Company $company): bool
    {
        return true;
    }
}
