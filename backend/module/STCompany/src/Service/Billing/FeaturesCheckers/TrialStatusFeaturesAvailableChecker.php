<?php

declare(strict_types=1);

namespace STCompany\Service\Billing\FeaturesCheckers;

use STApi\Entity\Exception\NotFoundApiException;
use STCall\Entity\Call;
use STCompany\Entity\BillingSettings\BillingSettings;
use STCompany\Entity\Company;
use STCompany\Service\Billing\BillingInfoGetter;
use STCompany\Service\Billing\BillingSettings\BillingSettingsSelector;

class TrialStatusFeaturesAvailableChecker implements FeaturesAvailableCheckerInterface
{
    public function __construct(
        private readonly BillingSettingsSelector $billingSettingsSelector,
        private readonly BillingInfoGetter $billingInfoGetter,
    ) {
    }

    /**
     * @param Company $company
     * @return bool
     */
    public function isCallAnalysisAvailable(Company $company): bool
    {
        $billingSettings = $this->getBillingSettings($company->getId());
        if (!$billingSettings) {
            return false;
        }

        $spentCallsSeconds = $this->billingInfoGetter->getTrialCallsSeconds($company, $billingSettings);

        return $spentCallsSeconds < $billingSettings->getCallsSeconds();
    }

    public function isChatAnalysisAvailable(Company $company): bool
    {
        $billingSettings = $this->getBillingSettings($company->getId());
        if (!$billingSettings) {
            return false;
        }

        $spentChatsAmount = $this->billingInfoGetter->getTrialChatsAmount($company, $billingSettings);

        return $spentChatsAmount < $billingSettings->getChatsAmount();
    }

    /**
     * @param Call $call
     * @param Company $company
     * @return bool
     */
    public function isCallAnalysisAvailableForCall(Call $call, Company $company): bool
    {
        return true;
    }

    public function isSummarizationAvailable(Company $company): bool
    {
        if (!$company->isSummarizationEnabled()) {
            return false;
        }

        $billingSettings = $this->getBillingSettings($company->getId());
        if (!$billingSettings) {
            return false;
        }

        $processedSummarizations = $this->billingInfoGetter->getTrialSummarizationsAmount($company, $billingSettings);

        return $processedSummarizations < $billingSettings->getSummarizationsAmount();
    }

    public function isClientSummarizationAvailable(Company $company): bool
    {
        return $company->isClientSummaryEnabled();
    }

    public function isChecklistAvailable(int $checklistId, Company $company): bool
    {
        $billingSettings = $this->getBillingSettings($company->getId());
        if (!$billingSettings) {
            return false;
        }

        $processedChecklistCalls = $this->billingInfoGetter->getTrialChecklistCallsAmount(
            $company,
            $checklistId,
            $billingSettings
        );

        return $processedChecklistCalls < $billingSettings->getEachChecklistsCallsAmount();
    }

    private function getBillingSettings(int $companyId): ?BillingSettings
    {
        try {
            return $this->billingSettingsSelector->getBillingSettings($companyId);
        } catch (NotFoundApiException) {
            return null;
        }
    }
}
