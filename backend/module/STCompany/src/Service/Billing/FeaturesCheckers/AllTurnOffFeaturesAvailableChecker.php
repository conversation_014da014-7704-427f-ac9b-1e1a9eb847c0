<?php

declare(strict_types=1);

namespace STCompany\Service\Billing\FeaturesCheckers;

use STCall\Entity\Call;
use STCompany\Entity\Company;

abstract class AllTurnOffFeaturesAvailableChecker
{
    /**
     * @param Company $company
     * @return bool
     */
    public function isCallAnalysisAvailable(Company $company): bool
    {
        return false;
    }

    public function isChatAnalysisAvailable(Company $company): bool
    {
        return false;
    }

    /**
     * @param Call $call
     * @param Company $company
     * @return bool
     */
    public function isCallAnalysisAvailableForCall(Call $call, Company $company): bool
    {
        return false;
    }

    public function isSummarizationAvailable(Company $company): bool
    {
        return false;
    }

    public function isClientSummarizationAvailable(Company $company): bool
    {
        return false;
    }

    public function isChecklistAvailable(int $checklistId, Company $company): bool
    {
        return false;
    }
}
