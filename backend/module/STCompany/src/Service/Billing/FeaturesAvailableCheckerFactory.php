<?php

declare(strict_types=1);

namespace STCompany\Service\Billing;

use RuntimeException;
use STCompany\Entity\Company;
use STCompany\Service\Billing\FeaturesCheckers\ActiveStatusFeaturesAvailableChecker;
use STCompany\Service\Billing\FeaturesCheckers\FeaturesAvailableCheckerInterface;
use STCompany\Service\Billing\FeaturesCheckers\SuspendedStatusFeaturesAvailableChecker;
use STCompany\Service\Billing\FeaturesCheckers\TrialExpiredStatusFeaturesAvailableChecker;
use STCompany\Service\Billing\FeaturesCheckers\TrialStatusFeaturesAvailableChecker;

class FeaturesAvailableCheckerFactory
{
    public function __construct(
        private readonly TrialStatusFeaturesAvailableChecker $trialStatusFeaturesAvailableChecker,
        private readonly TrialExpiredStatusFeaturesAvailableChecker $trialExpiredStatusFeaturesAvailableChecker,
        private readonly ActiveStatusFeaturesAvailableChecker $activeStatusFeaturesAvailableChecker,
        private readonly SuspendedStatusFeaturesAvailableChecker $suspendedStatusFeaturesAvailableChecker,
    ) {
    }

    public function create(string $status): FeaturesAvailableCheckerInterface
    {
        return match ($status) {
            Company::STATUS_TRIAL => $this->trialStatusFeaturesAvailableChecker,
            Company::STATUS_TRIAL_EXPIRED => $this->trialExpiredStatusFeaturesAvailableChecker,
            Company::STATUS_ACTIVE => $this->activeStatusFeaturesAvailableChecker,
            Company::STATUS_SUSPENDED => $this->suspendedStatusFeaturesAvailableChecker,
            default => throw new RuntimeException('Unknown company status: ' . $status),
        };
    }
}
