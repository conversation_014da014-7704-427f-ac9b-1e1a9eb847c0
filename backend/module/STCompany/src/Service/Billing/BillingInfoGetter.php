<?php

declare(strict_types=1);

namespace STCompany\Service\Billing;

use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Entity\BillingSettings\BillingSettings;
use STCompany\Entity\Company;
use STCompany\Service\Billing\BillingSettings\BillingSettingsSelector;
use STCompany\Service\Checklist\ChecklistService;
use STCompany\Service\Interfaces\BillingRepositoryInterface;

class BillingInfoGetter
{
    public function __construct(
        private readonly BillingSettingsSelector $billingSettingsSelector,
        private readonly BillingPeriodCalculator $billingPeriodCalculator,
        private readonly BillingRepositoryInterface $billingRepository,
        private readonly ChecklistService $checklistService,
    ) {
    }

    /**
     * @param Company $company
     * @return BillingInfo
     */
    public function getBillingInfo(Company $company): BillingInfo
    {
        try {
            $billingSettings = $this->billingSettingsSelector->getBillingSettings($company->getId());
            $billingPeriod = $this->billingPeriodCalculator->calculate($company, $billingSettings);
        } catch (NotFoundApiException $e) {
            $billingPeriod = $this->billingPeriodCalculator->calculate($company);
        }

        $billingInfo = new BillingInfo();

        $billingInfo->setSpentCallsSeconds(
            $this->billingRepository->getCallsSecondsDurationByPeriod($company->getId(), $billingPeriod)
        );

        $billingInfo->setSpentChats(
            $this->billingRepository->getChatsByPeriod($company->getId(), $billingPeriod)
        );

        $billingInfo->setSpentSummarizations(
            $this->billingRepository->getSummarizationsByPeriod($company->getId(), $billingPeriod)
        );

        $billingInfo->setSpentClientSummarizationCalls(
            $this->billingRepository->getClientSummarizationCallsByPeriod($company->getId(), $billingPeriod)
        );

        $billingInfo->setChecklistsData($this->getChecklistsData($company, $billingPeriod));

        return $billingInfo;
    }

    private function getChecklistsData(Company $company, BillingPeriod $billingPeriod): array
    {
        $checklists = $this->checklistService->getCompanyChecklists($company->getId());

        $data = [];

        foreach ($checklists as $checklist) {
            $data[] = [
                'id' => $checklist->getId(),
                'name' => $checklist->getName(),
                'analyzed' => $this->billingRepository->getChecklistCallsByPeriod(
                    $company->getId(),
                    $checklist->getId(),
                    $billingPeriod
                ),
            ];
        }

        return $data;
    }

    /**
     * @param Company $company
     * @param BillingSettings $billingSettings
     * @return int
     */
    public function getTrialCallsSeconds(Company $company, BillingSettings $billingSettings): int
    {
        $billingPeriod = $this->billingPeriodCalculator->calculateTrialPeriod($company, $billingSettings);

        return $this->billingRepository->getCallsSecondsDurationByPeriod($company->getId(), $billingPeriod);
    }

    /**
     * @param Company $company
     * @param BillingSettings $billingSettings
     * @return int
     */
    public function getTrialChatsAmount(Company $company, BillingSettings $billingSettings): int
    {
        $billingPeriod = $this->billingPeriodCalculator->calculateTrialPeriod($company, $billingSettings);

        return $this->billingRepository->getChatsByPeriod($company->getId(), $billingPeriod);
    }

    public function getTrialSummarizationsAmount(Company $company, BillingSettings $billingSettings): int
    {
        $billingPeriod = $this->billingPeriodCalculator->calculateTrialPeriod($company, $billingSettings);

        return $this->billingRepository->getSummarizationsByPeriod($company->getId(), $billingPeriod);
    }

    public function getTrialClientSummarizationCallsAmount(Company $company, BillingSettings $billingSettings): int
    {
        $billingPeriod = $this->billingPeriodCalculator->calculateTrialPeriod($company, $billingSettings);

        return $this->billingRepository->getClientSummarizationCallsByPeriod($company->getId(), $billingPeriod);
    }

    public function getTrialChecklistCallsAmount(
        Company $company,
        int $checklistId,
        BillingSettings $billingSettings
    ): int {
        $billingPeriod = $this->billingPeriodCalculator->calculateTrialPeriod($company, $billingSettings);

        return $this->billingRepository->getChecklistCallsByPeriod($company->getId(), $checklistId, $billingPeriod);
    }
}
