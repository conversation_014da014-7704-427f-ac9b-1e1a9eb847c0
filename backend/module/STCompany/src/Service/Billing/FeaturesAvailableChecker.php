<?php

declare(strict_types=1);

namespace STCompany\Service\Billing;

use STCall\Entity\Call;
use STCompany\Entity\Company;

final class FeaturesAvailableChecker
{
    public function __construct(
        private readonly FeaturesAvailableCheckerFactory $featuresAvailableCheckerFactory,
    ) {
    }

    /**
     * @param Company $company
     * @return bool
     */
    public function isCallAnalysisAvailable(Company $company): bool
    {
        $checker = $this->featuresAvailableCheckerFactory->create($company->getStatus());

        return $checker->isCallAnalysisAvailable($company);
    }

    public function isChatAnalysisAvailable(Company $company): bool
    {
        $checker = $this->featuresAvailableCheckerFactory->create($company->getStatus());

        return $checker->isChatAnalysisAvailable($company);
    }

    /**
     * @param Call $call
     * @param Company $company
     * @return bool
     */
    public function isCallAnalysisAvailableForCall(Call $call, Company $company): bool
    {
        $checker = $this->featuresAvailableCheckerFactory->create($company->getStatus());

        return $checker->isCallAnalysisAvailableForCall($call, $company);
    }

    public function isSummarizationAvailable(Company $company): bool
    {
        $checker = $this->featuresAvailableCheckerFactory->create($company->getStatus());

        return $checker->isSummarizationAvailable($company);
    }

    public function isClientSummarizationAvailable(Company $company): bool
    {
        $checker = $this->featuresAvailableCheckerFactory->create($company->getStatus());

        return $checker->isClientSummarizationAvailable($company);
    }

    public function isChecklistAvailable(int $checklistId, Company $company): bool
    {
        $checker = $this->featuresAvailableCheckerFactory->create($company->getStatus());

        return $checker->isChecklistAvailable($checklistId, $company);
    }
}
