<?php

declare(strict_types=1);

namespace STCompany\Service\Billing\BillingSettings;

use ST<PERSON>pi\Entity\Exception\NotFoundApiException;
use STCompany\Data\BillingSettingsTable;
use STCompany\Data\CompaniesTable;
use STCompany\Entity\BillingSettings\BillingSettings;
use STCompany\Entity\Company;
use STLib\Db\TransactionService;
use Throwable;

class BillingSettingsSaver
{
    public const int DEFAULT_TRIAL_DURATION_PERIOD_IN_DAYS = 14;
    public const int DEFAULT_AVAILABLE_CALLS_SECONDS = 72000;
    public const int DEFAULT_AVAILABLE_CHATS_AMOUNT = 100;
    public const int DEFAULT_AVAILABLE_SUMMARIZATIONS_AMOUNT = 100;
    public const int DEFAULT_AVAILABLE_CHECKLISTS_AMOUNT = 10;
    public const int DEFAULT_AVAILABLE_EACH_CHECKLISTS_CALLS_AMOUNT = 100;

    public function __construct(
        private readonly BillingSettingsTable $billingSettingsTable,
        private readonly CompaniesTable $companiesTable,
        private readonly TransactionService $transactionService,
    ) {
    }

    public function createWithDefaultSettings(Company $company): void
    {
        $billingSettings = new BillingSettings();
        $billingSettings->setCompanyId($company->getId());
        $billingSettings->setDuration(self::DEFAULT_TRIAL_DURATION_PERIOD_IN_DAYS);
        $billingSettings->setCallsSeconds(self::DEFAULT_AVAILABLE_CALLS_SECONDS);
        $billingSettings->setChatsAmount(self::DEFAULT_AVAILABLE_CHATS_AMOUNT);
        $billingSettings->setSummarizationsAmount(self::DEFAULT_AVAILABLE_SUMMARIZATIONS_AMOUNT);
        $billingSettings->setChecklistsAmount(self::DEFAULT_AVAILABLE_CHECKLISTS_AMOUNT);
        $billingSettings->setEachChecklistsCallsAmount(self::DEFAULT_AVAILABLE_EACH_CHECKLISTS_CALLS_AMOUNT);

        $this->billingSettingsTable->createBillingSettings($billingSettings);
    }

    /**
     * @throws NotFoundApiException
     * @throws Throwable
     */
    public function update(
        Company $company,
        int $duration,
        int $callsSeconds,
        int $chatsAmount,
        int $summarizationsAmount,
        int $checklistsAmount,
        int $eachChecklistsCallsAmount
    ): BillingSettings {
        $billingSettings = $this->billingSettingsTable->getBillingSettings($company->getId());

        $billingSettings->setCompanyId($company->getId());
        $billingSettings->setDuration($duration);
        $billingSettings->setCallsSeconds($callsSeconds);
        $billingSettings->setChatsAmount($chatsAmount);
        $billingSettings->setSummarizationsAmount($summarizationsAmount);
        $billingSettings->setChecklistsAmount($checklistsAmount);
        $billingSettings->setEachChecklistsCallsAmount($eachChecklistsCallsAmount);

        if ($company->getStatus() === Company::STATUS_TRIAL_EXPIRED) {
            $this->transactionService->beginTransaction();
            try {
                $this->billingSettingsTable->updateBillingSettings($billingSettings);
                $this->companiesTable->partialUpdateCompany($company->getId(), ['status' => Company::STATUS_TRIAL]);
                $this->transactionService->commit();

                return $billingSettings;
            } catch (Throwable $e) {
                $this->transactionService->rollBack();

                throw $e;
            }
        }

        $this->billingSettingsTable->updateBillingSettings($billingSettings);

        return $billingSettings;
    }
}
