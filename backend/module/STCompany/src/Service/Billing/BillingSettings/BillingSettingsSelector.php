<?php

declare(strict_types=1);

namespace STCompany\Service\Billing\BillingSettings;

use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Data\BillingSettingsTable;
use STCompany\Entity\BillingSettings\BillingSettings;

class BillingSettingsSelector
{
    public function __construct(private readonly BillingSettingsTable $billingSettingsTable)
    {
    }

    /**
     * @throws NotFoundApiException
     */
    public function getBillingSettings(int $companyId): BillingSettings
    {
        return $this->billingSettingsTable->getBillingSettings($companyId);
    }
}
