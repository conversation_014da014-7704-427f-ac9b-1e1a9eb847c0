<?php

declare(strict_types=1);

namespace STCompany\Service\Billing;

class BillingInfo
{
    private int $spentCallsSeconds;

    private int $spentChats;

    private int $spentSummarizations;

    private int $spentClientSummarizationCalls;

    private array $checklistsData;

    public function getSpentCallsSeconds(): int
    {
        return $this->spentCallsSeconds;
    }

    public function setSpentCallsSeconds(int $spentCallsSeconds): void
    {
        $this->spentCallsSeconds = $spentCallsSeconds;
    }

    public function getSpentChats(): int
    {
        return $this->spentChats;
    }

    public function setSpentChats(int $spentChats): void
    {
        $this->spentChats = $spentChats;
    }

    public function getSpentSummarizations(): int
    {
        return $this->spentSummarizations;
    }

    public function setSpentSummarizations(int $spentSummarizations): void
    {
        $this->spentSummarizations = $spentSummarizations;
    }

    public function getSpentClientSummarizationCalls(): int
    {
        return $this->spentClientSummarizationCalls;
    }

    public function setSpentClientSummarizationCalls(int $spentClientSummarizationCalls): void
    {
        $this->spentClientSummarizationCalls = $spentClientSummarizationCalls;
    }

    public function getChecklistsData(): array
    {
        return $this->checklistsData;
    }

    public function setChecklistsData(array $checklistsData): void
    {
        $this->checklistsData = $checklistsData;
    }
}
