<?php

declare(strict_types=1);

namespace STCompany\Service;

use ReflectionException;
use STCompany\Data\EventsColorsTable;
use STCompany\Entity\Event\Category;
use STCompany\Entity\Event\CategoryCollection;
use STCompany\Entity\Event\Color;
use STCompany\Entity\Event\Event;
use STCompany\Entity\Event\EventCollection;
use STCompany\Entity\Role;

class EventService
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;
    use \STLib\Db\ProvidesTransaction;
    use \STCall\Service\Precalculation\CallPrecalculationManagerServiceTrait;

    /**
     *
     * @var \STCompany\Data\EventsCategoriesTable
     */
    protected \STCompany\Data\EventsCategoriesTable $eventsCategoriesTable;

    /**
     *
     * @var \STCompany\Data\EventsTable
     */
    protected \STCompany\Data\EventsTable $eventsTable;

    /**
     *
     * @var \STCompany\Data\EventsSearchWordsTable
     */
    protected \STCompany\Data\EventsSearchWordsTable $eventsSearchWordsTable;

    /**
     *
     * @var \STCompany\Data\EventsAlgoEventsTable
     */
    protected \STCompany\Data\EventsAlgoEventsTable $eventsAlgoEventsTable;

    /**
     *
     * @var EventsColorsTable
     */
    protected EventsColorsTable $eventsColorsTable;

    /**
     *
     * @var \STCall\Data\CallsAlgoEventsTable
     */
    protected \STCall\Data\CallsAlgoEventsTable $callsAlgoEventsTable;

    /**
     *
     * @var \STCompany\Data\RolesTable
     */
    protected \STCompany\Data\RolesTable $rolesTable;

    /**
     *
     * @var \STAlgo\Data\AlgoEventsTable
     */
    protected \STAlgo\Data\AlgoEventsTable $algoEventsTable;

    /**
     *
     * @param \STCompany\Data\EventsCategoriesTable $eventsCategoriesTable
     * @param \STCompany\Data\EventsTable $eventsTable
     * @param \STCompany\Data\EventsSearchWordsTable $eventsSearchWordsTable
     * @param \STCompany\Data\EventsAlgoEventsTable $eventsAlgoEventsTable
     * @param EventsColorsTable $eventsColorsTable
     * @param \STCall\Data\CallsAlgoEventsTable $callsAlgoEventsTable
     * @param \STCompany\Data\RolesTable $rolesTable
     * @param \STAlgo\Data\AlgoEventsTable $algoEventsTable
     */
    public function __construct(
        \STCompany\Data\EventsCategoriesTable $eventsCategoriesTable,
        \STCompany\Data\EventsTable $eventsTable,
        \STCompany\Data\EventsSearchWordsTable $eventsSearchWordsTable,
        \STCompany\Data\EventsAlgoEventsTable $eventsAlgoEventsTable,
        EventsColorsTable $eventsColorsTable,
        \STCall\Data\CallsAlgoEventsTable $callsAlgoEventsTable,
        \STCompany\Data\RolesTable $rolesTable,
        \STAlgo\Data\AlgoEventsTable $algoEventsTable,
    ) {
        $this->eventsCategoriesTable = $eventsCategoriesTable;
        $this->eventsTable = $eventsTable;
        $this->eventsSearchWordsTable = $eventsSearchWordsTable;
        $this->eventsAlgoEventsTable = $eventsAlgoEventsTable;
        $this->eventsColorsTable = $eventsColorsTable;
        $this->callsAlgoEventsTable = $callsAlgoEventsTable;
        $this->rolesTable = $rolesTable;
        $this->algoEventsTable = $algoEventsTable;
    }

    /**
     *
     * @return \STCompany\Entity\Event\ColorCollection
     */
    public function getColors(): \STCompany\Entity\Event\ColorCollection
    {
        $colorCollection = new \STCompany\Entity\Event\ColorCollection();
        $colorsData = $this->eventsColorsTable->getColors();
        foreach ($colorsData as $colorData) {
            $colorCollection->add($this->hydrate((array) $colorData, Color::class));
        }
        return $colorCollection;
    }

    /**
     *
     * @param int $colorId
     * @return Color
     */
    public function getColor(int $colorId): Color
    {
        $colorData = $this->eventsColorsTable->getColor($colorId);
        return $this->hydrate((array) $colorData->current(), Color::class);
    }

    /**
     * @throws ReflectionException
     */
    public function getColorByName(string $colorName): ?Color
    {
        $colorData = $this->eventsColorsTable->getColorByName($colorName);

        if ($colorData->count() === 0) {
            return null;
        }

        return $this->hydrate((array) $colorData->current(), Color::class);
    }

    /**
     * @param int $companyId
     * @param int $roleId
     * @return string[]
     * @throws ReflectionException
     */
    public function getUsedAlgoEventNames(int $companyId, int $roleId): array
    {
        $events = $this->getEvents($companyId, null, $roleId);
        $usedAlgoEventNames = [];
        foreach ($events as $event) {
            $usedAlgoEventNames = array_merge($usedAlgoEventNames, $event->getAlgoEvents());
        }
        return $usedAlgoEventNames;
    }

    /**
     *
     * @param int $companyId
     * @param int|null $roleId
     * @return \STCompany\Entity\Event\CategoryCollection
     */
    public function getCategories(int $companyId, ?int $roleId = null): \STCompany\Entity\Event\CategoryCollection
    {
        $categoryCollection = new \STCompany\Entity\Event\CategoryCollection();
        $categoriesData = $this->eventsCategoriesTable->getCategories($companyId, $roleId);
        foreach ($categoriesData as $categoryData) {
            $categoryCollection->add($this->hydrate((array) $categoryData, \STCompany\Entity\Event\Category::class, withConstructor: true));
        }

        $rolesData = is_int($roleId) ? $this->rolesTable->getRole($companyId, $roleId) : $this->rolesTable->getRoles($companyId);
        $rolesCollection = new \STCompany\Entity\RoleCollection();
        foreach ($rolesData as $roleData) {
            $role = $this->hydrate((array) $roleData, \STCompany\Entity\Role::class);
            $rolesCollection->add($role);
        }

        $colorCollection = $this->getColors();

        foreach ($categoryCollection as $category) {
            $category
                ->setRole($rolesCollection->offsetGet($category->getRole()->getId()))
                ->setColor($colorCollection->offsetGet($category->getColor()->getId()))
            ;
        }

        $eventCollection = $this->getEvents($companyId, null, $roleId);
        foreach ($eventCollection as $event) {
            if (!$categoryCollection->offsetExists($event->getCategoryId())) {
                continue;
            }
            $categoryCollection
                ->offsetGet($event->getCategoryId())
                ->getEvents()
                ->add($event)
            ;
        }

        return $categoryCollection;
    }

    /**
     *
     * @param int $companyId
     * @param int $categoryId
     * @return \STCompany\Entity\Event\Category
     */
    public function getCategory(int $companyId, int $categoryId): \STCompany\Entity\Event\Category
    {
        $categoryData = $this->eventsCategoriesTable->getCategory($companyId, $categoryId);
        $category = $this->hydrate($categoryData->current()->getArrayCopy(), \STCompany\Entity\Event\Category::class, withConstructor: true);

        $roleData = $this->rolesTable->getRole($companyId, $category->getRole()->getId());
        $category->setRole($this->hydrate($roleData->current()->getArrayCopy(), \STCompany\Entity\Role::class));

        $colorData = $this->eventsColorsTable->getColor($category->getColor()->getId());
        $category->setColor($this->hydrate($colorData->current()->getArrayCopy(), Color::class));

        $category->setEvents($this->getEvents($companyId, $categoryId));

        return $category;
    }

    /**
     * @param int $companyId
     * @param int|array|null $categoryIds
     * @param int|null $roleId
     * @return EventCollection
     * @throws ReflectionException
     */
    public function getEvents(int $companyId, null|int|array $categoryIds = null, ?int $roleId = null): EventCollection
    {
        $colors = $this->getColors();

        $eventsCollection = $this->eventsTable->getEvents($companyId, $categoryIds, $roleId);
        foreach ($eventsCollection as $event) {
            $event->setColor(
                $colors->offsetExists($event->getColor()->getId())
                    ? $colors->offsetGet($event->getColor()->getId())
                    : $colors->offsetGet(EventsColorsTable::GREY_COLOR_ID)
            );
        }
        $eventIds = $eventsCollection->keys();

        $searchWordsData = $this->eventsSearchWordsTable->getSearchWordsByEventIds($eventIds);
        foreach ($searchWordsData as $searchWordData) {
            $eventsCollection
                    ->offsetGet($searchWordData['event_id'])
                    ->getSearchWord()
                    ->add($this->hydrate((array) $searchWordData, \STCompany\Entity\Event\SearchWord::class));
        }

        $algoEvents = $this->eventsAlgoEventsTable->getAlgoEvents($eventIds);
        foreach ($algoEvents as $algoEvent) {
            $eventsCollection
                    ->offsetGet($algoEvent['event_id'])
                    ->addAlgoEventName($algoEvent['algo_event']);
        }

        $rolesData = $this->rolesTable->getRoles($companyId);
        $rolesCollection = new \STCompany\Entity\RoleCollection();
        foreach ($rolesData as $roleData) {
            $rolesCollection->add($this->hydrate((array) $roleData, \STCompany\Entity\Role::class));
        }

        foreach ($eventsCollection as $event) {
            $event->setRole($rolesCollection->offsetGet($event->getRole()->getId()));
        }

        return $eventsCollection;
    }

    /**
     *
     * @param int $companyId
     * @param int $eventId
     * @return \STCompany\Entity\Event\Event
     */
    public function getEvent(int $companyId, int $eventId): \STCompany\Entity\Event\Event
    {
        $event = $this->eventsTable->getEvent($companyId, $eventId);

        $roleData = $this->rolesTable->getRole($companyId, $event->getRole()->getId());
        $event->setRole($this->hydrate($roleData->current()->getArrayCopy(), \STCompany\Entity\Role::class));

        $searchWordsData = $this->eventsSearchWordsTable->getSearchWordsByEventIds($event->getId());
        foreach ($searchWordsData as $searchWordData) {
            $event
                    ->getSearchWord()
                    ->add($this->hydrate((array) $searchWordData, \STCompany\Entity\Event\SearchWord::class));
        }

        $algoEvents = array_column($this->eventsAlgoEventsTable->getAlgoEvents($eventId)->toArray(), 'algo_event');
        $event->setAlgoEvents($algoEvents);

        return $event;
    }

    /**
     *
     * @param int $companyId
     * @param int $eventId
     * @param int $searchWordId
     * @return \STCompany\Entity\Event\SearchWord
     */
    public function getSearchWord(int $companyId, int $eventId, int $searchWordId): \STCompany\Entity\Event\SearchWord
    {
        $searchWordData = $this->eventsSearchWordsTable->getSearchWord($companyId, $eventId, $searchWordId);
        return $this->hydrate($searchWordData->current()->getArrayCopy(), \STCompany\Entity\Event\SearchWord::class);
    }

    /**
     *
     * @param \STCompany\Entity\Event\Category $category
     * @return int
     */
    public function saveCategory(\STCompany\Entity\Event\Category $category): int
    {
        return $this->eventsCategoriesTable->saveCategory($category);
    }

    /**
     * @param CategoryCollection $categories
     * @param Role $destinationRole
     * @return void
     * @throws ReflectionException
     */
    public function cloneEventsFromCategories(CategoryCollection $categories, Role $destinationRole): void
    {
        /**
         * Ids need to be reassigned one by one, as of date of creation
         * this is the best way to duplicate data from role to role
         */
        foreach ($categories as $sourceCategory) {
            $sourceCategoryArray = $sourceCategory->toArray();
            unset(
                $sourceCategoryArray['role'],
                $sourceCategoryArray['color'],
                $sourceCategoryArray['events']
            );

            /** @var Category $destinationCategory */
            $destinationCategory = $this->hydrate($sourceCategoryArray, Category::class);
            $destinationCategory
                ->setId(null)
                ->setRole($destinationRole)
                ->setColor($sourceCategory->getColor())
                ->setEvents($sourceCategory->getEvents());
            if ($sourceCategory->getId() > 0) {
                $destinationCategory->setId($this->saveCategory($destinationCategory));
            }

            foreach ($destinationCategory->getEvents() as $destinationEvent) {
                $destinationEvent
                    ->setId(null)
                    ->setRole($destinationRole)
                    ->setCategoryId($destinationCategory->getId());
                $this->saveEvent($destinationEvent);
            }
        }
    }

    /**
     *
     * @param \STCompany\Entity\Event\Event $event
     * @return int
     */
    public function saveEvent(\STCompany\Entity\Event\Event $event): int
    {
        $originalEvent = null;

        if ($event->getId() > 0) {
            $originalEvent = $this->getEvent($event->getRole()->getCompanyId(), $event->getId());
        }

        $this->beginTransaction();
        try {
            $eventId = $this->eventsTable->saveEvent($event);

            // Only delete existing search words and algo events for existing events
            if ($event->getId() > 0) {
                $this->eventsSearchWordsTable->deleteSearchWords($event->getId());
                $this->eventsAlgoEventsTable->deleteAlgoEvents($event->getId());
            }

            $this->eventsSearchWordsTable->saveSearchWords($event);
            $this->eventsAlgoEventsTable->saveAlgoEvents($event);
            $this->commit();
        } catch (\Exception $e) {
            $this->rollback();
            throw $e;
        }

        // mark as required to precalculations only if meaningful changes were made
        if ($this->shouldMarkForPrecalculations($event, $originalEvent)) {
            $this->getCallPrecalculationManager()->markRoleAsRequiredToPrecalculations(
                $event->getRole()->getCompanyId(),
                $event->getRole()->getId(),
            );
        }

        return $eventId;
    }

    /**
     *
     * @param \STCompany\Entity\Role $role
     * @param \STCompany\Entity\Event\SearchWord $searchWord
     * @return int
     */
    public function saveSearchWord(\STCompany\Entity\Role $role, \STCompany\Entity\Event\SearchWord $searchWord): int
    {
        $affectedCount = $this->eventsSearchWordsTable->addSearchWordByEventId($searchWord);
        // mark as required to precalculations
        $this->getCallPrecalculationManager()->markRoleAsRequiredToPrecalculations($role->getCompanyId(), $role->getId());
        return $affectedCount;
    }

    /**
     *
     * @param \STCompany\Entity\Role $role
     * @param array|int $categoryId
     * @return int
     */
    public function deleteCategory(\STCompany\Entity\Role $role, array|int $categoryId): int
    {
        $affectedCount = $this->eventsCategoriesTable->deleteCategory($categoryId);
        // mark as required to precalculations
        $this->getCallPrecalculationManager()->markRoleAsRequiredToPrecalculations($role->getCompanyId(), $role->getId());
        return $affectedCount;
    }

    /**
     *
     * @param \STCompany\Entity\Role $role
     * @param int $eventId
     * @return int
     */
    public function deleteEvent(\STCompany\Entity\Role $role, int $eventId): int
    {
        $affectedCount = $this->eventsTable->deleteEvent($eventId);
        // mark as required to precalculations
        $this->getCallPrecalculationManager()->markRoleAsRequiredToPrecalculations($role->getCompanyId(), $role->getId());
        return $affectedCount;
    }

    /**
     *
     * @param int $eventId
     * @param int $searchWordId
     * @return int
     */
    public function deleteSearchWord(\STCompany\Entity\Role $role, int $eventId, int $searchWordId): int
    {
        $affectedCount = $this->eventsSearchWordsTable->deleteSearchWord($eventId, $searchWordId);
        // mark as required to precalculations
        $this->getCallPrecalculationManager()->markRoleAsRequiredToPrecalculations($role->getCompanyId(), $role->getId());
        return $affectedCount;
    }

    private function shouldMarkForPrecalculations(Event $event, ?Event $eventBeforeUpdate): bool
    {
        if ($eventBeforeUpdate === null) {
            return true;
        }

        $fieldsToCompare = [
            'getName',
            'getCategoryId',
            'getIsConfirmNeeded',
            'getScore',
            'getIsPinned'
        ];

        foreach ($fieldsToCompare as $method) {
            if ($event->$method() !== $eventBeforeUpdate->$method()) {
                return true;
            }
        }

        if ($event->getRole()->getId() !== $eventBeforeUpdate->getRole()->getId()) {
            return true;
        }

        if ($this->hasSearchWordsChanged($event, $eventBeforeUpdate)) {
            return true;
        }

        if ($this->hasAlgoEventsChanged($event, $eventBeforeUpdate)) {
            return true;
        }

        // If we reach here, only icon might have changed
        return false;
    }

    private function hasSearchWordsChanged(\STCompany\Entity\Event\Event $event, \STCompany\Entity\Event\Event $eventBeforeUpdate): bool
    {
        $currentSearchWords = [];
        foreach ($event->getSearchWord() as $searchWord) {
            $currentSearchWords[] = $searchWord->getName();
        }
        sort($currentSearchWords);

        $previousSearchWords = [];
        foreach ($eventBeforeUpdate->getSearchWord() as $searchWord) {
            $previousSearchWords[] = $searchWord->getName();
        }
        sort($previousSearchWords);

        return $currentSearchWords !== $previousSearchWords;
    }

    private function hasAlgoEventsChanged(\STCompany\Entity\Event\Event $event, \STCompany\Entity\Event\Event $eventBeforeUpdate): bool
    {
        $currentAlgoEvents = $event->getAlgoEvents();
        $previousAlgoEvents = $eventBeforeUpdate->getAlgoEvents();

        sort($currentAlgoEvents);
        sort($previousAlgoEvents);

        return $currentAlgoEvents !== $previousAlgoEvents;
    }
}
