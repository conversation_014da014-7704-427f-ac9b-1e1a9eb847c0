<?php

declare(strict_types=1);

namespace STCompany\Service;

class CompanyAvatarService
{
    public const DEFAULT_COMPANY_AVATAR_FILE = 'default.png';
    public const DEFAULT_COMPANY_AVATAR_FILE_EXTENTION = 'png';
    public const SYSTEM_COMPANY_AVATAR_DIR = '/public/company/avatar/';

    /**
     *
     * @param \STCompany\Entity\Company $company
     * @return string
     */
    public function saveAvatar(\STCompany\Entity\Company $company): string
    {
        $companyAvatarFile = getcwd() . static::SYSTEM_COMPANY_AVATAR_DIR . '/' . $company->getId() . '.png';

        if (is_string($company->getAvatar())) {
            file_put_contents($companyAvatarFile, base64_decode(preg_replace('/^data:image\/[\w]+;base64,/', '', $company->getAvatar())));
        } else {
            copy(getcwd() . static::SYSTEM_COMPANY_AVATAR_DIR . static::DEFAULT_COMPANY_AVATAR_FILE, $companyAvatarFile);
        }
        return $companyAvatarFile;
    }

    /**
     *
     * @param \STCompany\Entity\Company $company
     * @return bool
     */
    public function deleteAvatar(\STCompany\Entity\Company $company): bool
    {
        $companyAvatarFile = getcwd() . static::SYSTEM_COMPANY_AVATAR_DIR . '/' . $company->getId() . '.png';

        if (file_exists($companyAvatarFile)) {
            unlink($companyAvatarFile);
        }
        return true;
    }
}
