<?php

declare(strict_types=1);

namespace STCompany\Service;

class CompanyDataRemoveService
{
    /**
     * @param \STCompany\Data\CompanyDataRemoveRepository $companyDataRemoveRepository
     */
    public function __construct(protected readonly \STCompany\Data\CompanyDataRemoveRepository $companyDataRemoveRepository)
    {
    }

    /**
     * @param int $companyId
     * @return void
     */
    public function removeCallsLogsData(int $companyId): void
    {
        $this->companyDataRemoveRepository->removeCallsLogsData($companyId);
    }

    /**
     * @param int $companyId
     * @param \Carbon\Carbon $startDate
     * @param \Carbon\Carbon $endDate
     * @return void
     */
    public function removeCallsData(int $companyId, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): void
    {
        $this->companyDataRemoveRepository->removeCallsData($companyId, $startDate, $endDate);
    }

    /**
     * @param int $companyId
     * @param \Carbon\Carbon $startDate
     * @param \Carbon\Carbon $endDate
     * @return void
     */
    public function removeAlgoEventsData(int $companyId, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): void
    {
        $this->companyDataRemoveRepository->removeAlgoEventsData($companyId, $startDate, $endDate);
    }

    /**
     * @param int $companyId
     * @return void
     */
    public function removeNotConsistentCallsCommentsData(int $companyId): void
    {
        $this->companyDataRemoveRepository->removeNotConsistentCallsCommentsData($companyId);
    }

    /**
     * @param int $companyId
     * @return void
     */
    public function removeNotConsistentCallsCommentsNotificationsData(int $companyId): void
    {
        $this->companyDataRemoveRepository->removeNotConsistentCallsCommentsNotificationsData($companyId);
    }

    /**
     * @param int $companyId
     * @param \Carbon\Carbon $startDate
     * @param \Carbon\Carbon $endDate
     * @return void
     */
    public function removeCallsParagraphsData(int $companyId, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): void
    {
        $this->companyDataRemoveRepository->removeCallsParagraphsData($companyId, $startDate, $endDate);
    }

    /**
     * @param int $companyId
     * @param \Carbon\Carbon $startDate
     * @param \Carbon\Carbon $endDate
     * @return void
     */
    public function removeCallsReviewsData(int $companyId, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): void
    {
        $this->companyDataRemoveRepository->removeCallsReviewsData($companyId, $startDate, $endDate);
    }

    /**
     * @param int $companyId
     * @return void
     */
    public function removeNotConsistentClientsData(int $companyId): void
    {
        $this->companyDataRemoveRepository->removeNotConsistentClientsData($companyId);
    }

    /**
     * @param int $companyId
     * @param \Carbon\Carbon $startDate
     * @param \Carbon\Carbon $endDate
     * @return void
     */
    public function removePrecalculatedAgentsData(int $companyId, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): void
    {
        $this->companyDataRemoveRepository->removePrecalculatedAgentsData($companyId, $startDate, $endDate);
    }

    /**
     * @param int $companyId
     * @param \Carbon\Carbon $startDate
     * @param \Carbon\Carbon $endDate
     * @return void
     */
    public function removePrecalculatedCallsData(int $companyId, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): void
    {
        $this->companyDataRemoveRepository->removePrecalculatedCallsData($companyId, $startDate, $endDate);
    }

    /**
     * @param int $companyId
     * @param \Carbon\Carbon $startDate
     * @param \Carbon\Carbon $endDate
     * @return void
     */
    public function removePrecalculatedCallsEventsData(int $companyId, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): void
    {
        $this->companyDataRemoveRepository->removePrecalculatedCallsEventsData($companyId, $startDate, $endDate);
    }

    /**
     * @param int $companyId
     * @param \Carbon\Carbon $startDate
     * @param \Carbon\Carbon $endDate
     * @return void
     */
    public function removePrecalculatedClientsData(int $companyId, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate): void
    {
        $this->companyDataRemoveRepository->removePrecalculatedClientsData($companyId, $startDate, $endDate);
    }
}
