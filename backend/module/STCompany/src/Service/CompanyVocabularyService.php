<?php

declare(strict_types=1);

namespace STCompany\Service;

class CompanyVocabularyService
{
    use \STLib\Mvc\Hydrator\MappingHydratorTrait;

    /**
     *
     * @var \STCompany\Data\CompaniesVocabularyWordsTable
     */
    protected \STCompany\Data\CompaniesVocabularyWordsTable $companiesVocabularyWordsTable;

    /**
     *
     * @param \STCompany\Data\CompaniesVocabularyWordsTable $companiesVocabularyWordsTable
     */
    public function __construct(\STCompany\Data\CompaniesVocabularyWordsTable $companiesVocabularyWordsTable)
    {
        $this->companiesVocabularyWordsTable = $companiesVocabularyWordsTable;
    }

    /**
     *
     * @param int $companyId
     * @return \STCompany\Entity\CompanyVocabularyCollection
     */
    public function getCompanyVocabulary(int $companyId): \STCompany\Entity\CompanyVocabularyCollection
    {
        $companyVocabularyWordsData = $this->companiesVocabularyWordsTable->getCompanyVocabulary($companyId);
        $companyVocabularyCollection = new \STCompany\Entity\CompanyVocabularyCollection();

        foreach ($companyVocabularyWordsData as $companyVocabularyWordData) {
            $companyVocabularyCollection->add($this->hydrate($companyVocabularyWordData->getArrayCopy(), \STCompany\Entity\CompanyVocabularyWord::class, [
                'word_id' => 'id',
            ]));
        }

        return $companyVocabularyCollection;
    }

    /**
     *
     * @param int $companyId
     * @param int $wordId
     * @return \STCompany\Entity\CompanyVocabularyWord
     */
    public function getCompanyVocabularyWord(int $companyId, int $wordId): \STCompany\Entity\CompanyVocabularyWord
    {
        $vocabularyWordData = $this->companiesVocabularyWordsTable->getCompanyVocabularyWord($companyId, $wordId)->current();
        return $this->hydrate((array) $vocabularyWordData, \STCompany\Entity\CompanyVocabularyWord::class, [
            'word_id' => 'id',
        ]);
    }

    /**
     *
     * @param \STCompany\Entity\CompanyVocabularyWord $companyVocabularyWord
     * @return int
     */
    public function saveCompanyVocabularyWord(\STCompany\Entity\CompanyVocabularyWord $companyVocabularyWord): int
    {
        return $this->companiesVocabularyWordsTable->saveCompanyVocabularyWord($companyVocabularyWord);
    }

    /**
     *
     * @param int $companyId
     * @param int $wordId
     * @return void
     */
    public function deleteCompanyVocabularyWord(int $companyId, int $wordId): void
    {
        $this->companiesVocabularyWordsTable->deleteCompanyVocabularyWord($companyId, $wordId);
    }
}
