<?php

namespace STCompany\Service\ClientPrecalculation;

class ClientPrecalculationManagerServiceWrapper
{
    /**
     *
     * @var ClientPrecalculationManagerService|null
     */
    private static ?ClientPrecalculationManagerService $clientPrecalculationManagerService = null;

    /**
     *
     * @param ClientPrecalculationManagerService $clientPrecalculationManagerService
     * @return void
     */
    public static function setStaticClientPrecalculationManagerService(ClientPrecalculationManagerService $clientPrecalculationManagerService): void
    {
        self::$clientPrecalculationManagerService = $clientPrecalculationManagerService;
    }

    /**
     *
     * @return ClientPrecalculationManagerService
     */
    public function getStaticClientPrecalculationManagerService(): ClientPrecalculationManagerService
    {
        return self::$clientPrecalculationManagerService;
    }
}
