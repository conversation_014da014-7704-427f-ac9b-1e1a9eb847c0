<?php

declare(strict_types=1);

namespace STCompany\Service\ClientPrecalculation;

class ClientPrecalculationService
{
    /**
     * @var \STCompany\Data\PrecalculatedClientsTable
     */
    protected \STCompany\Data\PrecalculatedClientsTable $precalculatedClientsTable;

    /**
     * @param \STCompany\Data\PrecalculatedClientsTable $precalculatedClientsTable
     */
    public function __construct(
        \STCompany\Data\PrecalculatedClientsTable $precalculatedClientsTable,
    ) {
        $this->precalculatedClientsTable = $precalculatedClientsTable;
    }

    /**
     * @param int $companyId
     * @param array|string $clientIds
     * @param \Carbon\Carbon|null $startDate
     * @param \Carbon\Carbon|null $endDate
     * @param int|array|null $roleId
     * @return void
     */
    public function precalculateClients(
        int $companyId,
        array|string $clientIds,
        \Carbon\Carbon $startDate = null,
        \Carbon\Carbon $endDate = null,
        int|array $roleId = null
    ): void {
        $this->precalculatedClientsTable->precalculateClients($companyId, $clientIds, $startDate, $endDate, $roleId);
    }
}
