<?php

namespace STCompany\Service\ClientPrecalculation;

trait ClientPrecalculationManagerServiceTrait
{
    /**
     *
     * @var ClientPrecalculationManagerServiceWrapper|null
     */
    private ?ClientPrecalculationManagerServiceWrapper $clientPrecalculationManagerServiceWrapper = null;

    /**
     *
     * @return ClientPrecalculationManagerService
     */
    protected function getClientPrecalculationManager(): ClientPrecalculationManagerService
    {
        if (is_null($this->clientPrecalculationManagerServiceWrapper)) {
            $this->clientPrecalculationManagerServiceWrapper = new ClientPrecalculationManagerServiceWrapper();
        }
        return $this->clientPrecalculationManagerServiceWrapper->getStaticClientPrecalculationManagerService();
    }
}
