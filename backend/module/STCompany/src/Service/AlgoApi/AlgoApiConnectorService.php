<?php

declare(strict_types=1);

namespace STCompany\Service\AlgoApi;

use ReflectionException;
use STAlgo\Data\CompaniesAlgoApisTable;
use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Entity\AlgoApi\AlgoApi;

class AlgoApiConnectorService
{
    public function __construct(
        private readonly CompaniesAlgoApisTable $companiesAlgoApisTable,
        private readonly AlgoApisSelectorService $companiesAlgoApisSelector
    ) {
    }

    /**
     * @throws ReflectionException
     * @throws NotFoundApiException
     */
    public function connect(int $algoApiId, int $companyId): AlgoApi
    {
        $algoApi = new AlgoApi();
        $algoApi->setId($algoApiId);
        $algoApi->setCompanyId($companyId);

        $this->companiesAlgoApisTable->saveAlgoApi($algoApi);

        return $this->companiesAlgoApisSelector->getAlgoApi($algoApi->getId(), $algoApi->getCompanyId());
    }

    public function disconnect(int $algoApiId, int $companyId): void
    {
        $this->companiesAlgoApisTable->deleteAlgoApi($algoApiId, $companyId);
    }
}
