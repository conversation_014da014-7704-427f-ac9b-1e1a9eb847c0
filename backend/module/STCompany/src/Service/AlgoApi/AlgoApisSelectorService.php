<?php

declare(strict_types=1);

namespace STCompany\Service\AlgoApi;

use Lam<PERSON>\Db\ResultSet\ResultSet;
use ReflectionException;
use STAlgo\Data\CompaniesAlgoApisTable;
use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Entity\AlgoApi\AlgoApi;
use STCompany\Entity\AlgoApi\AlgoApiCollection;
use STLib\Mvc\Hydrator\Hydrator;

class AlgoApisSelectorService
{
    public function __construct(
        private readonly CompaniesAlgoApisTable $companiesAlgoApisTable,
        private readonly Hydrator $hydrator
    ) {
    }

    /**
     * @throws ReflectionException
     * @throws NotFoundApiException
     */
    public function getAlgoApi(int $algoApiId, int $companyId): AlgoApi
    {
        /**
         * @var ResultSet $algoApiData
         */
        $algoApiData = $this->companiesAlgoApisTable->getAlgoApi($algoApiId, $companyId);

        return $this->hydrator->hydrateClass(
            (array) $algoApiData->current(),
            AlgoApi::class,
            true
        );
    }

    /**
     * @throws ReflectionException
     */
    public function getDirectNerAlgoApis(int $companyId): AlgoApiCollection
    {
        /**
         * @var ResultSet $algoApisData
         */
        $algoApisData = $this->companiesAlgoApisTable->getDirectNerAlgoApis($companyId);

        $algoApiCollection = new AlgoApiCollection();
        foreach ($algoApisData->toArray() as $algoApiData) {
            $algoApi = $this->hydrator->hydrateClass($algoApiData, AlgoApi::class, true);
            $algoApiCollection->add($algoApi);
        }

        return $algoApiCollection;
    }
}
