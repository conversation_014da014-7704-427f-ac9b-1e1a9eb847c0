<?php

namespace STCompany\Service\Industry;

use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Data\CompanyIndustriesTable;
use STCompany\Entity\Industry\Industry;

class IndustryConnectorService
{
    public function __construct(private readonly CompanyIndustriesTable $companyIndustryTable)
    {
    }

    /**
     * @throws NotFoundApiException
     */
    public function connect(int $industryId, int $companyId): Industry
    {
        $industry = new Industry();
        $industry->setId($industryId);
        $industry->setCompanyId($companyId);

        $this->companyIndustryTable->saveIndustry($industry);

        return $this->companyIndustryTable->getIndustry($industry->getId(), $industry->getCompanyId());
    }

    public function disconnect(int $industryId, int $companyId): void
    {
        $this->companyIndustryTable->deleteIndustry($industryId, $companyId);
    }
}
