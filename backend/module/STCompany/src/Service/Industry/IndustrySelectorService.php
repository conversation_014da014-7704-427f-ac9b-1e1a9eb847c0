<?php

declare(strict_types=1);

namespace STCompany\Service\Industry;

use STCompany\Data\CompanyIndustriesTable;
use STCompany\Entity\Industry\IndustryCollection;

class IndustrySelectorService
{
    public function __construct(private readonly CompanyIndustriesTable $companyIndustryTable)
    {
    }

    public function getIndustries(int $companyId): IndustryCollection
    {
        return $this->companyIndustryTable->getIndustries($companyId);
    }
}
