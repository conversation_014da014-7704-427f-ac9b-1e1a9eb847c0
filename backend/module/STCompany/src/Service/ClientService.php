<?php

declare(strict_types=1);

namespace STCompany\Service;

use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Entity\Client;
use STCompany\Entity\ClientCollection;

class ClientService
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    /**
     *
     * @var \STCompany\Data\ClientsTable
     */
    protected \STCompany\Data\ClientsTable $clientsTable;

    /**
     *
     * @param \STCompany\Data\ClientsTable $clientsTable
     */
    public function __construct(
        \STCompany\Data\ClientsTable $clientsTable,
    ) {
        $this->clientsTable = $clientsTable;
    }

    /**
     *
     * @param \STCompany\Entity\Company $company
     * @return array
     */
    public function getClientStatusesByCompany(\STCompany\Entity\Company $company): array
    {
        return $this->clientsTable->getClientStatusesByCompanyId($company->getId());
    }

    /**
     *
     * @param int $companyId
     * @param string $clientId
     * @return ?array
     * @throws NotFoundApiException
     */
    public function getClientById(int $companyId, string $clientId): ?array
    {
        return $this->clientsTable->getClientById($companyId, $clientId);
    }

    public function getClientsByIds(array $clientIds, int $companyId): ClientCollection
    {
        $clients = new ClientCollection();

        foreach ($this->clientsTable->getClientsByIds($clientIds, $companyId) as $clientData) {
            $clients->add($this->hydrate((array) $clientData, Client::class, withConstructor: true));
        }

        return $clients;
    }

    /**
     *
     * @param \STCompany\Entity\Company $company
     * @return array
     */
    public function getClientCountriesByCompany(\STCompany\Entity\Company $company): array
    {
        return $this->clientsTable->getClientCountriesByCompanyId($company->getId());
    }

    /**
     *
     * @param \STCompany\Entity\Company $company
     * @return array
     */
    public function getClientSourcesByCompany(\STCompany\Entity\Company $company): array
    {
        return $this->clientsTable->getClientSourcesByCompanyId($company->getId());
    }

    /**
     *
     * @param \STCompany\Entity\Company $company
     * @return array
     */
    public function getClientCampaignIdsByCompany(\STCompany\Entity\Company $company): array
    {
        return $this->clientsTable->getClientCampaignIdsByCompanyId($company->getId());
    }

    /**
     *
     * @param \STCompany\Entity\Client $client
     * @return void
     */
    public function saveClient(\STCompany\Entity\Client $client): void
    {
        $this->clientsTable->saveClient($client);
    }
}
