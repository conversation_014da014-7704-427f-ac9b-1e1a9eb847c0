<?php

declare(strict_types=1);

namespace STCompany\Service;

use Exception;
use ReflectionException;
use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Data\PermissionsTable;
use STCompany\Data\RolesPermissionsTable;
use STCompany\Data\RolesTable;
use STCompany\Data\UsersCompaniesRolesTable;
use STCompany\Entity\Company;
use STCompany\Entity\Permission\PermissionCollection;
use STCompany\Entity\Permission\UserPermission;
use STCompany\Entity\Role;
use STCompany\Entity\RoleCollection;
use STCompany\Entity\User;
use STLib\Db\ProvidesTransaction;
use STLib\Mvc\Hydrator\BaseHydratorTrait;
use STLib\Mvc\Hydrator\MappingHydratorTrait;
use STUser\Data\UsersTable;

class RoleService
{
    use BaseHydratorTrait, MappingHydratorTrait {
        BaseHydratorTrait::hydrate insteadof MappingHydratorTrait;
        BaseHydratorTrait::extract insteadof MappingHydratorTrait;
        BaseHydratorTrait::hydrate as baseHydrate;
        BaseHydratorTrait::extract as baseExtract;
        MappingHydratorTrait::hydrate as mapperHydrate;
        MappingHydratorTrait::extract as mapperExtract;
    }
    use ProvidesTransaction;

    public const string CALLS_REPORT_NAME = 'calls';
    public const string CLIENTS_REPORT_NAME = 'clients';

    public const string DEFAULT_ADMIN_ROLE_NAME = 'Hidden admin';
    public const string DEFAULT_COMPANY_ADMIN_ROLE_NAME = 'Admin';
    public const string DEFAULT_COMPANY_SUPERVISOR_ROLE_NAME = 'Supervisor';
    public const string DEFAULT_QC_ROLE_NAME = 'QC manager';
    public const string DEFAULT_AGENT_ROLE_NAME = 'Agent';

    /**
     *
     * @var RolesTable
     */
    protected RolesTable $rolesTable;

    /**
     *
     * @var PermissionsTable
     */
    protected PermissionsTable $permissionsTable;

    /**
     *
     * @var RolesPermissionsTable
     */
    protected RolesPermissionsTable $rolesPermissionsTable;

    /**
     *
     * @var UsersCompaniesRolesTable
     */
    protected UsersCompaniesRolesTable $usersCompaniesRolesTable;

    /**
     *
     * @var UsersTable
     */
    protected UsersTable $usersTable;

    /**
     *
     * @param RolesTable $rolesTable
     * @param PermissionsTable $permissionsTable
     * @param RolesPermissionsTable $rolesPermissionsTable
     * @param UsersCompaniesRolesTable $usersCompaniesRolesTable
     * @param UsersTable $usersTable
     */
    public function __construct(
        RolesTable $rolesTable,
        PermissionsTable $permissionsTable,
        RolesPermissionsTable $rolesPermissionsTable,
        UsersCompaniesRolesTable $usersCompaniesRolesTable,
        UsersTable $usersTable,
    ) {
        $this->rolesTable = $rolesTable;
        $this->permissionsTable = $permissionsTable;
        $this->rolesPermissionsTable = $rolesPermissionsTable;
        $this->usersCompaniesRolesTable = $usersCompaniesRolesTable;
        $this->usersTable = $usersTable;
    }

    /**
     *
     * @param int|array $companyId
     * @param array $roleTypes
     * @return RoleCollection
     * @throws ReflectionException
     */
    public function getRoles(int|array $companyId, array $roleTypes = []): RoleCollection
    {
        $roleCollection = new RoleCollection();
        $rolesData = $this->rolesTable->getRoles($companyId, $roleTypes);
        foreach ($rolesData as $roleData) {
            $roleCollection->add($this->baseHydrate((array) $roleData, Role::class, withConstructor: true));
        }
        $roleIds = $roleCollection->keys();

        $eventsCount = $this->rolesTable->getEventsCount($roleIds);
        foreach ($eventsCount as $eventCount) {
            $roleCollection
                ->offsetGet($eventCount['role_id'])
                ->setEventsCount($eventCount['count']);
        }

        $permissionsData = $this->rolesPermissionsTable->getPermissionsByRoleIds($roleIds);
        foreach ($permissionsData as $permissionData) {
            $roleCollection
                ->offsetGet($permissionData['role_id'])
                ->getPermissions()
                ->add(
                    $this->mapperHydrate((array) $permissionData, UserPermission::class, [
                        'permission_id' => 'id',
                        'permission_name' => 'name',
                    ])
                );
        }

        return $roleCollection;
    }

    public function getRoleIdByUserId(int $companyId, int $userId): int
    {
        return $this->rolesTable->getRoleByUserId($companyId, $userId)->current()['role_id'];
    }

    /**
     *
     * @param int $companyId
     * @param int $roleId
     * @return Role
     */
    public function getRole(int $companyId, int $roleId): Role
    {
        $roleData = $this->rolesTable->getRole($companyId, $roleId);
        $role = $this->baseHydrate($roleData->current()->getArrayCopy(), Role::class, withConstructor: true);

        $permissionsData = $this->rolesPermissionsTable->getPermissionsByRoleIds($role->getId());
        foreach ($permissionsData as $permissionData) {
            $role
                ->getPermissions()
                ->add(
                    $this->mapperHydrate((array) $permissionData, UserPermission::class, [
                        'permission_id' => 'id',
                        'permission_name' => 'name',
                    ])
                );
        }

        $users = $this->usersCompaniesRolesTable->getUser($companyId, $roleId);
        foreach ($users as $user) {
            $role
                ->getUsers()
                ->add($this->mapperHydrate((array) $user, User::class));
        }

        return $role;
    }

    /**
     *
     * @param int $companyId
     * @return array
     */
    public function getRoleIds(int $companyId): array
    {
        return array_map(function ($roleId) {
            return (int) $roleId;
        }, array_column($this->rolesTable->getRoleIds($companyId)->toArray(), 'role_id'));
    }

    /**
     * @throws NotFoundApiException
     */
    public function getRoleIdByType(int $roleType, int $companyId): int
    {
        return $this->rolesTable->getRoleByType($roleType, $companyId)->current()['role_id'];
    }

    /**
     *
     * @param Role $role
     * @return int
     */
    public function saveRole(Role $role): int
    {
        $this->beginTransaction();
        try {
            $roleId = $this->rolesTable->saveRole($role);
            $this->rolesPermissionsTable->deletePermissions($role->getId());
            $this->rolesPermissionsTable->savePermissions($role);
            $this->commit();
        } catch (Exception $e) {
            $this->rollback();
            throw $e;
        }
        return $roleId;
    }

    /**
     *
     * @param Company $company
     * @param PermissionCollection $companyPermissionCollection
     * @return array
     * @throws ReflectionException
     * @throws Exception
     */
    public function saveDefaultRolesToNewCompany(
        Company $company,
        PermissionCollection $companyPermissionCollection
    ): array {
        $managerPermissionCollection = $this->getManagerPermissionCollection($companyPermissionCollection);

        // create default roles array
        $defaultRoles = [
            $this->hydrate([
                'name' => static::DEFAULT_ADMIN_ROLE_NAME,
                'company_id' => $company->getId(),
                'role_type' => Role::ADMIN_ROLE_TYPE,
            ], Role::class, withConstructor: true),
            $this->hydrate([
                'name' => static::DEFAULT_COMPANY_ADMIN_ROLE_NAME,
                'company_id' => $company->getId(),
                'role_type' => Role::COMPANY_ADMIN_ROLE_TYPE,
            ], Role::class, withConstructor: true),
            $this->hydrate([
                'name' => static::DEFAULT_COMPANY_SUPERVISOR_ROLE_NAME,
                'company_id' => $company->getId(),
                'role_type' => Role::COMPANY_SUPERVISOR_ROLE_TYPE,
                'permissions' => $managerPermissionCollection,
            ], Role::class, withConstructor: true),
            $this->hydrate([
                'name' => static::DEFAULT_QC_ROLE_NAME,
                'company_id' => $company->getId(),
                'role_type' => Role::MANAGER_ROLE_TYPE,
                'permissions' => $managerPermissionCollection,
            ], Role::class, withConstructor: true),
            $this->hydrate([
                'name' => static::DEFAULT_AGENT_ROLE_NAME,
                'company_id' => $company->getId(),
                'role_type' => Role::AGENT_ROLE_TYPE,
            ], Role::class, withConstructor: true),
        ];

        foreach ($defaultRoles as $role) {
            $this->saveRole($role);
        }

        return $defaultRoles;
    }

    /**
     * @throws ReflectionException
     */
    private function getManagerPermissionCollection(PermissionCollection $companyPermissionCollection): PermissionCollection
    {
        $managerPermissionCollection = new PermissionCollection();
        foreach ($companyPermissionCollection as $permission) {
            $managerPermissionCollection->add(
                $this->hydrate(
                    array_merge($permission->toArray(), ['access_level' => PermissionsTable::WRITE_PERMISSION]),
                    UserPermission::class
                )
            );
        }

        return $managerPermissionCollection;
    }

    /**
     *
     * @param array $userIds
     * @param int $roleId
     * @param int $companyId
     * @return int
     * @throws Exception
     */
    public function bulkSaveUsers(array $userIds, int $roleId, int $companyId): int
    {
        return $this->usersCompaniesRolesTable->saveUsersData($userIds, $companyId, $roleId);
    }

    /**
     *
     * @param int $companyId
     * @param int $roleId
     * @return int
     */
    public function deleteRole(int $companyId, int $roleId): int
    {
        return $this->rolesTable->deleteRole($companyId, $roleId);
    }
}
