<?php

declare(strict_types=1);

namespace STCompany\Service\Interfaces;

use STCompany\Service\Billing\BillingPeriod;

interface BillingRepositoryInterface
{
    public function getCallsSecondsDurationByPeriod(int $companyId, BillingPeriod $billingPeriod): int;

    public function getChatsByPeriod(int $companyId, BillingPeriod $billingPeriod): int;

    public function getSummarizationsByPeriod(int $companyId, BillingPeriod $billingPeriod): int;

    public function getClientSummarizationCallsByPeriod(int $companyId, BillingPeriod $billingPeriod): int;

    public function getChecklistCallsByPeriod(
        int $companyId,
        int $checklistId,
        BillingPeriod $trialPeriod
    ): int;
}
