<?php

declare(strict_types=1);

namespace STCompany\Service\CompanyCreation\Onboarding\EventsCreation;

use Exception;
use STCompany\Entity\Company;
use STCompany\Service\EventService;
use STLlmEvent\Entity\LlmEventCollection;

class CompanyEventsCreator
{
    public function __construct(
        private readonly CompanyEventBuilder $companyEventBuilder,
        private readonly EventService $eventService
    ) {
    }

    /**
     * @throws Exception
     */
    public function createCompanyEvents(LlmEventCollection $llmEvents, Company $company): void
    {
        $category = $this->companyEventBuilder->buildCategory($company);
        $this->eventService->saveCategory($category);

        foreach ($llmEvents as $llmEvent) {
            $companyEvent = $this->companyEventBuilder->buildCompanyEvent($llmEvent, $company, $category);
            $this->eventService->saveEvent($companyEvent);
        }
    }
}
