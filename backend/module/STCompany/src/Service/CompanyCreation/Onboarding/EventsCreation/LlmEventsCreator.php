<?php

declare(strict_types=1);

namespace STCompany\Service\CompanyCreation\Onboarding\EventsCreation;

use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Service\Interfaces\LlmEventSaverInterface;
use STCompany\Service\Interfaces\LlmEventSelectorInterface;
use STLlmEvent\Entity\LlmEventCollection;

class LlmEventsCreator
{
    public function __construct(
        private readonly LlmEventSelectorInterface $llmEventSelector,
        private readonly LlmEventSaverInterface $llmEventSaver,
    ) {
    }

    public function createLlmEvents(array $llmEventsData): LlmEventCollection
    {
        $llmEventCollection = new LlmEventCollection();
        foreach ($llmEventsData as $llmEventData) {
            $name = trim($llmEventData['name']);

            if (array_key_exists('id', $llmEventData)) {
                try {
                    $llmEvent = $this->llmEventSelector->getLlmEvent($llmEventData['id']);
                } catch (NotFoundApiException) {
                    // Create deleted event again
                    $llmEvent = $this->llmEventSaver->save($name, $llmEventData['description']);
                }
            } else {
                $llmEvent = $this->llmEventSaver->save($name, $llmEventData['description']);
            }
            $llmEventCollection->add($llmEvent);
        }

        return $llmEventCollection;
    }
}
