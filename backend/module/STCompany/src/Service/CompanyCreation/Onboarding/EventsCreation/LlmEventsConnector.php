<?php

declare(strict_types=1);

namespace STCompany\Service\CompanyCreation\Onboarding\EventsCreation;

use STCompany\Data\CompanyLlmEventsTable;
use STCompany\Entity\Company;
use STCompany\Entity\LlmEvent\LlmEvent as CompanyLlmEvent;
use STCompany\Entity\LlmEvent\LlmEventCollection as CompanyLlmEventCollection;
use STLlmEvent\Entity\LlmEventCollection;

class LlmEventsConnector
{
    public function __construct(private readonly CompanyLlmEventsTable $companyLlmEventsTable)
    {
    }

    public function connectEvents(Company $company, LlmEventCollection $llmEvents): void
    {
        $companyLlmEventCollection = new CompanyLlmEventCollection();
        foreach ($llmEvents as $llmEvent) {
            $companyLlmEvent = new CompanyLlmEvent();
            $companyLlmEvent->setId($llmEvent->getId());
            $companyLlmEvent->setCompanyId($company->getId());

            $companyLlmEventCollection->add($companyLlmEvent);
        }

        $this->companyLlmEventsTable->saveEvents($companyLlmEventCollection);
    }
}
