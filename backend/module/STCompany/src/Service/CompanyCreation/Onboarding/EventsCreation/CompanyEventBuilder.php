<?php

declare(strict_types=1);

namespace STCompany\Service\CompanyCreation\Onboarding\EventsCreation;

use ReflectionException;
use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Entity\Company;
use STCompany\Entity\Event\Category;
use STCompany\Entity\Event\Event;
use STCompany\Entity\Role;
use STCompany\Service\EventService;
use STCompany\Service\RoleService;
use STLlmEvent\Entity\LlmEvent;

class CompanyEventBuilder
{
    private const string CATEGORY_NAME = 'General';
    private const string COLOR_NAME_ORANGE = 'orange';

    public function __construct(
        private readonly RoleService $roleService,
        private readonly EventService $eventService
    ) {
    }
    /**
     * @throws NotFoundApiException
     * @throws ReflectionException
     */
    public function buildCategory(Company $company): Category
    {
        $roleId = $this->roleService->getRoleIdByType(Role::COMPANY_ADMIN_ROLE_TYPE, $company->getId());
        $orangeColor = $this->eventService->getColorByName(self::COLOR_NAME_ORANGE);

        $category = new Category();
        $category->setName(self::CATEGORY_NAME);
        $category->setRoleId($roleId);
        $category->getRole()->setCompanyId($company->getId());
        if ($orangeColor && $orangeColor->getId() !== null) {
            $category->setColor($orangeColor);
        }

        return $category;
    }

    public function buildCompanyEvent(LlmEvent $llmEvent, Company $company, Category $category): Event
    {
        $event = new Event();
        $event->setName($llmEvent->getName());
        $event->setIcon('');
        $event->setRoleId($category->getRole()->getId());
        $event->getRole()->setCompanyId($company->getId());
        $event->setCategoryId($category->getId());
        $event->isPinned(true);
        $event->isConfirmNeeded(false);
        $event->setScore(1);
        $event->setAlgoEvents([$llmEvent->getName()]);

        return $event;
    }
}
