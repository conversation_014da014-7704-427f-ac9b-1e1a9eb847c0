<?php

declare(strict_types=1);

namespace STCompany\Service\CompanyCreation\Onboarding\UsersCreation;

use STCompany\Entity\Company;
use STCompany\Entity\User;
use STCompany\Service\Interfaces\AuthCodesGeneratorInterface;
use STMail\Service\MailService;

class UserInviter
{
    public const int|float INVITE_CODE_LIFETIME = 60 * 60 * 24 * 365;

    public function __construct(
        private readonly AuthCodesGeneratorInterface $authCodesGenerator,
        private readonly MailService $mailService,
    ) {
    }

    public function inviteNewUser(User $user, Company $company, string $inviteLink, string $senderName): void
    {
        $inviteLink = $this->buildInviteLink($inviteLink, $user, $company);

        $this->mailService
            ->addToQueue($company->getFrontId(), $user, [
                'template_id' => 'invite-new-user',
                'substitutions' => [
                    'dynamic_html' => [
                        'account_link' => $inviteLink,
                        'invitation_link' => $inviteLink,
                    ],
                    'inviter' => $senderName,
                    'company_name' => $company->getName(),
                    'user_email' => $user->getEmail(),
                ],
            ]);
    }

    private function buildInviteLink(string $rawInviteLink, User $user, Company $company): string
    {
        $code = $this->authCodesGenerator->getChangePasswordCode($user, static::INVITE_CODE_LIFETIME);

        $inviteLink = $this->replacePlaceholder('{code}', $rawInviteLink, $code);
        $inviteLink = $this->replacePlaceholder('{companyId}', $inviteLink, (string) $company->getId());
        $inviteLink = $this->replacePlaceholder('{companyName}', $inviteLink, $company->getName());
        $inviteLink .= $user->getEmail();

        return $inviteLink;
    }

    private function replacePlaceholder(string $placeholder, string $string, mixed $value): string
    {
        if (str_contains($string, $placeholder)) {
            return str_replace($placeholder, $value, $string);
        }

        return $string;
    }

    public function inviteExistedUser(User $user, Company $company, string $senderName): void
    {
        $this->mailService
            ->addToQueue($company->getFrontId(), $user, [
                'template_id' => 'invite-to-company',
                'substitutions' => [
                    'inviter' => $senderName,
                    'subject' => 'Robonote: invite to ' . $company->getName(),
                    'company_name' => $company->getName(),
                    'user_email' => $user->getEmail(),
                ],
            ]);
    }
}
