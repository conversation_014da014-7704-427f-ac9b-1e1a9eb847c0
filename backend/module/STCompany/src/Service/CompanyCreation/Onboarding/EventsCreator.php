<?php

declare(strict_types=1);

namespace STCompany\Service\CompanyCreation\Onboarding;

use Exception;
use STCompany\Entity\Company;
use STCompany\Service\CompanyCreation\Onboarding\EventsCreation\CompanyEventsCreator;
use STCompany\Service\CompanyCreation\Onboarding\EventsCreation\LlmEventsConnector;
use STCompany\Service\CompanyCreation\Onboarding\EventsCreation\LlmEventsCreator;

class EventsCreator
{
    public function __construct(
        private readonly LlmEventsCreator $llmEventsCreator,
        private readonly LlmEventsConnector $llmEventsConnector,
        private readonly CompanyEventsCreator $companyEventsCreator,
    ) {
    }

    /**
     * @throws Exception
     */
    public function createEvents(Company $company, array $events): void
    {
        $llmEventCollection = $this->llmEventsCreator->createLlmEvents($events);

        $this->llmEventsConnector->connectEvents($company, $llmEventCollection);

        $this->companyEventsCreator->createCompanyEvents($llmEventCollection, $company);
    }
}
