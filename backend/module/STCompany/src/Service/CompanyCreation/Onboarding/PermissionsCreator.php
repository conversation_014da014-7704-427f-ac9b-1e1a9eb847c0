<?php

declare(strict_types=1);

namespace STCompany\Service\CompanyCreation\Onboarding;

use Exception;
use ReflectionException;
use STCompany\Entity\Company;
use STCompany\Service\PermissionService;
use STCompany\Service\RoleService;
use STFront\Service\FrontService;
use STUser\Service\UserService;

class PermissionsCreator
{
    public function __construct(
        private readonly PermissionService $permissionService,
        private readonly RoleService $roleService,
        private readonly UserService $userService,
        private readonly FrontService $frontService
    ) {
    }

    /**
     * @throws ReflectionException
     * @throws Exception
     */
    public function createPermissions(Company $company): void
    {
        $permissions = $this->permissionService->getPermissions();
        $defaultRoles = $this->roleService->saveDefaultRolesToNewCompany($company, $permissions);

        $hiddenAdminDefaultRoleId = array_reduce($defaultRoles, function ($hiddenAdminRoleId, $defaultRole) {
            if ($defaultRole->isAdmin()) {
                $hiddenAdminRoleId = $defaultRole->getId();
            }
            return $hiddenAdminRoleId;
        });

        // add global admin users as hidden admins
        $globalAdminUsers = $this->userService->getGlobalAdminUsers();
        $globalAdminUserIds = array_column($globalAdminUsers->toArray(), 'id');
        $this->roleService->bulkSaveUsers($globalAdminUserIds, $hiddenAdminDefaultRoleId, $company->getId());

        // add active front global admins
        $activeFrontGlobalAdmins = $this->userService->getFrontGlobalAdminUsers(
            $this->frontService->getActiveFront()->getId()
        );
        $activeFrontGlobalAdminsUserIds = array_column($activeFrontGlobalAdmins->toArray(), 'id');
        $this->roleService->bulkSaveUsers(
            $activeFrontGlobalAdminsUserIds,
            $hiddenAdminDefaultRoleId,
            $company->getId()
        );
    }
}
