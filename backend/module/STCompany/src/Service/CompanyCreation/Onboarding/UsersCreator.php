<?php

declare(strict_types=1);

namespace STCompany\Service\CompanyCreation\Onboarding;

use Exception;
use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Entity\Company;
use STCompany\Entity\Team;
use STCompany\Service\CompanyCreation\Onboarding\UsersCreation\UserBuilder;
use STCompany\Service\CompanyCreation\Onboarding\UsersCreation\UserInviter;
use STCompany\Service\TeamService;
use STCompany\Service\UserService;
use STUser\Entity\User;

class UsersCreator
{
    public function __construct(
        private readonly UserBuilder $userBuilder,
        private readonly TeamService $teamService,
        private readonly UserService $userService,
        private readonly UserInviter $userInviter,
    ) {
    }

    /**
     * @throws Exception
     */
    public function createUsers(Company $company, array $usersData, string $inviteLink): void
    {
        $team = $this->userBuilder->buildTeam($company);
        $this->teamService->saveTeam($team);

        $senderName = null;
        $isServiceAccountSent = false;
        foreach ($usersData as $userData) {
            $this->createUser($company, $team, $userData, $isServiceAccountSent, $inviteLink, $senderName);
        }

        if (!$isServiceAccountSent) {
            $serviceAccountData = [
                'name' => User::DEFAULT_HIDDEN_ADMIN_NAME,
                'email' => User::DEFAULT_HIDDEN_ADMIN_EMAIL,
            ];
            $this->createUser($company, $team, $serviceAccountData, $isServiceAccountSent, $inviteLink, $senderName);
        }
    }

    /**
     * @throws NotFoundApiException
     * @throws Exception
     */
    private function createUser(
        Company $company,
        Team $team,
        array $userData,
        bool &$isServiceAccountSent,
        string $inviteLink,
        ?string &$senderName
    ): void {
        if (!$senderName) {
            $senderName = $userData['name'];
        }

        $email = $userData['email'];
        if ($email === User::DEFAULT_HIDDEN_ADMIN_EMAIL) {
            $isServiceAccountSent = true;
        }

        $user = $this->userBuilder->buildUser($userData['name'], $email, $company, $team);
        $this->userService->saveUser($user, $company->getId());

        if ($user->isFirstLogin()) {
            $this->userInviter->inviteNewUser($user, $company, $inviteLink, $senderName);
        } else {
            $this->userInviter->inviteExistedUser($user, $company, $senderName);
        }
    }
}
