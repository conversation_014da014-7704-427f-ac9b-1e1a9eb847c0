<?php

declare(strict_types=1);

namespace STCompany\Service;

use STCompany\Entity\Role;
use STLib\Db\ProvidesTransaction;

class TeamService
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;
    use \STCompany\Service\ClientPrecalculation\ClientPrecalculationManagerServiceTrait;
    use ProvidesTransaction;

    /**
     *
     * @var \STCompany\Data\TeamsTable
     */
    protected \STCompany\Data\TeamsTable $teamsTable;

    /**
     *
     * @var \STCompany\Data\UsersTeamsTable
     */
    protected \STCompany\Data\UsersTeamsTable $usersTeamsTable;

    /**
     *
     * @param \STCompany\Data\TeamsTable $teamsTable
     * @param \STCompany\Data\UsersTeamsTable $usersTeamsTable
     */
    public function __construct(
        \STCompany\Data\TeamsTable $teamsTable,
        \STCompany\Data\UsersTeamsTable $usersTeamsTable,
    ) {
        $this->teamsTable = $teamsTable;
        $this->usersTeamsTable = $usersTeamsTable;
    }

    /**
     *
     * @param int $companyId
     * @param int|null $userId
     * @return \STCompany\Entity\TeamCollection
     */
    public function getTeams(int $companyId, ?int $userId = null): \STCompany\Entity\TeamCollection
    {
        $teamCollection = new \STCompany\Entity\TeamCollection();
        $teamsData = $this->teamsTable->getTeams($companyId, $userId);
        foreach ($teamsData as $teamData) {
            $teamCollection->add($this->hydrate((array) $teamData, \STCompany\Entity\Team::class));
        }
        return $teamCollection;
    }

    /**
     *
     * @param int $companyId
     * @param array $teamIds
     * @return \STCompany\Entity\TeamCollection
     */
    public function getTeamsByIds(int $companyId, array $teamIds): \STCompany\Entity\TeamCollection
    {
        $teamCollection = new \STCompany\Entity\TeamCollection();
        $teamsData = $this->teamsTable->getTeamsByIds($companyId, $teamIds);
        foreach ($teamsData as $teamData) {
            $teamCollection->add($this->hydrate((array) $teamData, \STCompany\Entity\Team::class));
        }
        return $teamCollection;
    }

    /**
     *
     * @param int $companyId
     * @param int $teamId
     * @return \STCompany\Entity\Team
     */
    public function getTeam(int $companyId, int $teamId): \STCompany\Entity\Team
    {
        $teamData = $this->teamsTable->getTeam($companyId, $teamId);
        /** @var \STCompany\Entity\Team $team */
        $team = $this->hydrate($teamData->current()->getArrayCopy(), \STCompany\Entity\Team::class, withConstructor: true);
        $users = $this->usersTeamsTable->getUsersByTeamId($companyId, $teamId);
        foreach ($users as $user) {
            $team
                ->getUsers()
                ->add($this->hydrate((array) $user, \STCompany\Entity\User::class));
        }
        return $team;
    }

    /**
     *
     * @param int $companyId
     * @param int $userId
     * @param int $agentId
     * @return bool
     */
    public function checkIfUserHasAccessToAgentsTeam(int $companyId, int $userId, int $agentId): bool
    {
        return $this->teamsTable->getUsersEqualTeams($companyId, $userId, $agentId)->count() > 0;
    }

    /**
     *
     * @param \STCompany\Entity\Team $team
     * @return int
     */
    public function saveTeam(\STCompany\Entity\Team $team): int
    {
        return $this->teamsTable->saveTeam($team);
    }

    /**
     *
     * @param int $companyId
     * @param array $userIds
     * @param int $teamId
     * @return void
     * @throws \Exception
     */
    public function bulkSaveUsers(int $companyId, array $userIds, int $teamId): void
    {
        $this->beginTransaction();

        try {
            $usersWithTeamsData = $this->usersTeamsTable->getUsersWithTeams($companyId, [Role::AGENT_ROLE_TYPE], $userIds);
            $usersWithTeamsIds = array_column($usersWithTeamsData->toArray(), 'user_id');

            if (count($usersWithTeamsIds) > 0) {
                $this->usersTeamsTable->deleteUsersFromAllTeams($usersWithTeamsIds);
            }

            $this->usersTeamsTable->saveUsers($userIds, $teamId);

            $this->commit();
        } catch (\Exception $e) {
            $this->rollback();
            throw $e;
        }
    }

    /**
     *
     * @param int $companyId
     * @param int $teamId
     * @return int
     */
    public function deleteTeam(int $companyId, int $teamId): int
    {
        return $this->teamsTable->deleteTeam($companyId, $teamId);
    }
}
