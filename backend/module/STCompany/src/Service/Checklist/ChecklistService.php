<?php

declare(strict_types=1);

namespace STCompany\Service\Checklist;

use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Data\ChecklistsTable;
use STCompany\Entity\Checklist\Checklist;
use STCompany\Entity\Checklist\ChecklistCollection;

class ChecklistService
{
    public function __construct(private ChecklistsTable $checklistsTable)
    {
    }

    /**
     * @throws NotFoundApiException
     */
    public function getChecklist(int $checklistId, int $companyId): Checklist
    {
        return $this->checklistsTable->getChecklist($checklistId, $companyId);
    }

    public function getCompanyChecklistsSummary(int $companyId): array
    {
        return $this->checklistsTable->getCompanyChecklistsSummary($companyId);
    }

    public function getCompanyChecklists(int $companyId): ChecklistCollection
    {
        return $this->checklistsTable->getCompanyChecklists($companyId);
    }

    public function save(Checklist $checklist): Checklist
    {
        $this->checklistsTable->saveChecklist($checklist);
        return $checklist;
    }

    public function delete(int $checklistId, int $companyId): void
    {
        $this->checklistsTable->deleteChecklist($checklistId, $companyId);
    }
}
