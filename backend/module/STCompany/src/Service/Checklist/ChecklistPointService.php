<?php

declare(strict_types=1);

namespace STCompany\Service\Checklist;

use Carbon\Carbon;
use Exception;
use ST<PERSON>pi\Entity\Exception\ForbiddenApiException;
use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Data\ChecklistsPointsTable;
use ST<PERSON><PERSON>pany\Entity\Checklist\ChecklistPoint;
use STCompany\Entity\Checklist\ChecklistPointCollection;

class ChecklistPointService
{
    public function __construct(private ChecklistsPointsTable $checklistsPointsTable)
    {
    }

    /**
     * @throws NotFoundApiException
     */
    public function getChecklistPoint(int $checklistPointId, int $companyId): ChecklistPoint
    {
        return $this->checklistsPointsTable->getChecklistPoint($checklistPointId, $companyId);
    }

    public function getChecklistPointsByChecklistId(int $checklistId, int $companyId): ChecklistPointCollection
    {
        return $this->checklistsPointsTable->getChecklistPointsByChecklistId($checklistId, $companyId);
    }

    public function getChecklistPointLastUpdate(int $companyId, ?int $checklistId = null): ?Carbon
    {
        return $this->checklistsPointsTable->getChecklistPointLastUpdate($companyId, $checklistId);
    }

    public function save(
        ChecklistPoint $checklistPoint,
    ): ChecklistPoint {
        $checklistPoint->setUpdatedAt(Carbon::now());
        $this->checklistsPointsTable->saveChecklistPoint($checklistPoint);

        return $checklistPoint;
    }

    /**
     * @throws ForbiddenApiException
     */
    public function delete(int $checklistPointId, int $companyId): void
    {
        try {
            $this->getChecklistPoint(
                $checklistPointId,
                $companyId
            );
        } catch (NotFoundApiException $e) {
            throw new ForbiddenApiException('Checklist point does not belong to your company');
        }


        $this->checklistsPointsTable->deleteChecklistPoint($checklistPointId);
    }

    /**
     * @throws Exception
     */
    public function changeOrder(array $orderedIds): void
    {
        $this->checklistsPointsTable->updateOrder($orderedIds);
    }
}
