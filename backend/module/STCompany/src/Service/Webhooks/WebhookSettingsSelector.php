<?php

declare(strict_types=1);

namespace STCompany\Service\Webhooks;

use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Data\CompaniesWebhooksSettingsTable;

class WebhookSettingsSelector
{
    public function __construct(private readonly CompaniesWebhooksSettingsTable $companiesWebhooksSettingsTable)
    {
    }

    public function isWebhooksEnabled(string $webhookType, int $companyId): bool
    {
        $webhookSettingsData = $this->companiesWebhooksSettingsTable->getWebhookSettingsDataByType($webhookType, $companyId);

        if (is_null($webhookSettingsData)) {
            return false;
        }

        return (bool) $webhookSettingsData['is_enabled'];
    }

    /**
     * @throws NotFoundApiException
     */
    public function getCompaniesWebhooksSettingsData(int $id, int $companyId): ?array
    {
        $data = $this->companiesWebhooksSettingsTable->getWebhookSettingsData($id, $companyId);

        return $this->formatData($data);
    }

    public function getCompaniesWebhooksSettingsDataList(int $companyId): array
    {
        $list = $this->companiesWebhooksSettingsTable->getCompaniesWebhookSettingsDataList($companyId);
        foreach ($list as &$data) {
            $data = $this->formatData($data);
        }

        return $list;
    }

    public function getCompaniesWebhooksSettingsDataByType(string $webhookType, int $companyId): ?array
    {
        $data = $this->companiesWebhooksSettingsTable->getWebhookSettingsDataByType($webhookType, $companyId);

        return $this->formatData($data);
    }

    private function formatData(?array $data): ?array
    {
        if (!is_null($data) && array_key_exists('headers', $data)) {
            $data['headers'] = !is_null($data['headers']) ? json_decode($data['headers'], true) : [];
        }

        return $data;
    }
}
