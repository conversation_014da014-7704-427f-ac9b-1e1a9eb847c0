<?php

declare(strict_types=1);

namespace STCompany\Service\Webhooks;

use STCompany\Data\CompaniesWebhooksSettingsTable;

final class WebhooksSettingsSaver
{
    public function __construct(private readonly CompaniesWebhooksSettingsTable $companiesWebhooksSettingsTable)
    {
    }

    public function createWebhookSettings(
        string $webhookType,
        string $url,
        array $headers,
        bool $isEnabled,
        int $companyId
    ): int {
        return $this
            ->companiesWebhooksSettingsTable
            ->createWebhookSettings($webhookType, $url, $headers, $isEnabled, $companyId);
    }

    public function updateWebhookSettings(
        int $companyWebhookSettingId,
        string $url,
        array $headers,
        bool $isEnabled
    ): void {
        $this
            ->companiesWebhooksSettingsTable
            ->updateWebhookSettings($companyWebhookSettingId, $url, $headers, $isEnabled);
    }
}
