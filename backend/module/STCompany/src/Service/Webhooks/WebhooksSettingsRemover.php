<?php

declare(strict_types=1);

namespace STCompany\Service\Webhooks;

use STCompany\Data\CompaniesWebhooksSettingsTable;

final class WebhooksSettingsRemover
{
    public function __construct(private readonly CompaniesWebhooksSettingsTable $companiesWebhooksSettingsTable)
    {
    }

    public function deleteWebhookSettings(int $companyWebhookSettingId): void
    {
        $this
            ->companiesWebhooksSettingsTable
            ->deleteWebhookSettings($companyWebhookSettingId);
    }
}
