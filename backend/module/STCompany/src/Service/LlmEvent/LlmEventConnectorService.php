<?php

namespace STCompany\Service\LlmEvent;

use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Data\CompanyLlmEventsTable;
use STCompany\Entity\LlmEvent\LlmEvent;

class LlmEventConnectorService
{
    public function __construct(private readonly CompanyLlmEventsTable $llmEventsTable)
    {
    }

    /**
     * @throws NotFoundApiException
     */
    public function connect(int $llmEventId, int $companyId): LlmEvent
    {
        $llmEvent = $this->getConnectedCompanyLlmEvent($llmEventId, $companyId);
        if (!is_null($llmEvent)) {
            return $llmEvent;
        }

        $llmEvent = new LlmEvent();
        $llmEvent->setId($llmEventId);
        $llmEvent->setCompanyId($companyId);

        $this->llmEventsTable->saveEvent($llmEvent);

        return $this->llmEventsTable->getLlmEvent($llmEvent->getId(), $llmEvent->getCompanyId());
    }

    public function disconnect(int $llmEventId, int $companyId): void
    {
        $this->llmEventsTable->deleteEvent($llmEventId, $companyId);
    }

    public function getConnectedCompanyLlmEvent(int $llmEventId, int $companyId): ?LlmEvent
    {
        try {
            return $this->llmEventsTable->getLlmEvent($llmEventId, $companyId);
        } catch (NotFoundApiException) {
            return null;
        }
    }
}
