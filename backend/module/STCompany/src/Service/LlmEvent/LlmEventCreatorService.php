<?php

namespace STCompany\Service\LlmEvent;

use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Entity\LlmEvent\LlmEvent;
use STCompany\Service\Interfaces\LlmEventSaverInterface;
use STLib\Db\TransactionService;
use Throwable;

class LlmEventCreatorService
{
    public function __construct(
        private readonly TransactionService $transactionService,
        private readonly LlmEventSaverInterface $llmEventSaver,
        private readonly LlmEventConnectorService $llmEventConnectorService
    ) {
    }

    /**
     * @param string $name
     * @param string $description
     * @param int $companyId
     * @return LlmEvent
     * @throws NotFoundApiException
     * @throws Throwable
     */
    public function create(string $name, string $description, int $companyId): LlmEvent
    {
        $this->transactionService->beginTransaction();

        try {
            $llmEvent = $this->llmEventSaver->save($name, $description);
            $llmEvent = $this->llmEventConnectorService->connect($llmEvent->getId(), $companyId);

            $this->transactionService->commit();
        } catch (Throwable $t) {
            $this->transactionService->rollback();
            throw $t;
        }


        return $llmEvent;
    }
}
