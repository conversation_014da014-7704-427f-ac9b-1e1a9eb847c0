<?php

declare(strict_types=1);

namespace STCompany\Service\Notification;

use ST<PERSON>all\Entity\Call;
use STFront\Entity\Front;
use STUser\Entity\User;
use ST<PERSON>all\Entity\NotificationCollection;
use ST<PERSON>all\Entity\Comment;

class UserNotificationSenderService
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    /**
     *
     * @var \STCompany\Data\UsersNotificationsTable
     */
    protected \STCompany\Data\UsersNotificationsTable $usersNotificationsTable;

    /**
     *
     * @var \STCompany\Data\UsersNotificationsCallEventsTable
     */
    protected \STCompany\Data\UsersNotificationsCallEventsTable $usersNotificationsCallEventsTable;

    /**
     *
     * @param \STCompany\Data\UsersNotificationsTable $usersNotificationsTable
     * @param \STCompany\Data\UsersNotificationsCallEventsTable $usersNotificationsCallEventsTable
     */
    public function __construct(
        \STCompany\Data\UsersNotificationsTable $usersNotificationsTable,
        \STCompany\Data\UsersNotificationsCallEventsTable $usersNotificationsCallEventsTable,
    ) {
        $this->usersNotificationsTable = $usersNotificationsTable;
        $this->usersNotificationsCallEventsTable = $usersNotificationsCallEventsTable;
    }

    /**
     *
     * @param Comment $comment
     * @param array $notifiedUserIds
     * @return void
     */
    public function sendCommentNotification(Comment $comment, Front $front, array $notifiedUserIds = []): void
    {
        $mentionedCommentsNotificationsData = $this->usersNotificationsTable->getNotificationsByNotificationType($comment->getCompanyId(), \STCompany\Entity\Notification\UserNotification::COMMENT_MENTIONS_TYPE);
        $allCommentsNotificationsData = $this->usersNotificationsTable->getNotificationsByNotificationType($comment->getCompanyId(), \STCompany\Entity\Notification\UserNotification::COMMENT_ALL_TYPE);

        foreach ($mentionedCommentsNotificationsData as $mentionedCommentsNotificationData) {
            if (!in_array($mentionedCommentsNotificationData['user_id'], $notifiedUserIds)) {
                continue;
            }
            foreach (json_decode($mentionedCommentsNotificationData['delivery_ways']) as $deliveryWay) {
                $this->getCommentNotificationSender($deliveryWay)->send(
                    $comment,
                    $this->hydrate((array) $mentionedCommentsNotificationData, User::class),
                    $front,
                );
            }
        }

        foreach ($allCommentsNotificationsData as $allCommentsNotificationData) {
            foreach (json_decode($allCommentsNotificationData['delivery_ways']) as $deliveryWay) {
                $this->getCommentNotificationSender($deliveryWay)->send(
                    $comment,
                    $this->hydrate((array) $allCommentsNotificationData, User::class),
                    $front,
                );
            }
        }
    }

    /**
     *
     * @param Call $call
     * @param string $reviewerName
     * @return void
     */
    public function sendCallEventNotification(Call $call, string $reviewerName, Front $front): void
    {
        $notificationsData = $this->usersNotificationsTable->getNotificationsByNotificationType($call->getCompanyId(), \STCompany\Entity\Notification\UserNotification::CALL_EVENTS_TYPE);
        $notificationsEvents = $this->usersNotificationsCallEventsTable->getNotificationCallEventsByEventIds($call->getCompanyId(), $call->getReviewedEventIds());
        $notificationsEventIdsByUserId = [];
        foreach ($notificationsEvents as $notificationEvents) {
            $userId = (int) $notificationEvents['user_id'];
            $eventId = (int) $notificationEvents['event_id'];
            if (!isset($notificationsEventIdsByUserId[$userId])) {
                $notificationsEventIdsByUserId[$userId] = [];
            }
            $notificationsEventIdsByUserId[$userId][] = $eventId;
        }

        foreach ($notificationsData as $notificationData) {
            $userId = (int) $notificationData['user_id'];
            $userNotificationEventIds = $notificationsEventIdsByUserId[$userId] ?? [];
            $reviewedEventHappeningsForNotification = $call->getReviewedEventHappenings()->slice(function ($eventHappening) use ($userNotificationEventIds) {
                return in_array($eventHappening->getEvent()->getId(), $userNotificationEventIds);
            });
            if ($reviewedEventHappeningsForNotification->isEmpty()) {
                continue;
            }
            foreach (json_decode($notificationData['delivery_ways']) as $deliveryWay) {
                $this->getEventNotificationSender($deliveryWay)->send(
                    $call,
                    $reviewerName,
                    $reviewedEventHappeningsForNotification,
                    $this->hydrate((array) $notificationData, User::class),
                    $front,
                );
            }
        }
    }

    /**
     *
     * @param string $deliveryType
     * @return Sender\CommentNotification\SenderInterface
     */
    private function getCommentNotificationSender(string $deliveryType): Sender\CommentNotification\SenderInterface
    {
        return match ($deliveryType) {
            \STCompany\Entity\Notification\UserNotification::EMAIL_DELIVERY_WAY => new Sender\CommentNotification\EmailSender(),
            default => throw new \Exception('Delivery type ' . $deliveryType . ' is not valid')
        };
    }

    /**
     *
     * @param string $deliveryType
     * @return Sender\CallEventsNotification\SenderInterface
     */
    private function getEventNotificationSender(string $deliveryType): Sender\CallEventsNotification\SenderInterface
    {
        return match ($deliveryType) {
            \STCompany\Entity\Notification\UserNotification::EMAIL_DELIVERY_WAY => new Sender\CallEventsNotification\EmailSender(),
            default => throw new \Exception('Delivery type ' . $deliveryType . ' is not valid')
        };
    }
}
