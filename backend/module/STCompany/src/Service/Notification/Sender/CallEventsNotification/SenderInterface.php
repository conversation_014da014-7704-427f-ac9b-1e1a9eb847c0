<?php

namespace STCompany\Service\Notification\Sender\CallEventsNotification;

use ST<PERSON>all\Entity\Call;
use ST<PERSON>all\Entity\EventHappeningCollection;
use STFront\Entity\Front;
use STUser\Entity\User;

interface SenderInterface
{
    /**
     *
     * @param Call $call
     * @param string $reviewerName
     * @param EventHappeningCollection $EventHappenings
     * @param User $user
     * @param Front $front
     */
    public function send(
        Call $call,
        string $reviewerName,
        EventHappeningCollection $EventHappenings,
        \STUser\Entity\User $user,
        Front $front,
    );
}
