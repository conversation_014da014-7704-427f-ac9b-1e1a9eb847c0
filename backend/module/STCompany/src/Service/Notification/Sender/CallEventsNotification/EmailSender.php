<?php

declare(strict_types=1);

namespace STCompany\Service\Notification\Sender\CallEventsNotification;

use ST<PERSON>all\Entity\Call;
use ST<PERSON>all\Entity\EventHappeningCollection;
use STFront\Entity\Front;
use STUser\Entity\User;

class EmailSender implements SenderInterface
{
    use \STMail\Service\ProvidesMail;

    /**
     *
     * @param Call $call
     * @param string $reviewerName
     * @param EventHappeningCollection $eventHappenings
     * @param User $user
     * @param Front $front
     * @return void
     */
    public function send(
        Call $call,
        string $reviewerName,
        EventHappeningCollection $eventHappenings,
        \STUser\Entity\User $user,
        Front $front,
    ): void {
        $eventsList = [];
        $eventsPreview = [];
        foreach ($eventHappenings as $eventHappening) {
            if (!isset($eventsList[$eventHappening->getEvent()->getId()])) {
                $eventsList[$eventHappening->getEvent()->getId()] = [
                    'event_name' => '',
                    'count' => 0,
                ];
            }
            $eventsList[$eventHappening->getEvent()->getId()]['event_name'] = $eventHappening->getEvent()->getName();
            $eventsList[$eventHappening->getEvent()->getId()]['count']++;
            $eventsPreview[] = [
                'highlight' => $eventHappening->getHighlight(),
                'event_name' => $eventHappening->getEvent()->getName(),
            ];
        }

        $this->mail()->addToQueue($front->getId(), $user, [
            'template_id' => 'call-events',
            'substitutions' => [
                'events_list' => array_values($eventsList),
                'events_preview' => $eventsPreview,
                'agent_name' => $call->getAgentName(),
                'call_date' => $call->getCreated()->toDayDateTimeString(),
                'reviewer' => $reviewerName,
                'client_id' => $call->getClientId(),
                'review_date' => $call->getReviewedTime()?->toDayDateTimeString(),
            ],
        ]);
    }
}
