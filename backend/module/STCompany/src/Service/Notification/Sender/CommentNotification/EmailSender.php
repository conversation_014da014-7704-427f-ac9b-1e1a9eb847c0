<?php

declare(strict_types=1);

namespace STCompany\Service\Notification\Sender\CommentNotification;

use ST<PERSON>all\Entity\Comment;
use STFront\Entity\Front;
use STUser\Entity\User;

class EmailSender implements SenderInterface
{
    use \STMail\Service\ProvidesMail;

    /**
     *
     * @param Comment $comment
     * @param User $user
     * @param Front $front
     * @return void
     */
    public function send(Comment $comment, User $user, Front $front): void
    {
        $this->mail()->addToQueue($front->getId(), $user, [
            'template_id' => 'user-notification',
            'substitutions' => [
                'comment_author_name' => $comment->getUser()->getName(),
                'comment_date' => $comment->getCreated()->toDayDateTimeString(),
                'call_id' => $comment->getCallId(),
                'comment_text' => $comment->getMessageBodyWithMentions(),
            ],
        ]);
    }
}
