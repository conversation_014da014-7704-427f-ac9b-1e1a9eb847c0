<?php

declare(strict_types=1);

namespace STCompany\Service\Notification;

use STC<PERSON>pany\Entity\Notification\UserNotificationCollection;
use STCompany\Data\UsersNotificationsTable;
use STCompany\Data\UsersNotificationsCallEventsTable;

class UserNotificationService
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;
    use \STLib\Db\ProvidesTransaction;

    /**
     *
     * @var UsersNotificationsTable
     */
    protected UsersNotificationsTable $usersNotificationsTable;

    /**
     *
     * @var UsersNotificationsCallEventsTable
     */
    protected UsersNotificationsCallEventsTable $usersNotificationsCallEventsTable;

    /**
     *
     * @param UsersNotificationsTable $usersNotificationsTable
     * @param UsersNotificationsCallEventsTable $usersNotificationsCallEventsTable
     */
    public function __construct(
        UsersNotificationsTable $usersNotificationsTable,
        UsersNotificationsCallEventsTable $usersNotificationsCallEventsTable,
    ) {
        $this->usersNotificationsTable = $usersNotificationsTable;
        $this->usersNotificationsCallEventsTable = $usersNotificationsCallEventsTable;
    }

    /**
     *
     * @param int $companyId
     * @param int $userId
     * @return UserNotificationCollection
     */
    public function getUserNotifications(int $companyId, int $userId): UserNotificationCollection
    {
        $userNotificationData = $this->usersNotificationsTable->getNotifications($companyId, $userId);
        $userNotificationCallEventsData = $this->usersNotificationsCallEventsTable->getNotificationCallEvents($userId);
        return $this->hydrateNotificationCollectionFromArray(
            $userNotificationData->toArray(),
            $userNotificationCallEventsData->toArray(),
        );
    }

    /**
     *
     * @param int $companyId
     * @param int $userId
     * @param UserNotificationCollection $notificationCollection
     * @return int
     * @throws \Exception
     */
    public function saveUserNotifications(int $companyId, int $userId, UserNotificationCollection $notificationCollection): int
    {
        $this->beginTransaction();
        try {
            $this->usersNotificationsTable->deleteNotifications($companyId, $userId);
            $this->usersNotificationsTable->insertNotifications($companyId, $userId, $notificationCollection);
            foreach ($notificationCollection as $notification) {
                if ($notification instanceof \STCompany\Entity\Notification\CallEventUserNotification) {
                    $this->usersNotificationsCallEventsTable->deleteNotificationCallEvents($userId);
                    $this->usersNotificationsCallEventsTable->insertNotificationCallEvents($userId, $notification->getEventIds());
                }
            }
            $this->commit();
        } catch (\Exception $e) {
            $this->rollback();
            throw $e;
        }
        return $notificationCollection->count();
    }

    /**
     *
     * @param array $notificationsData
     * @param array $eventIdsData
     * @return \STCompany\Entity\Notification\UserNotificationCollection
     * @throws \RuntimeException
     */
    public function hydrateNotificationCollectionFromArray(array $notificationsData, array $eventIdsData): \STCompany\Entity\Notification\UserNotificationCollection
    {
        $notificationCollection = new \STCompany\Entity\Notification\UserNotificationCollection();
        foreach ($notificationsData as $notificationData) {
            if (!array_key_exists('notification_type', $notificationData)) {
                throw new \RuntimeException('Required parameter "notification_type" is missed');
            }
            switch ($notificationData['notification_type']) {
                case \STCompany\Entity\Notification\UserNotification::CALL_EVENTS_TYPE:
                    $notificationData['event_ids'] = $eventIdsData;
                    $userNotificationClass = \STCompany\Entity\Notification\CallEventUserNotification::class;
                    break;
                default:
                    $userNotificationClass = \STCompany\Entity\Notification\UserNotification::class;
                    break;
            }
            $notificationCollection->add($this->hydrate($notificationData, $userNotificationClass));
        }
        return $notificationCollection;
    }
}
