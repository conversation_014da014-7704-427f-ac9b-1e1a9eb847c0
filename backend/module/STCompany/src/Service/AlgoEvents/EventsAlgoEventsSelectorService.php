<?php

declare(strict_types=1);

namespace STCompany\Service\AlgoEvents;

use STCompany\Data\EventsAlgoEventsTable;

class EventsAlgoEventsSelectorService
{
    public function __construct(private readonly EventsAlgoEventsTable $eventsAlgoEventsTable)
    {
    }

    public function filterMostPopularAlgoEventNames(array $eventNames, int $limit): array
    {
        $resultSet = $this->eventsAlgoEventsTable->orderAlgoEventsByCount($eventNames, $limit);

        $result = [];
        foreach ($resultSet->toArray() as $row) {
            $result[] = $row['algo_event'];
        }

        return $result;
    }
}
