<?php

namespace STCompany\Service\AgentPrecalculation;

class AgentPrecalculationManagerServiceWrapper
{
    /**
     *
     * @var AgentPrecalculationManagerService|null
     */
    private static ?AgentPrecalculationManagerService $agentPrecalculationManagerService = null;

    /**
     *
     * @param AgentPrecalculationManagerService $agentPrecalculationManagerService
     * @return void
     */
    public static function setStaticAgentPrecalculationManagerService(AgentPrecalculationManagerService $agentPrecalculationManagerService): void
    {
        self::$agentPrecalculationManagerService = $agentPrecalculationManagerService;
    }

    /**
     *
     * @return AgentPrecalculationManagerService
     */
    public function getStaticAgentPrecalculationManagerService(): AgentPrecalculationManagerService
    {
        return self::$agentPrecalculationManagerService;
    }
}
