<?php

declare(strict_types=1);

namespace STCompany\Service\AgentPrecalculation;

class AgentPrecalculationManagerService
{
    use \STRabbit\Service\ProvidesRabbit;

    public const MANUAL_LAUNCH_PRIORITY = 70;
    public const CALL_PRECALCULATED_PRIORITY = 60;
    public const CALL_SENT_PRIORITY = 30;

    /**
     *
     * @var \STCall\Data\CallsTable
     */
    protected \STCall\Data\CallsTable $callsTable;

    /**
     *
     * @param \STCall\Data\CallsTable $callsTable
     */
    public function __construct(\STCall\Data\CallsTable $callsTable)
    {
        $this->callsTable = $callsTable;
    }

    /**
     *
     * @param int $companyId
     * @param int|array $agentId
     * @param \Carbon\Carbon|null $startDate
     * @param \Carbon\Carbon|null $endDate
     * @param int|array|null $roleId
     * @param int $priority
     * @param int $batchSize
     * @return bool
     */
    public function addAgentsToPrecalculateQueue(
        int $companyId,
        int|array $agentId,
        \Carbon\Carbon $startDate = null,
        \Carbon\Carbon $endDate = null,
        int|array $roleId = null,
        int $priority = 1,
        int $batchSize = 20
    ): bool {
        $agentIds = is_array($agentId) ? $agentId : [$agentId];
        $channel = $this->rabbit()->getChannel();
        foreach (array_chunk($agentIds, $batchSize) as $agentIdsBatch) {
            $message = new \PhpAmqpLib\Message\AMQPMessage(json_encode([
                'company_id' => $companyId,
                'agent_ids' => $agentIdsBatch,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'role_id' => $roleId,
            ]), [
                'priority' => $priority,
            ]);
            $channel->basic_publish($message, '', routing_key: \STCompany\Daemon\PrecalculateAgentsDaemon::AGENT_PRECALCULATION_QUEUE);
        }
        $channel->close();
        return true;
    }

    /**
     *
     * @param \STCall\Entity\Call $call
     * @param int|array|null $roleId
     * @param int $priority
     * @return bool
     */
    public function addAgentsFromCallToPrecalculateQueue(
        \STCall\Entity\Call $call,
        int|array $roleId = null,
        int $priority = 1
    ): bool {
        $startDate = clone ($call->getTime())->startOfDay();
        $endDate = clone ($call->getTime())->endOfDay();
        return $this->addAgentsToPrecalculateQueue(
            $call->getCompanyId(),
            $call->getAgentId(),
            $startDate,
            $endDate,
            $roleId,
            $priority,
        );
    }
}
