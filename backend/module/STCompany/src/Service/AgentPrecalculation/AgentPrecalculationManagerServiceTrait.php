<?php

namespace STCompany\Service\AgentPrecalculation;

trait AgentPrecalculationManagerServiceTrait
{
    /**
     *
     * @var AgentPrecalculationManagerServiceWrapper|null
     */
    private ?AgentPrecalculationManagerServiceWrapper $agentPrecalculationManagerServiceWrapper = null;

    /**
     *
     * @return AgentPrecalculationManagerService
     */
    protected function getAgentPrecalculationManager(): AgentPrecalculationManagerService
    {
        if (is_null($this->agentPrecalculationManagerServiceWrapper)) {
            $this->agentPrecalculationManagerServiceWrapper = new AgentPrecalculationManagerServiceWrapper();
        }
        return $this->agentPrecalculationManagerServiceWrapper->getStaticAgentPrecalculationManagerService();
    }
}
