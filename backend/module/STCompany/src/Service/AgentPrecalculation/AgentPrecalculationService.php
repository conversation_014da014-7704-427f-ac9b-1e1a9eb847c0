<?php

declare(strict_types=1);

namespace STCompany\Service\AgentPrecalculation;

class AgentPrecalculationService
{
    /**
     * @var \STCompany\Data\PrecalculatedAgentsTable
     */
    protected \STCompany\Data\PrecalculatedAgentsTable $precalculatedAgentsTable;

    /**
     * @param \STCompany\Data\PrecalculatedAgentsTable $precalculatedAgentsTable
     */
    public function __construct(
        \STCompany\Data\PrecalculatedAgentsTable $precalculatedAgentsTable,
    ) {
        $this->precalculatedAgentsTable = $precalculatedAgentsTable;
    }

    /**
     * @param int $companyId
     * @param array|int $agentIds
     * @param \Carbon\Carbon|null $startDate
     * @param \Carbon\Carbon|null $endDate
     * @param int|array|null $roleId
     * @return void
     */
    public function precalculateAgents(
        int $companyId,
        array|int $agentIds,
        \Carbon\Carbon $startDate = null,
        \Carbon\Carbon $endDate = null,
        int|array $roleId = null
    ): void {
        $this->precalculatedAgentsTable->precalculateAgents($companyId, $agentIds, $startDate, $endDate, $roleId);
    }
}
