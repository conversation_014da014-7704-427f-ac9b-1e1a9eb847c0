<?php

declare(strict_types=1);

namespace STCompany\Service;

use STCompany\Data\PermissionsTable;
use STCompany\Entity\Permission\Permission;
use STCompany\Entity\Permission\PermissionCollection;
use STLib\Mvc\Hydrator\BaseHydratorTrait;

class PermissionService
{
    use BaseHydratorTrait;

    /**
     *
     * @var PermissionsTable
     */
    protected PermissionsTable $permissionsTable;

    /**
     *
     * @param PermissionsTable $permissionsTable
     */
    public function __construct(PermissionsTable $permissionsTable)
    {
        $this->permissionsTable = $permissionsTable;
    }

    /**
     *
     * @return PermissionCollection
     */
    public function getPermissionHierarchy(): PermissionCollection
    {
        $permissionCollection = new PermissionCollection();
        $permissionsData = $this->permissionsTable->getPermissions();
        $permissionsToBeAddedToTree = [];
        // get all permissions
        foreach ($permissionsData as $permissionData) {
            $permission = $this->hydrate((array) $permissionData, Permission::class, withConstructor: true);
            $permissionsToBeAddedToTree[] = $permission;
        }
        // build tree hierarchy
        while (count($permissionsToBeAddedToTree) > 0) {
            $permission = array_shift($permissionsToBeAddedToTree);
            // add element to end of the list, if parent is not added to tree
            if (!is_null($permission->getParentPermissionId()) && !$permissionCollection->offsetExists($permission->getParentPermissionId())) {
                array_push($permissionsToBeAddedToTree, $permission);
                continue;
            }
            $permissionCollection->add($permission);
            // add element to parent
            if (!is_null($permission->getParentPermissionId())) {
                $permissionCollection->offsetGet($permission->getParentPermissionId())->getChildren()->add($permission);
            }
        }
        // remove not root elements from root level
        $permissionCollection->filter(function ($permission) {
            return is_null($permission->getParentPermissionId());
        });
        return $permissionCollection;
    }

    /**
     *
     * @return PermissionCollection
     */
    public function getPermissions(): PermissionCollection
    {
        $permissionCollection = new PermissionCollection();
        $permissionsData = $this->permissionsTable->getPermissions();
        foreach ($permissionsData as $permissionData) {
            $permission = $this->hydrate((array) $permissionData, Permission::class);
            $permissionCollection->add($permission);
        }
        return $permissionCollection;
    }
}
