<?php

declare(strict_types=1);

namespace STCompany\Service;

use Carbon\Carbon;
use ST<PERSON>pi\Entity\Exception\NotFoundApiException;
use STCompany\Data\BillingSettingsTable;
use STCompany\Data\CompaniesTable;
use STCompany\Entity\Company;
use STLib\Db\TransactionService;
use Throwable;

class CompanyStatusUpdater
{
    public function __construct(
        private readonly CompaniesTable $companiesTable,
        private readonly BillingSettingsTable $billingSettingsTable,
        private readonly TransactionService $transactionService,
    ) {
    }

    /**
     * @throws NotFoundApiException
     * @throws Throwable
     */
    public function update(Company $company, string $status): void
    {
        if ($this->needReduceTrialDuration($company->getStatus(), $status)) {
            $this->reduceTrialDuration($company, $status);

            return;
        }

        $this->companiesTable->partialUpdateCompany($company->getId(), ['status' => $status]);
    }

    private function needReduceTrialDuration(string $currentStatus, string $newStatus): bool
    {
        if ($currentStatus !== Company::STATUS_TRIAL) {
            return false;
        }

        return in_array($newStatus, [Company::STATUS_TRIAL_EXPIRED, Company::STATUS_ACTIVE, Company::STATUS_SUSPENDED]);
    }

    /**
     * @throws Throwable
     * @throws NotFoundApiException
     */
    private function reduceTrialDuration(Company $company, string $newStatus): void
    {
        $billingSettings = $this->billingSettingsTable->getBillingSettings($company->getId());

        $actualTrialDuration = ((int) $company->getCreatedAt()->diffInDays(Carbon::now())) - 1;
        $billingSettings->setDuration($actualTrialDuration);

        $this->transactionService->beginTransaction();

        try {
            $this->companiesTable->partialUpdateCompany($company->getId(), ['status' => $newStatus]);
            $this->billingSettingsTable->updateBillingSettings($billingSettings);

            $this->transactionService->commit();
        } catch (Throwable $t) {
            $this->transactionService->rollBack();

            throw $t;
        }
    }
}
