<?php

declare(strict_types=1);

namespace STCompany\Entity\BillingSettings;

use STLib\Db\Entity;

class BillingSettings extends Entity
{
    private int $companyId;
    private int $duration;
    private int $callsSeconds;
    private int $chatsAmount;
    private int $summarizationsAmount;
    private int $checklistsAmount;
    private int $eachChecklistsCallsAmount;

    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    public function setCompanyId(int $companyId): static
    {
        $this->companyId = $companyId;

        return $this;
    }

    public function getDuration(): int
    {
        return $this->duration;
    }

    public function setDuration(int $duration): static
    {
        $this->duration = $duration;

        return $this;
    }

    public function getCallsSeconds(): int
    {
        return $this->callsSeconds;
    }

    public function setCallsSeconds(int $callsSeconds): static
    {
        $this->callsSeconds = $callsSeconds;

        return $this;
    }

    public function getChatsAmount(): int
    {
        return $this->chatsAmount;
    }

    public function setChatsAmount(int $chatsAmount): static
    {
        $this->chatsAmount = $chatsAmount;

        return $this;
    }

    public function getSummarizationsAmount(): int
    {
        return $this->summarizationsAmount;
    }

    public function setSummarizationsAmount(int $summarizationsAmount): static
    {
        $this->summarizationsAmount = $summarizationsAmount;

        return $this;
    }

    public function getChecklistsAmount(): int
    {
        return $this->checklistsAmount;
    }

    public function setChecklistsAmount(int $checklistsAmount): static
    {
        $this->checklistsAmount = $checklistsAmount;

        return $this;
    }

    public function getEachChecklistsCallsAmount(): int
    {
        return $this->eachChecklistsCallsAmount;
    }

    public function setEachChecklistsCallsAmount(int $eachChecklistsCallsAmount): static
    {
        $this->eachChecklistsCallsAmount = $eachChecklistsCallsAmount;

        return $this;
    }

    public function toArray(): array
    {
        return [
            'company_id' => $this->companyId,
            'duration' => $this->duration,
            'calls_seconds' => $this->callsSeconds,
            'chats_amount' => $this->chatsAmount,
            'summarizations_amount' => $this->summarizationsAmount,
            'checklists_amount' => $this->checklistsAmount,
            'each_checklists_calls_amount' => $this->eachChecklistsCallsAmount
        ];
    }

    public function getId(): ?int
    {
        return $this->getCompanyId();
    }
}
