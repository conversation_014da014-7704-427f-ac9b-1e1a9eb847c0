<?php

declare(strict_types=1);

namespace STCompany\Entity;

class TeamCollection extends \STLib\Expand\Collection
{
    /**
     *
     * @param \STCompany\Entity\Team $team
     * @param mixed $key
     * @return \STCompany\Entity\Team
     * @throws \RuntimeException
     */
    public function add(mixed $team, string|int|null $key = null): \STLib\Expand\Collection
    {
        if (!($team instanceof Team)) {
            throw new \RuntimeException('Team must be an instace of "\STCompany\Entity\Team"');
        }
        parent::add($team, $key ?? $team->getId());
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        $result = [];
        foreach ($this as $team) {
            $result[] = $team->toArray();
        }
        return $result;
    }
}
