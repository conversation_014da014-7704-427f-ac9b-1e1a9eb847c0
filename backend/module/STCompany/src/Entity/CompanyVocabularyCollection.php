<?php

declare(strict_types=1);

namespace STCompany\Entity;

class CompanyVocabularyCollection extends \STLib\Expand\Collection
{
    /**
     *
     * @param mixed $vocabularyWord
     * @param string|int|null $key
     * @return \STLib\Expand\Collection
     * @throws \RuntimeException
     */
    public function add(mixed $vocabularyWord, string|int|null $key = null): \STLib\Expand\Collection
    {
        if (!($vocabularyWord instanceof CompanyVocabularyWord)) {
            throw new \RuntimeException('Vocabulary word must be an instance of "' . CompanyVocabularyWord::class . '"');
        }

        parent::add($vocabularyWord, $key ?? $vocabularyWord->getId());
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        $result = [];
        foreach ($this as $vocabularyWord) {
            $result[] = $vocabularyWord->toArray();
        }
        return $result;
    }
}
