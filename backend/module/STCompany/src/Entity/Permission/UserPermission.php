<?php

declare(strict_types=1);

namespace STCompany\Entity\Permission;

class UserPermission extends BasePermission
{
    /**
     *
     * @var string
     */
    protected string $accessLevel;

    /**
     *
     * @return string
     */
    public function getAccessLevel(): string
    {
        return $this->accessLevel;
    }

    /**
     *
     * @param string $accessLevel
     * @return UserPermission
     */
    public function setAccessLevel(string $accessLevel): UserPermission
    {
        $this->accessLevel = $accessLevel;
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        return $this->extract($this);
    }
}
