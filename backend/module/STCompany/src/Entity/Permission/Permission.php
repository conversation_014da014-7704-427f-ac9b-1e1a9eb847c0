<?php

declare(strict_types=1);

namespace STCompany\Entity\Permission;

class Permission extends BasePermission
{
    /**
     *
     * @var PermissionCollection|null
     */
    protected ?PermissionCollection $children = null;

    /**
     *
     * Class constructor
     */
    public function __construct()
    {
        $this->children = new PermissionCollection();
    }

    /**
     *
     * @return PermissionCollection|null
     */
    public function getChildren(): ?PermissionCollection
    {
        return $this->children;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        $result = $this->extract($this);
        $result['children'] = $this->children instanceof PermissionCollection ? $this->children->toArray() : [];
        return $result;
    }
}
