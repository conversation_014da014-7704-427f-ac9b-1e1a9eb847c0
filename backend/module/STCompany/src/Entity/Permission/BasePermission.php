<?php

declare(strict_types=1);

namespace STCompany\Entity\Permission;

abstract class BasePermission
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    /**
     *
     * @var int
     */
    protected ?int $id = null;
    /**
     *
     * @var int
     */
    protected ?int $parentPermissionId = null;

    /**
     *
     * @var string
     */
    protected string $name;

    /**
     *
     * @var string
     */
    protected string $systemName;

    /**
     *
     * @var bool
     */
    protected bool $isReadPermissionOnly = false;

    /**
     *
     * @var bool
     */
    protected bool $isWritePermissionOnly = false;

    /**
     *
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     *
     * @return int|null
     */
    public function getParentPermissionId(): ?int
    {
        return $this->parentPermissionId;
    }

    /**
     *
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     *
     * @return string
     */
    public function getSystemName(): string
    {
        return $this->systemName;
    }

    /**
     *
     * @return bool
     */
    public function getIsReadPermissionOnly(): bool
    {
        return $this->isReadPermissionOnly;
    }

    /**
     *
     * @return bool
     */
    public function getIsWritePermissionOnly(): bool
    {
        return $this->isWritePermissionOnly;
    }

    /**
     *
     * @param int|null $id
     * @return BasePermission
     */
    public function setId(?int $id): BasePermission
    {
        $this->id = $id;
        return $this;
    }

    /**
     *
     * @param int|null $parentPermissionId
     * @return BasePermission
     */
    public function setParentPermissionId(?int $parentPermissionId): BasePermission
    {
        $this->parentPermissionId = $parentPermissionId;
        return $this;
    }

    /**
     *
     * @param string $name
     * @return BasePermission
     */
    public function setName(string $name): BasePermission
    {
        $this->name = $name;
        return $this;
    }

    /**
     *
     * @param string $systemName
     * @return  BasePermission
     */
    public function setSystemName(string $systemName): BasePermission
    {
        $this->systemName = $systemName;
        return $this;
    }

    /**
     *
     * @param bool $isReadPermissionOnly
     * @return BasePermission
     */
    public function setIsReadPermissionOnly(bool $isReadPermissionOnly): BasePermission
    {
        $this->isReadPermissionOnly = $isReadPermissionOnly;
        return $this;
    }

    /**
     *
     * @param bool $isWritePermissionOnly
     * @return BasePermission
     */
    public function setIsWritePermissionOnly(bool $isWritePermissionOnly): BasePermission
    {
        $this->isWritePermissionOnly = $isWritePermissionOnly;
        return $this;
    }

    /**
     *
     * @param bool|null $isReadPermissionOnly
     * @return BasePermission|bool
     */
    public function isReadPermissionOnly(?bool $isReadPermissionOnly): BasePermission|bool
    {
        if (is_null($isReadPermissionOnly)) {
            return $this->isReadPermissionOnly($isReadPermissionOnly);
        }
        $this->isReadPermissionOnly = $isReadPermissionOnly;
        return $this;
    }

    /**
     *
     * @param bool|null $isWritePermissionOnly
     * @return BasePermission|bool
     */
    public function isWritePermissionOnly(?bool $isWritePermissionOnly): BasePermission|bool
    {
        if (is_null($isWritePermissionOnly)) {
            return $this->isWritePermissionOnly;
        }
        $this->isWritePermissionOnly = $isWritePermissionOnly;
        return $this;
    }

    /**
     *
     * @return array
     */
    abstract public function toArray(): array;
}
