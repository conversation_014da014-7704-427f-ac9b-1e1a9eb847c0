<?php

declare(strict_types=1);

namespace STCompany\Entity\Permission;

use RuntimeException;
use STLib\Expand\Collection;

class PermissionCollection extends Collection
{
    /**
     *
     * @param BasePermission $permission
     * @param string|int|null $key
     * @return Collection
     * @throws RuntimeException
     */
    public function add(mixed $permission, string|int|null $key = null): Collection
    {
        if (!($permission instanceof BasePermission)) {
            throw new RuntimeException('Permission must be an instace of "\STCompany\Entity\BasePermission"');
        }
        parent::add($permission, $key ?? $permission->getId());
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        $result = [];
        foreach ($this as $permission) {
            $result[] = $permission->toArray();
        }
        return $result;
    }
}
