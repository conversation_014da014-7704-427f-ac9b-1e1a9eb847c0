<?php

declare(strict_types=1);

namespace STCompany\Entity;

use STLib\Mvc\Hydrator\BaseHydratorTrait;

class CompanyVocabularyWord
{
    use BaseHydratorTrait;

    /**
     *
     * @var ?int
     */
    protected ?int $id = null;

    /**
     *
     * @var int
     */
    protected int $companyId;

    /**
     *
     * @var string|null
     */
    protected ?string $word = null;

    /**
     *
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     *
     * @return int
     */
    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    /**
     *
     * @return string|null
     */
    public function getWord(): ?string
    {
        return $this->word;
    }

    /**
     *
     * @param int|null $id
     * @return CompanyVocabularyWord
     */
    public function setId(?int $id): CompanyVocabularyWord
    {
        $this->id = $id;
        return $this;
    }

    /**
     *
     * @param int $companyId
     * @return CompanyVocabularyWord
     */
    public function setCompanyId(int $companyId): CompanyVocabularyWord
    {
        $this->companyId = $companyId;
        return $this;
    }

    /**
     *
     * @param string|null $word
     * @return CompanyVocabularyWord
     */
    public function setWord(?string $word): CompanyVocabularyWord
    {
        $this->word = $word;
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        return $this->extract($this);
    }
}
