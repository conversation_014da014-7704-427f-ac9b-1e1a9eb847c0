<?php

declare(strict_types=1);

namespace STCompany\Entity;

use STLib\Expand\Collection;
use Traversable;

/**
 * @extends Traversable<array-key,Company>
 */
class CompanyCollection extends Collection
{
    /**
     * @param mixed $company
     * @param string|int|null $key
     * @return Collection
     */
    public function add(mixed $company, string|int|null $key = null): Collection
    {
        if (!($company instanceof Company)) {
            throw new \RuntimeException('Company must be an instance of "\STCompany\Entity\Company"');
        }
        parent::add($company, $key ?? $company->getId());
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        $result = [];
        foreach ($this as $company) {
            $result[] = $company->toArray();
        }
        return $result;
    }
}
