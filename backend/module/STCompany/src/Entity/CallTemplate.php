<?php

declare(strict_types=1);

namespace STCompany\Entity;

class CallTemplate
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    /**
     *
     * @var int
     */
    protected int $id;

    /**
     *
     * @var string
     */
    protected string $regex;

    /**
     *
     * @var int|null
     */
    protected ?int $callIdNumber;

    /**
     *
     * @var int|null
     */
    protected ?int $clientIdNumber;

    /**
     *
     * @var int
     */
    protected int $agentIdNumber;

    /**
     *
     * @var int
     */
    protected int $dateNumber;

    /**
     *
     * @var int|null
     */
    protected ?int $timeNumber;

    /**
     *
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     *
     * @return string
     */
    public function getRegex(): string
    {
        return $this->regex;
    }

    /**
     *
     * @return int|null
     */
    public function getCallIdNumber(): ?int
    {
        return $this->callIdNumber;
    }

    /**
     *
     * @return int|null
     */
    public function getClientIdNumber(): ?int
    {
        return $this->clientIdNumber;
    }

    /**
     *
     * @return int
     */
    public function getAgentIdNumber(): int
    {
        return $this->agentIdNumber;
    }

    /**
     *
     * @return int
     */
    public function getDateNumber(): int
    {
        return $this->dateNumber;
    }

    /**
     *
     * @return int|null
     */
    public function getTimeNumber(): ?int
    {
        return $this->timeNumber;
    }

    /**
     *
     * @param int $id
     * @return CallTemplate
     */
    public function setId(int $id): CallTemplate
    {
        $this->id = $id;
        return $this;
    }

    /**
     *
     * @param string $regex
     * @return CallTemplate
     */
    public function setRegex(string $regex): CallTemplate
    {
        $this->regex = $regex;
        return $this;
    }

    /**
     *
     * @param int|null $callIdNumber
     * @return CallTemplate
     */
    public function setCallIdNumber(?int $callIdNumber): CallTemplate
    {
        $this->callIdNumber = $callIdNumber;
        return $this;
    }

    /**
     *
     * @param int|null $clientIdNumber
     * @return CallTemplate
     */
    public function setClientIdNumber(?int $clientIdNumber): CallTemplate
    {
        $this->clientIdNumber = $clientIdNumber;
        return $this;
    }

    /**
     *
     * @param int $agentIdNumber
     * @return CallTemplate
     */
    public function setAgentIdNumber(int $agentIdNumber): CallTemplate
    {
        $this->agentIdNumber = $agentIdNumber;
        return $this;
    }

    /**
     *
     * @param int $dateNumber
     * @return CallTemplate
     */
    public function setDateNumber(int $dateNumber): CallTemplate
    {
        $this->dateNumber = $dateNumber;
        return $this;
    }

    /**
     *
     * @param int|null $timeNumber
     * @return CallTemplate
     */
    public function setTimeNumber(?int $timeNumber): CallTemplate
    {
        $this->timeNumber = $timeNumber;
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        return $this->extract($this);
    }
}
