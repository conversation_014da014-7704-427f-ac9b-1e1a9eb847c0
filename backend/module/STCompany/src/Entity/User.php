<?php

namespace STCompany\Entity;

class User extends \STUser\Entity\User
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    /**
     *
     * @var Role|null
     */
    protected ?Role $role = null;

    /**
     *
     * @var Role|null
     */
    protected ?Role $baseRole = null;

    /**
     *
     * @var TeamCollection|null
     */
    protected ?TeamCollection $teams = null;

    /**
     *
     * @var bool|null
     */
    protected ?bool $isActive = null;

    /**
     *
     * @var array|null
     */
    protected ?array $languages = null;

    /**
     *
     * @var string|null
     */
    protected ?string $companyUserId = null;

    /**
     *
     * @var bool|null
     */
    protected ?bool $isAutoAnalyzeCalls = true;

    protected bool $isPending = false;

    /**
     *
     * Class constructor
     */
    public function __construct()
    {
        $this->teams = new TeamCollection();
        $this->role = new Role();
        $this->languages = [];
    }

    /**
     *
     * @return Role|null
     */
    public function getRole(): ?Role
    {
        return $this->role;
    }

    /**
     *
     * @return Role|null
     */
    public function getBaseRole(): ?Role
    {
        return $this->baseRole;
    }

    /**
     *
     * @return TeamCollection|null
     */
    public function getTeams(): ?TeamCollection
    {
        return $this->teams;
    }

    /**
     *
     * @return bool|null
     */
    public function getIsActive(): ?bool
    {
        return $this->isActive;
    }

    /**
     *
     * @return array|null
     */
    public function getLanguages(): ?array
    {
        return $this->languages;
    }

    /**
     *
     * @return string|null
     */
    public function getCompanyUserId(): ?string
    {
        return $this->companyUserId;
    }

    /**
     *
     * @param Role|null $role
     * @return User
     */
    public function setRole(?Role $role): User
    {
        $this->role = $role;
        return $this;
    }

    /**
     *
     * @param Role|null $baseRole
     * @return User
     */
    public function setBaseRole(?Role $baseRole): User
    {
        $this->baseRole = $baseRole;
        return $this;
    }

    /**
     *
     * @param TeamCollection|null $teams
     * @return User
     */
    public function setTeams(?TeamCollection $teams): User
    {
        $this->teams = $teams;
        return $this;
    }

    /**
     *
     * @param bool|null $isActive
     * @return User
     */
    public function setIsActive(?bool $isActive): User
    {
        $this->isActive = $isActive;
        return $this;
    }

    /**
     *
     * @param array $languages
     * @return User
     */
    public function setLanguages(array $languages): User
    {
        $this->languages = $languages;
        return $this;
    }

    /**
     *
     * @param string|null $companyUserId
     * @return User
     */
    public function setCompanyUserId(?string $companyUserId): User
    {
        $this->companyUserId = $companyUserId;
        return $this;
    }

    /**
     *
     * @param bool|null $isActive
     * @return null|bool|User
     */
    public function isActive(?bool $isActive = null): null|bool|User
    {
        if (is_bool($isActive)) {
            $this->isActive = $isActive;
            return $this;
        }
        return $this->isActive;
    }

    /**
     *
     * @param string $language
     * @return User
     */
    public function addLanguage(string $language): User
    {
        $this->languages = $this->languages ?? [];
        $this->languages[] = $language;
        return $this;
    }

    /**
     *
     * @param bool|null $isAutoAnalyzeCalls
     * @return bool|User
     */
    public function isAutoAnalyzeCalls(?bool $isAutoAnalyzeCalls = null): bool|User
    {
        if (is_null($isAutoAnalyzeCalls)) {
            return $this->isAutoAnalyzeCalls;
        }
        $this->isAutoAnalyzeCalls = $isAutoAnalyzeCalls;
        return $this;
    }

    public function isPending(?bool $isPending = null): bool|User
    {
        if (is_null($isPending)) {
            return $this->isPending;
        }
        $this->isPending = $isPending;
        return $this;
    }

    /**
     *
     * @param array|null $attributes
     * @return array
     */
    public function toArray(array $attributes = null): array
    {
        $array = $this->extract($this);
        unset($array['password']);
        unset($array['password_confirm']);
        unset($array['two_factor_secret']);
        unset($array['registration_date']);
        unset($array['has_two_factor_secret']);
        unset($array['base_role']);
        if (is_null($this->isActive)) {
            unset($array['is_active']);
        }
        if (empty($this->token)) {
            unset($array['token']);
        }
        if (is_null($this->languages)) {
            unset($array['languages']);
        }
        $array['role'] = $this->getRole() instanceof Role && $this->getRole()->getId() > 0 ? $this->getRole()->toArray() : null;
        $array['teams'] = $this->getTeams() instanceof TeamCollection ? $this->getTeams()->toArray() : null;
        if (is_null($attributes)) {
            return $array;
        }
        return array_filter($array, function ($attribute) use ($attributes) {
            return in_array($attribute, $attributes);
        }, ARRAY_FILTER_USE_KEY);
    }
}
