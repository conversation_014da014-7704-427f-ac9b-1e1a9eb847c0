<?php

declare(strict_types=1);

namespace STCompany\Entity\Checklist;

use RuntimeException;
use STLib\Expand\Collection;
use Traversable;

/**
 * @extends Traversable<array-key,ChecklistPoint>
 */
class ChecklistPointCollection extends Collection
{
    /**
     * @param mixed $checklistPoint
     * @param string|int|null $key
     * @return Collection
     * @throws RuntimeException
     */
    public function add(mixed $checklistPoint, string|int|null $key = null): Collection
    {
        if (!($checklistPoint instanceof ChecklistPoint)) {
            throw new RuntimeException(
                sprintf(
                    'Checklist point must be an instance of "%s"',
                    $this->getEntityClass()
                )
            );
        }
        parent::add($checklistPoint, $key ?? $checklistPoint->getId());

        return $this;
    }

    /**
     * @param bool $asArray
     * @return array|Checklist[]
     */
    public function toArray(bool $asArray = true): array
    {
        if (!$asArray) {
            return parent::toArray();
        }

        $result = [];
        foreach ($this as $checklistPoint) {
            $result[] = $checklistPoint->toArray();
        }
        return $result;
    }

    protected function getEntityClass(): string
    {
        return ChecklistPoint::class;
    }
}
