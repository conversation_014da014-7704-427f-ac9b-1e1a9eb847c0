<?php

declare(strict_types=1);

namespace STCompany\Entity\Checklist;

use STLib\Db\Entity;

class Checklist extends Entity
{
    public const string CALLS_SCOPE_ALL = 'all_calls';
    public const string CALLS_SCOPE_FIRST = 'first_calls';

    private ?int $id = null;
    private int $companyId;
    private string $name;
    private int $callDurationThreshold;
    private array $callsTeams = [];
    private string $callsScope = self::CALLS_SCOPE_ALL;
    private array $callsStatuses = [];
    private ?string $description = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @return int
     */
    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    /**
     * @param int $companyId
     * @return Checklist
     */
    public function setCompanyId(int $companyId): self
    {
        $this->companyId = $companyId;
        return $this;
    }


    /**
     * @param string $name
     * @return Checklist
     */
    public function setName(string $name): self
    {
        $this->name = $name;
        return $this;
    }

    public function getCallDurationThreshold(): int
    {
        return $this->callDurationThreshold;
    }

    public function setCallDurationThreshold(int $callDurationThreshold): self
    {
        $this->callDurationThreshold = $callDurationThreshold;
        return $this;
    }

    public function getCallsTeams(): array
    {
        return $this->callsTeams;
    }

    public function setCallsTeams(array $callsTeams): self
    {
        $this->callsTeams = $callsTeams;
        return $this;
    }

    public function getCallsScope(): string
    {
        return $this->callsScope;
    }

    public function setCallsScope(string $callsScope): self
    {
        $this->callsScope = $callsScope;
        return $this;
    }

    public function getCallsStatuses(): ?array
    {
        return $this->callsStatuses;
    }

    /**
     * @param array|null $callsStatuses
     * @return Checklist
     */
    public function setCallsStatuses(?array $callsStatuses): self
    {
        $this->callsStatuses = $callsStatuses;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getDescription(): ?string
    {
        return $this->description;
    }

    /**
     * @param string|null $description
     * @return Checklist
     */
    public function setDescription(?string $description): self
    {
        $this->description = $description;
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        return $this->hydrator->extract($this);
    }

    public function exchangeArray($data): void
    {
        $data['calls_statuses'] = $this->exchangeJson($data, 'calls_statuses');
        $data['calls_teams'] = $this->exchangeJson($data, 'calls_teams');

        parent::exchangeArray($data);
    }
}
