<?php

namespace STCompany\Entity\Checklist;

use Carbon\Carbon;
use STLib\Db\Entity;
use STLib\Mvc\Hydrator\BaseHydratorTrait;

class ChecklistPoint extends Entity
{
    use BaseHydratorTrait;

    private ?int $id = null;
    private int $checklistId;
    private string $title;
    private ?string $description = null;
    private string $expectedActions;
    private string $goodPerformanceDescription;
    private string $badPerformanceDescription;
    private ?int $order = null;
    private ?string $goodPerformanceExample = null;
    private ?string $badPerformanceExample = null;
    private ?Carbon $updatedAt = null;
    private bool $isOptional = false;
    private ?string $triggerCondition = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getChecklistId(): int
    {
        return $this->checklistId;
    }

    public function setChecklistId(int $checklistId): self
    {
        $this->checklistId = $checklistId;
        return $this;
    }

    public function getTitle(): string
    {
        return $this->title;
    }

    public function setTitle(string $title): self
    {
        $this->title = $title;
        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;
        return $this;
    }

    public function getExpectedActions(): string
    {
        return $this->expectedActions;
    }

    public function setExpectedActions(string $expectedActions): self
    {
        $this->expectedActions = $expectedActions;
        return $this;
    }

    public function getGoodPerformanceDescription(): string
    {
        return $this->goodPerformanceDescription;
    }

    public function setGoodPerformanceDescription(string $goodPerformanceDescription): self
    {
        $this->goodPerformanceDescription = $goodPerformanceDescription;
        return $this;
    }

    public function getGoodPerformanceExample(): ?string
    {
        return $this->goodPerformanceExample;
    }

    public function setGoodPerformanceExample(?string $goodPerformanceExample): self
    {
        $this->goodPerformanceExample = $goodPerformanceExample;
        return $this;
    }

    public function getBadPerformanceDescription(): string
    {
        return $this->badPerformanceDescription;
    }

    public function setBadPerformanceDescription(string $badPerformanceDescription): self
    {
        $this->badPerformanceDescription = $badPerformanceDescription;
        return $this;
    }

    public function getBadPerformanceExample(): ?string
    {
        return $this->badPerformanceExample;
    }

    public function setBadPerformanceExample(?string $badPerformanceExample): self
    {
        $this->badPerformanceExample = $badPerformanceExample;
        return $this;
    }

    public function getOrder(): ?int
    {
        return $this->order;
    }

    public function setOrder(?int $order): self
    {
        $this->order = $order;
        return $this;
    }

    public function isOptional(?bool $isOptional = null): null|bool|self
    {
        if (is_bool($isOptional)) {
            $this->isOptional = $isOptional;

            return $this;
        }
        return $this->isOptional;
    }

    public function setIsOptional(bool $isOptional): self
    {
        $this->isOptional = $isOptional;
        return $this;
    }

    public function getTriggerCondition(): ?string
    {
        return $this->triggerCondition;
    }

    public function setTriggerCondition(?string $triggerCondition): self
    {
        $this->triggerCondition = $triggerCondition;
        return $this;
    }

    public function getUpdatedAt(): ?Carbon
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(Carbon|string $updatedAt): self
    {
        if (! $updatedAt instanceof Carbon) {
            $updatedAt = new Carbon($updatedAt);
        }

        $this->updatedAt = $updatedAt;
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        return $this->extract($this);
    }
}
