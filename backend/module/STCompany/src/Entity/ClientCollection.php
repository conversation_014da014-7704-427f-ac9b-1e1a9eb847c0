<?php

declare(strict_types=1);

namespace STCompany\Entity;

class ClientCollection extends \STLib\Expand\Collection
{
    /**
     *
     * @param \STCompany\Entity\Client $client
     * @param mixed $key
     * @return \STCompany\Entity\Client
     * @throws \RuntimeException
     */
    public function add(mixed $client, string|int|null $key = null): \STLib\Expand\Collection
    {
        if (!($client instanceof Client)) {
            throw new \RuntimeException('Client must be an instace of "\STCompany\Entity\Client"');
        }
        parent::add($client, $key ?? $client->getId());
        return $this;
    }

    /**
     *
     * @param array $attributes
     * @return array
     */
    public function toArray(array $attributes = null): array
    {
        $result = [];
        foreach ($this as $client) {
            $result[] = $client->toArray($attributes);
        }
        return $result;
    }
}
