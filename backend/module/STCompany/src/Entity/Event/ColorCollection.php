<?php

declare(strict_types=1);

namespace STCompany\Entity\Event;

class ColorCollection extends \STLib\Expand\Collection
{
    /**
     *
     * @param mixed $color
     * @param string|int|null $key
     * @return \STLib\Expand\Collection
     * @throws \RuntimeException
     */
    public function add(mixed $color, string|int|null $key = null): \STLib\Expand\Collection
    {
        if (!($color instanceof Color)) {
            throw new \RuntimeException('Color must be an instace of "\STCompany\Entity\Color"');
        }
        parent::add($color, $key ?? $color->getId());
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        $result = [];
        foreach ($this as $color) {
            $result[] = $color->toArray();
        }
        return $result;
    }
}
