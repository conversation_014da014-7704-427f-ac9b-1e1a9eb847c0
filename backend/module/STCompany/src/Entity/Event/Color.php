<?php

declare(strict_types=1);

namespace STCompany\Entity\Event;

class Color
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    /**
     *
     * @var int
     */
    protected ?int $id = null;

    /**
     *
     * @var string
     */
    protected string $name;

    /**
     *
     * @var string
     */
    protected string $fillHex;

    /**
     *
     * @var string
     */
    protected string $outlineHex;

    /**
     *
     * @var int
     */
    protected int $priority;

    /**
     *
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     *
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @todo remove method, temporary hardcoded inclusion to not break frontend on implementation
     *
     * @return string
     */
    public function getHex(): string
    {
        return $this->fillHex;
    }

    /**
     *
     * @return string
     */
    public function getFillHex(): string
    {
        return $this->fillHex;
    }

    /**
     *
     * @return string
     */
    public function getOutlineHex(): string
    {
        return $this->outlineHex;
    }

    /**
     *
     * @return int
     */
    public function getPriority(): int
    {
        return $this->priority;
    }

    /**
     *
     * @param int|null $id
     * @return Color
     */
    public function setId(?int $id): Color
    {
        $this->id = $id;
        return $this;
    }

    /**
     *
     * @param string $name
     * @return Color
     */
    public function setName(string $name): Color
    {
        $this->name = $name;
        return $this;
    }

    /**
     *
     * @param string $fillHex
     * @return Color
     */
    public function setFillHex(string $fillHex): Color
    {
        $this->fillHex = $fillHex;
        return $this;
    }

    /**
     *
     * @param string $outlineHex
     * @return Color
     */
    public function setOutlineHex(string $outlineHex): Color
    {
        $this->outlineHex = $outlineHex;
        return $this;
    }

    /**
     *
     * @param int $priority
     * @return Color
     */
    public function setPriority(int $priority): Color
    {
        $this->priority = $priority;
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        $colorArray = $this->extract($this);
        return $colorArray;
    }
}
