<?php

declare(strict_types=1);

namespace STCompany\Entity\Event;

class Category
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    /**
     *
     * @var int
     */
    protected ?int $id = null;

    /**
     *
     * @var \STCompany\Entity\Role
     */
    protected \STCompany\Entity\Role $role;

    /**
     *
     * @var Color
     */
    protected Color $color;

    /**
     *
     * @var string
     */
    protected string $name;

    /**
     *
     * @var EventCollection
     */
    protected EventCollection $events;

    /**
     *
     * Class constructor
     */
    public function __construct()
    {
        $this->role = new \STCompany\Entity\Role();
        $this->color = new Color();
        $this->events = new EventCollection();
    }

        /**
     *
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     *
     * @return \STCompany\Entity\Role
     */
    public function getRole(): \STCompany\Entity\Role
    {
        return $this->role;
    }

    /**
     *
     * @return Color
     */
    public function getColor(): Color
    {
        return $this->color;
    }

    /**
     *
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     *
     * @return EventCollection
     */
    public function getEvents(): EventCollection
    {
        return $this->events;
    }

    /**
     *
     * @param int|null $id
     * @return Category
     */
    public function setId(?int $id): Category
    {
        $this->id = $id;
        return $this;
    }

    /**
     *
     * @param \STCompany\Entity\Role $role
     * @return Category
     */
    public function setRole(\STCompany\Entity\Role $role): Category
    {
        $this->role = $role;
        return $this;
    }

    /**
     *
     * @param Color $color
     * @return Category
     */
    public function setColor(Color $color): Category
    {
        $this->color = $color;
        return $this;
    }

    /**
     *
     * @param string $name
     * @return Category
     */
    public function setName(string $name): Category
    {
        $this->name = $name;
        return $this;
    }

    /**
     *
     * @param int $roleId
     * @return Category
     */
    public function setRoleId(int $roleId): Category
    {
        $this->role->setId($roleId);
        return $this;
    }

    /**
     *
     * @param int $colorId
     * @return Category
     */
    public function setColorId(int $colorId): Category
    {
        $this->color->setId($colorId);
        return $this;
    }

    /**
     *
     * @param EventCollection $events
     * @return Category
     */
    public function setEvents(EventCollection $events): Category
    {
        $this->events = $events;
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        $result = $this->extract($this);
        $result['role'] = $this->getRole()->toArray();
        $result['color'] = $this->getColor()->toArray();
        $result['events'] = $this->getEvents()->toArray();
        return $result;
    }
}
