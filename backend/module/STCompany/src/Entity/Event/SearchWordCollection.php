<?php

declare(strict_types=1);

namespace STCompany\Entity\Event;

class SearchWordCollection extends \STLib\Expand\Collection
{
    /**
     *
     * @param mixed $searchWord
     * @param string|int|null $key
     * @return \STLib\Expand\Collection
     * @throws \RuntimeException
     */
    public function add(mixed $searchWord, string|int|null $key = null): \STLib\Expand\Collection
    {
        if (!($searchWord instanceof SearchWord)) {
            throw new \RuntimeException('Search word must be an instace of "\STCompany\Entity\Event\SearchWord"');
        }
        parent::add($searchWord, $key ?? $searchWord->getId());
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        $result = [];
        foreach ($this as $searchWord) {
            $result[] = $searchWord->toArray();
        }
        return $result;
    }
}
