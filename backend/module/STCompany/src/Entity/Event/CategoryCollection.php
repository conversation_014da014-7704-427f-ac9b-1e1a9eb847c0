<?php

declare(strict_types=1);

namespace STCompany\Entity\Event;

class CategoryCollection extends \STLib\Expand\Collection
{
    /**
     *
     * @param mixed $category
     * @param string|int|null $key
     * @return \STLib\Expand\Collection
     * @throws \RuntimeException
     */
    public function add(mixed $category, string|int|null $key = null): \STLib\Expand\Collection
    {
        if (!($category instanceof Category)) {
            throw new \RuntimeException('Color must be an instace of "\STCompany\Entity\Event\Category"');
        }
        parent::add($category, $key ?? $category->getId());
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        $result = [];
        foreach ($this as $category) {
            $result[] = $category->toArray();
        }
        return $result;
    }
}
