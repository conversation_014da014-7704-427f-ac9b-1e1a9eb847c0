<?php

declare(strict_types=1);

namespace STCompany\Entity\Event;

class SearchWord
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    /**
     *
     * @var int
     */
    protected ?int $id = null;

    /**
     *
     * @var int
     */
    protected int $eventId;

    /**
     *
     * @var string
     */
    protected string $name;


    /**
     *
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     *
     * @return int|null
     */
    public function getEventId(): int
    {
        return $this->eventId;
    }

    /**
     *
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }


    /**
     *
     * @param int|null $id
     */
    public function setId(?int $id): SearchWord
    {
        $this->id = $id;
        return $this;
    }

    /**
     *
     * @param int|null $eventId
     */
    public function setEventId(int $eventId): SearchWord
    {
        $this->eventId = $eventId;
        return $this;
    }

    /**
     *
     * @param string $name
     */
    public function setName(string $name): SearchWord
    {
        $this->name = strtolower($name);
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        $result = $this->extract($this);
        unset($result['event_id']);
        return $result;
    }
}
