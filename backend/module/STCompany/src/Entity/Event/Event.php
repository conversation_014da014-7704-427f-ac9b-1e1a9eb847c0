<?php

declare(strict_types=1);

namespace STCompany\Entity\Event;

use STCompany\Entity\Role;
use STLib\Db\Entity;
use STLib\Mvc\Hydrator\BaseHydratorTrait;

class Event extends Entity
{
    use BaseHydratorTrait;

    /**
     * @var int|null
     */
    protected ?int $id = null;

    /**
     *
     * @var int
     */
    protected int $categoryId;

    /**
     *
     * @var string
     */
    protected string $name;

    /**
     *
     * @var string
     */
    protected string $icon;

    /**
     *
     * @var Role
     */
    protected Role $role;

    /**
     *
     * @var bool
     */
    protected bool $isConfirmNeeded = false;

    /**
     *
     * @var int
     */
    protected int $score = 0;

    /**
     *
     * @var bool
     */
    protected bool $isPinned = false;

    /**
     *
     * @var SearchWordCollection
     */
    protected SearchWordCollection $searchWord;

    /**
     *
     * @var string[]
     */
    protected array $algoEvents = [];

    /**
     *
     * @var Color|null
     */
    protected ?Color $color = null;

    public function __construct()
    {
        parent::__construct();
        $this->init();
    }

    public function init(): void
    {
        $this->role = new Role();
        $this->searchWord = new SearchWordCollection();
        $this->color = new Color();
    }

    /**
     *
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     *
     * @return int
     */
    public function getCategoryId(): int
    {
        return $this->categoryId;
    }

    /**
     *
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     *
     * @return string
     */
    public function getIcon(): string
    {
        return $this->icon;
    }

    /**
     *
     * @return Role
     */
    public function getRole(): Role
    {
        return $this->role;
    }

    /**
     *
     * @return bool
     */
    public function getIsConfirmNeeded(): bool
    {
        return $this->isConfirmNeeded;
    }

    /**
     *
     * @return int
     */
    public function getScore(): int
    {
        return $this->score;
    }

    /**
     *
     * @return bool
     */
    public function getIsPinned(): bool
    {
        return $this->isPinned;
    }

    /**
     *
     * @return SearchWordCollection
     */
    public function getSearchWord(): SearchWordCollection
    {
        return $this->searchWord;
    }

    /**
     *
     * @return array
     */
    public function getAlgoEvents(): array
    {
        return $this->algoEvents;
    }

    /**
     *
     * @return Color|null
     */
    public function getColor(): ?Color
    {
        return $this->color;
    }

    /**
     *
     * @param int|null $id
     * @return Event
     */
    public function setId(?int $id): Event
    {
        $this->id = $id;
        return $this;
    }

    /**
     *
     * @param int $categoryId
     * @return Event
     */
    public function setCategoryId(int $categoryId): Event
    {
        $this->categoryId = $categoryId;
        return $this;
    }

    /**
     *
     * @param string $name
     * @return Event
     */
    public function setName(string $name): Event
    {
        $this->name = $name;
        return $this;
    }

    /**
     *
     * @param string $icon
     * @return Event
     */
    public function setIcon(string $icon): Event
    {
        $this->icon = $icon;
        return $this;
    }

    /**
     *
     * @param Role $role
     * @return Event
     */
    public function setRole(Role $role): Event
    {
        $this->role = $role;
        return $this;
    }

    /**
     *
     * @param bool $isConfirmNeeded
     * @return Event
     */
    public function setIsConfirmNeeded(bool $isConfirmNeeded): Event
    {
        $this->isConfirmNeeded = $isConfirmNeeded;
        return $this;
    }

    /**
     *
     * @param int $score
     * @return Event
     */
    public function setScore(int $score): Event
    {
        $this->score = $score;
        return $this;
    }

    /**
     *
     * @param bool $isPinned
     * @return Event
     */
    public function setIsPinned(bool $isPinned): Event
    {
        $this->isPinned = $isPinned;
        return $this;
    }

    /**
     *
     * @param SearchWordCollection $searchWord
     * @return Event
     */
    public function setSearchWord(SearchWordCollection|array $searchWord): Event
    {
        if ($searchWord instanceof SearchWordCollection) {
            $this->searchWord = $searchWord;
        }
        if (is_array($searchWord)) {
            $this->searchWord->clear();
            foreach ($searchWord as $name) {
                $this->searchWord->add($this->hydrate([
                    'name' => $name,
                ], SearchWord::class));
            }
        }
        return $this;
    }

    /**
     *
     * @param int $roleId
     * @return Event
     */
    public function setRoleId(int $roleId): Event
    {
        $this->getRole()->setId($roleId);
        return $this;
    }

    /**
     *
     * @param array $algoEventNames
     * @return Event
     */
    public function setAlgoEvents(array $algoEventNames): Event
    {
        $this->algoEvents = $algoEventNames;

        return $this;
    }

    /**
     *
     * @param int|null $colorId
     * @return Event
     */
    public function setColorId(?int $colorId): Event
    {
        $this->getColor()->setId($colorId);

        return $this;
    }

    /**
     *
     * @param Color $color
     * @return Event
     */
    public function setColor(Color $color): Event
    {
        $this->color = $color;
        return $this;
    }

    /**
     *
     * @param string $algoEventName
     * @return Event
     */
    public function addAlgoEventName(string $algoEventName): Event
    {
        $this->algoEvents[] = $algoEventName;
        return $this;
    }

    /**
     *
     * @param bool|null $isConfirmNeeded
     * @return bool|Event
     */
    public function isConfirmNeeded(?bool $isConfirmNeeded = null): bool|Event
    {
        if (is_null($isConfirmNeeded)) {
            return $this->isConfirmNeeded;
        }
        $this->isConfirmNeeded = $isConfirmNeeded;
        return $this;
    }

    /**
     *
     * @param bool|null $isPinned
     * @return bool|Event
     */
    public function isPinned(?bool $isPinned = null): bool|Event
    {
        if (is_null($isPinned)) {
            return $this->isPinned;
        }
        $this->isPinned = $isPinned;
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        $result = $this->extract($this);
        $result['search_word'] = $this->getSearchWord()->toArray();
        $result['role'] = $this->getRole()->toArray();

        if ($this->getColor() instanceof Color && !is_null($this->getColor()->getId())) {
            $result['color'] = $this->getColor()->toArray();
        }

        return $result;
    }
}
