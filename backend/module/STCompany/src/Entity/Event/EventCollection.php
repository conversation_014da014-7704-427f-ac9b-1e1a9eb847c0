<?php

declare(strict_types=1);

namespace STCompany\Entity\Event;

use RuntimeException;
use STLib\Expand\Collection;
use Traversable;

/**
 * @extends Traversable<array-key,Event>
 * @method Event offsetGet(mixed $index): mixed
 */
class EventCollection extends Collection
{
    /**
     *
     * @var array|null
     */
    protected ?array $searchWordsAndEventsAccordance = null;

    /**
     *
     * @var array|null
     */
    protected ?array $algoEventsAndEventsAccordance = null;

    /**
     *
     * @param mixed $event
     * @param string|int|null $key
     * @return Collection
     * @throws RuntimeException
     */
    public function add(mixed $event, string|int|null $key = null): Collection
    {
        if (!($event instanceof Event)) {
            throw new RuntimeException('Event must be an instance of "\STCompany\Entity\Event"');
        }
        parent::add($event, $key ?? $event->getId());
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        $result = [];
        foreach ($this as $event) {
            $result[] = $event->toArray();
        }
        return $result;
    }
}
