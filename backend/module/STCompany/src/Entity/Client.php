<?php

declare(strict_types=1);

namespace STCompany\Entity;

class Client
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    /**
     *
     * @var string
     */
    protected string $id;

    /**
     *
     * @var int
     */
    protected int $companyId;

    /**
     *
     * @var int
     */
    protected string $name;

    /**
     *
     * @var string|null
     */
    protected ?string $country = null;

    /**
     *
     * @var string|null
     */
    protected ?string $status = null;

    /**
     *
     * @var string|null
     */
    protected ?string $source = null;

    /**
     *
     * @var \Carbon\Carbon|null
     */
    protected ?\Carbon\Carbon $acquisitionDate = null;

    /**
     *
     * @var bool|null
     */
    protected ?bool $isConverted = null;

    /**
     *
     * @var \Carbon\Carbon|null
     */
    protected ?\Carbon\Carbon $convertedDate = null;

    /**
     *
     * @var \Carbon\Carbon|null
     */
    protected ?\Carbon\Carbon $lastTransactionDate = null;

    /**
     *
     * @var string|null
     */
    protected ?string $campaignId = null;

    /**
     *
     * @var float|null
     */
    protected ?float $value = null;

    /**
     *
     * @var \Carbon\Carbon|null
     */
    protected ?\Carbon\Carbon $created = null;

    /**
     *
     * @return string
     */
    public function getId(): string
    {
        return $this->id;
    }

    /**
     *
     * @return int
     */
    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    /**
     *
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     *
     * @return string|null
     */
    public function getCountry(): ?string
    {
        return $this->country;
    }

    /**
     *
     * @return string|null
     */
    public function getStatus(): ?string
    {
        return $this->status;
    }

    /**
     *
     * @return string|null
     */
    public function getSource(): ?string
    {
        return $this->source;
    }

    /**
     *
     * @return \Carbon\Carbon|null
     */
    public function getAcquisitionDate(): ?\Carbon\Carbon
    {
        return $this->acquisitionDate;
    }

    /**
     *
     * @return bool|null
     */
    public function getIsConverted(): ?bool
    {
        return $this->isConverted;
    }

    /**
     *
     * @return \Carbon\Carbon|null
     */
    public function getConvertedDate(): ?\Carbon\Carbon
    {
        return $this->convertedDate;
    }

    /**
     *
     * @return \Carbon\Carbon|null
     */
    public function getLastTransactionDate(): ?\Carbon\Carbon
    {
        return $this->lastTransactionDate;
    }

    /**
     *
     * @return string|null
     */
    public function getCampaignId(): ?string
    {
        return $this->campaignId;
    }

    /**
     *
     * @return float|null
     */
    public function getValue(): ?float
    {
        return $this->value;
    }

    /**
     *
     * @return \Carbon\Carbon|null
     */
    public function getCreated(): ?\Carbon\Carbon
    {
        return $this->created;
    }

    /**
     *
     * @param string $id
     * @return Client
     */
    public function setId(string $id): Client
    {
        $this->id = $id;
        return $this;
    }

    /**
     *
     * @param int $companyId
     * @return Client
     */
    public function setCompanyId(int $companyId): Client
    {
        $this->companyId = $companyId;
        return $this;
    }

    /**
     *
     * @param string $name
     * @return Client
     */
    public function setName(string $name): Client
    {
        $this->name = $name;
        return $this;
    }

    /**
     *
     * @param string|null $country
     * @return Client
     */
    public function setCountry(?string $country): Client
    {
        $this->country = $country;
        return $this;
    }

    /**
     *
     * @param string|null $status
     * @return Client
     */
    public function setStatus(?string $status): Client
    {
        $this->status = $status;
        return $this;
    }

    /**
     *
     * @param string|null $source
     * @return Client
     */
    public function setSource(?string $source): Client
    {
        $this->source = $source;
        return $this;
    }

    /**
     *
     * @param \Carbon\Carbon|null $acquisitionDate
     * @return Client
     */
    public function setAcquisitionDate(null|\Carbon\Carbon|string $acquisitionDate): Client
    {
        if ($acquisitionDate instanceof \Carbon\Carbon) {
            $this->acquisitionDate = $acquisitionDate;
        } elseif (is_string($acquisitionDate)) {
            $this->acquisitionDate = \Carbon\Carbon::parse($acquisitionDate);
        }
        return $this;
    }

    /**
     *
     * @param bool|null $isConverted
     * @return Client
     */
    public function setIsConverted(?bool $isConverted): Client
    {
        $this->isConverted = $isConverted;
        return $this;
    }

    /**
     *
     * @param \Carbon\Carbon|null $convertedDate
     * @return Client
     */
    public function setConvertedDate(null|string|\Carbon\Carbon $convertedDate): Client
    {
        if ($convertedDate instanceof \Carbon\Carbon) {
            $this->convertedDate = $convertedDate;
        } elseif (is_string($convertedDate)) {
            $this->convertedDate = \Carbon\Carbon::parse($convertedDate);
        }
        return $this;
    }

    /**
     *
     * @param \Carbon\Carbon|null $lastTransactionDate
     * @return Client
     */
    public function setLastTransactionDate(null|string|\Carbon\Carbon $lastTransactionDate): Client
    {
        if ($lastTransactionDate instanceof \Carbon\Carbon) {
            $this->lastTransactionDate = $lastTransactionDate;
        } elseif (is_string($lastTransactionDate)) {
            $this->lastTransactionDate = \Carbon\Carbon::parse($lastTransactionDate);
        }
        return $this;
    }

    /**
     *
     * @param string|null $campaignId
     * @return Client
     */
    public function setCampaignId(?string $campaignId): Client
    {
        $this->campaignId = $campaignId;
        return $this;
    }

    /**
     *
     * @param float|null $value
     * @return Client
     */
    public function setValue(?float $value): Client
    {
        $this->value = $value;
        return $this;
    }

    /**
     *
     * @param \Carbon\Carbon|null $created
     * @return Client
     */
    public function setCreated(?\Carbon\Carbon $created): Client
    {
        $this->created = $created;
        return $this;
    }
    /**
     *
     * @param array $attributes
     * @return array
     */
    public function toArray(array $attributes = null): array
    {
        $array = $this->extract($this);
        if (is_null($attributes)) {
            return $array;
        }
        return array_filter($array, function ($attribute) use ($attributes) {
            return in_array($attribute, $attributes);
        }, ARRAY_FILTER_USE_KEY);
    }
}
