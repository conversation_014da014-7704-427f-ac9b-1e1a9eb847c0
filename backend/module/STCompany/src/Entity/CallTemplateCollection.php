<?php

declare(strict_types=1);

namespace STCompany\Entity;

class CallTemplateCollection extends \STLib\Expand\Collection
{
    /**
     *
     * @param \STCompany\Entity\Company $template
     * @param mixed $key
     * @return \STCompany\Entity\Company
     * @throws \RuntimeException
     */
    public function add(mixed $template, string|int|null $key = null): \STLib\Expand\Collection
    {
        if (!($template instanceof CallTemplate)) {
            throw new \RuntimeException('Call template must be an instace of "\STCompany\Entity\CallTemplate"');
        }
        parent::add($template, $key ?? $template->getId());
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        $result = [];
        foreach ($this as $template) {
            $result[] = $template->toArray();
        }
        return $result;
    }
}
