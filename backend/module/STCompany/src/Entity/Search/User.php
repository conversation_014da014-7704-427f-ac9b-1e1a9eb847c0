<?php

namespace STCompany\Entity\Search;

class User extends \STCompany\Entity\User
{
    /**
     *
     * @var int
     */
    protected int $callsCount = 0;

    /**
     *
     * @return int
     */
    public function getCallsCount(): int
    {
        return $this->callsCount;
    }

    /**
     *
     * @param int $callsCount
     * @return User
     */
    public function setCallsCount(int $callsCount): User
    {
        $this->callsCount = $callsCount;
        return $this;
    }
}
