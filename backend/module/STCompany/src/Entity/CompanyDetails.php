<?php

namespace STCompany\Entity;

use STLib\Db\Entity;

class CompanyDetails extends Entity
{
    protected ?int $companyId = null;
    private ?string $industry = null;
    private ?string $productsServices = null;
    private ?string $salesModel = null;
    private ?string $keyGoals = null;
    private ?string $callTypesCategories = null;
    private ?string $biggestRisks = null;
    private ?string $regulationsStandards = null;
    private ?string $uniqueProposition = null;
    private ?string $additionalInfo = null;

    public function getId(): ?int
    {
        return $this->companyId;
    }

    public function setId(?int $id): void
    {
        $this->companyId = $id;
    }

    public function getCompanyId(): ?int
    {
        return $this->getId();
    }

    public function setCompanyId(?int $companyId): void
    {
        $this->setId($companyId);
    }

    public function getIndustry(): ?string
    {
        return $this->industry;
    }

    public function setIndustry(?string $industry): void
    {
        $this->industry = $industry;
    }

    public function getProductsServices(): ?string
    {
        return $this->productsServices;
    }

    public function setProductsServices(?string $productsServices): void
    {
        $this->productsServices = $productsServices;
    }

    public function getSalesModel(): ?string
    {
        return $this->salesModel;
    }

    public function setSalesModel(?string $salesModel): void
    {
        $this->salesModel = $salesModel;
    }

    public function getKeyGoals(): ?string
    {
        return $this->keyGoals;
    }

    public function setKeyGoals(?string $keyGoals): void
    {
        $this->keyGoals = $keyGoals;
    }

    public function getCallTypesCategories(): ?string
    {
        return $this->callTypesCategories;
    }

    public function setCallTypesCategories(?string $callTypesCategories): void
    {
        $this->callTypesCategories = $callTypesCategories;
    }

    public function getBiggestRisks(): ?string
    {
        return $this->biggestRisks;
    }

    public function setBiggestRisks(?string $biggestRisks): void
    {
        $this->biggestRisks = $biggestRisks;
    }

    public function getRegulationsStandards(): ?string
    {
        return $this->regulationsStandards;
    }

    public function setRegulationsStandards(?string $regulationsStandards): void
    {
        $this->regulationsStandards = $regulationsStandards;
    }

    public function getUniqueProposition(): ?string
    {
        return $this->uniqueProposition;
    }

    public function setUniqueProposition(?string $uniqueProposition): void
    {
        $this->uniqueProposition = $uniqueProposition;
    }

    public function getAdditionalInfo(): ?string
    {
        return $this->additionalInfo;
    }

    public function setAdditionalInfo(?string $additionalInfo): void
    {
        $this->additionalInfo = $additionalInfo;
    }

    public function toArray(): array
    {
        return [
            'company_id' => $this->getId(),
            'industry' => $this->getIndustry(),
            'products_services' => $this->getProductsServices(),
            'sales_model' => $this->getSalesModel(),
            'key_goals' => $this->getKeyGoals(),
            'call_types_categories' => $this->getCallTypesCategories(),
            'biggest_risks' => $this->getBiggestRisks(),
            'regulations_standards' => $this->getRegulationsStandards(),
            'unique_proposition' => $this->getUniqueProposition(),
            'additional_info' => $this->getAdditionalInfo(),
        ];
    }
}
