<?php

declare(strict_types=1);

namespace STCompany\Entity;

use STLib\Expand\Collection;
use Traversable;
use RuntimeException;

/**
 * @extends Traversable<array-key, CompanyDetails>
 */
class CompanyDetailsCollection extends Collection
{
    public function add(mixed $object, string|int|null $key = null): Collection
    {
        if (!($object instanceof CompanyDetails)) {
            throw new RuntimeException(
                sprintf(
                    'CompanyDetails must be an instance of "%s"',
                    $this->getEntityClass()
                )
            );
        }
        parent::add($object, $key ?? $object->getId());

        return $this;
    }

    /**
     * @param bool $asArray
     * @return array|CompanyDetails[]
     */
    public function toArray(bool $asArray = true): array
    {
        if (!$asArray) {
            return parent::toArray();
        }

        $result = [];
        foreach ($this as $industry) {
            $result[] = $industry->toArray();
        }
        return $result;
    }

    protected function getEntityClass(): string
    {
        return CompanyDetails::class;
    }
}
