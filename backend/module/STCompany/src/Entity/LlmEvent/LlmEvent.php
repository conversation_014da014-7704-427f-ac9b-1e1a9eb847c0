<?php

namespace STCompany\Entity\LlmEvent;

use STLlmEvent\Entity\LlmEvent as BaseLlmEvent;

class LlmEvent extends BaseLlmEvent
{
    private int $company_id;

    public function getCompanyId(): int
    {
        return $this->company_id;
    }

    public function setCompanyId(int $companyId): void
    {
        $this->company_id = $companyId;
    }

    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'company_id' => $this->company_id,
        ]);
    }
}
