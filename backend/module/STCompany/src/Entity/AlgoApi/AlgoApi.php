<?php

namespace STCompany\Entity\AlgoApi;

use STAlgo\Entity\AlgoApi as BaseAlgoApi;

class AlgoApi extends BaseAlgoApi
{
    private int $companyId;

    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    public function setCompanyId(int $companyId): void
    {
        $this->companyId = $companyId;
    }

    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'company_id' => $this->companyId,
        ]);
    }
}
