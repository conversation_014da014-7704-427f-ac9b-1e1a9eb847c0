<?php

declare(strict_types=1);

namespace STCompany\Entity\AlgoApi;

use RuntimeException;
use STAlgo\Entity\AlgoApiCollection as BaseCollection;
use STLib\Expand\Collection;
use Traversable;

/**
 * @extends Traversable<array-key,AlgoApi>
 */
class AlgoApiCollection extends BaseCollection
{
    /**
     *
     * @param mixed $algoApi
     * @param string|int|null $key
     * @return Collection
     * @throws RuntimeException
     */
    public function add(mixed $algoApi, string|int|null $key = null): Collection
    {
        if (!($algoApi instanceof AlgoApi)) {
            throw new RuntimeException('Algo<PERSON><PERSON> must be an instance of "\STCompany\Entity\AlgoApi"');
        }
        parent::add($algoApi, $key ?? $algoApi->getId());
        return $this;
    }
}
