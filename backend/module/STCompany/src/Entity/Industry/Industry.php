<?php

namespace STCompany\Entity\Industry;

use STIndustry\Entity\Industry as BaseIndustry;

class Industry extends BaseIndustry
{
    private int $companyId;

    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    public function setCompanyId(int $companyId): void
    {
        $this->companyId = $companyId;
    }

    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'company_id' => $this->companyId,
        ]);
    }
}
