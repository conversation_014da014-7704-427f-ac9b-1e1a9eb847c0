<?php

declare(strict_types=1);

namespace STCompany\Entity;

use STLib\Mvc\Entity\ToArrayTrait;
use STLib\Mvc\Hydrator\BaseHydratorTrait;

class Role
{
    use BaseHydratorTrait;
    use ToArrayTrait;

    public const ADMIN_ROLE_TYPE = 1;
    public const COMPANY_ADMIN_ROLE_TYPE = 4;
    public const COMPANY_SUPERVISOR_ROLE_TYPE = 5;
    public const MANAGER_ROLE_TYPE = 2;
    public const AGENT_ROLE_TYPE = 3;
    public const DEFAULT_EFFECTIVE_CALL_THRESHOLD_BAR = 180;

    /**
     *
     * @var ?int
     */
    protected ?int $id = null;

    /**
     *
     * @var int|null
     */
    protected ?int $roleType = self::MANAGER_ROLE_TYPE;

    /**
     *
     * @var int|null
     */
    protected ?int $companyId = null;

    /**
     *
     * @var ?string
     */
    protected ?string $name = null;

    /**
     *
     * @var ?Permission\PermissionCollection
     */
    protected ?Permission\PermissionCollection $permissions = null;

    /**
     *
     * @var ?UserCollection
     */
    protected ?UserCollection $users = null;

    /**
     *
     * @var int
     */
    protected int $eventsCount = 0;

    /**
     *
     * @var int
     */
    protected int $effectiveCallThresholdBar = self::DEFAULT_EFFECTIVE_CALL_THRESHOLD_BAR;

    /**
     *
     * Class constructor
     */
    public function __construct()
    {
        $this->permissions = new Permission\PermissionCollection();
        $this->users = new UserCollection();
    }

    /**
     *
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     *
     * @return int
     */
    public function getRoleType(): int
    {
        return $this->roleType;
    }

    /**
     *
     * @return int|null
     */
    public function getCompanyId(): ?int
    {
        return $this->companyId;
    }

    /**
     *
     * @return string|null
     */
    public function getName(): ?string
    {
        return $this->name;
    }

    /**
     *
     * @return Permission\PermissionCollection|null
     */
    public function getPermissions(): ?Permission\PermissionCollection
    {
        return $this->permissions;
    }

    /**
     *
     * @return UserCollection|null
     */
    public function getUsers(): ?UserCollection
    {
        return $this->users;
    }

    /**
     *
     * @return int
     */
    public function getEventsCount(): int
    {
        return $this->eventsCount;
    }

    /**
     *
     * @return int
     */
    public function getEffectiveCallThresholdBar(): int
    {
        return $this->effectiveCallThresholdBar;
    }

    /**
     *
     * @param int|null $id
     */
    public function setId(?int $id): Role
    {
        $this->id = $id;
        return $this;
    }

    /**
     *
     * @param int|null $roleType
     * @return Role
     */
    public function setRoleType(?int $roleType): Role
    {
        $this->roleType = $roleType ?? static::MANAGER_ROLE_TYPE;
        return $this;
    }

    /**
     *
     * @param int|null $companyId
     */
    public function setCompanyId(?int $companyId): Role
    {
        $this->companyId = $companyId;
        return $this;
    }

    /**
     *
     * @param string|null $name
     */
    public function setName(?string $name): Role
    {
        $this->name = $name;
        return $this;
    }

    /**
     *
     * @param Permission\PermissionCollection $permissions
     * @return Role
     */
    public function setPermissions(Permission\PermissionCollection $permissions): Role
    {
        $this->permissions = $permissions;
        return $this;
    }

    /**
     *
     * @param UserCollection|null $users
     * @return Role
     */
    public function setUsers(?UserCollection $users): Role
    {
        $this->users = $users;
        return $this;
    }

    /**
     *
     * @param int $eventsCount
     * @return Role
     */
    public function setEventsCount(int $eventsCount): Role
    {
        $this->eventsCount = $eventsCount;
        return $this;
    }

    /**
     *
     * @param int $effectiveCallThresholdBar
     * @return Role
     */
    public function setEffectiveCallThresholdBar(int $effectiveCallThresholdBar): Role
    {
        $this->effectiveCallThresholdBar = $effectiveCallThresholdBar;
        return $this;
    }

    /**
     *
     * @return bool
     */
    public function isAdmin(): bool
    {
        return $this->getRoleType() === static::ADMIN_ROLE_TYPE;
    }

    /**
     *
     * @return bool
     */
    public function isCompanyAdmin(): bool
    {
        return $this->getRoleType() === static::COMPANY_ADMIN_ROLE_TYPE;
    }

    /**
     *
     * @return bool
     */
    public function isCompanySupervisor(): bool
    {
        return $this->getRoleType() === static::COMPANY_SUPERVISOR_ROLE_TYPE;
    }

    /**
     *
     * @return bool
     */
    public function isManager(): bool
    {
        return $this->getRoleType() === static::MANAGER_ROLE_TYPE;
    }

    /**
     *
     * @return bool
     */
    public function isAgent(): bool
    {
        return $this->getRoleType() === static::AGENT_ROLE_TYPE;
    }

    /**
     *
     * @param array|null $attributes
     * @return array
     */
    public function toArray(?array $attributes = null): array
    {
        $result = $this->extract($this);

        if (!is_null($this->getPermissions()) && $this->isRequested('permissions', $attributes)) {
            $result['permissions'] = $this->getPermissions()->toArray();
        } else {
            unset($result['permissions']);
        }

        if (!is_null($this->getUsers()) && $this->isRequested('users', $attributes)) {
            $result['users'] = $this->getUsers()->toArray();
        } else {
            unset($result['users']);
        }

        if (is_null($attributes)) {
            return $result;
        }

        return array_filter($result, function ($attribute) use ($attributes) {
            return in_array($attribute, $attributes);
        }, ARRAY_FILTER_USE_KEY);
    }
}
