<?php

declare(strict_types=1);

namespace STCompany\Entity;

class CompanyHierarchy
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    public const USERS_WITHOUT_TEAM = 'Users without team';
    public const INACTIVE_USERS = 'Inactive users';

    /**
     *
     * @var TeamCollection
     */
    protected TeamCollection $teamCollection;

    /**
     *
     * @var UserCollection
     */
    protected UserCollection $userCollection;

    /**
     *
     * @var RoleCollection
     */
    protected RoleCollection $roleCollection;

    /**
     *
     * Class constructor
     */
    public function __construct()
    {
        $this->teamCollection = new TeamCollection();
        $this->userCollection = new UserCollection();
        $this->roleCollection = new RoleCollection();
    }

    /**
     *
     * @param \ArrayObject $hierarchyData
     * @return CompanyHierarchy
     */
    public function addTeams(\ArrayObject $hierarchyData): CompanyHierarchy
    {
        if ($hierarchyData['is_active'] === 0) {
            $teamId = -1;
        } else {
            $teamId = $hierarchyData['team_id'] ?? 0;
        }
        if (!$this->teamCollection->offsetExists($teamId)) {
            $this->teamCollection->add($this->hydrate([
                'id' => $teamId,
                'name' => match ($teamId) {
                    -1 => static::INACTIVE_USERS,
                    0 => static::USERS_WITHOUT_TEAM,
                    default => $hierarchyData['team_name'],
                },
            ], Team::class, withConstructor: true));
        }

        $roleId = $hierarchyData['role_id'] ?? 0;
        if (!$this->roleCollection->offsetExists($roleId)) {
            $role = $this->hydrate([
                'id' => $roleId,
                'name' => $hierarchyData['role_name'],
                'role_type' => $hierarchyData['role_type'],
            ], Role::class);
            $this->roleCollection->add($role);
        }

        if (!is_null($hierarchyData['user_id']) && !$this->userCollection->offsetExists($hierarchyData['user_id'])) {
            $user = $this->hydrate([
                'id' => $hierarchyData['user_id'],
                'name' => $hierarchyData['user_name'],
                'avatar' => $hierarchyData['user_avatar'],
                'email' => $hierarchyData['user_email'],
                'is_active' => $hierarchyData['is_active'],
                'company_user_id' => $hierarchyData['company_user_id'],
                'is_auto_analyze_calls' => $hierarchyData['is_auto_analyze_calls'],
                'languages' => [],
            ], User::class);
            $user->setRole($this->roleCollection->offsetGet($roleId));
            $this->userCollection->add($user);
        }

        if (!is_null($hierarchyData['user_id'])) {
            $this
                    ->teamCollection
                    ->offsetGet($teamId)
                    ->getUsers()
                    ->add($this->userCollection->offsetGet($hierarchyData['user_id']));
        }

        return $this;
    }

    /**
     *
     * @param \ArrayObject $hierarchyData
     * @return CompanyHierarchy
     */
    public function addUsers(\ArrayObject $hierarchyData): CompanyHierarchy
    {
        $roleId = $hierarchyData['role_id'] ?? 0;
        if (!$this->roleCollection->offsetExists($roleId)) {
            $role = $this->hydrate([
                'id' => $roleId,
                'name' => $hierarchyData['role_name'],
                'role_type' => $hierarchyData['role_type'],
            ], Role::class);
            $this->roleCollection->add($role);
        }

        if (!is_null($hierarchyData['user_id']) && !$this->userCollection->offsetExists($hierarchyData['user_id'])) {
            $user = $this->hydrate([
                'id' => $hierarchyData['user_id'],
                'name' => $hierarchyData['user_name'],
                'avatar' => $hierarchyData['user_avatar'],
                'email' => $hierarchyData['user_email'],
                'is_active' => $hierarchyData['is_active'],
                'company_user_id' => $hierarchyData['company_user_id'],
                'is_auto_analyze_calls' => $hierarchyData['is_auto_analyze_calls'],
                'languages' => [],
            ], User::class);
            $user->setRole($this->roleCollection->offsetGet($roleId));
            $this->userCollection->add($user);

            $teamId = 0;

            if (!$this->teamCollection->offsetExists($teamId)) {
                $this->teamCollection->add($this->hydrate([
                    'id' => $teamId,
                    'name' => static::USERS_WITHOUT_TEAM,
                ], Team::class, withConstructor: true));
            }

            if (!is_null($hierarchyData['user_id'])) {
                $this
                        ->teamCollection
                        ->offsetGet($teamId)
                        ->getUsers()
                        ->add($this->userCollection->offsetGet($hierarchyData['user_id']));
            }
        }

        return $this;
    }

    /**
     *
     * @param array $languages
     * @return CompanyHierarchy
     */
    public function injectLanguages(array $languages): CompanyHierarchy
    {
        foreach ($languages as $language) {
            $this->userCollection->offsetGet($language['user_id'])
                    ->addLanguage($language['language']);
        }
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        $result = [];
        $rolesHierarchy = $this->getRolesHierarchy();
        foreach ($this->teamCollection as $team) {
            $rolesArray = [];
            foreach ($team->getUsers() as $user) {
                if ($user->getRole()->isAdmin()) {
                    continue;
                }
                if (!isset($rolesArray[$user->getRole()->getId()])) {
                    $rolesArray[$user->getRole()->getId()] = $user->getRole()->toArray();
                    $rolesArray[$user->getRole()->getId()]['users'] = [];
                }
                $rolesArray[$user->getRole()->getId()]['users'][] = $user->toArray([
                    'id',
                    'name',
                    'email',
                    'avatar',
                    'is_active',
                    'languages',
                    'company_user_id',
                    'is_auto_analyze_calls',
                ]);
            }
            usort($rolesArray, function ($roleA, $roleB) use ($rolesHierarchy) {
                $roleAIndex = array_search($roleA['role_type'], $rolesHierarchy);
                $roleBIndex = array_search($roleB['role_type'], $rolesHierarchy);
                return (int) ($roleAIndex > $roleBIndex);
            });
            if (empty($rolesArray) && $team->getId() === 0) {
                continue;
            }
            $result[] = [
                'id' => $team->getId(),
                'name' => $team->getName(),
                'roles' => array_values($rolesArray),
            ];
        }
        usort($result, function ($teamA, $teamB) {
            return (int) ($teamA['id'] < $teamB['id']);
        });
        return $result;
    }

    /**
     *
     * @return array
     */
    protected function getRolesHierarchy(): array
    {
        return [
            Role::ADMIN_ROLE_TYPE,
            Role::COMPANY_ADMIN_ROLE_TYPE,
            Role::COMPANY_SUPERVISOR_ROLE_TYPE,
            Role::MANAGER_ROLE_TYPE,
            Role::AGENT_ROLE_TYPE,
        ];
    }
}
