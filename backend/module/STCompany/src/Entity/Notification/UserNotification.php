<?php

declare(strict_types=1);

namespace STCompany\Entity\Notification;

use STLib\Mvc\Hydrator\BaseHydratorTrait;

class UserNotification
{
    use BaseHydratorTrait;

    public const EMAIL_DELIVERY_WAY = 'email';

    public const COMMENT_ALL_TYPE = 'comment_all';
    public const COMMENT_MENTIONS_TYPE = 'comment_mentions';
    public const CALL_EVENTS_TYPE = 'call_events';

    public const DELIVERY_WAYS = [
        self::EMAIL_DELIVERY_WAY,
    ];

    public const TYPES = [
        self::COMMENT_ALL_TYPE,
        self::COMMENT_MENTIONS_TYPE,
        self::CALL_EVENTS_TYPE,
    ];

    /**
     *
     * @var array
     */
    protected array $deliveryWays;

    /**
     *
     * @var string|null
     */
    protected ?string $notificationType = null;

    /**
     *
     * @return array
     */
    public function getDeliveryWays(): array
    {
        return $this->deliveryWays;
    }

    /**
     *
     * @param array|string $deliveryWays
     * @return UserNotification
     */
    public function setDeliveryWays(array|string $deliveryWays): UserNotification
    {
        if (is_string($deliveryWays)) {
            $deliveryWays = json_decode($deliveryWays, true);
            if (!is_array($deliveryWays)) {
                throw new \InvalidArgumentException('Invalid value for delivery_way property');
            }
        }
        $this->deliveryWays = $deliveryWays;
        return $this;
    }

    /**
     *
     * @return string|null
     */
    public function getNotificationType(): ?string
    {
        return $this->notificationType;
    }

    /**
     *
     * @param string $notificationType
     * @return UserNotification
     */
    public function setNotificationType(string $notificationType): UserNotification
    {
        $this->notificationType = $notificationType;
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        return $this->extract($this);
    }
}
