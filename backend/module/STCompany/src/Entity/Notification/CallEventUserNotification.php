<?php

declare(strict_types=1);

namespace STCompany\Entity\Notification;

class CallEventUserNotification extends UserNotification
{
    /**
     *
     * @var array
     */
    protected array $eventIds = [];

    /**
     *
     * @return array
     */
    public function getEventIds(): array
    {
        return $this->eventIds;
    }

    /**
     *
     * @param array $eventIds
     * @return CallEventUserNotification
     */
    public function setEventIds(array $eventIds): CallEventUserNotification
    {
        $this->eventIds = $eventIds;
        return $this;
    }
}
