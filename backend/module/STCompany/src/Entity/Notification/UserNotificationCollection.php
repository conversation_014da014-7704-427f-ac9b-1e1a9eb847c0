<?php

declare(strict_types=1);

namespace STCompany\Entity\Notification;

class UserNotificationCollection extends \STLib\Expand\Collection
{
    /**
     *
     * @param UserNotification $userNotification
     * @param string|int|null $key
     * @return \STLib\Expand\Collection
     * @throws \RuntimeException
     */
    public function add(mixed $userNotification, string|int|null $key = null): \STLib\Expand\Collection
    {
        if (!($userNotification instanceof UserNotification)) {
            throw new \RuntimeException('User notification must be an instace of "\STCompany\Entity\Notification\UserNotification"');
        }
        parent::add($userNotification, $key ?? $userNotification->getNotificationType());
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        $result = [];
        foreach ($this as $userNotification) {
            $result[] = $userNotification->toArray();
        }
        return $result;
    }
}
