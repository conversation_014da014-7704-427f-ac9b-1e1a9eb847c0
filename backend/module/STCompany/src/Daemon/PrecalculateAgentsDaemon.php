<?php

namespace STCompany\Daemon;

class PrecalculateAgentsDaemon extends \STRabbit\Entity\AbstractDaemon
{
    public const AGENT_PRECALCULATION_QUEUE = 'agents-precalculation';
    public const AGENT_PRECALCULATION_QUEUE_ERROR = 'agents-precalculation_error';

    /**
     *
     * @param string $message
     * @return void
     */
    public function handle(string $message): void
    {
        $data = json_decode($message);
        $companyId = (int) $data->company_id;
        $agentIds = $data->agent_ids;
        $startDate = !is_null($data->start_date) ? \Carbon\Carbon::parse($data->start_date) : null;
        $endDate = !is_null($data->end_date) ? \Carbon\Carbon::parse($data->end_date) : null;
        $roleId = $data->role_id ?? null;
        /** @var \STCompany\Service\AgentPrecalculation\AgentPrecalculationService $agentPrecalculationService */
        $agentPrecalculationService = $this->params()->offsetGet(\STCompany\Service\AgentPrecalculation\AgentPrecalculationService::class);
        $agentPrecalculationService->precalculateAgents($companyId, $agentIds, $startDate, $endDate, $roleId);
    }

    /**
     *
     * @return \STRabbit\Entity\AbstractDaemon
     */
    public function init(): \STRabbit\Entity\AbstractDaemon
    {
        $this->getChannel()->queue_declare(
            queue: $this->getQueueName(),
            passive: false,
            durable: true,
            exclusive: false,
            auto_delete: false,
            nowait: false,
            arguments: new \PhpAmqpLib\Wire\AMQPTable([
                'x-max-priority' => 100,
            ]),
        );
        $this->getChannel()->queue_declare(
            queue: $this->getErrorQueueName(),
            passive: false,
            durable: true,
            exclusive: false,
            auto_delete: false,
        );
        return $this;
    }
}
