<?php

declare(strict_types=1);

namespace STCompany\Daemon;

use Carbon\Carbon;
use STCall\Service\AwsTrait;
use STCall\Service\CallService;
use STCompany\Service\CompanyService;
use STRabbit\Entity\AbstractDaemon;

class DeleteCallsDaemon extends AbstractDaemon
{
    use AwsTrait;

    public const string QUEUE = 'company-calls-delete';
    public const string QUEUE_ERROR = 'company-calls-delete-error';

    protected const int S3_DELETION_BATCH_SIZE = 1000;

    /**
     *
     * @param string $message
     * @return void
     * @throws \Exception
     */
    public function handle(string $message): void
    {
        $data = json_decode($message);

        $awsConfig = $this->params()->offsetGet('aws_config');
        /** @var CompanyService $companyService */
        $companyService = $this->params()->offsetGet(CompanyService::class);
        /** @var CallService $callService */
        $callService = $this->params()->offsetGet(CallService::class);

        $company = $companyService->getCompany((int) $data->company_id);
        $callsEndDate = (new Carbon())->subDays($company->getDaysToRemoveCalls());

        $this->setAwsConfig($awsConfig['api']);
        $this->setEnv($awsConfig['env']);
        $this->setCompany($company);

        $this->deleteS3Files(
            $callService->getS3Files($company->getId(), null, $callsEndDate)
        );
        $callService->deleteCalls(
            $company->getId(),
            null,
            $callsEndDate,
        );
    }

    /**
     * @param array $s3Files
     * @return void
     */
    protected function deleteS3Files(array $s3Files): void
    {
        foreach (array_chunk($s3Files, self::S3_DELETION_BATCH_SIZE) as $s3FilesBatch) {
            $this->deleteFilesFromS3($s3FilesBatch);
        }
    }
}
