<?php

declare(strict_types=1);

namespace STCompany\Controller\Plugin;

use Laminas\Mvc\Controller\Plugin\AbstractPlugin;

class Team<PERSON>er<PERSON><PERSON><PERSON>cker extends AbstractPlugin
{
    /**
     *
     * @return bool
     * @throws \STApi\Entity\Exception\ForbiddenApiException
     * @throws \STApi\Entity\Exception\NotFoundApiException
     */
    public function checkCallAccess(): bool
    {
        $companyId = (int) $this->getController()->getRequest()->getHeaders()->get('company-id')->getFieldValue();
        $user = $this->getController()->company()->user()->getUser($companyId, $this->getController()->auth()->getUser()->getId());
        if ($user->getBaseRole()->isAdmin() || $user->getBaseRole()->isCompanyAdmin() || $user->getBaseRole()->isCompanySupervisor()) {
            return true;
        }
        $company = $this->getController()->company()->getCompany($companyId, $user->getId());
        $callId = $this->getController()->params()->fromRoute('call_id');
        $agentId = (int) $this->getController()->call()->getCallParam($company->getId(), $callId, 'agent_id');
        if ($agentId === 0) {
            throw new \STApi\Entity\Exception\NotFoundApiException('Call not found');
        }
        if (!$this->getController()->company()->team()->checkIfUserHasAccessToAgentsTeam($company->getId(), $user->getId(), $agentId)) {
            throw new \STApi\Entity\Exception\ForbiddenApiException('The agent you are trying to see is under team that you are not part of it');
        }
        return true;
    }

    /**
     *
     * @return bool
     * @throws \STApi\Entity\Exception\ForbiddenApiException
     */
    public function checkUserAccess(): bool
    {
        $companyId = (int) $this->getController()->getRequest()->getHeaders()->get('company-id')->getFieldValue();
        $user = $this->getController()->company()->user()->getUser($companyId, $this->getController()->auth()->getUser()->getId());
        if ($user->getBaseRole()->isAdmin() || $user->getBaseRole()->isCompanyAdmin() || $user->getBaseRole()->isCompanySupervisor()) {
            return true;
        }
        $company = $this->getController()->company()->getCompany($companyId, $user->getId());
        $requestedUserId = (int) $this->getController()->params()->fromRoute('user_id');
        if (!$this->getController()->company()->team()->checkIfUserHasAccessToAgentsTeam($company->getId(), $user->getId(), $requestedUserId)) {
            throw new \STApi\Entity\Exception\ForbiddenApiException('User doesn\'t have access to requested user');
        }
        return true;
    }
}
