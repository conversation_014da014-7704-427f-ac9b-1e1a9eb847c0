<?php

declare(strict_types=1);

namespace STCompany\Controller\Plugin;

use Api\Controller\V0\BaseController;
use <PERSON><PERSON>\Mvc\Controller\Plugin\AbstractPlugin;
use OutOfRangeException;
use ST<PERSON>pi\Entity\Exception\ForbiddenApiException;
use STApi\Entity\Exception\UnauthorizedApiException;
use STCompany\Data\PermissionsTable;
use STCompany\Entity\Company as CompanyEntity;

/**
 * @method BaseController getController()
 */
class CompanyPermissionChecker extends AbstractPlugin
{
    /**
     *
     * @var CompanyEntity
     */
    protected CompanyEntity $company;

    /**
     *
     * @return CompanyEntity
     * @throws UnauthorizedApiException
     */
    public function getActiveCompany(): CompanyEntity
    {
        if (!$this->getController()->getRequest()->getHeaders()->has('company-id')) {
            throw new UnauthorizedApiException('Access to company is forbidden');
        }
        $companyId = (int) $this->getController()->getRequest()->getHeaders()->get('company-id')->getFieldValue();
        $user = $this->getController()->auth()->getUser();
        $company = $this->getController()->company()->getCompany($companyId, $user->getId());
        $isActiveUser = $this->getController()->company()->user()->getCompanyUserActivity($companyId, $user->getId());

        if (is_null($company)) {
            throw new UnauthorizedApiException('Access to company is forbidden');
        }

        if (!$isActiveUser) {
            throw new UnauthorizedApiException('User is not active');
        }
        $this->company = $company;
        return $this->company;
    }

    /**
     *
     * @return bool
     * @throws OutOfRangeException
     * @throws ForbiddenApiException
     */
    public function checkRoleAccess(): bool
    {
        $userId = $this->getController()->auth()->getUser()->getId();
        $user = $this->getController()->company()->user()->getUser($this->company->getId(), $userId);
        if ($user->getRole()->isAdmin() || $user->getRole()->isCompanyAdmin()) {
            return true;
        }

        $route = $this->getController()->getServiceManager()->get('application')->getMvcEvent()->getRouteMatch();
        $requiredPermissions = $route->getParam('permissions');

        if (is_null($requiredPermissions)) {
            throw new OutOfRangeException('Permissions are not set for route "' . $route->getMatchedRouteName() . '"');
        }

        foreach ($requiredPermissions as $requiredPermission) {
            $permissionId = $requiredPermission['permission'];
            $accessLevel = $requiredPermission['level'];
            $userPermissions = $user->getRole()->getPermissions();
            if (!$userPermissions->offsetExists($permissionId)) {
                throw new ForbiddenApiException('User doesn\'t have required permission');
            }
            if ($accessLevel === PermissionsTable::WRITE_PERMISSION && $userPermissions->offsetGet($permissionId)->getAccessLevel() !== PermissionsTable::WRITE_PERMISSION) {
                throw new ForbiddenApiException('User has required permission, but doesn\'t have adequate access level');
            }
        }
        return true;
    }
}
