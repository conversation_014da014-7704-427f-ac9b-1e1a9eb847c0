<?php

declare(strict_types=1);

namespace STCompany\Controller\Plugin;

use Lam<PERSON>\Mvc\Controller\Plugin\AbstractPlugin;

class UserNotification extends AbstractPlugin
{
    /**
     *
     * @param string $name
     * @param array $arguments
     * @return mixed
     * @throws \BadMethodCallException
     */
    public function __call(string $name, array $arguments): mixed
    {
        $userNotificationService = $this->getController()->getServiceManager()->get(\STCompany\Service\Notification\UserNotificationService::class);
        if (!method_exists($userNotificationService, $name)) {
            throw new \BadMethodCallException('Invalid \STCompany\Service\Notification\UserNotificationService method: ' . $name);
        }
        return call_user_func_array([
            $userNotificationService,
            $name
        ], $arguments);
    }

    /**
     *
     * @return \STCompany\Service\Notification\UserNotificationSenderService
     */
    public function sender(): \STCompany\Service\Notification\UserNotificationSenderService
    {
        return $this->getController()->getServiceManager()->get(\STCompany\Service\Notification\UserNotificationSenderService::class);
    }
}
