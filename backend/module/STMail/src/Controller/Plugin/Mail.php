<?php

declare(strict_types=1);

namespace STMail\Controller\Plugin;

use Laminas\Mvc\Controller\Plugin\AbstractPlugin;

class Mail extends AbstractPlugin
{
    /**
     *
     * @param string $name
     * @param array $arguments
     * @return mixed
     */
    public function __call(string $name, array $arguments): mixed
    {
        $service = $this->getController()->getServiceManager()->get(\STMail\Service\MailService::class);
        return call_user_func_array([
            $service,
            $name
        ], $arguments);
    }
}
