<?php

declare(strict_types=1);

namespace STMail\Service;

class MailService
{
    use \STRabbit\Service\ProvidesRabbit;
    use \STFront\Service\ProvidesFrontService;

    public const DRIVER_SPARKPOST = 'sparkpost';
    public const MAIL_QUEUE_NAME = 'mail';
    public const ERROR_MAIL_QUEUE_NAME = 'error_mail';

    /**
     *
     * @var array
     */
    protected array $config = [];

    /**
     *
     * @var array
     */
    protected array $drivers = [];

    /**
     *
     * @param array $config
     * @throws \RuntimeException
     */
    public function __construct(array $config = [])
    {
        $this->config = $config;
    }

    /**
     *
     * @param string $driver
     * @param array $options
     * @return bool
     */
    public function addToQueue(int $frontId, \STUser\Entity\User $user, array $options, string $driver = self::DRIVER_SPARKPOST): bool
    {
        $front = $this->frontService()->getFrontById($frontId);

        if (empty($front->getSendingEmail())) {
            return false;
        }
        $options['recipients'] = [
            [
                'address' => [
                    'name' => $user->getName(),
                    'email' => $user->getEmail(),
                ],
            ],
        ];
        $channel = $this->rabbit()->getChannel();
        $message = new \PhpAmqpLib\Message\AMQPMessage(json_encode([
            'driver' => $driver,
            'options' => $options,
            'front_id' => $front->getId(),
        ]));
        $channel->basic_publish($message, '', static::MAIL_QUEUE_NAME);
        $channel->close();
        return true;
    }

    /**
     *
     * @param int $frontId
     * @param string $driverName
     * @param array $options
     * @return void
     */
    public function send(int $frontId, string $driverName, array $options): void
    {
        $front = $this->frontService()->getFrontById($frontId);
        $siteUrl = (new \Laminas\Uri\Uri())
                ->setHost($front->getDomain())
                ->setScheme('https')
                ->toString();
        $options['substitutions']['dynamic_html']['logo_link'] = $front->getEmailLogoLink();
        $options['substitutions']['dynamic_html']['main_page_url_link'] = $siteUrl;
        $options['substitutions']['from_email'] = $front->getSendingEmail();
        $options['substitutions']['from_email_sender'] = $front->getSendingEmailSender();
        $driver = $this->getDriver($driverName);
        $driver->send($options);
    }

    /**
     *
     * @return \STMail\Service\MailService
     */
    public function getMailServiceInstance(): MailService
    {
        return $this;
    }

    /**
     *
     * @param string $driverName
     * @return \STMail\Driver\DriverInterface
     * @throws \OutOfRangeException
     */
    protected function getDriver(string $driverName): \STMail\Driver\DriverInterface
    {
        if (!isset($this->drivers[$driverName])) {
            if (!isset($this->config['drivers'][$driverName]['class'])) {
                throw new \OutOfRangeException('Cannot find class setting for mail driver "' . $driverName . '"');
            }
            $driver = new $this->config['drivers'][$driverName]['class']();
            $driver->setOptions($this->config['drivers'][$driverName]['options'] ?? []);
            $this->drivers[$driverName] = $driver;
        }
        return $this->drivers[$driverName];
    }
}
