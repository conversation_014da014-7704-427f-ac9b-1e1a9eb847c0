<?php

declare(strict_types=1);

namespace STMail\Service;

class MailServiceFactory implements \Laminas\ServiceManager\Factory\FactoryInterface
{
    /**
     *
     * @param \Interop\Container\ContainerInterface $container
     * @param string $requestedName
     * @param array $options
     * @return \STMail\Service\MailService
     */
    public function createService(\Interop\Container\ContainerInterface $container, $requestedName, array $options = null): MailService
    {
        return $this($container, $requestedName ?: PatternPluginManager::class, $this->creationOptions);
    }

    /**
     *
     * @param \Interop\Container\ContainerInterface $container
     * @param string $requestedName
     * @param array $options
     * @return \STMail\Service\MailService
     */
    public function __invoke(\Interop\Container\ContainerInterface $container, $requestedName, array $options = null): MailService
    {
        $config = $container->get('config');
        $mailConfig = $config['mail'] ?? [];
        return new MailService($mailConfig);
    }
}
