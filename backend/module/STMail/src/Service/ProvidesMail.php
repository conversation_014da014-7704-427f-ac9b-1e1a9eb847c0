<?php

declare(strict_types=1);

namespace STMail\Service;

trait ProvidesMail
{
    /**
     *
     * @var MailService
     */
    private ?MailServiceWrapper $mailServiceWrapper = null;

    /**
     *
     * @return MailService|null
     */
    protected function mail(): ?MailService
    {
        if (is_null($this->mailServiceWrapper)) {
            $this->mailServiceWrapper = new MailServiceWrapper();
        }
        return $this->mailServiceWrapper->getStaticMailService();
    }
}
