<?php

declare(strict_types=1);

namespace STMail\Service;

final class MailServiceWrapper
{
    /**
     *
     * @var MailService|null
     */
    private static ?MailService $staticMailService = null;

    /**
     *
     * @param MailService $mailService
     * @return void
     */
    public static function setStaticMailService(MailService $mailService): void
    {
        self::$staticMailService = $mailService;
    }

    /**
     *
     * @return MailService|null
     */
    public function getStaticMailService(): ?MailService
    {
        return self::$staticMailService;
    }
}
