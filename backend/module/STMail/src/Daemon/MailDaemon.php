<?php

declare(strict_types=1);

namespace STMail\Daemon;

use STMail\Service\MailService;

class <PERSON>Dae<PERSON> extends \STRabbit\Entity\AbstractDaemon
{
    /**
     *
     * @param string $message
     * @return void
     */
    public function handle(string $message): void
    {
        $data = json_decode($message, true);
        $frontId = (int) $data['front_id'];
        $driver = $data['driver'];
        $options = $data['options'];

        /** @var MailService $mailService */
        $mailService = $this->params()->offsetGet(\STMail\Service\MailService::class);
        $mailService->send($frontId, $driver, $options);
    }
}
