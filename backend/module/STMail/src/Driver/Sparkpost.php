<?php

declare(strict_types=1);

namespace STMail\Driver;

use GuzzleHttp\Client;
use Http\Adapter\Guzzle7\Client as GuzzleAdapter;

final class Sparkpost implements DriverInterface
{
    /**
     *
     * @var \SparkPost\SparkPost
     */
    private ?\SparkPost\SparkPost $client = null;

    /**
     *
     * @var string
     */
    private string $key;

    /**
     *
     * @param array $options
     * @return DriverInterface
     * @throws \RuntimeException
     */
    public function setOptions(array $options = []): DriverInterface
    {
        if (!isset($options['key']) || empty($options['key'])) {
            throw new \RuntimeException('Sparkpost key must be provided');
        }
        $this->key = $options['key'];
        return $this;
    }

    /**
     *
     * @param array $options
     * @return DriverInterface
     * @throws \InvalidArgumentException
     */
    public function send(array $options = []): DriverInterface
    {
        if (!isset($options['template_id'])) {
            throw new \InvalidArgumentException('Options must include param "template_id"');
        }

        $client = $this->getClient();
        $payload = [
            'content' => [
                'template_id' => $options['template_id'],
            ],
            'substitution_data' => $options['substitutions'],
            'recipients' => $options['recipients'],
        ];

        $promise = $client->transmissions->post($payload);
        $response = $promise->wait();
        return $this;
    }

    /**
     *
     * @return \SparkPost\SparkPost
     */
    private function getClient(): \SparkPost\SparkPost
    {
        if (is_null($this->client)) {
            $httpClient = new GuzzleAdapter(new Client());
            $this->client = new \SparkPost\SparkPost($httpClient, [
                'key' => $this->key,
            ]);
        }
        return $this->client;
    }
}
