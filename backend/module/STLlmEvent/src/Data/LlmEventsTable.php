<?php

namespace STLlmEvent\Data;

use STApi\Entity\Exception\NotFoundApiException;
use STLib\Db\HydratedAbstractTable;
use STLlmEvent\Entity\LlmEvent;
use STLlmEvent\Entity\LlmEventCollection;

class LlmEventsTable extends HydratedAbstractTable
{
    public static string $table = 'llm_events';
    public static string $entityName = LlmEvent::class;
    public static string $collectionName = LlmEventCollection::class;

    public static function getTableName(): string
    {
        return self::$table;
    }

    public static function getEntityName(): string
    {
        return self::$entityName;
    }

    public static function getCollectionName(): string
    {
        return self::$collectionName;
    }

    /**
     * @return LlmEventCollection
     */
    public function getLlmEvents(): LlmEventCollection
    {
        return $this->tableGateway->selectWith($this->tableGateway->getSql()->select())->getCollection();
    }

    public function saveLlmEvent(LlmEvent $llmEvent): void
    {
        $data = [
            'name' => $llmEvent->getName(),
            'description' => $llmEvent->getDescription(),
        ];

        if (!is_null($llmEvent->getId())) {
            $this->tableGateway->update($data, [
                'llm_event_id' => $llmEvent->getId(),
            ]);
        } else {
            $this->tableGateway->insert($data);
            $llmEvent->setId($this->tableGateway->lastInsertValue);
        }
    }

    public function deleteLlmEvent(int $llmEventId): void
    {
        $this->tableGateway->delete(['llm_event_id' => $llmEventId]);
    }

    /**
     * @throws NotFoundApiException
     */
    public function getLlmEvent(int $llmEventId): LlmEvent
    {
        $select = $this->tableGateway->getSql()->select();
        $select->where(['llm_event_id' => $llmEventId]);

        $result = $this->tableGateway->selectWith($select);
        if ($result->count() === 0) {
            throw new NotFoundApiException('Llm event not found');
        }

        return $result->current();
    }
}
