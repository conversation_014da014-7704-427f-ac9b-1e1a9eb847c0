<?php

declare(strict_types=1);

namespace STLlmEvent\ServiceProviding;

use STApi\Entity\Exception\NotFoundApiException;
use STIndustry\Service\LlmEvent\Interfaces\LlmEventSelectorInterface;
use STLlmEvent\Entity\LlmEvent;
use STLlmEvent\Service\LlmEventSaverService;
use STLlmEvent\Service\LlmEventSelectorService;
use STOnboarding\Service\Interfaces\LlmEventSelectorInterface as OnboardingLlmEventSelectorInterface;
use STCompany\Service\Interfaces\LlmEventSelectorInterface as CompanyLlmEventSelectorInterface;
use STCompany\Service\Interfaces\LlmEventSaverInterface as CompanyLlmEventSaverInterface;

final readonly class LlmEventServiceProvider implements
    LlmEventSelectorInterface,
    OnboardingLlmEventSelectorInterface,
    CompanyLlmEventSelectorInterface,
    CompanyLlmEventSaverInterface
{
    public function __construct(
        private LlmEventSelectorService $llmEventSelector,
        private LlmEventSaverService $llmEventSaver,
    ) {
    }

    /**
     * @throws NotFoundApiException
     */
    public function getLlmEvent(int $llmEventId): LlmEvent
    {
        return $this->llmEventSelector->getLlmEvent($llmEventId);
    }

    public function save(string $name, string $description, ?int $id = null): LlmEvent
    {
        return $this->llmEventSaver->save($name, $description, $id);
    }
}
