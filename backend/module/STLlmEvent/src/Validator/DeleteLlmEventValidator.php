<?php

namespace STLlmEvent\Validator;

use ST<PERSON><PERSON>\Entity\Exception\NotFoundApiException;
use STLib\Validator\Validator;
use STLlmEvent\Data\LlmEventsTable;

class DeleteLlmEventValidator extends Validator
{
    protected const string ERROR_EVENT_DOES_NOT_EXIST = 'The event does not exists.';

    public function __construct(private readonly LlmEventsTable $llmEventsTable)
    {
    }

    public function run(): void
    {
        $llmEventId = $this->getInstance();

        try {
            $this->llmEventsTable->getLlmEvent($llmEventId);
        } catch (NotFoundApiException) {
            $this->addError('id', self::ERROR_EVENT_DOES_NOT_EXIST);

            return;
        }
    }
}
