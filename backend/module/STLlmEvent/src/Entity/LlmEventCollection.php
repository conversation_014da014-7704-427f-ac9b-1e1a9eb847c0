<?php

declare(strict_types=1);

namespace STLlmEvent\Entity;

use RuntimeException;
use STLib\Expand\Collection;
use Traversable;

/**
 * @extends Traversable<array-key,LlmEvent>
 */
class LlmEventCollection extends Collection
{
    /**
     * @param mixed $llmEvent
     * @param string|int|null $key
     * @return Collection
     * @throws RuntimeException
     */
    public function add(mixed $llmEvent, string|int|null $key = null): Collection
    {
        if (!($llmEvent instanceof LlmEvent)) {
            throw new RuntimeException(
                sprintf(
                    'LlmEvent must be an instance of "%s"',
                    $this->getEntityClass()
                )
            );
        }
        parent::add($llmEvent, $key ?? $llmEvent->getId());

        return $this;
    }

    /**
     * @param bool $asArray
     * @return array|LlmEvent[]
     */
    public function toArray(bool $asArray = true): array
    {
        if (!$asArray) {
            return parent::toArray();
        }

        $result = [];
        foreach ($this as $event) {
            $result[] = $event->toArray();
        }
        return $result;
    }

    protected function getEntityClass(): string
    {
        return LlmEvent::class;
    }
}
