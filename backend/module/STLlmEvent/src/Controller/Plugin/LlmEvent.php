<?php

declare(strict_types=1);

namespace STLlmEvent\Controller\Plugin;

use Lam<PERSON>\Mvc\Controller\Plugin\AbstractPlugin;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use STLib\Mvc\Controller\AbstractController;
use STLlmEvent\Service\LlmEventRemoverService;
use STLlmEvent\Service\LlmEventSaverService;
use STLlmEvent\Service\LlmEventSelectorService;

/**
 * @method AbstractController getController()
 */
class LlmEvent extends AbstractPlugin
{
    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function llmEventSelector(): LlmEventSelectorService
    {
        return $this->getController()->getServiceManager()->get(LlmEventSelectorService::class);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function llmEventSaver(): LlmEventSaverService
    {
        return $this->getController()->getServiceManager()->get(LlmEventSaverService::class);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function llmEventRemover(): LlmEventRemoverService
    {
        return $this->getController()->getServiceManager()->get(LlmEventRemoverService::class);
    }
}
