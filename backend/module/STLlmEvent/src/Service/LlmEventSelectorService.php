<?php

namespace STLlmEvent\Service;

use STApi\Entity\Exception\NotFoundApiException;
use STLib\Expand\Collection;
use STLlmEvent\Data\LlmEventsTable;
use STLlmEvent\Entity\LlmEvent;
use STLlmEvent\Entity\LlmEventCollection;

class LlmEventSelectorService
{
    public function __construct(private readonly LlmEventsTable $llmEventsTable)
    {
    }

    /**
     * @return LlmEventCollection
     */
    public function getLlmEvents(): Collection
    {
        return $this->llmEventsTable->getLlmEvents();
    }

    /**
     * @throws NotFoundApiException
     */
    public function getLlmEvent(int $id): LlmEvent
    {
        return $this->llmEventsTable->getLlmEvent($id);
    }
}
