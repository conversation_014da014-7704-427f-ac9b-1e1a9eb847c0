<?php

namespace STLlmEvent\Service;

use STLlmEvent\Data\LlmEventsTable;
use STLlmEvent\Entity\LlmEvent;

class LlmEventSaverService
{
    public function __construct(private readonly LlmEventsTable $llmEventsTable)
    {
    }

    public function save(string $name, string $description, ?int $id = null): LlmEvent
    {
        $llmEvent = new LlmEvent();
        $llmEvent->setId($id);
        $llmEvent->setName(trim($name));
        $llmEvent->setDescription($description);

        $this->llmEventsTable->saveLlmEvent($llmEvent);

        return $llmEvent;
    }
}
