<?php

declare(strict_types=1);

namespace STLlmEvent;

use STCompany\Service\Interfaces\LlmEventSelectorInterface as CompanyLlmEventSelectorInterface;
use STCompany\Service\Interfaces\LlmEventSaverInterface as CompanyLlmEventSaverInterface;
use STIndustry\Service\LlmEvent\Interfaces\LlmEventSelectorInterface;
use STLib\Mvc\Data\HydratedTableFactory;
use STLlmEvent\Controller\Plugin\LlmEvent;
use STLlmEvent\Data\LlmEventsTable;
use STLlmEvent\ServiceProviding\LlmEventServiceProvider;
use STOnboarding\Service\Interfaces\LlmEventSelectorInterface as OnboardingLlmEventSelectorInterface;

return [
    'controller_plugins' => [
        'invokables' => [
            'llmEvent' => LlmEvent::class,
        ],
    ],
    'service_manager' => [
        'factories' => [
            LlmEventsTable::class => HydratedTableFactory::class,
        ],
        'aliases' => [
            LlmEventSelectorInterface::class => LlmEventServiceProvider::class,
            OnboardingLlmEventSelectorInterface::class => LlmEventServiceProvider::class,
            CompanyLlmEventSelectorInterface::class => LlmEventServiceProvider::class,
            CompanyLlmEventSaverInterface::class => LlmEventServiceProvider::class,
        ],
    ],
];
