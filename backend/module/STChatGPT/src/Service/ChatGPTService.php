<?php

declare(strict_types=1);

namespace STChatGPT\Service;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;

class ChatGPTService
{
    /**
     *
     * @var array
     */
    protected array $config = [];

    /**
     *
     * @param array $config
     * @throws \RuntimeException
     */
    public function __construct(array $config = [])
    {
        $this->config = $config;
    }

    /**
     * @param string $prompt
     * @return string
     * @throws GuzzleException
     * @throws \JsonException
     */
    public function sendChatInteraction(string $prompt): string
    {
        $response = (new Client())->post('https://api.openai.com/v1/chat/completions', [
            'headers' => [
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $this->config['api']['key'],
            ],
            'json' => [
                'model' => 'gpt-4o',
                'messages' => [
                    ['role' => 'user', 'content' => $prompt],
                ],
                'temperature' => 0,
            ],
        ]);

        $responseData = json_decode(
            (string)$response->getBody(),
            true,
            512,
            JSON_THROW_ON_ERROR
        );

        return $responseData['choices'][0]['message']['content'];
    }
}
