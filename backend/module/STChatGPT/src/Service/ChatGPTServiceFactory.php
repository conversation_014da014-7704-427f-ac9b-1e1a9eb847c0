<?php

declare(strict_types=1);

namespace STChatGPT\Service;

class ChatGPTServiceFactory implements \Laminas\ServiceManager\Factory\FactoryInterface
{
    /**
     *
     * @param \Interop\Container\ContainerInterface $container
     * @param string $requestedName
     * @param array $options
     * @return \STChatGPT\Service\ChatGPTService
     */
    public function createService(\Interop\Container\ContainerInterface $container, $requestedName, array $options = null): ChatGPTService
    {
        return $this($container, $requestedName ?: PatternPluginManager::class, $this->creationOptions);
    }

    /**
     *
     * @param \Interop\Container\ContainerInterface $container
     * @param string $requestedName
     * @param array $options
     * @return \STChatGPT\Service\ChatGPTService
     */
    public function __invoke(\Interop\Container\ContainerInterface $container, $requestedName, array $options = null): ChatGPTService
    {
        $config = $container->get('config');

        return new ChatGPTService($config['chatgpt'] ?? []);
    }
}
