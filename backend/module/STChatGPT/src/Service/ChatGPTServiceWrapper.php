<?php

declare(strict_types=1);

namespace STChatGPT\Service;

final class ChatGPTServiceWrapper
{
    /**
     *
     * @var ?ChatGPTService
     */
    private static ?ChatGPTService $staticChatGPTService = null;

    /**
     *
     * @param ChatGPTService $chatGPTService
     * @return void
     */
    public static function setStaticRabbitService(ChatGPTService $chatGPTService): void
    {
        self::$staticChatGPTService = $chatGPTService;
    }

    /**
     *
     * @return ChatGPTService|null
     */
    public function getStaticChatGPTService(): ?ChatGPTService
    {
        return self::$staticChatGPTService;
    }
}
