<?php

declare(strict_types=1);

namespace STChatGPT\Service;

trait ProvidesChatGPT
{
    /**
     *
     * @var ?ChatGPTServiceWrapper
     */
    private ?ChatGPTServiceWrapper $chatGPTServiceWrapper = null;

    /**
     *
     * @return ChatGPTService
     */
    protected function chatGPT(): ChatGPTService
    {
        if (is_null($this->chatGPTServiceWrapper)) {
            $this->chatGPTServiceWrapper = new ChatGPTServiceWrapper();
        }
        return $this->chatGPTServiceWrapper->getStaticChatGPTService();
    }
}
