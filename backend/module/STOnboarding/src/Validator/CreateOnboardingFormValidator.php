<?php

declare(strict_types=1);

namespace STOnboarding\Validator;

use <PERSON><PERSON>\Validator\NotEmpty;
use Lam<PERSON>\Validator\StringLength;
use STLib\Validator\Validator;

class CreateOnboardingFormValidator extends Validator
{
    public function run(): void
    {
        /** @var array $input */
        $input = $this->getInstance();
        [
            'front_form_path' => $frontFormPath,
            'invite_path' => $invitePath,
            'company_name' => $companyName
        ] = $input;

        $isNotEmptyValidator = new NotEmpty();
        if (!$isNotEmptyValidator->isValid($frontFormPath)) {
            $this->addError('front_form_path', 'front_form_path is required.');
            return;
        }
        if (!$isNotEmptyValidator->isValid($invitePath)) {
            $this->addError('invite_path', 'invite_path is required.');
            return;
        }

        if (empty($companyName)) {
            return;
        }

        $stringValidator = new StringLength([
            'max' => 255,
        ]);

        if (!$stringValidator->isValid($companyName)) {
            $this->addError('company_name', 'Company name must be less than 255 symbols');
        }
    }
}
