<?php

namespace STOnboarding\Validator;

use <PERSON><PERSON><PERSON>\Entity\Exception\NotFoundApiException;
use STLib\Validator\Validator;
use STOnboarding\Data\OnboardingFormsTable;

class OnboardingFormExistsValidator extends Validator
{
    protected const string THE_ONBOARDING_FORM_DOES_NOT_EXISTS = 'The onboarding form does not exists.';

    public function __construct(protected readonly OnboardingFormsTable $onboardingFormsTable)
    {
    }

    public function run(): void
    {
        ['external_id' => $onboardingFormExternalId] = $this->getInstance();

        try {
            $this->onboardingFormsTable->getFormByExternalId($onboardingFormExternalId);
        } catch (NotFoundApiException) {
            $this->addError('id', self::THE_ONBOARDING_FORM_DOES_NOT_EXISTS);

            return;
        }
    }
}
