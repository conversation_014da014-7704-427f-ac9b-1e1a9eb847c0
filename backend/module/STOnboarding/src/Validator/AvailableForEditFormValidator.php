<?php

declare(strict_types=1);

namespace STOnboarding\Validator;

use ST<PERSON>pi\Entity\Exception\NotFoundApiException;

class AvailableForEditFormValidator extends OnboardingFormExistsValidator
{
    private const string ERROR_MESSAGE = 'The completed onboarding form is already submitted and disable for edit.';

    /**
     * @throws NotFoundApiException
     */
    public function run(): void
    {
        parent::run();
        if ($this->hasError()) {
            return;
        }

        ['external_id' => $onboardingFormExternalId] = $this->getInstance();

        $form = $this->onboardingFormsTable->getFormByExternalId($onboardingFormExternalId);

        if ($form->getIsSubmitted()) {
            $this->addError('id', self::ERROR_MESSAGE);
        }
    }
}
