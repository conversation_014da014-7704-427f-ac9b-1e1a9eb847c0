<?php

declare(strict_types=1);

namespace STOnboarding\Validator;

use <PERSON><PERSON>\Validator\EmailAddress;
use <PERSON><PERSON>\Validator\IsArray;
use <PERSON><PERSON>\Validator\NotEmpty;
use Laminas\Validator\StringLength;

class UpdateUsersValidator extends AvailableForEditFormValidator
{
    private const string ERROR_MESSAGE_WRONG_FORMAT = 'Each user must be an array with name and email fields, both required.';

    public function run(): void
    {
        parent::run();
        if ($this->hasError()) {
            return;
        }

        /** @var array $input */
        $input = $this->getInstance();
        $users = $input['users'];

        $isArrayValidator = new IsArray();

        if (!$isArrayValidator->isValid($users)) {
            $this->addError('users', self::ERROR_MESSAGE_WRONG_FORMAT);
            return;
        }
        $isNotEmptyValidator = new NotEmpty();
        if (!$isNotEmptyValidator->isValid($users)) {
            $this->addError('users', self::ERROR_MESSAGE_WRONG_FORMAT);
            return;
        }

        foreach ($users as $user) {
            if (!$this->isUserValid($user)) {
                $this->addError('users', self::ERROR_MESSAGE_WRONG_FORMAT);
                return;
            }
        }
    }

    private function isUserValid(array $user): bool
    {
        if (!array_key_exists('name', $user)) {
            return false;
        }
        if (!array_key_exists('email', $user)) {
            return false;
        }

        $isNotEmptyValidator = new NotEmpty();

        if (!$isNotEmptyValidator->isValid($user['name'])) {
            return false;
        }
        if (!$isNotEmptyValidator->isValid($user['email'])) {
            return false;
        }

        $maxLengthValidator = new StringLength([
            'max' => 255,
        ]);
        if (!$maxLengthValidator->isValid($user['name'])) {
            return false;
        }
        if (!$maxLengthValidator->isValid($user['email'])) {
            return false;
        }

        $emailValidator = new EmailAddress();
        if (!$emailValidator->isValid($user['email'])) {
            return false;
        }

        return true;
    }
}
