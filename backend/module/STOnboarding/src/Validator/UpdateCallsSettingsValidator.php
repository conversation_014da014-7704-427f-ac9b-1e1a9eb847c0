<?php

declare(strict_types=1);

namespace STOnboarding\Validator;

use Lam<PERSON>\Validator\IsArray;
use Laminas\Validator\NotEmpty;
use ST<PERSON>all\Data\CallsTable;

class UpdateCallsSettingsValidator extends AvailableForEditFormValidator
{
    private const string ERROR_MESSAGE_WRONG_FORMAT = 'Wrong data format.';

    public function run(): void
    {
        parent::run();
        if ($this->hasError()) {
            return;
        }

        /** @var array $input */
        $input = $this->getInstance();
        $callsSettings = $input['calls_settings'];

        $isArrayValidator = new IsArray();

        if (!$isArrayValidator->isValid($callsSettings)) {
            $this->addError('calls_settings', self::ERROR_MESSAGE_WRONG_FORMAT);
            return;
        }

        $isNotEmptyValidator = new NotEmpty();
        if (!$isNotEmptyValidator->isValid($callsSettings)) {
            $this->addError('calls_settings', self::ERROR_MESSAGE_WRONG_FORMAT);
            return;
        }

        if (!array_key_exists('languages', $callsSettings)) {
            $this->addError('calls_settings', self::ERROR_MESSAGE_WRONG_FORMAT);
            return;
        }

        if (!array_key_exists('min_call_duration_for_auto_analyze', $callsSettings)) {
            $this->addError('calls_settings', self::ERROR_MESSAGE_WRONG_FORMAT);
            return;
        }
        if (!$isNotEmptyValidator->isValid($callsSettings['min_call_duration_for_auto_analyze'])) {
            $this->addError('calls_settings', self::ERROR_MESSAGE_WRONG_FORMAT);
            return;
        }

        if (!filter_var($callsSettings['min_call_duration_for_auto_analyze'], FILTER_VALIDATE_INT)) {
            $this->addError('calls_settings', self::ERROR_MESSAGE_WRONG_FORMAT);
            return;
        }

        if ($callsSettings['min_call_duration_for_auto_analyze'] < 1 || $callsSettings['min_call_duration_for_auto_analyze'] > 300) {
            $this->addError('calls_settings', self::ERROR_MESSAGE_WRONG_FORMAT);
            return;
        }

        foreach ($callsSettings['languages'] as $language) {
            if (!array_key_exists($language, CallsTable::LANGUAGES)) {
                $this->addError('calls_settings', self::ERROR_MESSAGE_WRONG_FORMAT);
                return;
            }
        }
    }
}
