<?php

declare(strict_types=1);

namespace STOnboarding\Validator;

use Laminas\Validator\IsArray;
use Laminas\Validator\NotEmpty;
use Laminas\Validator\StringLength;
use STApi\Entity\Exception\NotFoundApiException;
use STOnboarding\Data\OnboardingFormsTable;
use STOnboarding\Service\Interfaces\IndustrySelectorInterface;
use STOnboarding\Service\Interfaces\LlmEventSelectorInterface;
use STOnboarding\Service\Notification\OnboardingFormChecker;
use STOnboarding\Service\OnboardingFormSaverService;

class UpdateActiveIndustryValidator extends AvailableForEditFormValidator
{
    private const string ERROR_MESSAGE_WRONG_FORMAT = 'Wrong data format.';

    public function __construct(
        OnboardingFormsTable $onboardingFormsTable,
        private readonly IndustrySelectorInterface $industrySelector,
        private readonly LlmEventSelectorInterface $llmEventSelector,
    ) {
        parent::__construct($onboardingFormsTable);
    }

    public function run(): void
    {
        parent::run();
        if ($this->hasError()) {
            return;
        }

        /** @var array $input */
        $input = $this->getInstance();
        $data = $input['data'];

        $isArrayValidator = new IsArray();

        if (!$isArrayValidator->isValid($data)) {
            $this->addError('active_industry', self::ERROR_MESSAGE_WRONG_FORMAT);
            return;
        }

        if (!array_key_exists('active_industry', $data)) {
            $this->addError('active_industry', self::ERROR_MESSAGE_WRONG_FORMAT);
            return;
        }

        if (!array_key_exists('events', $data)) {
            $this->addError('active_industry', self::ERROR_MESSAGE_WRONG_FORMAT);
            return;
        }

        if (!$isArrayValidator->isValid($data['events'])) {
            $this->addError('active_industry', self::ERROR_MESSAGE_WRONG_FORMAT);
            return;
        }

        $activeIndustryData = $data['active_industry'];
        if (!$this->isActiveIndustryValid($activeIndustryData)) {
            $this->addError('active_industry', self::ERROR_MESSAGE_WRONG_FORMAT);
            return;
        }

        foreach ($data['events'] as $event) {
            if (!$this->isEventValid($event)) {
                $this->addError('active_industry', self::ERROR_MESSAGE_WRONG_FORMAT);
                return;
            }
        }
    }

    private function isActiveIndustryValid(array $activeIndustryData): bool
    {
        if (!array_key_exists('type', $activeIndustryData)) {
            return false;
        }

        if (
            !in_array(
                $activeIndustryData['type'],
                [OnboardingFormSaverService::TYPE_EXISTING, OnboardingFormSaverService::TYPE_NEW]
            )
        ) {
            return false;
        }

        if ($activeIndustryData['type'] === OnboardingFormSaverService::TYPE_EXISTING) {
            if (!array_key_exists('id', $activeIndustryData)) {
                return false;
            }

            try {
                $this->industrySelector->getIndustry((int) $activeIndustryData['id']);
            } catch (NotFoundApiException) {
                return false;
            }
        } else {
            if (!array_key_exists('name', $activeIndustryData)) {
                return false;
            }
            $maxLengthValidator = new StringLength([
                'min' => 1,
                'max' => 255,
            ]);
            if (!$maxLengthValidator->isValid($activeIndustryData['name'])) {
                return false;
            }
        }

        return true;
    }

    private function isEventValid(array $event): bool
    {
        if (!array_key_exists('name', $event)) {
            return false;
        }
        if (!array_key_exists('description', $event)) {
            return false;
        }

        $isNotEmptyValidator = new NotEmpty();

        if (!$isNotEmptyValidator->isValid($event['name'])) {
            return false;
        }
        if (!$isNotEmptyValidator->isValid($event['description'])) {
            return false;
        }

        $lengthValidator = new StringLength([
            'max' => 255,
        ]);
        if (!$lengthValidator->isValid($event['name'])) {
            return false;
        }
        if (!$lengthValidator->isValid($event['description'])) {
            return false;
        }

        if (array_key_exists('id', $event)) {
            try {
                $this->llmEventSelector->getLlmEvent((int) $event['id']);
            } catch (NotFoundApiException) {
                return false;
            }
        }

        return true;
    }
}
