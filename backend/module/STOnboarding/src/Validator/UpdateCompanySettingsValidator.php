<?php

declare(strict_types=1);

namespace STOnboarding\Validator;

use Lam<PERSON>\Validator\StringLength;

class UpdateCompanySettingsValidator extends AvailableForEditFormValidator
{
    private const string ERROR_MESSAGE_COMPANY_NAME = 'Company name cannot be empty and must be less than 255 symbols.';
    private const string ERROR_MESSAGE_LOGO = 'Logo must be less than 50 kb.';

    public function run(): void
    {
        parent::run();
        if ($this->hasError()) {
            return;
        }

        /** @var array $input */
        $input = $this->getInstance();
        ['company_name' => $companyName, 'company_logo' => $companyLogo] = $input;

        $companyNameValidator = new StringLength([
            'min' => 1,
            'max' => 255,
        ]);

        if (!$companyNameValidator->isValid($companyName)) {
            $this->addError('company_name', self::ERROR_MESSAGE_COMPANY_NAME);
        }

        if (!empty($companyLogo)) {
            $avatarValidator = new StringLength([
                'max' => 50 * 1024,
            ]);
            if (!$avatarValidator->isValid($companyLogo)) {
                $this->addError('company_logo', self::ERROR_MESSAGE_LOGO);
            }
        }
    }
}
