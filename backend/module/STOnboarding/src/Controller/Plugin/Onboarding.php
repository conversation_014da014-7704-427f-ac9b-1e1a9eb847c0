<?php

declare(strict_types=1);

namespace STOnboarding\Controller\Plugin;

use Lam<PERSON>\Mvc\Controller\Plugin\AbstractPlugin;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use STLib\Mvc\Controller\AbstractController;
use STOnboarding\Service\NotificatorService;
use STOnboarding\Service\OnboardingFormSaverService;
use STOnboarding\Service\OnboardingFormSelectorService;
use STOnboarding\Service\OnboardingIndustryLlmEventsSelector;

/**
 * @method AbstractController getController()
 */
final class Onboarding extends AbstractPlugin
{
    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function onboardingFormSaver(): OnboardingFormSaverService
    {
        return $this->getController()->getServiceManager()->get(OnboardingFormSaverService::class);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function onboardingFormSelector(): OnboardingFormSelectorService
    {
        return $this->getController()->getServiceManager()->get(OnboardingFormSelectorService::class);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function onboardingIndustryLlmEventsSelector(): OnboardingIndustryLlmEventsSelector
    {
        return $this->getController()->getServiceManager()->get(OnboardingIndustryLlmEventsSelector::class);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function onboardingNotificator(): NotificatorService
    {
        return $this->getController()->getServiceManager()->get(NotificatorService::class);
    }
}
