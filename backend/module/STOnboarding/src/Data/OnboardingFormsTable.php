<?php

declare(strict_types=1);

namespace STOnboarding\Data;

use STApi\Entity\Exception\NotFoundApiException;
use STLib\Db\HydratedAbstractTable;
use STLib\Mvc\Data\CollectionResultSet;
use STOnboarding\Entity\OnboardingForm;
use STOnboarding\Entity\OnboardingFormCollection;

class OnboardingFormsTable extends HydratedAbstractTable
{
    public static string $table = 'onboarding_forms';
    public static string $entityName = OnboardingForm::class;
    public static string $collectionName = OnboardingFormCollection::class;

    public static function getTableName(): string
    {
        return self::$table;
    }

    public static function getEntityName(): string
    {
        return self::$entityName;
    }

    public static function getCollectionName(): string
    {
        return self::$collectionName;
    }

    /**
     * @throws NotFoundApiException
     */
    public function getFormByExternalId(string $externalId): OnboardingForm
    {
        $select = $this->tableGateway->getSql()->select();
        $select->where(['external_id' => $externalId]);

        $result = $this->tableGateway->selectWith($select);
        if ($result->count() === 0) {
            throw new NotFoundApiException('Onboarding form not found');
        }

        return $result->current();
    }

    public function getForms(): OnboardingFormCollection
    {
        $select = $this->tableGateway->getSql()->select()->order('onboarding_form_id DESC');

        /**
         * @var CollectionResultSet $result
         */
        $result = $this->tableGateway->selectWith($select);

        return $result->getCollection();
    }

    public function save(OnboardingForm $onboardingForm): void
    {
        $data = [
            'external_id' => $onboardingForm->getExternalId(),
            'front_form_link' => $onboardingForm->getFrontFormLink(),
            'invite_link' => $onboardingForm->getInviteLink(),
            'company_name' => $onboardingForm->getCompanyName(),
            'company_logo' => $onboardingForm->getCompanyLogo(),
            'users' => json_encode($onboardingForm->getUsers()),
            'industries' => json_encode($onboardingForm->getIndustries()),
            'calls_settings' => json_encode($onboardingForm->getCallsSettings()),
            'is_submitted' => $onboardingForm->getIsSubmitted(),
            'company_id' => $onboardingForm->getCompanyId(),
        ];

        if (!is_null($onboardingForm->getId())) {
            $this->tableGateway->update($data, [
                'onboarding_form_id' => $onboardingForm->getId(),
            ]);
        } else {
            $this->tableGateway->insert($data);
            $onboardingForm->setId((int) $this->tableGateway->lastInsertValue);
        }
    }
}
