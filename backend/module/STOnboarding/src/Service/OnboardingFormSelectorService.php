<?php

declare(strict_types=1);

namespace STOnboarding\Service;

use STApi\Entity\Exception\NotFoundApiException;
use STOnboarding\Data\OnboardingFormsTable;
use STOnboarding\Entity\OnboardingForm;
use STOnboarding\Entity\OnboardingFormCollection;

class OnboardingFormSelectorService
{
    public function __construct(private readonly OnboardingFormsTable $onboardingFormsTable)
    {
    }

    /**
     * @param string $externalId
     * @return OnboardingForm
     * @throws NotFoundApiException
     */
    public function getFormByExternalId(string $externalId): OnboardingForm
    {
        return $this->onboardingFormsTable->getFormByExternalId($externalId);
    }

    public function getForms(): OnboardingFormCollection
    {
        return $this->onboardingFormsTable->getForms();
    }
}
