<?php

declare(strict_types=1);

namespace STOnboarding\Service;

use STOnboarding\Entity\OnboardingForm;
use STOnboarding\Service\Interfaces\ConfigurationInterface;
use STOnboarding\Service\Notification\Interfaces\SenderInterface;
use STOnboarding\Service\Notification\OnboardingFormChecker;
use STRoboTruck\Service\DataCollection\DataCollector;
use Throwable;

class NotificatorService
{
    public function __construct(
        private readonly OnboardingFormChecker $onboardingFormChecker,
        private readonly ConfigurationInterface $configuration,
        private readonly SenderInterface $sender,
        private readonly DataCollector $dataCollector,
    ) {
    }

    public function notifyAboutFormSubmission(OnboardingForm $onboardingForm): void
    {
        try {
            $params = [
                'Company name' => $onboardingForm->getCompanyName(),
                'Form ID' => $onboardingForm->getExternalId(),
                'Link' => $onboardingForm->getFrontFormLink(),
            ];

            if ($this->onboardingFormChecker->isFormCompleted($onboardingForm)) {
                $header = '*Completed* onboarding form has been sent.';
            } else {
                $header = '*Incomplete* onboarding form has been sent.';
            }

            $unfilledFields = $this->onboardingFormChecker->getUnfilledFields($onboardingForm);
            if (!empty($unfilledFields)) {
                $params['Unfilled fields'] = $unfilledFields;
            }

            $this->sender->send(
                $header,
                $params,
                $this->configuration->get('slack')['channels']['onboarding']
            );
        } catch (Throwable $e) {
            $this->dataCollector->collect(
                DataCollector::EVENT_ONBOARDING_NOTIFICATION,
                'Onboarding notification failed.',
                [
                    'error message' => $e->getMessage(),
                    'code' => $e->getCode(),
                    'stack_trace' => $e->getTrace(),
                ]
            );
        }
    }

    public function notifyAboutNeedHelp(OnboardingForm $onboardingForm): void
    {
        $params = [
            'Company name' => $onboardingForm->getCompanyName(),
            'Form ID' => $onboardingForm->getExternalId(),
            'Link' => $onboardingForm->getFrontFormLink(),
        ];

        $header = 'The user requested *help* with the onboarding form.';

        $this->sender->send(
            $header,
            $params,
            $this->configuration->get('slack')['channels']['onboarding']
        );
    }
}
