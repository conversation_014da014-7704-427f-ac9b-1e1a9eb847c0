<?php

declare(strict_types=1);

namespace STOnboarding\Service;

use STApi\Entity\Exception\NotFoundApiException;
use STLib\IdGenerator\IdGeneratorService;
use STOnboarding\Data\OnboardingFormsTable;
use STOnboarding\Entity\OnboardingForm;
use STOnboarding\Service\Interfaces\CompanyCreatorInterface;
use STOnboarding\Service\Notification\OnboardingFormChecker;

class OnboardingFormSaverService
{
    public const string TYPE_EXISTING = 'existing';
    public const string TYPE_NEW = 'new';

    public function __construct(
        private readonly OnboardingFormSelectorService $onboardingFormSelector,
        private readonly OnboardingFormsTable $onboardingFormsTable,
        private readonly IdGeneratorService $idGenerator,
        private readonly NotificatorService $notificator,
        private readonly OnboardingFormChecker $onboardingFormChecker,
        private readonly CompanyCreatorInterface $companyCreator,
    ) {
    }

    public function createForm(?string $companyName, string $domain, string $frontFormPath, string $invitePath): OnboardingForm
    {
        $externalId = 'ID_' . $this->idGenerator->generatePseudoUniqueId();
        $onboardingForm = new OnboardingForm();
        $onboardingForm->setId(null);
        $onboardingForm->setExternalId($externalId);
        $onboardingForm->setFrontFormLink('https://' . $domain . $frontFormPath . $externalId);
        $onboardingForm->setInviteLink('https://' . $domain . $invitePath);
        $onboardingForm->setCompanyName($companyName);

        $this->onboardingFormsTable->save($onboardingForm);

        return $onboardingForm;
    }

    /**
     * @throws NotFoundApiException
     */
    public function updateCompanySettings(string $externalId, string $companyName, ?string $companyLogo): void
    {
        $onboardingForm = $this->onboardingFormSelector->getFormByExternalId($externalId);
        $onboardingForm->setCompanyName($companyName);
        if (!empty($companyLogo)) {
            $onboardingForm->setCompanyLogo($companyLogo);
        }

        $this->onboardingFormsTable->save($onboardingForm);
    }

    /**
     * @throws NotFoundApiException
     */
    public function updateUsers(string $externalId, array $users): void
    {
        $onboardingForm = $this->onboardingFormSelector->getFormByExternalId($externalId);
        $onboardingForm->setUsers($users);

        $this->onboardingFormsTable->save($onboardingForm);
    }

    /**
     * @throws NotFoundApiException
     */
    public function updateActiveIndustry(string $externalId, array $data): void
    {
        $onboardingForm = $this->onboardingFormSelector->getFormByExternalId($externalId);
        $existentIndustries = $onboardingForm->getIndustries();

        $activeIndustryData = $data['active_industry'];

        if ($activeIndustryData['type'] === self::TYPE_EXISTING) {
            $key = $activeIndustryData['id'];
        } else {
            $key = $activeIndustryData['name'];
        }

        foreach ($existentIndustries as &$industry) {
            $industry['is_active'] = false;
        }

        $existentIndustries[$key] = array_merge(
            $activeIndustryData,
            [
                'is_active' => true,
                'events' => $data['events']
            ]
        );

        $onboardingForm->setIndustries($existentIndustries);

        $this->onboardingFormsTable->save($onboardingForm);
    }

    /**
     * @throws NotFoundApiException
     */
    public function updateCallsSettings(string $externalId, array $callsSettings): void
    {
        $onboardingForm = $this->onboardingFormSelector->getFormByExternalId($externalId);
        $onboardingForm->setCallsSettings($callsSettings);

        $this->onboardingFormsTable->save($onboardingForm);
    }

    /**
     * @throws NotFoundApiException
     */
    public function submitForm(string $externalId): void
    {
        $onboardingForm = $this->onboardingFormSelector->getFormByExternalId($externalId);

        $this->notificator->notifyAboutFormSubmission($onboardingForm);

        if ($this->onboardingFormChecker->isFormCompleted($onboardingForm)) {
            $company = $this->companyCreator->createFromOnboardingForm($onboardingForm);

            $onboardingForm->setIsSubmitted(true);
            $onboardingForm->setCompanyId($company->getId());
            $this->onboardingFormsTable->save($onboardingForm);
        }
    }
}
