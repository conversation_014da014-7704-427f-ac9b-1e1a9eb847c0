<?php

declare(strict_types=1);

namespace STOnboarding\Service;

use STIndustry\Entity\LlmEvent\LlmEventCollection;
use STOnboarding\Service\Interfaces\ConfigurationInterface;
use STOnboarding\Service\Interfaces\EventsAlgoEventsSelectorInterface;
use STOnboarding\Service\Interfaces\IndustryLlmEventsSelectorInterface;

class OnboardingIndustryLlmEventsSelector
{
    public function __construct(
        private readonly IndustryLlmEventsSelectorInterface $industryLlmEventsSelector,
        private readonly EventsAlgoEventsSelectorInterface $eventsAlgoEventsSelector,
        private readonly ConfigurationInterface $configuration
    ) {
    }

    public function getPopularLlmEvents(int $industryId): LlmEventCollection
    {
        $limit = (int) $this->configuration->get('onboarding')['popular-events-limit'];

        $indexedByNameLlmEvents = $this->getIndexedByNameLlmEvents($industryId);

        $mostPopularLlmEvents = $this->getMostPopularLlmEvents($indexedByNameLlmEvents, $limit);

        $restLlmEvents = $this->getRestLlmEvents($indexedByNameLlmEvents, $mostPopularLlmEvents);

        if ($mostPopularLlmEvents->count() === $limit || $restLlmEvents->isEmpty()) {
            return $mostPopularLlmEvents;
        }

        foreach ($restLlmEvents as $llmEvent) {
            $mostPopularLlmEvents->add($llmEvent);

            if ($mostPopularLlmEvents->count() === $limit) {
                break;
            }
        }

        return $mostPopularLlmEvents;
    }

    private function getIndexedByNameLlmEvents(int $industryId): LlmEventCollection
    {
        $llmEvents = $this->industryLlmEventsSelector->getLlmEvents($industryId);

        $indexedByNameLlmEvents = new LlmEventCollection();
        foreach ($llmEvents as $llmEvent) {
            $indexedByNameLlmEvents->add($llmEvent, trim(strtolower($llmEvent->getName())));
        }

        return $indexedByNameLlmEvents;
    }

    private function getMostPopularLlmEvents(LlmEventCollection $indexedByNameLlmEvents, int $limit): LlmEventCollection
    {
        $llmEventNames = $indexedByNameLlmEvents->keys();

        $mostPopularLlmEventNames = $this->eventsAlgoEventsSelector->filterMostPopularAlgoEvents(
            $llmEventNames,
            $limit
        );

        $mostPopularLlmEvents = new LlmEventCollection();
        foreach ($mostPopularLlmEventNames as $llmEventName) {
            $mostPopularLlmEvents->add($indexedByNameLlmEvents->offsetGet(trim(strtolower($llmEventName))));
        }

        return $mostPopularLlmEvents;
    }

    private function getRestLlmEvents(
        LlmEventCollection $indexedByNameLlmEvents,
        LlmEventCollection $mostPopularLlmEvents
    ): LlmEventCollection {
        $restLlmEvents = clone $indexedByNameLlmEvents;

        foreach ($mostPopularLlmEvents as $llmEvent) {
            $restLlmEvents->removeByKey($llmEvent->getName());
        }

        return $restLlmEvents;
    }
}
