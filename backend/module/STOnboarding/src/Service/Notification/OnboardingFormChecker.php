<?php

declare(strict_types=1);

namespace STOnboarding\Service\Notification;

use STOnboarding\Entity\OnboardingForm;

class OnboardingFormChecker
{
    private const string FIELD_NAME_INDUSTRIES = 'industries';
    private const string FIELD_NAME_EVENTS = 'events';

    public function isFormCompleted(OnboardingForm $onboardingForm): bool
    {
        $unfilledFields = $this->getUnfilledFields($onboardingForm);
        // Remove non required fields
        unset($unfilledFields['company_logo'], $unfilledFields['calls_settings'], $unfilledFields['users']);

        return empty($unfilledFields);
    }

    public function getUnfilledFields(OnboardingForm $onboardingForm): array
    {
        $fields = $onboardingForm->toArray();
        unset($fields['is_submitted'], $fields['company_id']);

        $unfilledFields = [];
        foreach ($fields as $name => $value) {
            if (empty($value)) {
                $unfilledFields[$name] = $name;
                continue;
            }
            if ($name === self::FIELD_NAME_INDUSTRIES) {
                $events = $onboardingForm->getActiveIndustryEvents();
                if (empty($events)) {
                    $fieldName = self::FIELD_NAME_INDUSTRIES . '.' . self::FIELD_NAME_EVENTS;
                    $unfilledFields[$fieldName] = $fieldName;
                }
            }
        }

        return $unfilledFields;
    }
}
