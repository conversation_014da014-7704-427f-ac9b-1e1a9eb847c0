<?php

declare(strict_types=1);

namespace STOnboarding\Entity;

use STLib\Db\Entity;

class OnboardingForm extends Entity
{
    public function exchangeArray($data): void
    {
        $data['users'] = $this->exchangeJson($data, 'users');
        $data['industries'] = $this->exchangeJson($data, 'industries');
        $data['calls_settings'] = $this->exchangeJson($data, 'calls_settings');

        parent::exchangeArray($data);
    }

    protected ?int $id;
    protected string $externalId;

    protected string $frontFormLink;
    protected string $inviteLink;
    protected ?string $companyName;
    protected ?string $companyLogo = null;
    protected array $users = [];
    protected array $industries = [];
    protected array $callsSettings = [];
    protected bool $isSubmitted = false;
    protected ?int $companyId = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): void
    {
        $this->id = $id;
    }

    public function getExternalId(): string
    {
        return $this->externalId;
    }

    public function setExternalId(string $externalId): void
    {
        $this->externalId = $externalId;
    }

    public function getCompanyName(): ?string
    {
        return $this->companyName;
    }

    public function setCompanyName(?string $companyName): void
    {
        $this->companyName = $companyName;
    }

    public function getFrontFormLink(): string
    {
        return $this->frontFormLink;
    }

    public function setFrontFormLink(string $frontFormLink): void
    {
        $this->frontFormLink = $frontFormLink;
    }

    public function getInviteLink(): string
    {
        return $this->inviteLink;
    }

    public function setInviteLink(string $inviteLink): void
    {
        $this->inviteLink = $inviteLink;
    }

    public function getCompanyLogo(): ?string
    {
        return $this->companyLogo;
    }

    public function setCompanyLogo(?string $companyLogo): void
    {
        $this->companyLogo = $companyLogo;
    }

    public function getUsers(): array
    {
        return $this->users;
    }

    public function setUsers(array $users): void
    {
        $this->users = $users;
    }

    public function getIndustries(): array
    {
        return $this->industries;
    }

    public function setIndustries(array $industries): void
    {
        $this->industries = $industries;
    }

    public function getCallsSettings(): array
    {
        return $this->callsSettings;
    }

    public function setCallsSettings(array $callsSettings): void
    {
        $this->callsSettings = $callsSettings;
    }

    public function getIsSubmitted(): bool
    {
        return $this->isSubmitted;
    }

    public function setIsSubmitted(bool $isSubmitted): void
    {
        $this->isSubmitted = $isSubmitted;
    }

    public function getCompanyId(): ?int
    {
        return $this->companyId;
    }

    public function setCompanyId(?int $companyId): void
    {
        $this->companyId = $companyId;
    }

    public function getActiveIndustryEvents(): array
    {
        foreach ($this->getIndustries() as $industry) {
            if ($industry['is_active'] === true) {
                return $industry['events'];
            }
        }

        return [];
    }

    public function toArray(): array
    {
        return [
            'id' => $this->externalId,
            'front_form_link' => $this->getFrontFormLink(),
            'invite_link' => $this->getInviteLink(),
            'company_name' => $this->getCompanyName(),
            'company_logo' => $this->getCompanyLogo(),
            'users' => $this->getUsers(),
            'industries' => $this->getIndustries(),
            'calls_settings' => $this->getCallsSettings(),
            'is_submitted' => $this->getIsSubmitted(),
            'company_id' => $this->getCompanyId(),
        ];
    }
}
