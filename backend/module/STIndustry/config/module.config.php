<?php

declare(strict_types=1);

namespace STIndustry;

use STAlgo\Service\Interfaces\IndustrySelectorInterface as AlgoIndustrySelectorInterface;
use STCompany\Service\Interfaces\IndustrySelectorInterface as CompanyIndustrySelectorInterface;
use STIndustry\Controller\Plugin\Industry;
use STIndustry\Data\AlgoApisIndustriesTable;
use STIndustry\Data\IndustriesTable;
use STIndustry\ServiceProviding\IndustryServiceProvider;
use STLib\Mvc\Data\HydratedTableFactory;
use STLib\Mvc\Data\TableFactory;
use STOnboarding\Service\Interfaces\IndustryLlmEventsSelectorInterface as OnboardingIndustryLlmEventsSelectorInterface;
use STOnboarding\Service\Interfaces\IndustrySelectorInterface as OnboardingIndustrySelectorInterface;

return [
    'controller_plugins' => [
        'invokables' => [
            'industry' => Industry::class,
        ],
    ],
    'service_manager' => [
        'factories' => [
            IndustriesTable::class => HydratedTableFactory::class,
            AlgoApisIndustriesTable::class => TableFactory::class,
        ],
        'aliases' => [
            CompanyIndustrySelectorInterface::class => IndustryServiceProvider::class,
            AlgoIndustrySelectorInterface::class => IndustryServiceProvider::class,
            OnboardingIndustryLlmEventsSelectorInterface::class => IndustryServiceProvider::class,
            OnboardingIndustrySelectorInterface::class => IndustryServiceProvider::class,
        ],
    ],
];
