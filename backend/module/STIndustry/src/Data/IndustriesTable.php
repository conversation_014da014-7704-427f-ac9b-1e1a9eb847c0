<?php

declare(strict_types=1);

namespace STIndustry\Data;

use STApi\Entity\Exception\NotFoundApiException;
use STIndustry\Entity\Industry;
use STIndustry\Entity\IndustryCollection;
use STLib\Db\HydratedAbstractTable;
use STLib\Mvc\Data\CollectionResultSet;

class IndustriesTable extends HydratedAbstractTable
{
    public const int QC_INDUSTRY = 1;
    public const int SALES_INDUSTRY = 2;
    public const int MARKETING_INDUSTRY = 3;
    public const int RETENTION_INDUSTRY = 4;
    public const int EMOTIONS_INDUSTRY = 5;
    public const int KYC_INDUSTRY = 6;
    public const int VIOLATION_INDUSTRY = 7;
    public const int VISA_INDUSTRY = 8;
    public const int COMMON_INDUSTRY = 9;
    public const int TRAVEL_INDUSTRY = 10;
    public const int FX_RISK_INDUSTRY = 11;
    public const int RISK_INDUSTRY = 12;
    public const int FX_SALES_INDUSTRY = 13;
    public const int DEBT_CREDITORS = 14;
    public const int RESTAURANTS = 15;
    public const int HOSPITALITY = 16;
    public const int GAMING = 17;
    public const int INVESTING_FIRM = 18;
    public const int INSURANCE = 19;
    public const int BANKS = 20;
    public const int GOVERNMENT = 21;
    public const int TELECOMMUNICATIONS = 22;
    public const int HEALTHCARE = 23;
    public const int RETAIL = 24;
    public const int FINANCIAL_SERVICES = 25;
    public const int TECHNOLOGY = 26;
    public const int UTILITIES = 27;
    public const int AUTOMOTIVE = 28;
    public const int EDUCATION = 29;
    public const int FOOD_SERVICE = 30;
    public const int ENERGY = 31;
    public const int NON_PROFIT = 32;
    public const int LLM = 33;
    public const int LEAD_GENERATION = 34;

    public const string INDUSTRY_NAME_EMOTIONS = 'Emotions';
    public const string INDUSTRY_NAME_LLM = 'LLM';

    public static string $table = 'industries';
    public static string $entityName = Industry::class;
    public static string $collectionName = IndustryCollection::class;

    public static function getTableName(): string
    {
        return self::$table;
    }

    public static function getEntityName(): string
    {
        return self::$entityName;
    }

    public static function getCollectionName(): string
    {
        return self::$collectionName;
    }

    public function getIndustries(bool $withLlmEvents = false): IndustryCollection
    {
        $select = $this->tableGateway->getSql()->select();

        if ($withLlmEvents) {
            $select
                ->join(
                    [
                        'ile' => 'industries_llm_events',
                    ],
                    'ile.industry_id = ' . self::$table . '.industry_id',
                    []
                )
                ->group([self::$table . '.industry_id',  self::$table . '.name'])
                ->order('name');
        } else {
            $select->order('industry_id');
        }

        /**
         * @var CollectionResultSet $result
         */
        $result = $this->tableGateway->selectWith($select);

        return $result->getCollection();
    }

    /**
     * @throws NotFoundApiException
     */
    public function getIndustry(int $industryId): Industry
    {
        $select = $this->tableGateway->getSql()->select();
        $select->where(['industry_id' => $industryId]);

        $result = $this->tableGateway->selectWith($select);
        if ($result->count() === 0) {
            throw new NotFoundApiException('Industry not found');
        }

        return $result->current();
    }
}
