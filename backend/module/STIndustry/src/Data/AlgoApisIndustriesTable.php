<?php

declare(strict_types=1);

namespace STIndustry\Data;

use Laminas\Db\ResultSet\ResultSetInterface;
use STLib\Db\AbstractTable;

class AlgoApisIndustriesTable extends AbstractTable
{
    public static string $table = 'algo_apis_industries';

    public function getAlgoApis(int $industryId): ResultSetInterface
    {
        $select = $this->tableGateway->getSql()
            ->select()
            ->columns([])
            ->join(
                ['aa' => 'algo_apis'],
                'aa.algo_api_id = ' . self::$table .  '.algo_api_id',
            )
            ->where([
                self::$table . '.industry_id' => $industryId,
                'aa.is_deleted' => 0
            ]);

        return $this->tableGateway->selectWith($select);
    }
}
