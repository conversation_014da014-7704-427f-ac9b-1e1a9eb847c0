<?php

declare(strict_types=1);

namespace STIndustry\Data;

use Laminas\Db\Sql\Select;
use STApi\Entity\Exception\NotFoundApiException;
use STIndustry\Entity\LlmEvent\LlmEvent;
use STIndustry\Entity\LlmEvent\LlmEventCollection;
use STLib\Db\HydratedAbstractTable;
use STLib\Mvc\Data\CollectionResultSet;

class IndustriesLlmEventsTable extends HydratedAbstractTable
{
    public static string $table = 'industries_llm_events';
    public static string $entityName = LlmEvent::class;
    public static string $collectionName = LlmEventCollection::class;

    public static function getTableName(): string
    {
        return self::$table;
    }

    public static function getEntityName(): string
    {
        return self::$entityName;
    }

    public static function getCollectionName(): string
    {
        return self::$collectionName;
    }

    public function getLlmEvents(int|array $industryIds): LlmEventCollection
    {
        $select = $this->tableGateway->getSql()
            ->select()
            ->columns(['industry_id']);
        $this->joinLlmEvents($select);
        $select
            ->where([
                self::$table . '.industry_id' => $industryIds,
            ]);

        /**
         * @var CollectionResultSet $result
         */
        $result = $this->tableGateway->selectWith($select);

        return $result->getCollection();
    }

    /**
     * @throws NotFoundApiException
     */
    public function getLlmEvent(int $llmEventId, int $industryId): LlmEvent
    {
        $select = $this->tableGateway->getSql()->select()
            ->columns(['industry_id']);
        $this->joinLlmEvents($select);
        $select
            ->where([
                self::$table . '.industry_id' => $industryId,
                'le.llm_event_id' => $llmEventId
            ]);

        $result = $this->tableGateway->selectWith($select);
        if ($result->count() === 0) {
            throw new NotFoundApiException('Industry llm event not found');
        }

        return $result->current();
    }

    private function joinLlmEvents(Select $select): void
    {
        $select->join(
            [
                'le' => 'llm_events',
            ],
            'le.llm_event_id = ' . self::$table . '.llm_event_id'
        );
    }

    public function saveEvent(LlmEvent $llmEvent): void
    {
        $data = [
            'llm_event_id' => $llmEvent->getId(),
            'industry_id' => $llmEvent->getIndustryId(),
        ];

        $this->tableGateway->insert($data);
    }

    public function deleteEvent(int $llmEventId, int $industryId): void
    {
        $this->tableGateway->delete([
            'llm_event_id' => $llmEventId,
            'industry_id' => $industryId,
        ]);
    }
}
