<?php

declare(strict_types=1);

namespace STIndustry\ServiceProviding;

use STCompany\Service\Interfaces\IndustrySelectorInterface as CompanyIndustrySelectorInterface;
use STAlgo\Service\Interfaces\IndustrySelectorInterface as AlgoIndustrySelectorInterface;
use STIndustry\Entity\Industry;
use STIndustry\Entity\LlmEvent\LlmEventCollection;
use STIndustry\Service\IndustrySelectorService;
use STIndustry\Service\LlmEvent\LlmEventSelectorService;
use STOnboarding\Service\Interfaces\IndustryLlmEventsSelectorInterface as OnboardingIndustryLlmEventsSelectorInterface;
use STOnboarding\Service\Interfaces\IndustrySelectorInterface as OnboardingIndustrySelectorInterface;

final readonly class IndustryServiceProvider implements
    CompanyIndustrySelectorInterface,
    AlgoIndustrySelectorInterface,
    OnboardingIndustryLlmEventsSelectorInterface,
    OnboardingIndustrySelectorInterface
{
    public function __construct(
        private IndustrySelectorService $industryProviderService,
        private LlmEventSelectorService $llmEventSelectorService,
    ) {
    }

    public function getIndustry(int $industryId): Industry
    {
        return $this->industryProviderService->getIndustry($industryId);
    }

    public function getLlmEvents(int $industryId): LlmEventCollection
    {
        return $this->llmEventSelectorService->getLlmEvents($industryId);
    }
}
