<?php

declare(strict_types=1);

namespace STIndustry\Entity;

use STLib\Db\Entity;

class Industry extends Entity
{
    protected ?int $id = null;
    protected string $name;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): void
    {
        $this->id = $id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name
        ];
    }
}
