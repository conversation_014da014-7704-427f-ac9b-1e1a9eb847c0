<?php

namespace STIndustry\Entity\LlmEvent;

use STLlmEvent\Entity\LlmEvent as BaseLlmEvent;

class LlmEvent extends BaseLlmEvent
{
    private int $industry_id;

    public function getIndustryId(): int
    {
        return $this->industry_id;
    }

    public function setIndustryId(int $industryId): void
    {
        $this->industry_id = $industryId;
    }

    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'industry_id' => $this->industry_id,
        ]);
    }
}
