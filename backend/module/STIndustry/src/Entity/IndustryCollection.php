<?php

declare(strict_types=1);

namespace STIndustry\Entity;

use RuntimeException;
use STLib\Expand\Collection;
use Traversable;

/**
 * @extends Traversable<array-key,Industry>
 */
class IndustryCollection extends Collection
{
    public function add(mixed $object, string|int|null $key = null): Collection
    {
        if (!($object instanceof Industry)) {
            throw new RuntimeException(
                sprintf(
                    'Industry must be an instance of "%s"',
                    $this->getEntityClass()
                )
            );
        }
        parent::add($object, $key ?? $object->getId());

        return $this;
    }

    /**
     * @param bool $asArray
     * @return array|Industry[]
     */
    public function toArray(bool $asArray = true): array
    {
        if (!$asArray) {
            return parent::toArray();
        }

        $result = [];
        foreach ($this as $industry) {
            $result[] = $industry->toArray();
        }
        return $result;
    }

    protected function getEntityClass(): string
    {
        return Industry::class;
    }
}
