<?php

declare(strict_types=1);

namespace STIndustry\Service;

use STApi\Entity\Exception\NotFoundApiException;
use STIndustry\Data\IndustriesTable;
use STIndustry\Entity\IndustryCollection;
use STIndustry\Entity\Industry;

final readonly class IndustrySelectorService
{
    public function __construct(private IndustriesTable $industriesTable)
    {
    }

    public function getIndustries(bool $withLlmEvents = false): IndustryCollection
    {
        return $this->industriesTable->getIndustries($withLlmEvents);
    }

    /**
     * @throws NotFoundApiException
     */
    public function getIndustry(int $industryId): Industry
    {
        return $this->industriesTable->getIndustry($industryId);
    }
}
