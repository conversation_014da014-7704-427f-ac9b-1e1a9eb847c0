<?php

declare(strict_types=1);

namespace STIndustry\Service\AlgoApi;

use ReflectionException;
use STAlgo\Entity\AlgoApi;
use STAlgo\Entity\AlgoApiCollection;
use STIndustry\Data\AlgoApisIndustriesTable;
use STLib\Mvc\Hydrator\Hydrator;

class AlgoApiSelectorService
{
    public function __construct(
        private readonly AlgoApisIndustriesTable $industriesAlgoApisTable,
        private Hydrator $hydrator
    ) {
    }

    /**
     * @throws ReflectionException
     */
    public function getAlgoApis(int $industryId): AlgoApiCollection
    {
        $algoApisResultSet = $this->industriesAlgoApisTable->getAlgoApis($industryId);

        $algoApiCollection = new AlgoApiCollection();
        foreach ($algoApisResultSet->toArray() as $algoApiData) {
            /**
             * @var AlgoApi $algoApi
             */
            $algoApi = $this->hydrator->hydrateClass($algoApiData, AlgoApi::class);
            $algoApiCollection->add($algoApi);
        }

        return $algoApiCollection;
    }
}
