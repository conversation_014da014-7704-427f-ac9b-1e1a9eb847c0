<?php

declare(strict_types=1);

namespace STIndustry\Service\LlmEvent;

use STApi\Entity\Exception\NotFoundApiException;
use STIndustry\Data\IndustriesLlmEventsTable;
use STIndustry\Entity\LlmEvent\LlmEvent;
use STIndustry\Entity\LlmEvent\LlmEventCollection;

class LlmEventSelectorService
{
    public function __construct(private IndustriesLlmEventsTable $industriesLlmEventsTable)
    {
    }

    public function getLlmEvents(int|array $industryIds): LlmEventCollection
    {
        return $this->industriesLlmEventsTable->getLlmEvents($industryIds);
    }

    /**
     * @throws NotFoundApiException
     */
    public function getLlmEvent(int $llmEventId, int $industryId): LlmEvent
    {
        return $this->industriesLlmEventsTable->getLlmEvent($llmEventId, $industryId);
    }
}
