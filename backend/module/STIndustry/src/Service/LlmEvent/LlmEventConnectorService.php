<?php

declare(strict_types=1);

namespace STIndustry\Service\LlmEvent;

use STApi\Entity\Exception\NotFoundApiException;
use STIndustry\Data\IndustriesLlmEventsTable;
use STIndustry\Entity\LlmEvent\LlmEvent;

class LlmEventConnectorService
{
    public function __construct(private readonly IndustriesLlmEventsTable $llmEventsTable)
    {
    }

    /**
     * @throws NotFoundApiException
     */
    public function connect(int $llmEventId, int $industryId): LlmEvent
    {
        $llmEvent = new LlmEvent();
        $llmEvent->setId($llmEventId);
        $llmEvent->setIndustryId($industryId);

        $this->llmEventsTable->saveEvent($llmEvent);

        return $this->llmEventsTable->getLlmEvent($llmEvent->getId(), $llmEvent->getIndustryId());
    }

    public function disconnect(int $llmEventId, int $industryId): void
    {
        $this->llmEventsTable->deleteEvent($llmEventId, $industryId);
    }
}
