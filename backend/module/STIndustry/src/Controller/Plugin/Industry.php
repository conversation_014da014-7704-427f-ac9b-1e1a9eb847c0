<?php

declare(strict_types=1);

namespace STIndustry\Controller\Plugin;

use Laminas\Mvc\Controller\Plugin\AbstractPlugin;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use STIndustry\Service\AlgoApi\AlgoApiSelectorService;
use STIndustry\Service\IndustrySelectorService;
use STIndustry\Service\LlmEvent\LlmEventConnectorService;
use STIndustry\Service\LlmEvent\LlmEventSelectorService;
use STLib\Mvc\Controller\AbstractController;

/**
 * @method AbstractController getController()
 */
final class Industry extends AbstractPlugin
{
    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function industrySelector(): IndustrySelectorService
    {
        return $this->getController()->getServiceManager()->get(IndustrySelectorService::class);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function algoApiSelector(): AlgoApiSelectorService
    {
        return $this->getController()->getServiceManager()->get(AlgoApiSelectorService::class);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function llmEventSelector(): LlmEventSelectorService
    {
        return $this->getController()->getServiceManager()->get(LlmEventSelectorService::class);
    }

    public function llmEventConnector(): LlmEventConnectorService
    {
        return $this->getController()->getServiceManager()->get(LlmEventConnectorService::class);
    }
}
