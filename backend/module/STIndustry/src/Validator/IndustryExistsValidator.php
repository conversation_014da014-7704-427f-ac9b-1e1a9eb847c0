<?php

namespace STIndustry\Validator;

use STApi\Entity\Exception\NotFoundApiException;
use STIndustry\Data\IndustriesTable;
use STLib\Validator\Validator;

class IndustryExistsValidator extends Validator
{
    public function __construct(private readonly IndustriesTable $industriesTable)
    {
    }

    protected const string ERROR_INDUSTRY_DOES_NOT_EXIST = 'The industry does not exists.';

    public function run(): void
    {
        /** @var array $input */
        $industryId = $this->getInstance();

        try {
            $this->industriesTable->getIndustry($industryId);
        } catch (NotFoundApiException) {
            $this->addError('industry_id', self::ERROR_INDUSTRY_DOES_NOT_EXIST);

            return;
        }
    }
}
