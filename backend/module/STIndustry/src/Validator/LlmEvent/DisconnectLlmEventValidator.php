<?php

declare(strict_types=1);

namespace STIndustry\Validator\LlmEvent;

use STA<PERSON>\Entity\Exception\NotFoundApiException;
use STIndustry\Data\IndustriesLlmEventsTable;
use STIndustry\Service\LlmEvent\Interfaces\LlmEventSelectorInterface;
use STIndustry\Validator\IndustryExistsValidator;
use STLib\Validator\Validator;

class DisconnectLlmEventValidator extends Validator
{
    protected const string ERROR_EVENT_DOES_NOT_EXIST = 'The llm event does not exists.';
    protected const string ERROR_EVENT_WAS_NOT_CONNECTED = 'The event was not connected.';

    public function __construct(
        private readonly IndustryExistsValidator $industryExistsValidator,
        private readonly LlmEventSelectorInterface $llmEventSelector,
        private readonly IndustriesLlmEventsTable $industriesLlmEventsTable,
    ) {
    }

    public function run(): void
    {
        /** @var array $input */
        $input = $this->getInstance();

        ['llm_event_id' => $llmEventId, 'industry_id' => $industryId] = $input;

        $this->industryExistsValidator->setInstance($industryId);
        $this->industryExistsValidator->validate();
        if ($this->industryExistsValidator->hasError()) {
            $this->addError('id', $this->industryExistsValidator->getErrors()[0]);

            return;
        }

        try {
            $this->llmEventSelector->getLlmEvent($llmEventId);
        } catch (NotFoundApiException) {
            $this->addError('id', self::ERROR_EVENT_DOES_NOT_EXIST);

            return;
        }

        try {
            $this->industriesLlmEventsTable->getLlmEvent($llmEventId, $industryId);
        } catch (NotFoundApiException) {
            $this->addError('id', self::ERROR_EVENT_WAS_NOT_CONNECTED);
        }
    }
}
