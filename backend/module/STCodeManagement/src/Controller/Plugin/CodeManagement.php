<?php

declare(strict_types=1);

namespace STCodeManagement\Controller\Plugin;

use Laminas\Mvc\Controller\Plugin\AbstractPlugin;

class CodeManagement extends AbstractPlugin
{
    /**
     *
     * @return \STCodeManagement\Service\AirbrakeService
     */
    public function airbrake(): \STCodeManagement\Service\AirbrakeService
    {
        return $this->getController()->getServiceManager()->get(\STCodeManagement\Service\AirbrakeService::class);
    }
}
