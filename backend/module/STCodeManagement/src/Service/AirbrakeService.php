<?php

declare(strict_types=1);

namespace STCodeManagement\Service;

class AirbrakeService
{
    protected const DEFAULT_ENVIRONMENT = 'unknown';

    /**
     *
     * @var int
     */
    protected int $id = 0;

    /**
     *
     * @var string
     */
    protected string $key = '';

    /**
     *
     * @var string
     */
    protected string $environment;

    /**
     *
     * @var bool
     */
    protected bool $init = false;

    /**
     *
     * @var bool
     */
    protected bool $enable = false;

    /**
     *
     * @param array $config
     */
    public function __construct(array $config = [])
    {
        if (!isset($config['settings']['key'])) {
            throw new \OutOfBoundsException('Param "key" must be provided, check config files');
        }
        if (!isset($config['settings']['id'])) {
            throw new \OutOfBoundsException('Param "id" must be provided, check config files');
        }
        $this->key = $config['settings']['key'];
        $this->id = $config['settings']['id'];
        $this->environment = $config['settings']['environment'] ?? static::DEFAULT_ENVIRONMENT;
        $this->enable = $config['enable'] ?? false;
    }

    /**
     *
     * @param \Throwable $e
     * @return void
     */
    public function register(\Throwable $e): void
    {
        if (!$this->enable) {
            return;
        }
        $this->init();
        \Airbrake\Instance::notify($e);
    }

    /**
     *
     * @return bool
     */
    protected function init(): bool
    {
        if (!$this->init) {
            $notifier = new \Airbrake\Notifier([
                'projectId' => $this->id,
                'projectKey' => $this->key,
                'environment' => $this->environment,
            ]);

            \Airbrake\Instance::set($notifier);

            $handler = new \Airbrake\ErrorHandler($notifier);
            $handler->register();

            $this->init = true;
        }
        return $this->init;
    }
}
