<?php

declare(strict_types=1);

namespace STCodeManagement\Service;

class AirbrakeServiceFactory implements \Laminas\ServiceManager\Factory\FactoryInterface
{
    /**
     *
     * @param \Interop\Container\ContainerInterface $container
     * @param string $requestedName
     * @param array $options
     * @return \STCodeManagement\Service\AirbrakeService
     */
    public function createService(\Interop\Container\ContainerInterface $container, $requestedName, array $options = null): AirbrakeService
    {
        return $this($container, $requestedName ?: PatternPluginManager::class, $this->creationOptions);
    }

    /**
     *
     * @param \Interop\Container\ContainerInterface $container
     * @param string $requestedName
     * @param array $options
     * @return \STCodeManagement\Service\AirbrakeService
     */
    public function __invoke(\Interop\Container\ContainerInterface $container, $requestedName, array $options = null): AirbrakeService
    {
        $config = $container->get('config');
        $airbrakeConfig = $config['airbrake'] ?? [];
        return new AirbrakeService($airbrakeConfig);
    }
}
