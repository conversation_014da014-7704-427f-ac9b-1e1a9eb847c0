<?php

declare(strict_types=1);

namespace STCodeManagement;

class Module extends \STLib\ModuleManager\MultiConfigModule
{
    /**
     *
     * @param \Laminas\Mvc\MvcEvent $mvcEvent
     * @return void
     */
    public function onBootstrap(\Laminas\Mvc\MvcEvent $mvcEvent): void
    {
        $eventManager = $mvcEvent->getApplication()->getEventManager();
        $eventManager->attach(\Laminas\Mvc\MvcEvent::EVENT_DISPATCH_ERROR, function (\Laminas\Mvc\MvcEvent $event) {
            $serviceManager = $event->getApplication()->getServiceManager();
            $exception = $event->getParam('exception');
            if ($exception instanceof \Throwable) {
                $serviceManager->get(Service\AirbrakeService::class)->register($exception);
            }
        });
    }
}
