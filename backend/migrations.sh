#!/bin/bash

dir="$( cd "$( dirname "${BASH_SOURCE[0]}" )" >/dev/null 2>&1 && pwd )"

if [ ! -n "$2" ]
then
    "$dir"/vendor/bin/doctrine-migrations migrations:"$1" --configuration "$dir"/config/migration/doctrine/configuration.php --db-configuration "$dir"/config/migration/doctrine/db-configuration.php --no-interaction
else
    "$dir"/vendor/bin/doctrine-migrations migrations:"$1" "$2" --configuration "$dir"/config/migration/doctrine/configuration.php --db-configuration "$dir"/config/migration/doctrine/db-configuration.php --no-interaction
fi
