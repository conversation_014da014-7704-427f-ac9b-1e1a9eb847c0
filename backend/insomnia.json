{"_type": "export", "__export_format": 4, "__export_date": "2022-09-20T18:21:18.528Z", "__export_source": "insomnia.desktop.app:v2022.5.1", "resources": [{"_id": "req_3eee448d750b4c7da68778111246f60e", "parentId": "fld_e1246429b6fa471cb9b3250e9a71c816", "modified": 1663698029740, "created": 1663693833409, "url": "{{ _.base_url }}/api/v{{ _.version }}/company", "name": "company", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [{"id": "pair_af924b5f039343cb8b7e1b991f4584b4", "name": "Content-Type", "value": "application/json", "description": ""}, {"id": "pair_61c15aca4375488e894f51633f6fcaa7", "name": "auth-token", "value": "{{ _.auth_token }}", "description": ""}, {"id": "pair_9a522c2c096c48959796c185f555dbbf", "name": "company-id", "value": "{{ _.company_id }}", "description": ""}], "authentication": {"type": "bearer", "token": "{{ _.app_token }}"}, "metaSortKey": -1663300847710, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "fld_e1246429b6fa471cb9b3250e9a71c816", "parentId": "fld_b5c95356f2734cb5891ef646ba3616c5", "modified": 1663693810286, "created": 1663693810286, "name": "company", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1663693810286, "_type": "request_group"}, {"_id": "fld_b5c95356f2734cb5891ef646ba3616c5", "parentId": "wrk_b8d85e45fcc14fe9bad0b7d5d5544ccf", "modified": 1662975993007, "created": 1662975993007, "name": "v0", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1662975993007, "_type": "request_group"}, {"_id": "wrk_b8d85e45fcc14fe9bad0b7d5d5544ccf", "parentId": null, "modified": 1662975978357, "created": 1662975942487, "name": "robonote", "description": "", "scope": "collection", "_type": "workspace"}, {"_id": "req_9134ea5be1904c21b31e7cd1a4d06103", "parentId": "fld_e1246429b6fa471cb9b3250e9a71c816", "modified": 1663698032148, "created": 1663694006359, "url": "{{ _.base_url }}/api/v{{ _.version }}/company", "name": "company", "description": "", "method": "PUT", "body": {"mimeType": "application/json", "text": "{\n  \"name\": \"naga\",\n\t\"lower_threshold_bar\": 0.12,\n  \"upper_threshold_bar\": 0.6,\n\t\"avatar\": \"data:image/png;base64,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\"\n}"}, "parameters": [], "headers": [{"id": "pair_af924b5f039343cb8b7e1b991f4584b4", "name": "Content-Type", "value": "application/json", "description": ""}, {"id": "pair_61c15aca4375488e894f51633f6fcaa7", "name": "auth-token", "value": "{{ _.auth_token }}", "description": ""}, {"id": "pair_abe189617877481faad0a33f7acfc7bf", "name": "company-id", "value": "{{ _.company_id }}", "description": ""}], "authentication": {"type": "bearer", "token": "{{ _.app_token }}"}, "metaSortKey": -1663287627550.75, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_222e236eac0d497cabe966b0e7759f63", "parentId": "fld_db9a8d27a47b491289482b709eae1be3", "modified": 1663693899123, "created": 1663328574216, "url": "{{ _.base_url }}/api/v{{ _.version }}/auth", "name": "/auth", "description": "", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n\t\"avatar\": \"data:image/png;base64,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\",\n  \"name\": \"siarhei\",\n  \"email\": \"{{ _.email }}\",\n\t\"password\": \"{{ _.pass }}\",\n\t\"password_confirm\": \"{{ _.pass }}\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_92e7ec32bdb9444898f41bc68c1c0ae2"}], "authentication": {"type": "bearer", "token": "{{ _.app_token }}"}, "metaSortKey": -1663283220831, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "fld_db9a8d27a47b491289482b709eae1be3", "parentId": "fld_b5c95356f2734cb5891ef646ba3616c5", "modified": 1663318456039, "created": 1663318453088, "name": "auth", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1662976371894, "_type": "request_group"}, {"_id": "req_7ecdf34465814a35a483a60f16d86b39", "parentId": "fld_db9a8d27a47b491289482b709eae1be3", "modified": 1663692923657, "created": 1663495859388, "url": "{{ _.base_url }}/api/v{{ _.version }}/auth", "name": "/auth (without code)", "description": "", "method": "PUT", "body": {"mimeType": "application/json", "text": "{\n  \"email\": \"{{ _.email }}\",\n\t\"password\": \"{{ _.pass }}\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_92e7ec32bdb9444898f41bc68c1c0ae2"}], "authentication": {"type": "bearer", "token": "{{ _.app_token }}"}, "metaSortKey": -1663274407391.5, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_332c19bd14d54fb18071d0c1247598a7", "parentId": "fld_db9a8d27a47b491289482b709eae1be3", "modified": 1663597645732, "created": 1663328727437, "url": "{{ _.base_url }}/api/v{{ _.version }}/auth", "name": "/auth (with code)", "description": "", "method": "PUT", "body": {"mimeType": "application/json", "text": "{\n  \"email\": \"{{ _.email }}\",\n\t\"password\": \"{{ _.pass }}\",\n\t\"code\": 895054\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_92e7ec32bdb9444898f41bc68c1c0ae2"}], "authentication": {"type": "bearer", "token": "{{ _.app_token }}"}, "metaSortKey": -1663265593952, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_fb3a97e5164945428ed8c95982088fe0", "parentId": "fld_db9a8d27a47b491289482b709eae1be3", "modified": 1663692926767, "created": 1663329630455, "url": "{{ _.base_url }}/api/v{{ _.version }}/auth", "name": "/auth", "description": "", "method": "DELETE", "body": {"mimeType": "application/json", "text": ""}, "parameters": [], "headers": [{"id": "pair_a407c73af41b44a8b5a1ace12d36f4e1", "name": "auth-token", "value": "{{ _.auth_token }}", "description": ""}], "authentication": {"type": "bearer", "token": "{{ _.app_token }}"}, "metaSortKey": -1663265593902, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_42f8c876fad54bc4a2f43858f01eb827", "parentId": "fld_db9a8d27a47b491289482b709eae1be3", "modified": 1663692928597, "created": 1663500920434, "url": "{{ _.base_url }}/api/v{{ _.version }}/auth/password-recovery", "name": "/auth/password-recovery", "description": "", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n  \"email\": \"{{ _.email }}\",\n\t\"redirect_url\": \"https://robonote.io/update-password/{code}\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_92e7ec32bdb9444898f41bc68c1c0ae2"}], "authentication": {"type": "bearer", "token": "{{ _.app_token }}"}, "metaSortKey": -1663265593852, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_798e635b8f724b17b70229f1e5866186", "parentId": "fld_db9a8d27a47b491289482b709eae1be3", "modified": 1663692931500, "created": 1663504848619, "url": "{{ _.base_url }}/api/v{{ _.version }}/auth/password-recovery", "name": "/auth/password-recovery", "description": "", "method": "PUT", "body": {"mimeType": "application/json", "text": "{\n  \"email\": \"{{ _.email }}\",\n\t\"code\": \"632710e37de0f2.05146357\",\n\t\"password\": \"{{ _.pass }}\",\n\t\"password_confirm\": \"{{ _.pass }}\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_92e7ec32bdb9444898f41bc68c1c0ae2"}], "authentication": {"type": "bearer", "token": "{{ _.app_token }}"}, "metaSortKey": -1663256780462.5, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_b1dc4310bf8b4848ac35a4c15a8602d1", "parentId": "fld_630f241f44d3429c8479bd7901a542a4", "modified": 1663697208764, "created": 1663318474589, "url": "{{ _.base_url }}/api/v{{ _.version }}/user", "name": "user", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [{"id": "pair_af924b5f039343cb8b7e1b991f4584b4", "name": "Content-Type", "value": "application/json", "description": ""}, {"id": "pair_61c15aca4375488e894f51633f6fcaa7", "name": "auth-token", "value": "{{ _.auth_token }}", "description": ""}], "authentication": {"type": "bearer", "token": "{{ _.app_token }}"}, "metaSortKey": -1663318474589, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "fld_630f241f44d3429c8479bd7901a542a4", "parentId": "fld_b5c95356f2734cb5891ef646ba3616c5", "modified": 1663318466639, "created": 1663318463712, "name": "user", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1662976371869, "_type": "request_group"}, {"_id": "req_8dbd96d0f2d04209bbbaf8d36f25f1f7", "parentId": "fld_630f241f44d3429c8479bd7901a542a4", "modified": 1663697209751, "created": 1663695572578, "url": "{{ _.base_url }}/api/v{{ _.version }}/user/companies", "name": "user/companies", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [{"id": "pair_af924b5f039343cb8b7e1b991f4584b4", "name": "Content-Type", "value": "application/json", "description": ""}, {"id": "pair_61c15aca4375488e894f51633f6fcaa7", "name": "auth-token", "value": "{{ _.auth_token }}", "description": ""}], "authentication": {"type": "bearer", "token": "{{ _.app_token }}"}, "metaSortKey": -1663309661149.5, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_dc44ecc6363c4287926989a4a9101030", "parentId": "fld_b5c95356f2734cb5891ef646ba3616c5", "modified": 1663666619608, "created": 1662976371844, "url": "{{ _.base_url }}/api/v{{ _.version }}/health-check", "name": "health-check", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [{"id": "pair_e46e98070c6e400092158df780b8c532", "name": "", "value": "", "description": ""}], "authentication": {"type": "bearer", "token": "{{ _.app_token }}"}, "metaSortKey": -1662976371844, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "env_247d360863cf9322c333df8c9c2b760189c24d82", "parentId": "wrk_b8d85e45fcc14fe9bad0b7d5d5544ccf", "modified": 1663694110026, "created": 1662975942495, "name": "Base Environment", "data": {"version": 0}, "dataPropertyOrder": {"&": ["version"]}, "color": null, "isPrivate": false, "metaSortKey": 1662975942495, "_type": "environment"}, {"_id": "jar_247d360863cf9322c333df8c9c2b760189c24d82", "parentId": "wrk_b8d85e45fcc14fe9bad0b7d5d5544ccf", "modified": 1662975942501, "created": 1662975942501, "name": "<PERSON><PERSON><PERSON>", "cookies": [], "_type": "cookie_jar"}, {"_id": "spc_274939af5ad54230b42fd66d63712eff", "parentId": "wrk_b8d85e45fcc14fe9bad0b7d5d5544ccf", "modified": 1662975942488, "created": 1662975942488, "fileName": "Robonote", "contents": "", "contentType": "yaml", "_type": "api_spec"}, {"_id": "env_5893baf191ba4a3f9ea345897b24f0ba", "parentId": "env_247d360863cf9322c333df8c9c2b760189c24d82", "modified": 1663694115157, "created": 1662976115762, "name": "local", "data": {"base_url": "http://localhost:8015", "email": "<EMAIL>", "pass": "*********", "app_token": "R^kMX7dAQXxafxdJNmqBnzLATVrJX4gn", "auth_token": "1-72683b1bba431dbf03f619372598ebff6f3767bdba61da359ee23bbfc36646", "company_id": 1}, "dataPropertyOrder": {"&": ["base_url", "email", "pass", "app_token", "auth_token", "company_id"]}, "color": "#a6a6a6", "isPrivate": false, "metaSortKey": 1662976115762, "_type": "environment"}, {"_id": "env_81e186f8c2e84ebda444b58dfb2aeed2", "parentId": "env_247d360863cf9322c333df8c9c2b760189c24d82", "modified": 1663694114727, "created": 1662976164752, "name": "stage", "data": {"base_url": "https://stage.api.robonote.io", "auth_token": "", "email": "", "pass": "", "app_token": "", "company_id": 0}, "dataPropertyOrder": {"&": ["base_url", "auth_token", "email", "pass", "app_token", "company_id"]}, "color": "#229e00", "isPrivate": false, "metaSortKey": 1662976164752, "_type": "environment"}]}